
// stdafx.cpp : source file that includes just the standard includes
// AterEx.pch will be the pre-compiled header
// stdafx.obj will contain the pre-compiled type information

#include "stdafx.h"
#include "Utility.h"

#ifdef _DEBUG
#pragma comment(lib,"Utility_d.lib")
#pragma comment(lib,"UtilityCompanion_d.lib")
#pragma comment(lib,"StatusUpload_d.lib")
#pragma comment(lib,"skynet_public_fun_d.lib")
#pragma comment(lib,"LibSQLOperate_d.lib")
#else
#pragma comment(lib,"Utility.lib")
#pragma comment(lib,"UtilityCompanion.lib")
#pragma comment(lib,"StatusUpload.lib")
#pragma comment(lib,"skynet_public_fun.lib")
#pragma comment(lib,"LibSQLOperate.lib")
#endif

std::string GetAterExLocalConfigFilePath()
{
	std::string strLocalConfigFilePath = GetSystemRootDir() + "\\Yeestor\\";
	strLocalConfigFilePath += ATEREX_LOCAL_CONFIG_FILE;
	return strLocalConfigFilePath;
}

std::string GetTempIniFilePath()
{
	S8 strTempPath[MAX_PATH] = {0};
	GetTempPath(MAX_PATH, strTempPath);
	std::string tempPath = strTempPath;
	tempPath.pop_back();
	tempPath += MATERIALNO_PATH;

	return tempPath;
}

std::string GetAterExMapLocFilePath()
{
	std::string strLocalConfigFilePath = GetSystemRootDir() + "\\Yeestor\\";
	strLocalConfigFilePath += ATEREX_MAP_LOC_FILE;
	return strLocalConfigFilePath;
}

std::string g_DiskType[] = {"U2Disk",
							"U3Disk",
							"SDCard",		
							"EMMC",
							"SSD",	
							"SSD",	
						    };