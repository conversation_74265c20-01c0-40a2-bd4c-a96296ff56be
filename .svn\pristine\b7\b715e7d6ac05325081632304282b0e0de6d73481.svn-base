import pyodbc
import ufssummary
from openpyxl.styles import PatternFill, colors, Font, Alignment, Border, Side
from openpyxl.utils import get_column_letter
spealist = ['Front_end_1','Front_end_2','Front_end_3','Front_end_4','Front_end_5','Front_end_6','Front_end_7','Front_end_8']

def Run(workBook, alignment,test_no):
    ws = workBook['Test results']
    target_sheet = workBook.copy_worksheet(ws)
    target_sheet.title = 'Test summary'
    target_sheet.unmerge_cells('E1:E2')
    target_sheet.column_dimensions['E'].width = 9
    ws.alignment = alignment

    current_idx = workBook.index(target_sheet)
    target_idx = 3
    # 计算偏移量（若目标位置超过当前工作表总数，需调整）
    offset = target_idx - current_idx

    workBook.move_sheet("Test summary", offset) 
    skynetConn = pyodbc.connect('DRIVER={SQL Server};SERVER=***********;DATABASE=ys_rms;UID=yswms;PWD=**************')
    cursor = skynetConn.cursor()
    endline = 6
    time = -1
    sql = '''select * from report_ufs_result where TestNo = '%s'  order by MAC,Category_1,Category_2,Script,SampleID '''%(test_no)
    newdata = []
    tempws = []
    time += 1
    cursor.execute(sql)
    rows = cursor.fetchall()
    if rows == []:
        return
    for row in ws.iter_rows():
        temp = ['','','','']
        if row[0].value != None:
            temp[0] = row[0].value  
        if row[1].value != None:
            temp[1]  = row[1].value 
        if row[2].value != None:
            temp[2]  = row[2].value 
        if row[3].value != None:
            temp[3]  = row[3].value
        tempws.append(temp)
    GetNewDic(newdata,rows,cursor)
    sheet_title = test_no.split('_')[-1]
    endline = test_run(ws,newdata,endline,endline+7,tempws,ws.max_row,sheet_title)
    ufssummary.Run(workBook,newdata,tempws,sheet_title,time)

def write_excel(data,worksheet,startline,startcol):
    worksheet.row_dimensions[startline].height = 14
    alignment=Alignment(horizontal='center',vertical='center')
    worksheet.cell(row=startline, column=5, value=data['OrgCnt']).alignment = alignment
    worksheet.cell(row=startline, column=startcol, value=str(data['SampleID']+'#')).alignment = alignment
    startcol += 1
    resultfont = Font('等线',size=12,color=colors.BLACK)
    if data['Result'] == 0:
        data['Result'] = 'pass'
    elif data['Result'] == 1:
        data['Result'] = 'fail'
        resultfont = Font('等线',size=12,color='00FF0000')
    elif data['Result'] == 2:
        data['Result'] = 'ongoing'
    worksheet.cell(row=startline, column=startcol, value=data['Result']).alignment = alignment
    worksheet.cell(row=startline, column=startcol, value=data['Result']).font = resultfont
    startcol += 1 
    worksheet.cell(row=startline, column=startcol, value=data['Version']).alignment = alignment
    startcol += 1 
    worksheet.cell(row=startline, column=startcol, value=data['PassCnt']).alignment = alignment
    startcol += 1 
    worksheet.cell(row=startline, column=startcol, value=data['FailCnt']).alignment = alignment
    worksheet.cell(row=startline, column=startcol, value=data['FailCnt']).font = resultfont
    startcol += 1 
    worksheet.cell(row=startline, column=startcol, value=data['case']).alignment = Alignment(horizontal='left',vertical='center',wrap_text=True)
    worksheet.cell(row=startline, column=startcol, value=data['case']).font = resultfont

def GetNewDic(Newdic,data,cursor):
    for item in data:
        struct = {}
        struct['SampleID'] = item.SampleID
        struct['Result'] = item.Result
        struct['PassCnt'] = item.PassCnt
        struct['FailCnt'] = item.FailCnt
        struct['Version'] = item.Version
        struct['Category_1'] = item.Category_1
        struct['Category_2'] = item.Category_2
        struct['OrgCnt'] = item.OrgCnt
        struct['Script'] = item.Script
        struct['case'] = ''
        key = 1
        if struct['Result'] == 1:
            sql = '''select * from report_ufs where TestNo = '%s' and mac = '%s' and StartTime = '%s' and Category_1 = '%s' and Category_2 = '%s' and script = '%s' and Xu4_id = '%s' and sample_id = '%s' and result = '%s' '''%(item.TestNo,item.MAC,item.StartTime,item.Category_1,item.Category_2,item.Script,item.Xu4_id,item.SampleID,item.Result)
            cursor.execute(sql)
            rows = cursor.fetchall()
            if rows != []:
                for row in rows:
                    struct['case'] += row.case + '\n'
            else:
                key = 0
        if key == 1:
            Newdic.append(struct)

def test_run(ws,newdata,startcol,endcol,tempws,allrows,sheet_title):
    (Category_1,Category_2_1,Category_2_2,Script) = ('','','','')
    maxcol = startcol
    set_sheetstyle(startcol,ws,allrows)
    for item in newdata:
        line = 0
        key = 0
        for row in tempws:
            line += 1
            if row[0] != '':
                Category_1 = row[0]
            if row[1] != '':
                Category_2_1 = row[1]
            if row[2] != '':
                Category_2_2 = Category_2_1 +';'+row[2] 
            if row[3] != '':
                Script = row[3]
            if Category_1 == 'FV' and item['Category_1'] == 'Full_Test' and item['Category_2'] in spealist and item['Script'] == Script:
                if item['Category_2'] == Category_2_1:
                    key = 1
                    break
            if item['Category_1'] == Category_1 and item['Script'] == Script:
                if item['Category_2'] == '' or item['Category_2'] == Category_2_1 or item['Category_2'] == Category_2_2:
                    key = 1
                    break
        if key == 1:
            col = startcol
            cell = ws.cell(row=line, column=col)
            if cell.value != None:
                sheet_key = 0
                while(1):
                    cell = ws.cell(row=line, column=col) 
                    if cell.value == None:
                        break
                    col += 7
                    if col > maxcol:
                        sheet_key = 1
                    maxcol = max(col,maxcol)
                if sheet_key == 1:
                    set_sheetstyle(col,ws,allrows)
            write_excel(item,ws,line,col)
    set_sheettile(startcol,maxcol,ws,sheet_title)
    return maxcol + 7

def set_sheettile(startcol,col,ws,title):
    font=Font('Calibri',size=12,color=colors.BLACK,bold=True)
    alignment=Alignment(horizontal='center',vertical='center')
    bd = Border(left=Side(border_style='thin'),
                right=Side(border_style='thin'))
    ws.merge_cells(start_row=1, start_column=startcol, end_row=1, end_column=col+6)
    cell = ws.cell(row=1, column=startcol,value = title) 
    cell.fill = PatternFill("solid", fgColor="B5C6EA")
    cell.font = font
    cell.alignment = alignment
    cell.border = bd

def set_sheetstyle(col,ws,allrows):
    cellist = ['Sample ID','Result','Version','Pass Case No.','Fail Case No.','Fail case','Remark']
    font=Font('Calibri',size=12,color=colors.BLACK,bold=True)
    alignment=Alignment(horizontal='center',vertical='center')
    bd = Border(left=Side(border_style='thin'),
                right=Side(border_style='thin'),
                top=Side(border_style='thin'),
                bottom=Side(border_style='thin'))
    for row in ws.iter_rows(min_row=2, max_row=allrows, min_col=col, max_col=col+6):
        for cell in row:
            cell.border = bd
            cell.font = Font('等线',size=12,color=colors.BLACK)
    column_letter = get_column_letter(col+2)
    ws.column_dimensions[column_letter].width = 15
    column_letter = get_column_letter(col+5)
    ws.column_dimensions[column_letter].width = 30
    column_letter = get_column_letter(col+6)
    ws.column_dimensions[column_letter].width = 30
    for item in cellist:
        cell = ws.cell(row=2, column=col, value=item)
        col += 1 
        cell.fill = PatternFill("solid", fgColor="B5C6EA")
        cell.font = font
        cell.alignment = alignment
