import PublicFuc
from openpyxl.styles import Font, Alignment
font = Font(bold = True, size = 10)
align = Alignment(horizontal='center', vertical='center')

startLine = 12
wlKeyLst=['errcode','datacrc','cmdcrc','vdt','wdtr','slcbadblock_new','tlcbadblock_new','waf','tbw','tbr','wl_slc_max','wl_slc_min','wl_slc_avg','wl_tlc_max','wl_tlc_min','wl_tlc_avg']
colKeyLst = wlKeyLst+ ['slc_diff','tlc_diff','wear_avg']
colLst = ['C','D','E','F','G','H','I','J','K','L','M', 'Q','R','S','T','U','V']+['N','O','P']
wlMaxDic = {}
def Run(curpath, workBook, alignment):
    ws = workBook['WL']
    ws.alignment = alignment
    ProWl(curpath, ws)
    PublicFuc.WriteReportTime(ws,'H',1)
    PublicFuc.WriteReportOperator(ws,'L',1)

def SetFirstCol(ws, dataDic, item):
    global startLine
    lineCnt = 0
    for disk in dataDic:
        lineCnt += len(dataDic[disk])
    if lineCnt > 0:
        ws.merge_cells('A%d:B%d'%(startLine, startLine+lineCnt-1))
        ws['A%d'%startLine].alignment = align
        ws['A%d'%startLine].font = font
        ws['A%d'%startLine] = item
        startLine += lineCnt

def GetNewMarsDic(caseDic):
    newDic = {}
    for disk in caseDic:
        newDic[disk] = []
        for dicData in caseDic[disk]:
            tempDic = {}
            for key in wlKeyLst:
                if key not in dicData:
                    tempDic[key] = ''
                else:
                    value = dicData[key]
                    if value.startswith('0x'):
                        value = int(value,16)
                    tempDic[key] = value
            if '' != tempDic['wl_slc_max'] and '' != tempDic['wl_slc_min']:
                tempDic['slc_diff'] = tempDic['wl_slc_max']-tempDic['wl_slc_min']
            else:
                tempDic['slc_diff'] = ''
            if '' != tempDic['wl_tlc_max'] and '' != tempDic['wl_tlc_min']:
                tempDic['tlc_diff'] = tempDic['wl_tlc_max']-tempDic['wl_tlc_min']
            else:
                tempDic['tlc_diff'] = ''
            if '' != tempDic['wl_slc_avg'] and '' != tempDic['wl_tlc_avg'] and 0 != tempDic['wl_tlc_avg']:
                tempDic['wear_avg'] = round(tempDic['wl_slc_avg']/tempDic['wl_tlc_avg'], 2)
            else:
                tempDic['wear_avg'] = ''
            newDic[disk].append(tempDic)

    return newDic

def ProH2(curpath, ws):
    pattern = '.+\\\\Plan14\\\\T_EM_NA_C7\\\\H2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    pattern = '.+\\\\Plan15\\\\T_EM_NA_C7\\\\H2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    pattern = '.+\\\\Plan16\\\\T_EM_NA_C7\\\\H2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    pattern = '.+\\\\Plan1\\\\T_EM_NA_C7\\\\H2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)

    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'H2testw')

def ProCopyFile(curpath, ws):
    pattern = '.+\\\\Plan14\\\\T_EM_NA_C8\\\\CopyFile\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_CopyFile'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    pattern = '.+\\\\Plan15\\\\T_EM_NA_C8\\\\CopyFile\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    pattern = '.+\\\\Plan16\\\\T_EM_NA_C8\\\\CopyFile\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    pattern = '.+\\\\Plan20\\\\T_EM_NA_C8\\\\CopyFile\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    pattern = '.+\\\\Plan1\\\\T_EM_NA_C8\\\\CopyFile\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'Copy File/Compare')

def ProImtSmart(curpath, ws):
    pattern = '.+\\\\Plan7\\\\T_EM_NA_C10\\\\ReadSmart\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'Iometer 4K  W/R 12H')

def ProPOR(curpath, ws):
    pattern = '.+\\\\Plan14\\\\T_EM_NA_C28\\\\POR\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'POR'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    pattern = '.+\\\\Plan15\\\\T_EM_NA_C28\\\\POR\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'MS-POR (20000 cycles)')

def ProFixSectionSPOR1(curpath, ws):
    caseName = 'FixSectionSPOR1'
    resultDic = {}
    pattern = '.+\\\\Plan16\\\\T_EM_NA_D01\\\\FixSectionSPOR1\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'FixSectionSPOR1 (10000 cycles)')

def ProHCTest(curpath, ws):
    caseName = 'HCTest'
    resultDic = {}
    pattern = '.+\\\\Plan20\\\\T_EM_NA_C45\\\\HCTest\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'MS-HCTest')

def ProFixSectionSPOR2(curpath, ws):
    caseName = 'FixSectionSPOR2'
    resultDic = {}
    pattern = '.+\\\\Plan1\\\\T_EM_NA_D16\\\\FixSectionSPOR2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'FixSectionSPOR2 (10000 cycles)')

def ProStaitcSPOR(curpath, ws):
    caseName = 'StaticSpor'
    resultDic = {}
    pattern = '.+\\\\Plan1\\\\T_EM_NA_D17\\\\StaticSpor\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'StaticSPOR (10000 cycles)')

def ProSPOR(curpath, ws):
    pattern = '.+\\\\Plan14\\\\T_EM_NA_C27\\\\SPOR\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'SPOR'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    pattern = '.+\\\\Plan15\\\\T_EM_NA_C27\\\\SPOR\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    pattern = '.+\\\\Plan20\\\\T_EM_NA_C27\\\\SPOR\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'MS-SPOR (15000 cycles)')

def ProSPORPart(curpath, ws):
    pattern = '.+\\\\Plan16\\\\T_EM_NA_C29\\\\HL Freq SPOR\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'SPORPart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
   
    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'SPOR Part  (5000 cycles)')

def ProBIT(curpath, ws):
    pattern = '.+\\\\Plan14\\\\T_EM_NA_C37\\\\H2_Verify\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_Verify_H2'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    pattern = '.+\\\\Plan15\\\\T_EM_NA_C37\\\\H2_Verify\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    pattern = '.+\\\\Plan16\\\\T_EM_NA_C37\\\\H2_Verify\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    caseName = 'AT_Verify_CF'
    pattern = '.+\\\\Plan20\\\\T_EM_NA_C37\\\\CopyFile_Verify\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'RT BIT 1')

def ProWl(curpath, ws):
    ProH2(curpath, ws)
    ProCopyFile(curpath, ws)
    ProImtSmart(curpath, ws)
    ProPOR(curpath, ws)
    ProSPOR(curpath, ws)
    ProSPORPart(curpath, ws)
    ProBIT(curpath, ws)
    ProFixSectionSPOR1(curpath, ws)
    ProHCTest(curpath, ws)
    ProFixSectionSPOR2(curpath, ws)
    ProStaitcSPOR(curpath, ws)

