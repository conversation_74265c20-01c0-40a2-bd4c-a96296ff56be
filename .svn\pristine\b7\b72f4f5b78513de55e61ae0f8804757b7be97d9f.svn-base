if(!document.createElement("canvas").getContext){(function(){var R=Math;var S=R.round;var O=R.sin;var a=R.cos;var J=R.abs;var Y=R.sqrt;var A=10;var K=A/2;function G(){return this.context_||(this.context_=new M(this))}var Q=Array.prototype.slice;function b(c,d,e){var Z=Q.call(arguments,2);return function(){return c.apply(d,Z.concat(Q.call(arguments)))}}var H={init:function(Z){if(/MSIE/.test(navigator.userAgent)&&!window.opera){var c=Z||document;c.createElement("canvas");c.attachEvent("onreadystatechange",b(this.init_,this,c))}},init_:function(e){if(!e.namespaces.g_vml_){e.namespaces.add("g_vml_","urn:schemas-microsoft-com:vml","#default#VML")}if(!e.namespaces.g_o_){e.namespaces.add("g_o_","urn:schemas-microsoft-com:office:office","#default#VML")}if(!e.styleSheets.ex_canvas_){var d=e.createStyleSheet();d.owningElement.id="ex_canvas_";d.cssText="canvas{display:inline-block;overflow:hidden;text-align:left;width:300px;height:150px}g_vml_\\:*{behavior:url(#default#VML)}g_o_\\:*{behavior:url(#default#VML)}"}var c=e.getElementsByTagName("canvas");for(var Z=0;Z<c.length;Z++){this.initElement(c[Z])}},initElement:function(c){if(!c.getContext){c.getContext=G;c.innerHTML="";c.attachEvent("onpropertychange",X);c.attachEvent("onresize",B);var Z=c.attributes;if(Z.width&&Z.width.specified){c.style.width=Z.width.nodeValue+"px"}else{c.width=c.clientWidth}if(Z.height&&Z.height.specified){c.style.height=Z.height.nodeValue+"px"}else{c.height=c.clientHeight}}return c}};function X(c){var Z=c.srcElement;switch(c.propertyName){case"width":Z.style.width=Z.attributes.width.nodeValue+"px";Z.getContext().clearRect();break;case"height":Z.style.height=Z.attributes.height.nodeValue+"px";Z.getContext().clearRect();break}}function B(c){var Z=c.srcElement;if(Z.firstChild){Z.firstChild.style.width=Z.clientWidth+"px";Z.firstChild.style.height=Z.clientHeight+"px"}}H.init();var E=[];for(var V=0;V<16;V++){for(var U=0;U<16;U++){E[V*16+U]=V.toString(16)+U.toString(16)}}function N(){return[[1,0,0],[0,1,0],[0,0,1]]}function D(e,d){var c=N();for(var Z=0;Z<3;Z++){for(var h=0;h<3;h++){var f=0;for(var g=0;g<3;g++){f+=e[Z][g]*d[g][h]}c[Z][h]=f}}return c}function T(c,Z){Z.fillStyle=c.fillStyle;Z.lineCap=c.lineCap;Z.lineJoin=c.lineJoin;Z.lineWidth=c.lineWidth;Z.miterLimit=c.miterLimit;Z.shadowBlur=c.shadowBlur;Z.shadowColor=c.shadowColor;Z.shadowOffsetX=c.shadowOffsetX;Z.shadowOffsetY=c.shadowOffsetY;Z.strokeStyle=c.strokeStyle;Z.globalAlpha=c.globalAlpha;Z.arcScaleX_=c.arcScaleX_;Z.arcScaleY_=c.arcScaleY_;Z.lineScale_=c.lineScale_}function C(c){var f,e=1;c=String(c);if(c.substring(0,3)=="rgb"){var h=c.indexOf("(",3);var Z=c.indexOf(")",h+1);var g=c.substring(h+1,Z).split(",");f="#";for(var d=0;d<3;d++){f+=E[Number(g[d])]}if(g.length==4&&c.substr(3,1)=="a"){e=g[3]}}else{f=c}return{color:f,alpha:e}}function P(Z){switch(Z){case"butt":return"flat";case"round":return"round";case"square":default:return"square"}}function M(c){this.m_=N();this.mStack_=[];this.aStack_=[];this.currentPath_=[];this.strokeStyle="#000";this.fillStyle="#000";this.lineWidth=1;this.lineJoin="miter";this.lineCap="butt";this.miterLimit=A*1;this.globalAlpha=1;this.canvas=c;var Z=c.ownerDocument.createElement("div");Z.style.width=c.clientWidth+"px";Z.style.height=c.clientHeight+"px";Z.style.overflow="hidden";Z.style.position="absolute";c.appendChild(Z);this.element_=Z;this.arcScaleX_=1;this.arcScaleY_=1;this.lineScale_=1}var I=M.prototype;I.clearRect=function(){this.element_.innerHTML=""};I.beginPath=function(){this.currentPath_=[]};I.moveTo=function(c,Z){var d=this.getCoords_(c,Z);this.currentPath_.push({type:"moveTo",x:d.x,y:d.y});this.currentX_=d.x;this.currentY_=d.y};I.lineTo=function(c,Z){var d=this.getCoords_(c,Z);this.currentPath_.push({type:"lineTo",x:d.x,y:d.y});this.currentX_=d.x;this.currentY_=d.y};I.bezierCurveTo=function(d,c,j,i,h,f){var Z=this.getCoords_(h,f);var g=this.getCoords_(d,c);var e=this.getCoords_(j,i);L(this,g,e,Z)};function L(Z,e,d,c){Z.currentPath_.push({type:"bezierCurveTo",cp1x:e.x,cp1y:e.y,cp2x:d.x,cp2y:d.y,x:c.x,y:c.y});Z.currentX_=c.x;Z.currentY_=c.y}I.quadraticCurveTo=function(h,d,c,Z){var g=this.getCoords_(h,d);var f=this.getCoords_(c,Z);var i={x:this.currentX_+2/3*(g.x-this.currentX_),y:this.currentY_+2/3*(g.y-this.currentY_)};var e={x:i.x+(f.x-this.currentX_)/3,y:i.y+(f.y-this.currentY_)/3};L(this,i,e,f)};I.arc=function(k,i,j,f,c,d){j*=A;var o=d?"at":"wa";var l=k+a(f)*j-K;var n=i+O(f)*j-K;var Z=k+a(c)*j-K;var m=i+O(c)*j-K;if(l==Z&&!d){l+=0.125}var e=this.getCoords_(k,i);var h=this.getCoords_(l,n);var g=this.getCoords_(Z,m);this.currentPath_.push({type:o,x:e.x,y:e.y,radius:j,xStart:h.x,yStart:h.y,xEnd:g.x,yEnd:g.y})};I.rect=function(d,c,Z,e){this.moveTo(d,c);this.lineTo(d+Z,c);this.lineTo(d+Z,c+e);this.lineTo(d,c+e);this.closePath()};I.strokeRect=function(d,c,Z,e){var f=this.currentPath_;this.beginPath();this.moveTo(d,c);this.lineTo(d+Z,c);this.lineTo(d+Z,c+e);this.lineTo(d,c+e);this.closePath();this.stroke();this.currentPath_=f};I.fillRect=function(d,c,Z,e){var f=this.currentPath_;this.beginPath();this.moveTo(d,c);this.lineTo(d+Z,c);this.lineTo(d+Z,c+e);this.lineTo(d,c+e);this.closePath();this.fill();this.currentPath_=f};I.createLinearGradient=function(c,e,Z,d){var f=new W("gradient");f.x0_=c;f.y0_=e;f.x1_=Z;f.y1_=d;return f};I.createRadialGradient=function(e,g,d,c,f,Z){var h=new W("gradientradial");h.x0_=e;h.y0_=g;h.r0_=d;h.x1_=c;h.y1_=f;h.r1_=Z;return h};I.drawImage=function(s,e){var l,j,n,AA,q,o,u,AC;var m=s.runtimeStyle.width;var r=s.runtimeStyle.height;s.runtimeStyle.width="auto";s.runtimeStyle.height="auto";var k=s.width;var y=s.height;s.runtimeStyle.width=m;s.runtimeStyle.height=r;if(arguments.length==3){l=arguments[1];j=arguments[2];q=o=0;u=n=k;AC=AA=y}else{if(arguments.length==5){l=arguments[1];j=arguments[2];n=arguments[3];AA=arguments[4];q=o=0;u=k;AC=y}else{if(arguments.length==9){q=arguments[1];o=arguments[2];u=arguments[3];AC=arguments[4];l=arguments[5];j=arguments[6];n=arguments[7];AA=arguments[8]}else{throw Error("Invalid number of arguments")}}}var AB=this.getCoords_(l,j);var f=u/2;var c=AC/2;var z=[];var Z=10;var i=10;z.push(" <g_vml_:group",' coordsize="',A*Z,",",A*i,'"',' coordorigin="0,0"',' style="width:',Z,"px;height:",i,"px;position:absolute;");if(this.m_[0][0]!=1||this.m_[0][1]){var g=[];g.push("M11=",this.m_[0][0],",","M12=",this.m_[1][0],",","M21=",this.m_[0][1],",","M22=",this.m_[1][1],",","Dx=",S(AB.x/A),",","Dy=",S(AB.y/A),"");var x=AB;var v=this.getCoords_(l+n,j);var t=this.getCoords_(l,j+AA);var p=this.getCoords_(l+n,j+AA);x.x=R.max(x.x,v.x,t.x,p.x);x.y=R.max(x.y,v.y,t.y,p.y);z.push("padding:0 ",S(x.x/A),"px ",S(x.y/A),"px 0;filter:progid:DXImageTransform.Microsoft.Matrix(",g.join(""),", sizingmethod='clip');")}else{z.push("top:",S(AB.y/A),"px;left:",S(AB.x/A),"px;")}z.push(' ">','<g_vml_:image src="',s.src,'"',' style="width:',A*n,"px;"," height:",A*AA,'px;"',' cropleft="',q/k,'"',' croptop="',o/y,'"',' cropright="',(k-q-u)/k,'"',' cropbottom="',(y-o-AC)/y,'"'," />","</g_vml_:group>");this.element_.insertAdjacentHTML("BeforeEnd",z.join(""))};I.stroke=function(AE){var j=[];var k=false;var AP=C(AE?this.fillStyle:this.strokeStyle);var AA=AP.color;var AK=AP.alpha*this.globalAlpha;var f=10;var m=10;j.push("<g_vml_:shape",' filled="',!!AE,'"',' style="position:absolute;width:',f,"px;height:",m,'px;"',' coordorigin="0 0" coordsize="',A*f," ",A*m,'"',' stroked="',!AE,'"',' path="');var l=false;var AO={x:null,y:null};var w={x:null,y:null};for(var AJ=0;AJ<this.currentPath_.length;AJ++){var AI=this.currentPath_[AJ];var AN;switch(AI.type){case"moveTo":AN=AI;j.push(" m ",S(AI.x),",",S(AI.y));break;case"lineTo":j.push(" l ",S(AI.x),",",S(AI.y));break;case"close":j.push(" x ");AI=null;break;case"bezierCurveTo":j.push(" c ",S(AI.cp1x),",",S(AI.cp1y),",",S(AI.cp2x),",",S(AI.cp2y),",",S(AI.x),",",S(AI.y));break;case"at":case"wa":j.push(" ",AI.type," ",S(AI.x-this.arcScaleX_*AI.radius),",",S(AI.y-this.arcScaleY_*AI.radius)," ",S(AI.x+this.arcScaleX_*AI.radius),",",S(AI.y+this.arcScaleY_*AI.radius)," ",S(AI.xStart),",",S(AI.yStart)," ",S(AI.xEnd),",",S(AI.yEnd));break}if(AI){if(AO.x==null||AI.x<AO.x){AO.x=AI.x}if(w.x==null||AI.x>w.x){w.x=AI.x}if(AO.y==null||AI.y<AO.y){AO.y=AI.y}if(w.y==null||AI.y>w.y){w.y=AI.y}}}j.push(' ">');if(!AE){var v=this.lineScale_*this.lineWidth;if(v<1){AK*=v}j.push("<g_vml_:stroke",' opacity="',AK,'"',' joinstyle="',this.lineJoin,'"',' miterlimit="',this.miterLimit,'"',' endcap="',P(this.lineCap),'"',' weight="',v,'px"',' color="',AA,'" />')}else{if(typeof this.fillStyle=="object"){var n=this.fillStyle;var t=0;var AH={x:0,y:0};var AB=0;var r=1;if(n.type_=="gradient"){var q=n.x0_/this.arcScaleX_;var d=n.y0_/this.arcScaleY_;var o=n.x1_/this.arcScaleX_;var AQ=n.y1_/this.arcScaleY_;var AM=this.getCoords_(q,d);var AL=this.getCoords_(o,AQ);var h=AL.x-AM.x;var g=AL.y-AM.y;t=Math.atan2(h,g)*180/Math.PI;if(t<0){t+=360}if(t<0.000001){t=0}}else{var AM=this.getCoords_(n.x0_,n.y0_);var Z=w.x-AO.x;var e=w.y-AO.y;AH={x:(AM.x-AO.x)/Z,y:(AM.y-AO.y)/e};Z/=this.arcScaleX_*A;e/=this.arcScaleY_*A;var AG=R.max(Z,e);AB=2*n.r0_/AG;r=2*n.r1_/AG-AB}var z=n.colors_;z.sort(function(i,c){return i.offset-c.offset});var u=z.length;var y=z[0].color;var x=z[u-1].color;var AD=z[0].alpha*this.globalAlpha;var AC=z[u-1].alpha*this.globalAlpha;var AF=[];for(var AJ=0;AJ<u;AJ++){var s=z[AJ];AF.push(s.offset*r+AB+" "+s.color)}j.push('<g_vml_:fill type="',n.type_,'"',' method="none" focus="100%"',' color="',y,'"',' color2="',x,'"',' colors="',AF.join(","),'"',' opacity="',AC,'"',' g_o_:opacity2="',AD,'"',' angle="',t,'"',' focusposition="',AH.x,",",AH.y,'" />')}else{j.push('<g_vml_:fill color="',AA,'" opacity="',AK,'" />')}}j.push("</g_vml_:shape>");this.element_.insertAdjacentHTML("beforeEnd",j.join(""))};I.fill=function(){this.stroke(true)};I.closePath=function(){this.currentPath_.push({type:"close"})};I.getCoords_=function(d,c){var Z=this.m_;return{x:A*(d*Z[0][0]+c*Z[1][0]+Z[2][0])-K,y:A*(d*Z[0][1]+c*Z[1][1]+Z[2][1])-K}};I.save=function(){var Z={};T(this,Z);this.aStack_.push(Z);this.mStack_.push(this.m_);this.m_=D(N(),this.m_)};I.restore=function(){T(this.aStack_.pop(),this);this.m_=this.mStack_.pop()};I.translate=function(d,c){var Z=[[1,0,0],[0,1,0],[d,c,1]];this.m_=D(Z,this.m_)};I.rotate=function(d){var f=a(d);var e=O(d);var Z=[[f,e,0],[-e,f,0],[0,0,1]];this.m_=D(Z,this.m_)};I.scale=function(f,e){this.arcScaleX_*=f;this.arcScaleY_*=e;var c=[[f,0,0],[0,e,0],[0,0,1]];var Z=this.m_=D(c,this.m_);var d=Z[0][0]*Z[1][1]-Z[0][1]*Z[1][0];this.lineScale_=Y(J(d))};I.clip=function(){};I.arcTo=function(){};I.createPattern=function(){return new F};function W(Z){this.type_=Z;this.x0_=0;this.y0_=0;this.r0_=0;this.x1_=0;this.y1_=0;this.r1_=0;this.colors_=[]}W.prototype.addColorStop=function(c,Z){Z=C(Z);this.colors_.push({offset:c,color:Z.color,alpha:Z.alpha})};function F(){}G_vmlCanvasManager=H;CanvasRenderingContext2D=M;CanvasGradient=W;CanvasPattern=F})()};