import PublicFuc
from datetime import datetime,timedelta
import pymssql
import time,csv,re,os
from openpyxl.styles import  Pat<PERSON><PERSON>ill,Alignment
import configparser
config = configparser.RawConfigParser()
def Run(curpath, workBook, alignment):
    ws = workBook['协议、可选、扩展测试']
    ws.alignment = alignment
    ProRecycleTest(curpath, ws)
    ProTrimTest(curpath, ws)
    ProFreeTest(curpath, ws)
    ProPICT(curpath, ws)
    ProHBM(curpath, ws)
    PublicFuc.WriteReportTime(ws,'N',2)
    PublicFuc.WriteReportOperator(ws,'D',2)

def GetNewMarsDic(oldDic, keyLst):
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        dic = oldDic[key]
        newDic[key].append(dic['pc_name'])
        if dic['cap'] != '':
            newDic[key].append(int(float(dic['cap'])))
        else:
            newDic[key].append('')
        newDic[key].append(dic['test_result'])
        rwdata = ''
        if dic['06'] != '':
            rwdata += str(round(int(dic['06'],16)*512*1000/1024/1024/1024)) + '/'
        else:
            rwdata += '/'
        if dic['07'] != '':
            rwdata += str(round(int(dic['07'],16)*512*1000/1024/1024/1024))
        newDic[key].append(rwdata)
        #F1、F2 1个单位为32M，需转化为G
        if '' == dic['end_time'] or '' == dic['start_time']:
            newDic[key].append('')
        else:
            endtime = datetime.strptime(dic['end_time'], '%Y-%m-%d %H:%M:%S')
            starttime = datetime.strptime(dic['start_time'], '%Y-%m-%d %H:%M:%S')
            hours = timedelta.total_seconds(endtime-starttime)//(60*60)
            newDic[key].append('%dH'%hours)
        smart = ''
        #统计不为0的smart信息
        for innerKey in dic.keys():
            if innerKey.startswith('id_'):
                if '' == dic[innerKey]:
                    continue
                if 0 != int(dic[innerKey],16):
                    pos = innerKey.find('id_')
                    id = innerKey[pos+len('id_'):].upper()
                    if id in PublicFuc.commonSmartKey:
                        smart += '%s=%s,'%(id, dic[innerKey][2:].upper())
        if '' != smart:
            smart = smart[:-1]
        newDic[key].append(smart)
        for item in keyLst:
            newDic[key].append(dic[item])
    return newDic
#样片编号、电脑平台、容量、测试结果、读写数据量、测试时间、smart结果

def GetNewMarsDicEx(oldDic, keyLst):
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        dic = oldDic[key]
        newDic[key].append(dic['pc_name'])
        if dic['cap'] != '':
            newDic[key].append(int(float(dic['cap'])))
        else:
            newDic[key].append('')
        newDic[key].append(dic['test_result'])
        #F1、F2 1个单位为32M，需转化为G
        if '' == dic['end_time'] or '' == dic['start_time']:
            newDic[key].append('')
        else:
            endtime = datetime.strptime(dic['end_time'], '%Y-%m-%d %H:%M:%S')
            starttime = datetime.strptime(dic['start_time'], '%Y-%m-%d %H:%M:%S')
            hours = timedelta.total_seconds(endtime-starttime)//(60*60)
            newDic[key].append('%dH'%hours)
        smart = ''
        #统计不为0的smart信息
        for innerKey in dic.keys():
            if innerKey.startswith('id_'):
                if '' == dic[innerKey]:
                    continue
                if 0 != int(dic[innerKey],16):
                    pos = innerKey.find('id_')
                    id = innerKey[pos+len('id_'):].upper()
                    if id in PublicFuc.commonSmartKey:
                        smart += '%s=%s,'%(id, dic[innerKey][2:].upper())
        if '' != smart:
            smart = smart[:-1]
        newDic[key].append(smart)
        for item in keyLst:
            newDic[key].append(dic[item])
    return newDic

def proMarsCommon(curpath, worksheet, pattern, caseName, startLine, keyLst, colLst):
    caseDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, caseDic, caseName,4)
    #GetNewMarsDic把ini读出来的dic按照keyLst转换为模板所需数据列表
    newDic = GetNewMarsDic(caseDic, keyLst)
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst)

def proMarsCommonEx(curpath, worksheet, pattern, caseName, startLine, keyLst, colLst):
    caseDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, caseDic, caseName,4)
    #GetNewMarsDic把ini读出来的dic按照keyLst转换为模板所需数据列表
    newDic = GetNewMarsDicEx(caseDic, keyLst)
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst)

def ProRecycleTest(curpath, ws):
    keyLst = ['powercnts','waf']
    colLst = ['C','B','E','N','J','M','K','I','O']
 
    pattern = '.+\\\\Plan32\\\\T-SS_PCIE_M2-C74\\\\RecycleTest\\\\\\d{14}\\\\.+\\\\mms.*\.ini$'
    caseName = 'RecycleTest'
    startLine = 62
    proMarsCommon(curpath, ws, pattern, caseName, startLine, keyLst, colLst)

def ProTrimTest(curpath, ws):
    keyLst = ['waf']
    colLst = ['C','B','E','N','I','L','J','M']
 
    pattern = '.+\\\\Plan33\\\\T-SS_PCIE_M2-C77\\\\TrimTest\\\\\\d{14}\\\\.+\\\\mms.*\.ini$'
    caseName = 'TrimTest'
    startLine = 68
    proMarsCommon(curpath, ws, pattern, caseName, startLine, keyLst, colLst)

def ProPICT(curpath, ws):
    keyLst = ['waf']
    colLst = ['C','B','E','M','H','I','L']
 
    pattern = '.+\\\\Plan71\\\\T-SS_PCIE_M2-C106\\\\PICT\\\\\\d{14}\\\\.+\\\\mms.*\.ini$'
    caseName = 'PICT'
    startLine = 74
    proMarsCommonEx(curpath, ws, pattern, caseName, startLine, keyLst, colLst)

def ProHBM(curpath, ws):
    keyLst = ['waf']
    colLst = ['C','B','E','M','H','I','L']
 
    pattern = '.+\\\\Plan72\\\\T-SS_PCIE_M2-C111\\\\HMB\\\\\\d{14}\\\\.+\\\\mms.*\.ini$'
    caseName = 'HMB'
    startLine = 80
    proMarsCommonEx(curpath, ws, pattern, caseName, startLine, keyLst, colLst)
   
def merge_elements(lst, index1, index2):
    # 合并两个元素为一个新元素
    merged_element = lst[index1] +'/'+ lst[index2]
    
    # 将其他元素前移
    for i in range(index2, len(lst) - 1):
        lst[i] = lst[i + 1]
    
    # 删除最后一个元素
    lst.pop()
    
    # 将合并的元素插入到正确的位置
    lst[index1] = merged_element
    
    return lst

def ProImtFunc(curpath, worksheet, pattern, imtKeyLst, startLine, patten_t,lineCnt = 1 ):
    smartKey = PublicFuc.commonSmartKey
    smartKeyNew = ['SmartInfo', 'WAF']
    imtCol = ['C','B','E', 'T','L','M','N','O','S']

    newKey = imtKeyLst
    imtDic = {}
    PublicFuc.ReadQaIniData(curpath, pattern, imtDic, newKey, '', 2)
    smartDic = {}
    PublicFuc.ReadSmart(curpath, patten_t, smartDic,2)
    newDic = imtDic
    for item in imtDic:
        for ind,val in enumerate(imtDic[item]):
            if item not in smartDic or len(smartDic[item]) < ind:
                val.extend(('',''))
            else:
                val.extend((smartDic[item][ind][0],smartDic[item][ind][1]))
            val = merge_elements(val,5,6)
    newKey = imtKeyLst+smartKeyNew
    PublicFuc.WriteData(worksheet, startLine, newDic, imtCol, newKey, lineCnt)

def ProFreeTest(curpath, ws):
    imtC31Key = ['pc_no', 'Cap', 'qa_err_msg', 'Logical_512K_Random_Write60_Read_40_168H_Iops','Logical_512K_Random_Write60_Read_40_168H_MiBps','total_host_reads','total_host_reads']
    pattern = '.+\\\\Plan12\\\\T-SS_PCIE_M2-C80\\\\K3空闲状态写读\\\\\d{14}\\\\report.ini$'
    pattern_t = '.+\\\\Plan12\\\\T-SS_PCIE_M2-C80\\\\SMART\\\\\d{14}\\\\report.ini$'
    startLine = 86
    ProImtFunc(curpath, ws, pattern, imtC31Key, startLine,pattern_t, 2)