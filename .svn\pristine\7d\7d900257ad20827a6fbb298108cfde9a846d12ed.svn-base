import configparser
import csv,time
import os,re
from openpyxl.utils import get_column_letter,column_index_from_string
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors,Font
from datetime import datetime,timedelta

minKey = ['Read Acc Time']
alignment = Alignment(horizontal='center',vertical='center')
alignmentNewLine = Alignment(horizontal='center',vertical='center')
warnFill = PatternFill('solid', fgColor='FF0000')
restoreFill = PatternFill('solid',fgColor='FFFFFF')
maxFill = PatternFill('solid', fgColor='64C8FF')

errDiskLst = []
fileLst = []
time_range = {}
config = configparser.RawConfigParser()
wlMaxDic = {'slc_diff':750,'tlc_diff':75, 'wear_avg':10}
def GetAllFile(curpath):
    for dirpath,dirnames,filenames in os.walk(curpath):
        for filename in filenames:
            fullname = os.path.join(dirpath, filename)
            fileLst.append(fullname)

def WriteErrDiskFile(worksheet,errDiskFile):
    if 0 != len(errDiskLst):
        ws = worksheet.worksheets[0]
        ColLst = ['A','B','C','D','E','F','G','H','I','J','K','L','M','N']
        errDiskLst.sort(key=lambda x: (x[6]))
        itemall = {}
        for item in errDiskLst:
            if item[0] not in itemall:
                itemall[item[0]] = len(itemall)
            item.append(itemall[item[0]])
        errDiskLst.sort(key=lambda x: (x[14]))
        for lineIdx,errLst in enumerate(errDiskLst):
            filesplit = errLst[13].split('\\')
            length = len(filesplit)
            errLst[13] = filesplit[0]
            for i in range(1,length-2):
                errLst[13] = errLst[13] + "\\" + filesplit[i]
            for index,col in enumerate(ColLst):
                ws['%s%d'%(col, lineIdx+3)] = errLst[index]
                ws['%s%d'%(col, lineIdx+3)].font = Font(name = 'Arial',size = 10)
                ws['%s%d'%(col, lineIdx+3)].alignment = Alignment(vertical='center', horizontal='left')
                if index in [9,10,11,12] and errLst[index]>0:
                    ws['%s%d'%(col, lineIdx+3)].font = Font(name = 'Arial',size = 10,color = 'FF0000')
            ws['%s%d'%('I', lineIdx+3)].font = Font(name = 'Arial',size = 10,color = 'FF0000')
            ws['%s%d'%('N', lineIdx+3)].hyperlink = f"{errLst[13]}"
            ws['%s%d'%('N', lineIdx+3)].style = "Hyperlink"
        temp = ''
        startrow = 0
        endrow = 0
        for lineIdx,errLst in enumerate(errDiskLst):
            ws.row_dimensions[lineIdx+3].height = 20
            if errLst[0] == temp or temp == '':
                temp = errLst[0]
                continue
            endrow = lineIdx
            ws.merge_cells(start_row=startrow+3, end_row=endrow+2, start_column=1, end_column=1)
            ws['A%d'%(startrow+3)].alignment = Alignment(vertical='center', horizontal='left')
            startrow = lineIdx
            temp = errLst[0]
        ws.merge_cells(start_row=startrow+3, end_row=len(errDiskLst)+2, start_column=1, end_column=1)
        worksheet.save(errDiskFile)
        


#1个样片多条记录
def WriteCsvData(worksheet, startLine, dataDic):
    curLine = startLine

    rowIdx = 1
    for key in dataDic:
        for line in dataDic[key]:
            for col in range(len(line)+1):#columCnt + 1
                try:
                    if 0 == col:
                        #第一列是编号，直接填行号
                        worksheet['%s%d'%(get_column_letter(1), curLine)] = rowIdx
                    elif 1 == col:
                        worksheet['%s%d'%(get_column_letter(col+1), curLine)] = line[1]
                    elif 2 == col:
                        worksheet['%s%d'%(get_column_letter(col+1), curLine)] = line[0]
                    else:
                        worksheet['%s%d'%(get_column_letter(col+1), curLine)] = line[col-1]
                        worksheet['%s%d'%(get_column_letter(col+1), curLine)]
                    worksheet['%s%d'%(get_column_letter(col+1), curLine)].alignment = alignment
                    worksheet['%s%d'%(get_column_letter(col+1), curLine)].border = my_border('thin', 'thin', 'thin', 'thin')
                    
                #合并的单元格只能写一次，需要捕获异常
                except(AttributeError):
                    continue
            curLine += 1
            rowIdx += 1

    return  curLine   


    #定义边框样式
def my_border(t_border, b_border, l_border, r_border):
    border = Border(top=Side(border_style=t_border, color=colors.BLACK),
                    bottom=Side(border_style=b_border, color=colors.BLACK),
                    left=Side(border_style=l_border, color=colors.BLACK),
                    right=Side(border_style=r_border, color=colors.BLACK))
    return border


def ReadQaIniData(curpath, pattern, dataDic, keyLst, imageSuffix, recordCnt = 1, diskCnt = 1):
    unitLst = ['MByte/s', 'MB/s', 'ms', '%']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        for sec in config.sections():
            if sec not in dataDic:
                #diskCnt为0表示统计所有磁盘数据
                if diskCnt == len(dataDic) and 0 != diskCnt:
                    continue
                dataDic[sec] = []
            tempLst = []
            pcNo = ''
            if 'pc_no' in config[sec]:
                pcNo = config[sec]['pc_no']
            for key in keyLst:
                if key.lower() in config[sec]:
                    value = config[sec][key.lower()]
                    #去除单位
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    if value.isdecimal():
                        tempLst.append(value)
                    else:
                        try:
                            value = float(value)
                            value = '%.2f'%value
                            tempLst.append(value)
                        except:
                            tempLst.append(value)
                    if 'qa_err_msg' == key:
                            filemt= time.localtime(os.stat(file).st_mtime)  
                            strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                            errDiskLst.append([sec,pcNo,value,strTime])
                else:
                    tempLst.append('')
            if len(dataDic[sec]) < recordCnt and [] != tempLst:
                #imageSuffix为空不需要截图，只需要数据
                if '' != imageSuffix:
                    image = imagepath+'\\%s_%s'%(sec, imageSuffix)
                    if os.path.isfile(image):
                        tempLst.append(image)
                    else:
                        tempLst.append('')
                dataDic[sec].append(tempLst)

def ReadMarsIniData(curpath, pattern, resultDic, caseName):
    unitLst = ['M/s','MB']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        if 'HWCONFIG' not in config.sections() or caseName not in config.sections():
            continue
        keyName = config['HWCONFIG']['mms_flash']
        if keyName not in resultDic:
            resultDic[keyName] = []
        tempDic = {}
        tempDic['capacity'] = config['HWCONFIG']['capacity']
        tempDic['MMS_PC'] = config['HWCONFIG']['MMS_PC']
        for key in config[caseName]:
            value = config[caseName][key]
            #去除单位
            for unit in unitLst:
                if unit in value:
                    value = value[:value.find(unit)]
                    break
            tempDic[key] = value
        if 'test_result' not in tempDic or 'TRUE'== tempDic['test_result']:
            tempDic['test_result'] = 'TRUE'
        else:
            filemt= time.localtime(os.stat(file).st_mtime)  
            strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
            errDiskLst.append([keyName,tempDic['MMS_PC'],tempDic['test_result'],strTime])
        resultDic[keyName].append(tempDic)

def ReadMarsIniDataWithPicture_CAP_MB(curpath, pattern, dataDic, caseName, caseKey, keyLst, imageSuffix, diskCnt = 6):
    unitLst = ['M/s']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        if 'HWCONFIG' not in config.sections():
            continue
        keyName = config['HWCONFIG']['mms_flash']
        if keyName not in dataDic:
            #0表示样片数量不确定，需要获取全部数据
            if diskCnt == len(dataDic) and 0 != diskCnt:
                continue
            dataDic[keyName] = {}
        if caseName not in config.sections():
            continue

        dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']
        if caseKey not in dataDic[keyName]:
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            tempLst = []
            for key in keyLst:
                if 'Cap' == key:
                    cap = config['HWCONFIG']['capacity']
                    if '' == cap:
                        tempLst.append('')
                    else:
                        tempLst.append(str(int(float(cap)*1024)))
                    continue
                if 'mms_pc' == key:
                    pc_no = config['HWCONFIG']['MMS_PC']
                    tempLst.append(pc_no)
                    continue
                if key.lower() in config[caseName]:
                    value = config[caseName][key.lower()]
                    #去除单位
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    tempLst.append(value)
                else:
                    tempLst.append('')

            if 'test_result' not in dataDic[keyName] or 'TRUE'== dataDic[keyName]['test_result']:
                dataDic[keyName]['test_result'] = 'TRUE'
            else:
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],dataDic[keyName]['test_result'],strTime])

            #imageSuffix为空不需要截图，只需要数据
            if '' != imageSuffix:
                pos = file.rfind('\\report')
                imagepath = file[:pos]
                image = ''
                for i in os.listdir(imagepath):
                    if i.endswith('.bmp'):
                        image = os.path.join(imagepath,i)
                        break
                if os.path.isfile(image):
                    tempLst.append(image)
                else:
                    tempLst.append('')
            dataDic[keyName][caseKey] = tempLst

def ReadMarsIniDataWithPicture(curpath, pattern, dataDic, caseName, caseKey, keyLst, imageSuffix, diskCnt = 6):
    unitLst = ['M/s']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        if 'HWCONFIG' not in config.sections():
            continue
        keyName = config['HWCONFIG']['mms_flash']
        if keyName not in dataDic:
            #0表示样片数量不确定，需要获取全部数据
            if diskCnt == len(dataDic) and 0 != diskCnt:
                continue
            dataDic[keyName] = {}
        if caseName not in config.sections():
            continue

        dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']
        if caseKey not in dataDic[keyName]:
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            tempLst = []
            for key in keyLst:
                if 'Cap' == key:
                    cap = config['HWCONFIG']['capacity']
                    if '' == cap:
                        tempLst.append('')
                    else:
                        tempLst.append(str(int(float(cap))))
                    continue
                if 'mms_pc' == key:
                    pc_no = config['HWCONFIG']['MMS_PC']
                    tempLst.append(pc_no)
                    continue
                if key.lower() in config[caseName]:
                    value = config[caseName][key.lower()]
                    #去除单位
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    tempLst.append(value)
                else:
                    tempLst.append('')

            if 'test_result' not in dataDic[keyName] or 'TRUE'== dataDic[keyName]['test_result']:
                dataDic[keyName]['test_result'] = 'TRUE'
            else:
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],dataDic[keyName]['test_result'],strTime])

            #imageSuffix为空不需要截图，只需要数据
            if '' != imageSuffix:
                pos = file.rfind('\\report')
                imagepath = file[:pos]
                image = ''
                for i in os.listdir(imagepath):
                    if i.endswith('.bmp'):
                        image = os.path.join(imagepath,i)
                        break
                if os.path.isfile(image):
                    tempLst.append(image)
                else:
                    tempLst.append('')
            dataDic[keyName][caseKey] = tempLst

def WriteDataAndImage(worksheet, startLine, dataDic, colLst, imageCol, imgWidth, imgHeight):
    curLine =  startLine
    for key in dataDic:
        line = dataDic[key][0]
        for index,col in enumerate(colLst):
            try:
                if 0 == index:
                    worksheet['%s%d'%(col, curLine)] = key
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
            #合并的单元格只能写一次，需要捕获异常
            except(AttributeError):
                continue
        # 列表最后一项是图片路径
        if '' != line[-1]:
            img = Image(line[-1])
            img.width = imgWidth
            img.height = imgHeight
            worksheet.add_image(img, '%s%d'%(imageCol, startLine))

#1个样片多条记录
def WriteData(worksheet, startLine, dataDic, colLst, keyLst, lineCnt = 1):
    curLine = startLine
    for key in dataDic:
        for line in dataDic[key]:
            for index,col in enumerate(colLst):
                try:
                    if 0 == index:
                        #第一列是编号，直接填key
                        worksheet['%s%d'%(col, curLine)] = key
                    else:
                        worksheet['%s%d'%(col, curLine)] = line[index-1]
                        if 'A5-A6' == keyLst[index-1] and line[index-1] != '':
                            if line[index-1] >= 400:
                                worksheet['%s%d'%(col, curLine)].fill = warnFill
                    if 0 != index and 'SmartInfo'== keyLst[index-1]:
                        worksheet['%s%d'%(col, curLine)].alignment = alignmentNewLine
                    else:
                        worksheet['%s%d'%(col, curLine)].alignment = alignment
                #合并的单元格只能写一次，需要捕获异常
                except(AttributeError):
                    continue
            curLine += lineCnt
            startLine += 1
    return startLine
#写入mars磨损数据
def WriteMarsWlData(worksheet, startLine, dataDic, colLst, colKeyLst, wlMaxDic):
    curLine = startLine
    for disk in dataDic:
        for innerDic in dataDic[disk]:
            for index,col in enumerate(colLst):
                colName = colKeyLst[index]
                value = innerDic[colName]
                if colName == 'valume label':
                    value = '#'+value
                worksheet['%s%d'%(col, curLine)] = value
                worksheet['%s%d' % (col, curLine)].fill = restoreFill
                if '' != value:
                    if colName in wlMaxDic:
                        if value >= wlMaxDic[colName]:
                            worksheet['%s%d'%(col, curLine)] = value
                            worksheet['%s%d'%(col, curLine)].fill = warnFill
            curLine += 1

#获取每个编号每列数据的最值
def GetMaxOrMinValueLst(keyLst,dataDic):
    for key in dataDic:
        resultLst = []
        for index,col in enumerate(keyLst):
            tempLst = [line[index] for line in dataDic[key]]
            limitData = 0
            bFirstData = True
            for data in tempLst:
                try:
                    tempData = float(data)
                    #部分列需要取最小值，例如时间等
                    if bFirstData:
                        limitData = tempData
                        bFirstData = False
                        continue
                    if col in minKey:
                        if tempData < limitData:
                            limitData = tempData
                    else:
                        if tempData > limitData:
                            limitData = tempData
                except:
                    continue
            resultLst.append(limitData)
        dataDic[key].append(resultLst)

def FmtStrHex(strHex):
    #去掉十六进制前面的多个0
    strNew = strHex.lstrip('0')
    if '' == strNew:
        strNew = '0'
    return strNew

def GetImtResultPath(strImtBmpPath, key):
    pos = strImtBmpPath.rfind('\\')
    strPath = strImtBmpPath[:pos+1] + 'inst%s_IometerResults.csv'%key
    return strPath

def GetNewIoMeterDic(oldDic, startPos, smartKey, bA23 = False):
    #startPos之后的数据为smart信息，需转换为模板所需数据[F1(G),F2(G),smart(非0),A5-A6]
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        for dataLst in oldDic[key]:
            newLst = dataLst[:startPos]
            smartLst = dataLst[startPos:]
            #F1、F2 1个单位为32M，需转化为G
            if smartLst[0] == '':
                newLst.append('')
            else:
                newLst.append((int(smartLst[0],16)*32)//1024)
            if smartLst[1] == '':
                newLst.append('')
            else:
                newLst.append((int(smartLst[1],16)*32)//1024)
            strSmart = ''
            for idx,value in enumerate(smartLst[4:]):
                if bA23 and idx == len(smartLst[4:])-1:
                    break
                if value != '' and 0 != int(value,16):
                    strSmart += '%s=%s,'%(smartKey[4+idx], FmtStrHex(value))
            if '' != strSmart:
                strSmart = strSmart[:-1]
            newLst.append(strSmart)
            if smartLst[2] == '' or smartLst[3] == '':
                newLst.append('')
            else:
                newLst.append(int(smartLst[2],16)-int(smartLst[3],16))
            if bA23:
                strImtResultPath = GetImtResultPath(smartLst[-1], key)
                newLst.append(strImtResultPath)
            newDic[key].append(newLst)
    return newDic

def GetNewMarsDic(oldDic, keyLst):
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        dic = oldDic[key]
        #容量取整数G
        if '' == dic['capacity']:
            newDic[key].append('')
        else:
            newDic[key].append(int(float(dic['capacity'])))
        if '' == dic['end_circle'] or '' == dic['start_circle']:
            newDic[key].append('')
        else:
            newDic[key].append(int(dic['end_circle'])-int(dic['start_circle']))
        #F1、F2 1个单位为32M，需转化为G
        if '' == dic['end_id_f1'] or '' == dic['start_id_f1']:
            write = 0
        else:
            write = (int(dic['end_id_f1'],16)-int(dic['start_id_f1'],16))*32//1024
        if '' == ['end_id_f2'] or '' == dic['start_id_f2']:
            read = 0
        else:
            read = (int(dic['end_id_f2'],16)-int(dic['start_id_f2'],16))*32//1024
        newDic[key].append('%d/%d'%(write,read))
        if '' == dic['end_time'] or '' == dic['start_time']:
            newDic[key].append('')
        else:
            endtime = datetime.strptime(dic['end_time'], '%Y-%m-%d %H:%M:%S')
            starttime = datetime.strptime(dic['start_time'], '%Y-%m-%d %H:%M:%S')
            hours = timedelta.total_seconds(endtime-starttime)//(60*60)
            newDic[key].append('%dH'%hours)
        if 'porcnt' in keyLst:
            if '' == dic['end_por'] or '' == dic['start_por']:
                newDic[key].append('')
            else:
                newDic[key].append(int(dic['end_por'])-int(dic['start_por']))
        smart = ''
        #统计不为0的smart信息
        for innerKey in dic.keys():
            if innerKey.startswith('id_'):
                if 0 != int(dic[innerKey],16):
                    pos = innerKey.find('id_')
                    id = innerKey[pos+len('id_'):].upper()
                    smart += '%s=%s_'%(id, dic[innerKey][2:].upper())
        if '' != smart:
            smart = smart[:-1]
        newDic[key].append(smart)
        newDic[key].append(dic['average_write_vel'])
        newDic[key].append(dic['max_write_vel'])
        newDic[key].append(dic['min_write_vel'])
        newDic[key].append(dic['average_read_vel'])
        newDic[key].append(dic['max_read_vel'])
        newDic[key].append(dic['min_read_vel'])
        newDic[key].append(dic['write_overtime'])
        newDic[key].append(int(dic['id_a5'],16)-int(dic['id_a6'],16))
    return newDic

def GetNewBitDic(oldDic, startPos, smartKey):
    #startPos之后的数据为smart信息，需转换为模板所需数据[F1(G)/F2(G),smart(非0),A5-A6]
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        for dataLst in oldDic[key]:
            newLst = dataLst[:startPos]
            smartLst = dataLst[startPos:]
            #F1、F2 1个单位为32M，需转化为G
            write = 0
            if smartLst[0] != '':
                write = (int(smartLst[0],16)*32)//1024
            read = 0
            if smartLst[1] != '':
                read = (int(smartLst[1],16)*32)//1024
            newLst.append('%d/%d'%(write,read))
            strSmart = ''
            for idx,value in enumerate(smartLst[4:]):
                if value != '' and 0 != int(value,16):
                    strSmart += '%s=%s,'%(smartKey[4+idx], FmtStrHex(value))
            if '' != strSmart:
                strSmart = strSmart[:-1]
            newLst.append(strSmart)
            if smartLst[2] == '' or smartLst[3] == '':
                newLst.append('')
            else:
                newLst.append(int(smartLst[2],16)-int(smartLst[3],16))
            newDic[key].append(newLst)
    return newDic


def ReadRMSIniData(curpath, pattern, resultDic, cycle, count):
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        #必须清除
        config.clear()
        config.read(file)
        if 'Public' not in config.sections() or 'TestResult' not in config.sections():
            continue
        diskNo = config['Public']['diskno']
        platNo = config['Public']['platno']
        platType = config['Public']['plattype']
        cpuType = config['Public']['cputype']
        keyName = '%s_%s_%s_%s'%(cpuType, platType, platNo, diskNo)
        if keyName not in resultDic:
            resultDic[keyName] = {}
        if count not in resultDic[keyName]:
            resultDic[keyName][count]={}
 
        tempDic = {}
        for key in config['TestResult']:
            tempDic[key] = config['TestResult'][key]
        for key in config['Public']:
            tempDic[key] = config['Public'][key]
        resultDic[keyName][count][cycle] = tempDic



#写时间信息
def WriteReportTime(worksheet,columnName,rowNo):
    #capIdx = 0
    filemt= time.localtime()  
    strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
    worksheet['%s%d'%(columnName, rowNo)] = strTime

#写操作者信息
def WriteReportOperator(worksheet,columnName,rowNo,operatorName = 'Skynet'):
    #capIdx = 0
    worksheet['%s%d'%(columnName, rowNo)] = operatorName

def GetDate():
    filemt= time.localtime()  
    strTime = time.strftime('%Y-%m-%d', filemt)
    return strTime

#获取测试时间

def GetTestTime(dic):
    #测试时间
    endTimeStr = GetValueFromDic(dic,'end_time')
    startTimeStr = GetValueFromDic(dic,'start_time')
    if '' == endTimeStr or '' == startTimeStr:
       return ''
    else:
       endtime = datetime.strptime(endTimeStr, '%Y-%m-%d %H:%M:%S')
       starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
       totalSecond = timedelta.total_seconds(endtime-starttime)
       totalSecond = int(totalSecond)
       hour = int(totalSecond/3600)
       lefSeconds = totalSecond%3600
       minutes = int(lefSeconds/60)
       seconds = lefSeconds%60
       timeStr = '%d:%d:%d'%(hour,minutes,seconds)
       return timeStr

def GetValueFromDic(dataDic, key,defaultVulue = ''):
    if key in dataDic:
        return dataDic[key]
    else:
        return defaultVulue

def GetTimeRange(curpath, patterns, keyName):
    earliest_time = datetime.max
    latest_time = datetime.min
    for pattern in patterns:
        for file in fileLst:
            if not re.match(pattern, file):
                continue
            if 'result.txt' in file:
                with open(file, 'r', encoding = 'GBK') as fileReader:
                    # 读取每一行
                    for line in fileReader:
                        if '] start.' in line:
                            time_part = line.split(',')[0]
                            try:
                                time_obj = datetime.strptime(time_part, '%Y-%m-%d %H:%M:%S')
                            except ValueError:
                                break
                            if time_obj < earliest_time:
                                earliest_time = time_obj
                        elif '] finished.' in line:
                            time_part = line.split(',')[0]
                            try:
                                time_obj = datetime.strptime(time_part, '%Y-%m-%d %H:%M:%S')
                            except ValueError:
                                break
                            if time_obj > latest_time:
                                latest_time = time_obj
            elif 'ini' in file:
                config.clear()
                config.read(file, encoding='gbk')
                for sec in config.sections():
                    if 'START_TIME' in config[sec]:
                        startTimeStr = config[sec]['START_TIME']
                        try:
                            time_obj = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
                        except ValueError:
                            continue
                        if time_obj < earliest_time:
                            earliest_time = time_obj
                    if 'END_TIME' in config[sec]:
                        endTimeStr = config[sec]['END_TIME']
                        try:
                            time_obj = datetime.strptime(endTimeStr, '%Y-%m-%d %H:%M:%S')
                        except ValueError:
                            continue
                        if time_obj > latest_time:
                            latest_time = time_obj
    start_time = ''
    end_time = ''
    if earliest_time != datetime.max:
        start_time = earliest_time.strftime('%Y-%m-%d')
    if latest_time != datetime.min:
        end_time = latest_time.strftime('%Y-%m-%d')
    time_range[keyName] = [start_time, end_time]
