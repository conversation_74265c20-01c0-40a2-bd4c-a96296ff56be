import re,os
from collections import defaultdict
from openpyxl.styles import  <PERSON><PERSON><PERSON><PERSON>,Alignment,Border,Side,colors,Font
from openpyxl.utils import get_column_letter

alignmentFileCell = Alignment(wrap_text=True)

def parse_time_distribution(file_path):
    # 优化后的正则表达式模式
    pattern = r'\[.*?\]\s+((?:>=|<=)?\d+~?\d*)\s*ms:\s*(\d+)\s+(\d+\.?\d*%)'
    result = {}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                match = re.match(pattern, line)
                if match:
                    key = match.group(1).strip()
                    ms_value = int(match.group(2))
                    percent = match.group(3)
                    result[key] = [ms_value, percent]
    except:
        print('open file fail:' + file_path)
    
    return result

def GetAllResponseData(path: str):
    """聚合多个日志文件的响应时间数据"""
    filelist = [
        'verify performance verify_cmd17_NAC_time_in_4k_random_write 1 10000 SDR25.log',
        'verify performance verify_cmd17_NAC_time_in_4k_random_write 0 10000 SDR50.log',
        'verify performance verify_cmd17_NAC_time_in_4k_random_write 0 10000 DDR50.log',
        'verify performance verify_cmd17_NAC_time_in_4k_random_write 0 10000 HS200.log',
        'verify performance verify_cmd17_NAC_time_in_4k_random_write 0 10000 HS400.log',
        'verify performance full_partition_write_read_instructions_corresponding_time_test 1 SDR25.log',
        'verify performance full_partition_write_read_instructions_corresponding_time_test 0 SDR50.log',
        'verify performance full_partition_write_read_instructions_corresponding_time_test 0 HS200.log',
        'verify performance full_partition_write_read_instructions_corresponding_time_test 0 HS400.log',
        'verify performance cmdq_full_partition_write_read_instructions_corresponding_time_test 0 SDR25.log',
        'verify performance cmdq_full_partition_write_read_instructions_corresponding_time_test 0 SDR50.log',
        'verify performance cmdq_full_partition_write_read_instructions_corresponding_time_test 0 HS200.log',
        'verify performance cmdq_full_partition_write_read_instructions_corresponding_time_test 0 HS400.log'
    ]
    dicAllFileData = {}
    
    for filename in filelist:
        lognameNormal = filename
        lognamePass = 'Passed ' + filename
        lognameFail = 'Failed ' + filename
        logPath = os.path.join(path,lognamePass)
        if not os.path.exists(logPath):
            logPath = os.path.join(path,lognameFail)
        if not os.path.exists(logPath):  # Fixed indentation
            logPath = os.path.join(path,lognameNormal)
        if not os.path.exists(logPath):
            continue

        dicAllFileData[filename] = parse_time_distribution(logPath)
    
    return dicAllFileData

def WriteAllResponseData(wb, strType:str,dicAllResponseData, start_col=1, start_row=1):
    from openpyxl.styles import Border, Side, Alignment
    
    thin_border = Border(left=Side(style='thin'), 
                        right=Side(style='thin'),
                        top=Side(style='thin'),
                        bottom=Side(style='thin'))
    
    if 'rw_cmd_response_time' not in wb.sheetnames:
        ws = wb.create_sheet('rw_cmd_response_time')
    else:
        ws = wb['rw_cmd_response_time']

    dataDicRows = {'x1':2,'x2':40,'x4':78}
    start_row = dataDicRows[strType]
    
    col = start_col
    
    for filename, data in dicAllResponseData.items():
        current_row = start_row
        ws.column_dimensions[get_column_letter(col)].width = 20
        ws.column_dimensions[get_column_letter(col+1)].width = 20
        ws.column_dimensions[get_column_letter(col+2)].width = 20
        # 写入文件名标题
        ws.cell(row=current_row, column=col, value=filename)
        cellName = '%s%d'%(get_column_letter(col),current_row)
        #ws[cellName] = filename
        ws[cellName].alignment = alignmentFileCell
        print(ws[cellName].alignment.wrap_text)
       
        # 设置行高为默认值的3倍
        ws.row_dimensions[current_row].height = 75  # 默认行高约15*3=45
        
        # 写入数据明细
        current_row += 1
        for key, values in data.items():
            ws.cell(row=current_row, column=col, value=key)
            ws.cell(row=current_row, column=col+1, value=values[0])  # ms值
            ws.cell(row=current_row, column=col+2, value=values[1]) # 百分比
            current_row += 1
        
        # 设置格式
        max_row = current_row-1
        for row in ws.iter_rows(min_row=start_row+1, max_row=max_row, 
                              min_col=col, max_col=col+2):
            for cell in row:
                #cell.border = thin_border
                cell.alignment = Alignment(horizontal='center', vertical='center')
        
        col += 3  # 每个文件间隔3列

# 测试两种格式
if __name__ == "__main__":
    test_data = parse_time_distribution(r"D:\LocalReportData\YS8297_E09T(SEMMC)_010100_release_Release_2_128GB_20250401\XU4\PV-COM7-B268_1743510197\Passed verify performance verify_cmd17_NAC_time_in_4k_random_write 0 10000 DDR50.log")
    print(test_data)
    import os