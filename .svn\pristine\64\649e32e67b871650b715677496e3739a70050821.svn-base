﻿#pragma once

namespace YSNVMe
{
	typedef struct
	{
		BYTE bOpcode;
		DWORD dwNSID;
		DWORD dw10;
		DWORD dw11;
		DWORD dw12;
		DWORD dw13;
		DWORD dw14;
		DWORD dw15;
		BY<PERSON> bVendorControl;
		DWORD dwDataLength;
		BYTE bProtocolField;
		ULONG ulTimeout;
	} COMMAND_SET;

	typedef enum {
		StorageDeviceProperty = 0,
		StorageAdapterProperty,
		StorageDeviceIdProperty,
		StorageDeviceUniqueIdProperty,
		StorageDeviceWriteCacheProperty,
		StorageMiniportProperty,
		StorageAccessAlignmentProperty,
		StorageDeviceSeekPenaltyProperty,
		StorageDeviceTrimProperty,
		StorageDeviceWriteAggregationProperty,
		StorageDeviceDeviceTelemetryProperty,
		StorageDeviceLBProvisioningProperty,
		StorageDevicePowerProperty,
		StorageDeviceCopyOffloadProperty,
		StorageDeviceResiliencyProperty,
		StorageDeviceMediumProductType,
		StorageDeviceRpmbProperty,
		StorageDeviceIoCapabilityProperty = 48,
		StorageAdapterProtocolSpecificProperty,
		StorageDeviceProtocolSpecificProperty,
		StorageAdapterTemperatureProperty,
		StorageDeviceTemperatureProperty,
		StorageAdapterPhysicalTopologyProperty,
		StorageDevicePhysicalTopologyProperty,
		StorageDeviceAttributesProperty,
	} TStoragePropertyId;

	typedef enum {
		PropertyStandardQuery = 0,
		PropertyExistsQuery,
		PropertyMaskQuery,
		PropertyQueryMaxDefined
	} TStorageQueryType;

	typedef struct {
		TStoragePropertyId PropertyId;
		TStorageQueryType QueryType;
	} TStoragePropertyQuery;

	typedef enum {
		ProtocolTypeUnknown = 0x00,
		ProtocolTypeScsi,
		ProtocolTypeAta,
		ProtocolTypeNvme,
		ProtocolTypeSd,
		ProtocolTypeProprietary = 0x7E,
		ProtocolTypeMaxReserved = 0x7F
	} TStroageProtocolType;

	typedef struct {
		TStroageProtocolType ProtocolType;
		DWORD   DataType;
		DWORD   ProtocolDataRequestValue;
		DWORD   ProtocolDataRequestSubValue;
		DWORD   ProtocolDataOffset;
		DWORD   ProtocolDataLength;
		DWORD   FixedProtocolReturnData;
		DWORD   Reserved[3];
	} TStorageProtocolSpecificData;

	typedef enum {
		NVMeDataTypeUnknown = 0,
		NVMeDataTypeIdentify,
		NVMeDataTypeLogPage,
		NVMeDataTypeFeature,
	} TStorageProtocolNVMeDataType;

	typedef struct {
		TStoragePropertyQuery Query;
		TStorageProtocolSpecificData ProtocolSpecific;
		BYTE Buffer[4096];
	} TStorageQueryWithBuffer;

	
	/* NVME definition */

#define NVME_STORPORT_DRIVER 0xE000


	/* the following are the NVME driver private IOCTL definitions */
#define NVME_PASS_THROUGH_SRB_IO_CODE \
	CTL_CODE(NVME_STORPORT_DRIVER, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define NVME_RESET_DEVICE \
	CTL_CODE(NVME_STORPORT_DRIVER, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define NVME_HOT_ADD_NAMESPACE \
	CTL_CODE(NVME_STORPORT_DRIVER, 0x802, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define NVME_HOT_REMOVE_NAMESPACE \
	CTL_CODE(NVME_STORPORT_DRIVER, 0x803, METHOD_BUFFERED, FILE_ANY_ACCESS)

#define DFP_RECEIVE_DRIVE_DATA CTL_CODE(IOCTL_DISK_BASE, 0x0022, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS) 
#define IDE_ATA_IDENTIFY 0xEc
#define IOCTL_SCSI_MINIPORT_IDENTIFY ((FILE_DEVICE_SCSI << 16) + 0x0501) 


#define NVME_SIG_STR          "NvmeMini"
#define NVME_SIG_STR_LEN      8
#define SCSI_SIG_STR          "SCSIDISK"
#define SCSI_SIG_STR_LEN      8
#define NVME_NO_DATA_TX       0 /* No data transfer involved */
#define NVME_FROM_HOST_TO_DEV 1 /* Transfer data from host to device */
#define NVME_FROM_DEV_TO_HOST 2 /* Transfer data from device to host */
#define NVME_BI_DIRECTION     3 /* Tx data from host to device and back */

#define NVME_IOCTL_VENDOR_SPECIFIC_DW_SIZE 6  /* Vendor sp qualifier (DWORDs) */
#define NVME_IOCTL_CMD_DW_SIZE             16 /* NVMe cmd entry size (DWORDs) */
#define NVME_IOCTL_COMPLETE_DW_SIZE        4  /* NVMe cpl entry size (DWORDs) */



#pragma pack(1)
	
#pragma pack()

#define DRIVE_TEMPERATURE_CODE             0xE7
#define REALLOCATED_SECTORS_COUNT_CODE     0x05
#define ENDURANCE_REMAINING_CODE           0xE8
#define LBAS_READ_CODE                     0xF2
#define LBAS_WRITTEN_CODE                  0xF1
#define LOADED_HOURS_CODE                  0xDE
#define POWER_CYCLE_COUNT_CODE             0x0C
#define POWER_ON_HOURS_CODE                0x09
#define REPORTED_UNCORRECTABLE_ERRORS_CODE 0xBB

#pragma pack(1)
	/******************************************************************************
	* SMART Attribute structure.
	*
	* This structure contains the code and value pair for a SMART attribute
	******************************************************************************/
	typedef struct _NVME_SMART_ATTRIBUTES
	{
		UCHAR Code;
		UCHAR Value;

	} NVME_SMART_ATTRIBUTES, *PNVME_SMART_ATTRIBUTES;
#pragma pack()

#pragma pack(1)
	/******************************************************************************
	* NVMe SMART READ ATTRIBUTES DATA structure.
	*
	* This structure contains the information about SMART passed back when a
	* IOCTL_SCSI_MINIPORT_SMART_READ_ATTRIBS is requested.
	*
	* User applications need to allocate proper size of buffer(s) and populate the
	* fields to ensure the requests are being processed correctly after issuing.
	******************************************************************************/
	typedef struct _NVME_SMART_READ_ATTRIBUTES_DATA
	{
		SRB_IO_CONTROL SrbIoCtrl;
		NVME_SMART_ATTRIBUTES DriveTemperature;
		NVME_SMART_ATTRIBUTES ReallocatedSectorsCount;
		NVME_SMART_ATTRIBUTES EnduranceRemaining;
		NVME_SMART_ATTRIBUTES LBAsRead;
		NVME_SMART_ATTRIBUTES LBAsWritten;
		NVME_SMART_ATTRIBUTES LoadedHours;
		NVME_SMART_ATTRIBUTES PowerCycleCount;
		NVME_SMART_ATTRIBUTES PowerOnHours;
		NVME_SMART_ATTRIBUTES ReportedUncorrectableErrors;
	} NVME_SMART_READ_ATTRIBUTES_DATA, *PNVME_SMART_READ_ATTRIBUTES_DATA;
#pragma pack()

#pragma pack(1)
	/******************************************************************************
	* NVMe SMART READ THRESHOLDS DATA structure.
	*
	* This structure contains the information about SMART passed back when a
	* IOCTL_SCSI_MINIPORT_SMART_READ_THRESHOLDS is requested.
	*
	* User applications need to allocate proper size of buffer(s) and populate the
	* fields to ensure the requests are being processed correctly after issuing.
	******************************************************************************/
	typedef struct _NVME_SMART_READ_THRESHOLDS_DATA
	{
		SRB_IO_CONTROL SrbIoCtrl;
		NVME_SMART_ATTRIBUTES DriveTemperature;
		NVME_SMART_ATTRIBUTES ReallocatedSectorsCount;
	} NVME_SMART_READ_THRESHOLDS_DATA, *PNVME_SMART_READ_THRESHOLDS_DATA;
#pragma pack()

	typedef struct _NVMe_COMMAND_DWORD_0
	{
		/* [Opcode] This field indicates the opcode of the command to be executed */
		UCHAR    OPC;

		/*
		* [Fused Operation] In a fused operation, a complex command is created by
		* "fusing together two simpler commands. Refer to section 6.1. This field
		* indicates whether this command is part of a fused operation and if so,
		* which command it is in the sequence. Value 00b Normal Operation, Value
		* 01b == Fused operation, first command, Value 10b == Fused operation,
		* second command, Value 11b == Reserved.
		*/
		UCHAR    FUSE           :2;
		UCHAR    Reserved       :6;

		/*
		* [Command Identifier] This field indicates a unique identifier for the
		* command when combined with the Submission Queue identifier.
		*/
		USHORT   CID;
	} NVMe_COMMAND_DWORD_0, *PNVMe_COMMAND_DWORD_0;

	typedef struct _NVMe_COMMAND
	{
		/*
		* [Command Dword 0] This field is common to all commands and is defined
		* in Figure 6.
		*/
		NVMe_COMMAND_DWORD_0    CDW0;

		/*
		* [Namespace Identifier] This field indicates the namespace that this
		* command applies to. If the namespace is not used for the command, then
		* this field shall be cleared to 0h. If a command shall be applied to all
		* namespaces on the device, then this value shall be set to FFFFFFFFh.
		*/
		ULONG                   NSID;

		/* DWORD 2, 3 */
		ULONGLONG               Reserved;

		/*
		* [Metadata Pointer] This field contains the address of a contiguous
		* physical buffer of metadata. This field is only used if metadata is not
		* interleaved with the LBA data, as specified in the Format NVM command.
		* This field shall be Dword aligned.
		*/
		ULONGLONG               MPTR;

		/* [PRP Entry 1] This field contains the first PRP entry for the command. */
		ULONGLONG               PRP1;

		/*
		* [PRP Entry 2] This field contains the second PRP entry for the command.
		* If the data transfer spans more than two memory pages, then this field is
		* a PRP List pointer.
		*/
		ULONGLONG               PRP2;

		/* [Command Dword 10] This field is command specific Dword 10. */
		union {
			ULONG               CDW10;
			/*
			* Defined in Admin and NVM Vendor Specific Command format.
			* Number of DWORDs in PRP, data transfer (in Figure 8).
			*/
			ULONG               NDP;
		};

		/* [Command Dword 11] This field is command specific Dword 11. */
		union {
			ULONG               CDW11;
			/*
			* Defined in Admin and NVM Vendor Specific Command format.
			* Number of DWORDs in MPTR, Metadata transfer (in Figure 8).
			*/
			ULONG               NDM;
		};

		/* [Command Dword 12] This field is command specific Dword 12. */
		ULONG                   CDW12;

		/* [Command Dword 13] This field is command specific Dword 13. */
		ULONG                   CDW13;

		/* [Command Dword 14] This field is command specific Dword 14. */
		ULONG                   CDW14;

		/* [Command Dword 15] This field is command specific Dword 15. */
		ULONG                   CDW15;
	} NVMe_COMMAND, *PNVMe_COMMAND;

	//-------------------------------------------------
	// from Toolbox
	//-------------------------------------------------
#define SIZE_MAX_NVME           4096
#define SIZE_MAX_EC_TABLE       SIZE_MAX_NVME * 2

#define METHOD_BUFFERED                 0
#define METHOD_IN_DIRECT                1
#define METHOD_OUT_DIRECT               2
#define METHOD_NEITHER                  3
#define FILE_DEVICE_MASS_STORAGE        0x0000002d
#define IOCTL_STORAGE_BASE FILE_DEVICE_MASS_STORAGE
#define IOCTL_STORAGE_PROTOCOL_COMMAND              CTL_CODE(IOCTL_STORAGE_BASE, 0x04F0, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS)
	//
	// Command Specific Information for Storage Protocols - "CommandSpecific" field.
	//
#define STORAGE_PROTOCOL_SPECIFIC_NVME_ADMIN_COMMAND    0x01
#define STORAGE_PROTOCOL_SPECIFIC_NVME_NVM_COMMAND      0x02
	//
	// Command Length for Storage Protocols.
	//
#define STORAGE_PROTOCOL_COMMAND_LENGTH_NVME            0x40            // NVMe commands are always 64 bytes.
#define STORAGE_PROTOCOL_COMMAND_FLAG_ADAPTER_REQUEST    0x80000000     // Flag indicates the request targeting to adapter instead of device.
	//
	// Command completion status
	// The "Phase Tag" field and "Status Field" are separated in spec. We define them in the same data structure to ease the memory access from software.
	//
	typedef union
	{
		struct _DUMMYSTRUCTNAME
		{
			USHORT  P           : 1;        // Phase Tag (P)

			USHORT  SC          : 8;        // Status Code (SC)
			USHORT  SCT         : 3;        // Status Code Type (SCT)
			USHORT  Reserved    : 2;
			USHORT  M           : 1;        // More (M)
			USHORT  DNR         : 1;        // Do Not Retry (DNR)
		} DUMMYSTRUCTNAME;

		USHORT AsUshort;

	} NVME_COMMAND_STATUS, *PNVME_COMMAND_STATUS;
	//
	// Information of log: NVME_LOG_PAGE_ERROR_INFO. Size: 64 bytes
	//
	typedef struct
	{

		ULONGLONG           ErrorCount;
		USHORT              SQID;           // Submission Queue ID
		USHORT              CMDID;          // Command ID
		NVME_COMMAND_STATUS Status;         // Status Field: This field indicates the Status Field for the command  that completed.  The Status Field is located in bits 15:01, bit 00 corresponds to the Phase Tag posted for the command.

		struct
		{
			USHORT  Byte        : 8;        // Byte in command that contained the error.
			USHORT  Bit         : 3;        // Bit in command that contained the error.
			USHORT  Reserved    : 5;
		} ParameterErrorLocation;

		ULONGLONG           Lba;            // LBA: This field indicates the first LBA that experienced the error condition, if applicable.
		ULONG               NameSpace;      // Namespace: This field indicates the namespace that the error is associated with, if applicable.

		UCHAR               VendorInfoAvailable;    // Vendor Specific Information Available

		UCHAR               Reserved0[3];

		ULONGLONG           CommandSpecificInfo;    // This field contains command specific information. If used, the command definition specifies the information returned.

		UCHAR               Reserved1[24];

	} NVME_ERROR_INFO_LOG, *PNVME_ERROR_INFO_LOG;

	//
	// Parameter for IOCTL_STORAGE_PROTOCOL_COMMAND
	// Buffer layout: <STORAGE_PROTOCOL_COMMAND> <Command> [Error Info Buffer] [Data-to-Device Buffer] [Data-from-Device Buffer]
	//
#define STORAGE_PROTOCOL_STRUCTURE_VERSION              0x1

	typedef struct _STORAGE_PROTOCOL_COMMAND
	{

		DWORD   Version;                        // STORAGE_PROTOCOL_STRUCTURE_VERSION
		DWORD   Length;                         // sizeof(STORAGE_PROTOCOL_COMMAND)

		TStroageProtocolType  ProtocolType;
		DWORD   Flags;                          // Flags for the request

		DWORD   ReturnStatus;                   // return value
		DWORD   ErrorCode;                      // return value, optional

		DWORD   CommandLength;                  // non-zero value should be set by caller
		DWORD   ErrorInfoLength;                // optional, can be zero
		DWORD   DataToDeviceTransferLength;     // optional, can be zero. Used by WRITE type of request.
		DWORD   DataFromDeviceTransferLength;   // optional, can be zero. Used by READ type of request.

		DWORD   TimeOutValue;                   // in unit of seconds

		DWORD   ErrorInfoOffset;                // offsets need to be pointer aligned
		DWORD   DataToDeviceBufferOffset;       // offsets need to be pointer aligned
		DWORD   DataFromDeviceBufferOffset;     // offsets need to be pointer aligned

		DWORD   CommandSpecific;                // optional information passed along with Command.
		DWORD   Reserved0;

		DWORD   FixedProtocolReturnData;        // return data, optional. Some protocol, such as NVMe, may return a small amount data (DWORD0 from completion queue entry) without the need of separate device data transfer.
		DWORD   Reserved1[3];

		BYTE    Command[ANYSIZE_ARRAY];

	} STORAGE_PROTOCOL_COMMAND, *PSTORAGE_PROTOCOL_COMMAND;
	typedef union
	{
		struct 
		{
			ULONG   CNS      : 2;        // Controller or Namespace Structure (CNS)
			ULONG   Reserved : 30;
		} DUMMYSTRUCTNAME;

		ULONG AsUlong;

	} NVME_CDW10_IDENTIFY, *PNVME_CDW10_IDENTIFY;
	//
	// Command Dword 0
	//
	typedef union
	{
		struct
		{
			//LSB
			ULONG OPC       : 8;        // Opcode (OPC)
			ULONG FUSE      : 2;        // Fused Operation (FUSE)
			ULONG Reserved0 : 5;
			ULONG PSDT      : 1;        // PRP or SGL for Data Transfer (PSDT)
			ULONG CID       : 16;       // Command Identifier (CID)
			//MSB
		}DUMMYSTRUCTNAME;

		ULONG AsUlong;

	} NVME_COMMAND_DWORD0, *PNVME_COMMAND_DWORD0;
	//
	// NVMe command data structure
	//
	typedef struct
	{
		//
		// Common fields for all commands
		//
		NVME_COMMAND_DWORD0 CDW0;
		ULONG               NSID;
		ULONG               Reserved0[2];
		ULONGLONG           MPTR;
		ULONGLONG           PRP1;
		ULONGLONG           PRP2;

		//
		// Command independent fields from CDW10 to CDW15
		//
		union
		{

			//
			// General Command data fields
			//
			struct
			{
				ULONG   CDW10;
				ULONG   CDW11;
				ULONG   CDW12;
				ULONG   CDW13;
				ULONG   CDW14;
				ULONG   CDW15;
			} GENERAL;

			//
			// Admin Command: Identify
			//
			struct
			{
				NVME_CDW10_IDENTIFY CDW10;
				ULONG   CDW11;
				ULONG   CDW12;
				ULONG   CDW13;
				ULONG   CDW14;
				ULONG   CDW15;
			} IDENTIFY;
		} u;

	} NVME_COMMAND, *PNVME_COMMAND;
}