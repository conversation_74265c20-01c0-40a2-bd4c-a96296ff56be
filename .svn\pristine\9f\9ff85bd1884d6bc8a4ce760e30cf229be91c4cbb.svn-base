﻿<!DOCTYPE html>
<html><head>
   <title>Sleeper by PassMark Software</title>
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <meta http-equiv="X-UA-Compatible" content="IE=edge" />

   <!-- This line includes the general project style sheet (not required) -->
   <link type="text/css" href="default.css" rel="stylesheet" />

   <style type="text/css">
       body { background:#FFF; }
       .navbar      { font-size: 120%; }

       #idx          { margin: 0; padding: 0; }     /* div tag that wraps the keyword index */
       #idx a        { font-color: #000; text-decoration: none; }  /* all links in index appear as text */

       #idx p             { margin: 2px; }       /* keywords and secondary keywords */
       #idx p.idxkeyword2 { margin-left: 20px }  /* indentation for secondary keywords */

       table.idxtable { background: #F4F4F4;
                        border: 1px solid #000000;
                        border-collapse: collapse;
                        -moz-box-shadow: 2px 2px 2px #B0B0B0;
                        -webkit-box-shadow: 2px 2px 2px #B0B0B0;
                        box-shadow: 2px 2px 2px #B0B0B0;
                        filter: progid:DXImageTransform.Microsoft.Shadow(color=B0B0B0, Direction=135, Strength=4); }
       td.idxtable    { background: #F4F4F4; }

       /* font definitions for keyword section, keywords and popup links */
       .idxsection  { font-family: Arial,Helvetica; font-weight: bold; font-size: 14pt; color: #000000; text-decoration: none;
                      margin-top: 15px; margin-bottom: 15px; }
       .idxkeyword  { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .idxkeyword2 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .idxlink     { font-family: Arial,Helvetica; font-weight: normal; font-size: 9pt; color: #000000; text-decoration: none; }

   </style>
   <script type="text/javascript" src="jquery.js"></script>
   <script type="text/javascript" src="helpman_settings.js"></script>
   <script type="text/javascript" src="hmcontextids.js"></script>
</head>
<body>
<p class="navbar"><a href="hmcontent.htm">Contents</a>
 | <b>Index</b>
 | <a href="hmftsearch.htm">Search</a>
</p>
<hr/>

  <!-- Place holder for the keyword index - this variable is REQUIRED! -->
  <script type="text/javascript">

  function hmInitHideLinks(cssCode) {
  	var styleElement = document.createElement("style");
  	styleElement.type = "text/css";
  	if (styleElement.styleSheet) {
    	styleElement.styleSheet.cssText = cssCode;
  	}
  	else {
    	styleElement.appendChild(document.createTextNode(cssCode));
  	}
  	document.getElementsByTagName("head")[0].appendChild(styleElement);
  }

  hmInitHideLinks("#idx div { display: none }");

  var currentdiv = null;
  var canhidelinks = true;

  function hmshowLinks(divID) {
    var thisdiv = document.getElementById(divID);
    canhidelinks = true;
    hmhideLinks();
    if (thisdiv) {
      currentdiv = thisdiv;
      $(currentdiv).show();
      $(currentdiv).mouseover(hmdivMouseOver).mouseout(hmdivMouseOut);
      $(document).mouseup(hmhideLinks);
    }
  }
  function hmdivMouseOver() { canhidelinks = false; };
  function hmdivMouseOut() { canhidelinks = true; };
  function hmhideLinks() {
    if (canhidelinks) {
      if (currentdiv) {
        $(currentdiv).hide();
        $(currentdiv).unbind("onmouseover", "onmouseout");
      }
      currentdiv = null;
      $(document).unbind("onmouseup");
    }
  }
</script>
<div id="idx" style="margin:0;padding:0;border:none">
<a name="A" id="A"></a><p class="idxsection">- A -</p>
<p class="idxkeyword"><a href="hid_commandline.htm" target="hmcontent"><span class="idxkeyword">Arguments</span></a></p>
<a name="C" id="C"></a><p class="idxsection">- C -</p>
<p class="idxkeyword"><a href="hid_commandline.htm" target="hmcontent"><span class="idxkeyword">Command line</span></a></p>
<p class="idxkeyword"><a href="hid_config.htm" target="hmcontent"><span class="idxkeyword">Config</span></a></p>
<p class="idxkeyword"><a href="hid_commandline.htm" target="hmcontent"><span class="idxkeyword">Configuration mode</span></a></p>
<p class="idxkeyword"><a href="hid_config.htm" target="hmcontent"><span class="idxkeyword">Configuration Options</span></a></p>
<p class="idxkeyword"><a href="hid_contacts.htm" target="hmcontent"><span class="idxkeyword">Contacting PassMark</span></a></p>
<p class="idxkeyword"><a href="hid_copyright.htm" target="hmcontent"><span class="idxkeyword">Copyright notice</span></a></p>
<a name="E" id="E"></a><p class="idxsection">- E -</p>
<p class="idxkeyword"><a href="hid_copyright.htm" target="hmcontent"><span class="idxkeyword">EULA</span></a></p>
<a name="H" id="H"></a><p class="idxsection">- H -</p>
<p class="idxkeyword"><a href="hid_sleepstates.htm" target="hmcontent"><span class="idxkeyword">Hibernate</span></a></p>
<a name="I" id="I"></a><p class="idxsection">- I -</p>
<p class="idxkeyword"><a href="hid_commandline.htm" target="hmcontent"><span class="idxkeyword">Interactive mode</span></a></p>
<p class="idxkeyword"><a href="hid_overview.htm" target="hmcontent"><span class="idxkeyword">Introduction to Sleeper</span></a></p>
<a name="L" id="L"></a><p class="idxsection">- L -</p>
<p class="idxkeyword"><a href="hid_copyright.htm" target="hmcontent"><span class="idxkeyword">License (End user)</span></a></p>
<a name="M" id="M"></a><p class="idxsection">- M -</p>
<p class="idxkeyword"><a href="hid_systemreq.htm" target="hmcontent"><span class="idxkeyword">Minimum requirements</span></a></p>
<a name="O" id="O"></a><p class="idxsection">- O -</p>
<p class="idxkeyword"><a href="hid_config.htm" target="hmcontent"><span class="idxkeyword">Options</span></a></p>
<p class="idxkeyword"><a href="hid_overview.htm" target="hmcontent"><span class="idxkeyword">Overview of Sleeper</span></a></p>
<a name="P" id="P"></a><p class="idxsection">- P -</p>
<p class="idxkeyword"><a href="hid_contacts.htm" target="hmcontent"><span class="idxkeyword">PassMark on the Web</span></a></p>
<p class="idxkeyword"><a href="hid_sleepstates.htm" target="hmcontent"><span class="idxkeyword">Power State</span></a></p>
<a name="S" id="S"></a><p class="idxsection">- S -</p>
<p class="idxkeyword"><a href="hid_sleepstates.htm" target="hmcontent"><span class="idxkeyword">Sleep</span></a></p>
<p class="idxkeyword"><a href="hid_contacts.htm" target="hmcontent"><span class="idxkeyword">Support</span></a></p>
<p class="idxkeyword"><a href="hid_systemreq.htm" target="hmcontent"><span class="idxkeyword">System requirements</span></a></p>
<a name="U" id="U"></a><p class="idxsection">- U -</p>
<p class="idxkeyword"><a href="hid_ui.htm" target="hmcontent"><span class="idxkeyword">User Interface</span></a></p>
<a name="W" id="W"></a><p class="idxsection">- W -</p>
<p class="idxkeyword"><a href="hid_contacts.htm" target="hmcontent"><span class="idxkeyword">Web page</span></a></p>
<p class="idxkeyword"><a href="hid_systemreq.htm" target="hmcontent"><span class="idxkeyword">Windows requirements</span></a></p>
</div>


</body>
</html>

