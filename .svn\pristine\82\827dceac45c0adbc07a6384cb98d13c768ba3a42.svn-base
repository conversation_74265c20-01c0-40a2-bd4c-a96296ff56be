
import PublicFuc
import configparser
import csv
import os,re
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors,Font
from datetime import datetime,timedelta
import ErrDiskInfo

dicPfm = {} #存放性能相关数据
mp_column_name_map = {} #量产工具低格高格测试结果的列映射关系

PFM_DATA_START_LINE = 19

FW_VERSION = '' #主控版本
MP_VERSION = '' #量产工具版本
FLASH_ID = ''  #flash的ID
FLASH_NAME = ''  #flash的型号
FLASH_MCU = '' #主控版本号
TEST_TIME = '' #测试时间

def Run(curpath, workBook, alignment):
    ws = workBook['性能测试'] #'综合性能测试'
    ws.alignment = alignment
    ProPfm(curpath, ws)

def InitMPCsvColumnNameMap(columnNameMap,listHeader):
    #columnNameMap['端口'] = 0
    PublicFuc.RemoveSpace(listHeader)
    if listHeader == []:
        return
    columnNameMap.clear()
    columnNameMap['FlashNO'] = PublicFuc.GetIndex('FlashNO',listHeader)
    columnNameMap['LogCap'] = PublicFuc.GetIndex('LogCap',listHeader)  #开卡容量
    columnNameMap['MP_Result'] = PublicFuc.GetIndex('MP_Result',listHeader) #通过Pass 否则错误码
    columnNameMap['MP_Time'] = PublicFuc.GetIndex('MP_Time(min)',listHeader) #量产时间
    columnNameMap['BadBlkNum'] = PublicFuc.GetIndex('BadBlkNum',listHeader)
    columnNameMap['MPStatus'] = PublicFuc.GetIndex('MPStatus',listHeader) #模式
    columnNameMap['FlashID'] = PublicFuc.GetIndex('FlashID',listHeader)
    columnNameMap['PC_Name'] = PublicFuc.GetIndex('PC_Name',listHeader)

    columnNameMap['mp_version'] = PublicFuc.GetIndex('mp_version',listHeader) #量产工具版本
    columnNameMap['fw_version'] = PublicFuc.GetIndex('fw_version',listHeader) #固件版本
    columnNameMap['FlashName'] = PublicFuc.GetIndex('FlashName',listHeader) #Flash型号
    columnNameMap['ControlName'] = PublicFuc.GetIndex('ControlName',listHeader) #主控

def ProPfm(curpath, worksheet):
    #高格
    pattern = '.+\\\\Plan5\\\\T_GE_U3_C3\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicPfm,'HighFormat')
    pattern = '.+\\\\Plan18\\\\T_GE_U3_C3\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicPfm,'HighFormat')
    pattern = '.+\\\\Plan23\\\\T_GE_U3_C3\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicPfm,'HighFormat')
    pattern = '.+\\\\Plan30\\\\T_GE_U3_C34\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicPfm,'HighFormat')
    pattern = '.+\\\\Plan31\\\\T_GE_U3_C34\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicPfm,'HighFormat')
    pattern = '.+\\\\Plan32\\\\T_GE_U3_C34\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicPfm,'HighFormat')
    #高格
    pattern = '.+\\\\Plan2\\\\T_GE_U3_C3\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicPfm,'HighFormat')
   
    #hdbench
    hdbKey = ['Read','Write','RRead','RWrite']
    pattern = '.+\\\\Plan5\\\\T_GE_U3_C6\\\\HDBench\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDB_1', hdbKey, 'HDBench.bmp',0)
    pattern = '.+\\\\Plan18\\\\T_GE_U3_C6\\\\HDBench\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDB_1', hdbKey, 'HDBench.bmp',0)
    pattern = '.+\\\\Plan23\\\\T_GE_U3_C6\\\\HDBench\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDB_1', hdbKey, 'HDBench.bmp',0)
    pattern = '.+\\\\Plan30\\\\T_GE_U3_C6\\\\HDBench\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDB_1', hdbKey, 'HDBench.bmp',0)
    pattern = '.+\\\\Plan32\\\\T_GE_U3_C33\\\\HDBench\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDB_1', hdbKey, 'HDBench.bmp',0)
    pattern = '.+\\\\Plan2\\\\T_GE_U3_C6\\\\HDBench\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDB_1', hdbKey, 'HDBench.bmp',0)
    
    #cdm
    cdmKey = ['SeqQ32T1_Read','SeqQ32T1_Write','4KQ32T1_Read','4KQ32T1_Write','Seq_Read','Seq_Write','4K_Read','4K_Write']
    pattern = '.+\\\\Plan5\\\\T_GE_U3_C6\\\\CDM\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'CDM_1', cdmKey, 'CDM.bmp',0)
    pattern = '.+\\\\Plan18\\\\T_GE_U3_C6\\\\CDM\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'CDM_1', cdmKey, 'CDM.bmp',0)
    pattern = '.+\\\\Plan23\\\\T_GE_U3_C6\\\\CDM\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'CDM_1', cdmKey, 'CDM.bmp',0)
    pattern = '.+\\\\Plan30\\\\T_GE_U3_C6\\\\CDM\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'CDM_1', cdmKey, 'CDM.bmp',0)
    pattern = '.+\\\\Plan31\\\\T_GE_U3_C32\\\\CDM\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'CDM_1', cdmKey, 'CDM.bmp',0)
    pattern = '.+\\\\Plan2\\\\T_GE_U3_C6\\\\CDM\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'CDM_1', cdmKey, 'CDM.bmp',0)

    #h2testw
    h2Key = ['write speed','read speed','qa_err_msg']
    pattern = '.+\\\\Plan5\\\\T_GE_U3_C6\\\\满盘H2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'H2_1', h2Key, 'H2.bmp',0)
    pattern = '.+\\\\Plan18\\\\T_GE_U3_C6\\\\满盘H2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'H2_1', h2Key, 'H2.bmp',0)
    pattern = '.+\\\\Plan23\\\\T_GE_U3_C6\\\\满盘H2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'H2_1', h2Key, 'H2.bmp',0)
    pattern = '.+\\\\Plan30\\\\T_GE_U3_C6\\\\满盘H2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'H2_1', h2Key, 'H2.bmp',0)
    pattern = '.+\\\\Plan2\\\\T_GE_U3_C6\\\\满盘H2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'H2_1', h2Key, 'H2.bmp',0)

    #atto
    attoKey = ['64 MB_Write','64 MB_Read']
    #attoCol = ['C','B','E','M','G','I']
    pattern = '.+\\\\Plan5\\\\T_GE_U3_C28\\\\ATTO\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniDataATTO(curpath, pattern, dicPfm, 'ATTO_1',attoKey, 'ATTO.bmp',0)
    pattern = '.+\\\\Plan18\\\\T_GE_U3_C28\\\\ATTO\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniDataATTO(curpath, pattern, dicPfm, 'ATTO_1',attoKey, 'ATTO.bmp',0)

    #hdtune
    hdtuneKey =  ['min spped','max spped','avg spped','acess time','sundden trans rate','cpu usage']  #'Cap',
    pattern = '.+\\\\Plan5\\\\T_GE_U3_C28\\\\HDTune_Read\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDR1', hdtuneKey, 'HDTune.bmp',0)
    pattern = '.+\\\\Plan18\\\\T_GE_U3_C28\\\\HDTune_Read\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDR1', hdtuneKey, 'HDTune.bmp',0)
    pattern = '.+\\\\Plan5\\\\T_GE_U3_C28\\\\HDTune_Write\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDW1', hdtuneKey, 'HDTune.bmp',0)
    pattern = '.+\\\\Plan18\\\\T_GE_U3_C28\\\\HDTune_Write\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDW1', hdtuneKey, 'HDTune.bmp',0)

    #写数据
    startLine = PFM_DATA_START_LINE  
    WritePfmDic(worksheet,startLine,dicPfm)
    worksheet['%s%d'%('B', 9)] = FLASH_MCU
    worksheet['%s%d'%('G', 9)] = MP_VERSION
    worksheet['%s%d'%('G', 11)] = PublicFuc.GetDate()
    PublicFuc.WriteReportTime(worksheet,'Q',1)
    PublicFuc.WriteReportOperator(worksheet,'T',1)

def ReadFormatCsvData(curpath,pattern,dataDic,caseKey):
    csvHeaderColumn = []
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除

        #file_data = []
        with open(file,mode='r', buffering=-1, encoding='cp936') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            birth_header = next(csv_reader)  # 读取第一行每一列的标题
            if csvHeaderColumn == []:
                csvHeaderColumn = birth_header
                InitMPCsvColumnNameMap(mp_column_name_map,csvHeaderColumn)
            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中
                key = row[mp_column_name_map['FlashNO']]
                cap = row[mp_column_name_map['LogCap']]
                mode = row[mp_column_name_map['MPStatus']]
                pcNo = row[mp_column_name_map['PC_Name']]
                tempRow = [cap,mode,pcNo]
                if key not in dataDic:
                    dataDic[key] = {}
                if caseKey not in dataDic[key]:
                    dataDic[key][caseKey] = tempRow
                    fileMdTime = os.path.getmtime(file)
                    dataDic[key][PublicFuc.GetTimeKeyName(caseKey)] = fileMdTime
                else:
                    #查看是否是最新数据做覆盖处理。
                    oldTime = dataDic[key][PublicFuc.GetTimeKeyName(caseKey)]
                    fileMdTime = os.path.getmtime(file)
                    if fileMdTime < oldTime:
                        continue#数据不是新的，不做读取覆盖

                    dataDic[key][caseKey] = tempRow
                    dataDic[key][PublicFuc.GetTimeKeyName(caseKey)] = fileMdTime

                #begin此段为独立的记录公共信息的代码块
                global FW_VERSION
                global MP_VERSION
                global FLASH_ID
                global FLASH_NAME
                global FLASH_MCU
                if FW_VERSION == '':
                    FW_VERSION = row[mp_column_name_map['fw_version']]
                if MP_VERSION == '':
                    MP_VERSION = row[mp_column_name_map['mp_version']]
                if FLASH_ID == '':
                    FLASH_ID = row[mp_column_name_map['FlashID']]
                if FLASH_NAME == '':
                    FLASH_NAME = row[mp_column_name_map['FlashName']]
                if FLASH_MCU == '':
                    FLASH_MCU = row[mp_column_name_map['ControlName']]
                #end

#绘制表格。
def InitPfmReportTemplateInWorkSheet(worksheet):
    #titleFont=Font('宋体',size=11,color=colors.BLACK,bold=True,italic=False)
    cellfont=Font('宋体',size=10,color=colors.BLACK,bold=False,italic=False)
    for rowIdx in range(len(dicPfm)):
        for col in range(32):
            worksheet['%s%d'%(get_column_letter(col+1), PFM_DATA_START_LINE+rowIdx)].alignment = PublicFuc.alignment
            worksheet['%s%d'%(get_column_letter(col+1), PFM_DATA_START_LINE+rowIdx)].border = PublicFuc.my_border('thin', 'thin', 'thin', 'thin')
            worksheet['%s%d'%(get_column_letter(col+1), PFM_DATA_START_LINE+rowIdx)].font = cellfont


def WritePfmDic(worksheet, startLine, dataDic,imgWidth = 360, imgHeight = 300):
    InitPfmReportTemplateInWorkSheet(worksheet)#绘制表格

    curLine = startLine
    imageStartLine = startLine + len(dataDic)+2
    imageLine = imageStartLine
    imageCol = 1
    keySortLst = sorted(dataDic.keys(), reverse=False)
    for idx,key in enumerate(keySortLst):
        imageCol = 1
        imageLine = imageStartLine + idx*18

        worksheet['%s%d'%('A', curLine)] = idx+1 #填写数据序号
        worksheet['%s%d'%('B', curLine)] = key #填写flash编号
        if 'windows_version' in dataDic[key]:
            worksheet['%s%d'%('F', curLine)] = dataDic[key]['windows_version']

        caseKey = 'HighFormat'
        if caseKey in dataDic[key]:
            line = dataDic[key][caseKey]
            colLst = ['C','D','E']
            for index,col in enumerate(colLst):
                worksheet['%s%d'%(col, curLine)] = line[index]

        caseKey = 'HDB_1'
        if caseKey in dataDic[key]:
            line = dataDic[key][caseKey]
            colLst = ['G','H','I','J']
            for index,col in enumerate(colLst):
                worksheet['%s%d'%(col, curLine)] = line[index]

        caseKey = 'CDM_1'
        if caseKey in dataDic[key]:
            line = dataDic[key][caseKey]
            colLst = ['K','L','M','N','O','P','Q','R']
            for index,col in enumerate(colLst):
                worksheet['%s%d'%(col, curLine)] = line[index]

        caseKey = 'H2_1'
        if caseKey in dataDic[key]:
            line = dataDic[key][caseKey]
            colLst = ['S','T','U']
            for index,col in enumerate(colLst):
                if col == 'R':
                    if line[index] == '':
                        worksheet['%s%d'%(col, curLine)] = 'TRUE'
                    else:
                        worksheet['%s%d'%(col, curLine)] = 'FALSE'
                        tmpItemKey = ErrDiskInfo.GetCombinedKeyName('H2_1',ErrDiskInfo.g_pcnoKey)
                        pcNo = dataDic[key][tmpItemKey]
                        tmpItemKey = ErrDiskInfo.GetCombinedKeyName('H2_1',ErrDiskInfo.g_filepathKey)
                        file = dataDic[key][tmpItemKey]
                        errcode = line[index]
                        PublicFuc.AppendErrDiskInfo('性能测试_Err',key,errcode,pcNo,file)
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index]

        caseKey = 'ATTO_1'
        if caseKey in dataDic[key]:
            line = dataDic[key][caseKey]
            colLst = ['V','W']
            for index,col in enumerate(colLst):
                worksheet['%s%d'%(col, curLine)] = line[index]

        caseKey = 'HDR1'
        if caseKey in dataDic[key]:
            line = dataDic[key][caseKey]
            colLst = ['X','Y','Z','AA','AB','AC']
            for index,col in enumerate(colLst):
                worksheet['%s%d'%(col, curLine)] = line[index]

        caseKey = 'HDW1'
        if caseKey in dataDic[key]:
            line = dataDic[key][caseKey]
            colLst = ['AD','AE','AF','AG','AH','AI']
            for index,col in enumerate(colLst):
                worksheet['%s%d'%(col, curLine)] = line[index]

        #写图片
        worksheet['%s%d'%(get_column_letter(imageCol), imageLine)] = key #填写flash编号
        imageLine += 1
        
        if 'HDB_1' in dataDic[key]:
            line = dataDic[key]['HDB_1']
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
        imageCol += 5

        if 'CDM_1' in dataDic[key]:
            line = dataDic[key]['CDM_1']
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
        imageCol += 5

        if 'H2_1' in dataDic[key]:
            line = dataDic[key]['H2_1']
            # 列表最后一项是图片路径
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
        imageCol += 5

        if 'ATTO_1' in dataDic[key]:
            line = dataDic[key]['ATTO_1']
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
            imageCol += 5
            if '' != line[-2]:
                img = Image(line[-2])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
            imageCol += 5
        else:
            imageCol += 10

        if 'HDR1' in dataDic[key]:
            line = dataDic[key]['HDR1']
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
        imageCol += 5

        if 'HDW1' in dataDic[key]:
            line = dataDic[key]['HDW1']
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
                #imageCol += 5

        curLine += 1


      




                