import PublicFuc
from openpyxl.drawing.image import Image
import csv
import matplotlib.pyplot as plt
import tempfile,os
from openpyxl.utils import get_column_letter

strPCIETempDir = os.path.join(tempfile.gettempdir(), 'pcie_report_temp')
MarsMap = {}#marcdmtest的csv对应关系

def InitProMarMap(MarsMap):
    MarsMap.clear()
    MarsMap['SEQ1M_Q8T1_re'] = 1
    MarsMap['SEQ1M_Q8T1_wr'] = 5
    MarsMap['SEQ128k_Q32T1_re'] = 2
    MarsMap['SEQ128k_Q32T1_wr'] = 6
    MarsMap['RND4k_Q32T16_re'] = 3
    MarsMap['RND4k_Q32T16_wr'] = 7
    MarsMap['RND4k_Q1T1_re'] = 4
    MarsMap['RND4k_Q1T1_wr'] = 8


def InitProMarCDMtest(dataDic,resultDic):
    for key in dataDic:
        templist = []
        if len(dataDic[key]) < 2:
            break
        resultDic[key] = []
        del dataDic[key][0]
        limitline = [0,0,0,0,0,0,0,0]
        for item in dataDic[key]:
            temp = ['']*(len(MarsMap))
            if len(item) >= 8:
                temp[0] = item[MarsMap['SEQ1M_Q8T1_re']+1]
                temp[1] = item[MarsMap['SEQ1M_Q8T1_wr']+1]
                temp[2] = item[MarsMap['SEQ128k_Q32T1_re']+1]
                temp[3] = item[MarsMap['SEQ128k_Q32T1_wr']+1]
                temp[4] = item[MarsMap['RND4k_Q32T16_re']+1]
                temp[5] = item[MarsMap['RND4k_Q32T16_wr']+1]
                temp[6] = item[MarsMap['RND4k_Q1T1_re']+1]
                temp[7] = item[MarsMap['RND4k_Q1T1_wr']+1]
                for i in range(0,8):
                    limitline[i] = max(limitline[i],float(temp[i]))
            templist.append(temp)
        templist.append(limitline)
        resultDic[key].append(templist)

def InitProMarOverWrite(dataDic,resultDic):
    for key in dataDic:
        resultDic[key] = []
        if len(dataDic[key]) >= 1:
            del dataDic[key][0]
        if len(dataDic[key]) >= 3:
            del dataDic[key][2]
        if len(dataDic[key]) >= 5:
            del dataDic[key][4]
        if len(dataDic[key]) >= 7:
            del dataDic[key][6]
        temp = ['']*(8)
        for ind,item in enumerate(dataDic[key]):
            temp[ind] = item[2]    
        resultDic[key] = temp

def Run(curpath, workBook, alignment):
    ws = workBook['定容_性能测试']
    ws.alignment = alignment
    ProCdm(curpath, ws)  
    ProAssd(curpath, ws)
    ProMarCDMtest(curpath, ws)
    ProMarOverWrite(curpath, ws)
    #ProAtto(curpath, ws)
    #ProTxBENCH(curpath, ws)
    #ProAnvilsStorageUtilities(curpath, ws)
    #proAIDA64_Linear_Read(curpath, ws)
    #proAIDA64_Linear_Write(curpath, ws)
    #proAIDA64_Average_Read(curpath, ws)
    #proAIDA64_Average_Write(curpath, ws)
    #proHdtune(curpath, ws)
    #proHdtuneFileBase(curpath, ws)
    #proPCMark8(curpath, ws)
    #proPCMarkVantage(curpath, ws)
    #proIometer(curpath, ws)
    
    PublicFuc.WriteReportTime(ws,'P',2)
    PublicFuc.WriteReportOperator(ws,'D',2)

def ProCdm(curpath, worksheet):
    #配置文件中的键
    cdmKey = ['pc_no', 'Cap', 'qa_err_msg','SEQ1MQ8T1_Read','SEQ1MQ8T1_Write','SEQ128KQ32T1_Read','SEQ128KQ32T1_Write','RND4KQ32T16_Read','RND4KQ32T16_Write','RND4KQ1T1_Read','RND4KQ1T1_Write']
    #excel中键对应填充的列，第一个是编号的列，其他列应与键顺序一一对应
    cdmCol = ['C','B', 'E', 'P','G','H','I','J','K','L','M','N']
    cdmDic = {}
    pattern = '.+\\\\Plan2\\\\T-SS-NV-C01\\\\CDM测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, cdmDic, cdmKey, 'CDM.bmp')
    PublicFuc.GetMaxOrMinValueLst(cdmKey, cdmDic)
    startLine = 15
    imgWidth = 250
    imgHeight = 240
    PublicFuc.WriteDataAndImage(worksheet, startLine, cdmDic, cdmCol, cdmKey, imgWidth, imgHeight)

def ProAssd(curpath, worksheet):   
    assdKey = ['pc_no', 'Cap', 'qa_err_msg','Seq Read','Seq Write','4K Read','4K Write','4K 64Thrd Read','4K 64Thrd Write','Read Acc Time','Write Acc Time','Read Score','Write Score','Total Score']
    assdCol = ['C','B','E','S','G','H','I','J','K','L','M','N','O','P','Q']
    assdDic = {}
    pattern = '.+\\\\Plan2\\\\T-SS-NV-C02\\\\AS SSD Benchmark测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, assdDic, assdKey, 'AS_SSD.bmp')
    PublicFuc.GetMaxOrMinValueLst(assdKey, assdDic)
    startLine = 40
    imgWidth = 250
    imgHeight = 240
    PublicFuc.WriteDataAndImage(worksheet, startLine, assdDic, assdCol, assdKey, imgWidth, imgHeight)
    
def ProMarCDMtest(curpath, worksheet):
    InitProMarMap(MarsMap)
    attoCol =  ['H','I','J','K','L','M','N','O']
    smartCol =  ['B','C','E']
    pattern = '.+\\\\Plan73\\\\T-SS_PCIE_M2-C112\\\\Idle_Perf\\\\\d{14}\\\\.+\\\\pcie_cdm_performance.csv$'
    startLine = 66
    dataDic = {}
    resultDic = {}
    smarfinfo = {}
    #dataCol = ['A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R']
    PublicFuc.ReadMarsCsvData(curpath,pattern,dataDic,smarfinfo,True)
    InitProMarCDMtest(dataDic, resultDic)
    PublicFuc.WriteDataMarsCsv(worksheet, startLine, resultDic, attoCol, smarfinfo, smartCol,2)

def ProMarOverWrite(curpath, worksheet):
    InitProMarMap(MarsMap)
    attoCol =  ['G','H','I','J','K','L','M','N']
    smartCol =  ['B','C','E']
    pattern = '.+\\\\Plan65\\\\T-SS_PCIE_M2-C94\\\\OverWrite\\\\\d{14}\\\\.+\\\\writeSpeed.csv$'
    startLine = 104
    dataDic = {}
    resultDic = {}
    smarfinfo = {}
    #dataCol = ['A','B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R']
    PublicFuc.ReadMarsCsvData(curpath,pattern,dataDic,smarfinfo,True)
    InitProMarOverWrite(dataDic, resultDic)
    PublicFuc.WriteDataMarsOverwirteCsv(worksheet, startLine, resultDic, attoCol, smarfinfo, smartCol,2)

def ProAtto(curpath, worksheet):
    attoKey = ['pc_no', 'Cap', 'qa_err_msg','64 MB_Write','64 MB_Read']
    attoCol = ['C','B','E','M','G','I']
    attoDic = {}
    pattern = '.+\\\\Plan2\\\\T-SS-NV-C03\\\\ATTO Disk Benchmark测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, attoDic, attoKey, 'ATTO4_0_MBps.bmp')
    PublicFuc.GetMaxOrMinValueLst(attoKey, attoDic)
    startLine = 66
    imgWidth = 250
    imgHeight = 330
    PublicFuc.WriteDataAndImage(worksheet, startLine, attoDic, attoCol, attoKey, imgWidth, imgHeight)

def WriteDataAndImageOfHdtune(worksheet, startLine, dataDic, colLst, imgWidth, imgHeight):
    imageLine = startLine+2
    curLine = startLine
    for key in dataDic:
        imageCol = 1
        for line in dataDic[key]:
            for index,col in enumerate(colLst):
                if 0 == index:
                    worksheet['%s%d'%(col, curLine)] = key
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
            curLine += 1
            # hdtune列表最后两项是图片路径(读和写)
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
            imageCol += 3
        curLine = startLine+1
        imageLine += 1

def WriteDataAndImageOfHdtuneFullWrite(worksheet, startLine, dataDic, colLst, imgWidth, imgHeight):
    imageLine = startLine+2
    curLine = startLine
    imageCol = 1
    for key in dataDic:
        for line in dataDic[key]:
            for index,col in enumerate(colLst):
                try:
                    if 0 == index:
                        worksheet['%s%d'%(col, curLine)] = key
                    else:
                        worksheet['%s%d'%(col, curLine)] = line[index-1]
                 #合并的单元格只能写一次，需要捕获异常
                except(AttributeError):
                    continue
            curLine += 1
            # 列表最后一项是图片路径
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
            imageCol += 8

def proHdtune(curpath, worksheet):
    hdtuneKey = ['pc_no', 'Cap', 'qa_err_msg','min spped','max spped','avg spped','acess time','sundden trans rate','cpu usage']
    hdtuneCol = ['C','B','E','T','G','I','K','M','O','Q']
    readDic = {}
    pattern = '.+\\\\Plan2\\\\T-SS-NV-C10\\\\基准测试 for Read\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, readDic, hdtuneKey, 'HDTune.bmp', 1)
    
    startLine = 91
    imgWidth = 450
    imgHeight = 350
    WriteDataAndImageOfHdtune(worksheet, startLine, readDic, hdtuneCol, imgWidth, imgHeight)

def proHdtuneFileBase(curpath, worksheet):
    hdtuneKey = ['pc_no', 'Cap', 'qa_err_msg','sequential read speed','sequential write speed','4kb read speed','4kb write speed','4kb queue read speed','4kb queue write speed','data mode','file length']
    hdtuneCol = ['C','B','E','T','G','I','K','M','O','P','Q','R']
    dataDic = {}
    pattern = '.+\\\\Plan2\\\\T-SS-NV-C17\\\\定容文件基准测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dataDic, hdtuneKey, 'HDTune.bmp', 1)

    for keyNo in dataDic:
        keySet = dataDic[keyNo]
        for line in keySet:
            if line[2] == '':
                line[2] = 'PASS'

    imgWidth = 666
    imgHeight = 666
    #WriteDataAndImageOfHdtune(worksheet, startLine, rwDic, hdtuneCol, imgWidth, imgHeight)
    # hdtune满盘写
    startLine = 98
    WriteDataAndImageOfHdtuneFullWrite(worksheet, startLine, dataDic, hdtuneCol, imgWidth, imgHeight)

def GetImagePath(strCsvFile, key):
    strPath = ''
    dataLst = []
    with open(strCsvFile, 'r', errors='ignore') as f:
        rowLst = list(csv.reader(f))
        for line in rowLst:
            if len(line) >= 14 and 'WORKER' == line[1]:
                dataLst.append(float(line[13]))
    if [] != dataLst:
        plt.figure(figsize=(13,5))
        plt.title('%s  Phy_Seq_1M_2H(MB/s)'%key)
        xLst = [x for x in range(len(dataLst))]
        plt.plot(xLst, dataLst)
        ax = plt.gca()
        ax.xaxis.set_major_locator(plt.MultipleLocator(120))
        plt.xticks(rotation=90)
        ax.yaxis.set_major_locator(plt.MultipleLocator(50))
        if not os.path.exists(strPCIETempDir):
            os.mkdir(strPCIETempDir)
        strPath = os.path.join(strPCIETempDir, '%s_iometer.png'%key)
        plt.savefig(strPath, bbox_inches='tight')
        plt.close()  
    return strPath

def proIometer(curpath, worksheet):
    smartKey = PublicFuc.commonSmartKey
    smartKeyNew = ['F1', 'F2', 'SmartInfo', 'A5-A6']
    imtCol = ['C','B','E','R', 'G','I','K','L','M','P']
    imtKeyLst = ['pc_no', 'Cap', 'qa_err_msg', 'Seq_1M_2H_Iops', 'Seq_1M_2H_MiBps']
    pattern = '.+\\\\Plan1\\\\T-SS_PCIE_M2-C43\\\\IOmeter_Phy_Seq_1M_2H\\\\\d{14}\\\\report.ini$'
    newKey = imtKeyLst+smartKey
    imtDic = {}
    PublicFuc.ReadQaIniData(curpath, pattern, imtDic, newKey, 'IOmeter.bmp', 1)
    newDic = PublicFuc.GetNewIoMeterDic(imtDic, len(imtKeyLst), smartKey, True)
    newKey = imtKeyLst+smartKeyNew
    startLine = 214
    PublicFuc.WriteData(worksheet, startLine, newDic, imtCol, newKey)
    startLine += 2
    imgWidth = 1000
    imgHeight = 320
    for key in newDic:
        for line in newDic[key]:
            strPath = GetImagePath(line[-1], key)
            if '' != strPath:
                img = Image(strPath)
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, 'G%d'%startLine)
                startLine += 1


def ProTxBENCH(curpath, worksheet):
    #配置文件中的键
    cdmKey = ['pc_no', 'Cap','transmission1','transmission2', 'qa_err_msg','read0','write0','read1','write1','read2','write2','read3','write3']
    #excel中键对应填充的列，第一个是编号的列，其他列应与键顺序一一对应
    cdmCol = ['C','B', 'E','G','H', 'R','I','J','K','L','M','N','O','P']
    cdmDic = {}
    pattern = '.+\\\\Plan1\\\\T-SS_PCIE_M2-C31\\\\TXBENCH测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, cdmDic, cdmKey, 'TxBench.bmp')
    #PublicFuc.GetMaxOrMinValueLst(cdmKey, cdmDic)
    startLine = 102
    imgWidth = 510
    imgHeight = 390
    PublicFuc.WriteDataAndImageCommon(worksheet, startLine,startLine+20, cdmDic, cdmCol, cdmKey,imgWidth, imgHeight,8,2)
    #PublicFuc.WriteDataAndImage(worksheet, startLine, cdmDic, cdmCol, cdmKey, imgWidth, imgHeight)

def ProAnvilsStorageUtilities(curpath, worksheet):
    #配置文件中的键
    cdmKey = ['pc_no', 'Cap', 'qa_err_msg']  #获取不到信息，因此只填关键信息，和截图
    #excel中键对应填充的列，第一个是编号的列，其他列应与键顺序一一对应
    cdmCol = ['C','B', 'E', 'AO']
    cdmDic = {}
    pattern = '.+\\\\Plan1\\\\T-SS_PCIE_M2-C32\\\\Anvils Storage Utilities测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, cdmDic, cdmKey, 'AnvilsStorage.bmp')
    #PublicFuc.GetMaxOrMinValueLst(cdmKey, cdmDic)
    startLine = 127
    imgWidth = 700
    imgHeight = 500
    PublicFuc.WriteDataAndImageCommon(worksheet, startLine,startLine+20, cdmDic, cdmCol, cdmKey,imgWidth, imgHeight,10,2)
    #PublicFuc.WriteDataAndImage(worksheet, startLine, cdmDic, cdmCol, cdmKey, imgWidth, imgHeight)

def proAIDA64_Linear_Read(curpath, worksheet):
    dataKey = ['pc_no', 'Cap', 'qa_err_msg','current','min','max','avg','cpu_current','cpu_min','cpu_max','cpu_avg','blocksize']
    dataCol = ['C','B','E','R','G','H','I','J','K','L','M','N','O']
    dataDic = {}

    imgWidth = 700
    imgHeight = 498
    pattern = '.+\\\\Plan1\\\\T-SS_PCIE_M2-C33\\\\AIDA64 Linear Read测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dataDic, dataKey, 'AIDA.bmp', 1)
    startLine = 152
    PublicFuc.WriteDataAndImageCommon(worksheet, startLine,startLine+2, dataDic, dataCol,dataKey,imgWidth, imgHeight,8,2)

def proAIDA64_Linear_Write(curpath, worksheet):
    dataKey = ['pc_no', 'Cap', 'qa_err_msg','current','min','max','avg','cpu_current','cpu_min','cpu_max','cpu_avg','blocksize']
    dataCol = ['C','B','E','R','G','H','I','J','K','L','M','N','O']
    dataDic = {}

    imgWidth = 700
    imgHeight = 498
    pattern = '.+\\\\Plan1\\\\T-SS_PCIE_M2-C34\\\\AIDA64 Linear Write测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dataDic, dataKey, 'AIDA.bmp', 1)
    startLine = 159
    PublicFuc.WriteDataAndImageCommon(worksheet, startLine,startLine+2, dataDic, dataCol, dataKey,imgWidth, imgHeight,8,2)

def proAIDA64_Average_Read(curpath, worksheet):
    dataKey = ['pc_no', 'Cap', 'qa_err_msg','current','min','max','avg','cpu_current','cpu_min','cpu_max','cpu_avg','blocksize']
    dataCol = ['C','B','E','R','G','H','I','J','K','L','M','N','O']
    dataDic = {}

    imgWidth = 700
    imgHeight = 498
    pattern = '.+\\\\Plan1\\\\T-SS_PCIE_M2-C35\\\\AIDA64 Average Read测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dataDic, dataKey, 'AIDA.bmp', 1)
    startLine = 166
    PublicFuc.WriteDataAndImageCommon(worksheet, startLine,startLine+2, dataDic, dataCol, dataKey,imgWidth, imgHeight,8,2)

def proAIDA64_Average_Write(curpath, worksheet):
    dataKey = ['pc_no', 'Cap', 'qa_err_msg','current','min','max','avg','cpu_current','cpu_min','cpu_max','cpu_avg','blocksize']
    dataCol = ['C','B','E','R','G','H','I','J','K','L','M','N','O']
    dataDic = {}

    imgWidth = 700
    imgHeight = 498
    pattern = '.+\\\\Plan1\\\\T-SS_PCIE_M2-C36\\\\AIDA64 Average Write测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dataDic, dataKey, 'AIDA.bmp', 1)
    startLine = 173
    PublicFuc.WriteDataAndImageCommon(worksheet, startLine,startLine+2, dataDic, dataCol, dataKey,imgWidth, imgHeight,8,2)


def proPCMark8(curpath, worksheet):
    dataKey = ['pc_no', 'Cap', 'qa_err_msg','score','bandwidth']
    dataCol = ['C','B','E','K','G','I']
    dataDic = {}

    imgWidth = 450
    imgHeight = 350
    pattern = '.+\\\\Plan1\\\\T-SS_PCIE_M2-C41\\\\PCMark8测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dataDic, dataKey, 'PCMark.bmp', 1)
    startLine = 200
    PublicFuc.WriteDataAndImageCommon(worksheet, startLine,startLine+2, dataDic, dataCol, dataKey,imgWidth, imgHeight,8,2)


def proPCMarkVantage(curpath, worksheet):
    dataKey = ['pc_no', 'Cap', 'qa_err_msg','score']
    dataCol = ['C','B','E','K','G']
    dataDic = {}

    imgWidth = 450
    imgHeight = 350
    pattern = '.+\\\\Plan1\\\\T-SS_PCIE_M2-C42\\\\PCMark Vantage测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dataDic, dataKey, 'PCMarkVantage.bmp', 1)
    startLine = 207
    PublicFuc.WriteDataAndImageCommon(worksheet, startLine,startLine+2, dataDic, dataCol, dataKey,imgWidth, imgHeight,8,2)