import PublicFuc,re,DetailAndConclusion
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors,Font
from datetime import datetime,timedelta

dicData = {}

def Run(curpath, workBook, alignment):
    ws = workBook['测试汇总']
    ws.alignment = alignment
    ProDetailAndConclusionSummary(ws,workBook)
    #ProPfmSummary(ws,workBook)
    ProPCCopyfileSummary(ws,workBook)
    ProRTBurnInSummary(ws,workBook)
    ProPorSummary(ws,workBook)
    ProSporSummary(ws,workBook)
    ProHighBurnInSummary(ws,workBook)
    ProLowBurnInSummary(ws,workBook)
    Properformance_full(ws, workBook)
    Properformance_simple(ws, workBook)

def ProPorSummary(ws,workBook):
    wsTmp = workBook['Power Off Test']
    targetLineNo = 16
    MIN_LINE = 7
    MAX_LINE = 22
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'G',wsTmp)
    #colList = ['H']
    dataList = GetSpecialDataList(MIN_LINE,MAX_LINE,'G',wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['D%d'%targetLineNo] = totalSampleCnt

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt = GetFailCnt(dataList,validResultList)
    ws['F%d'%targetLineNo] = totalFailCnt
    totalPassCnt = totalSampleCnt - totalFailCnt
    ws['E%d'%targetLineNo] = totalPassCnt
    tmpRate = ''
    if totalSampleCnt != 0:
        tmpRate = "%.2f%%" % (float(totalPassCnt)*100/float(totalSampleCnt))
    ws['G%d'%targetLineNo] = tmpRate

def ProSporSummary(ws,workBook):
    wsTmp = workBook['Power Off Test']
    targetLineNo = 17
    MIN_LINE = 27
    MAX_LINE = 42
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'G',wsTmp)
    #colList = ['H']
    dataList = GetSpecialDataList(MIN_LINE,MAX_LINE,'G',wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['D%d'%targetLineNo] = totalSampleCnt

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt = GetFailCnt(dataList,validResultList)
    ws['F%d'%targetLineNo] = totalFailCnt
    totalPassCnt = totalSampleCnt - totalFailCnt
    ws['E%d'%targetLineNo] = totalPassCnt
    tmpRate = ''
    if totalSampleCnt != 0:
        tmpRate = "%.2f%%" % (float(totalPassCnt)*100/float(totalSampleCnt))
    ws['G%d'%targetLineNo] = tmpRate

def ProDetailAndConclusionSummary(ws,workBook):
    totalLow1Cnt = 0
    totalLow1PassCnt = 0

    lowH2_1 = 0
    lowH2_2 = 0
    lowH2_1_Pass = 0
    lowH2_2_Pass = 0
    if DetailAndConclusion.IsFormat1_LowFormat():
        totalLow1Cnt = DetailAndConclusion.GetTotalMP1Cnt()
        totalLow1PassCnt = DetailAndConclusion.GetTotalPassMP1Cnt()
        lowH2_1 = DetailAndConclusion.GetTotalH2_1_Cnt()
        lowH2_1_Pass = DetailAndConclusion.GetTotalPassH2_1_Cnt()
        lowH2_2 = DetailAndConclusion.GetTotalH2_2_Cnt()
        lowH2_2_Pass = DetailAndConclusion.GetTotalPassH2_2_Cnt()

    totalLow2Cnt = 0
    totalLow2PassCnt = 0
    if DetailAndConclusion.IsFormat2_LowFormat():
        totalLow2Cnt = DetailAndConclusion.GetTotalMP2Cnt()
        totalLow2PassCnt = DetailAndConclusion.GetTotalPassMP2Cnt()
        lowH2_1 += DetailAndConclusion.GetTotalH2_3_Cnt()
        lowH2_1_Pass += DetailAndConclusion.GetTotalPassH2_3_Cnt()

    totalLowMpCnt = totalLow1Cnt + totalLow2Cnt
    totalLowMpPassCnt = totalLow1PassCnt + totalLow2PassCnt
    totalLowMpFailCnt = totalLowMpCnt - totalLowMpPassCnt

    lowH2_1_Fail = lowH2_1 - lowH2_1_Pass
    lowH2_2_Fail = lowH2_2 - lowH2_2_Pass
    #数据准备充分，填写前三行
    ws['D8'] = totalLowMpCnt
    ws['E8'] = totalLowMpPassCnt
    ws['F8'] = totalLowMpFailCnt
    tmpRate = ''
    if totalLowMpCnt != 0:
        tmpRate = "%.2f%%" % (float(totalLowMpPassCnt)*100/float(totalLowMpCnt))
    ws['G8'] = tmpRate

    ws['D9'] = lowH2_1
    ws['E9'] = lowH2_1_Pass
    ws['F9'] = lowH2_1_Fail
    tmpRate = ''
    if lowH2_1 != 0:
        tmpRate = "%.2f%%" % (float(lowH2_1_Pass)*100/float(lowH2_1))
    ws['G9'] = tmpRate

    ws['D10'] = lowH2_2
    ws['E10'] = lowH2_2_Pass
    ws['F10'] = lowH2_2_Fail
    tmpRate = ''
    if lowH2_2 != 0:
        tmpRate = "%.2f%%" % (float(lowH2_2_Pass)*100/float(lowH2_2))
    ws['G10'] = tmpRate


    #获取高格数据
    totalHigh1Cnt = 0
    totalHigh1PassCnt = 0

    highH2_1 = 0
    highH2_2 = 0
    highH2_1_Pass = 0
    highH2_2_Pass = 0
    if DetailAndConclusion.IsFormat1_HighFormat():
        totalHigh1Cnt = DetailAndConclusion.GetTotalMP1Cnt()
        totalHigh1PassCnt = DetailAndConclusion.GetTotalPassMP1Cnt()
        highH2_1 = DetailAndConclusion.GetTotalH2_1_Cnt()
        highH2_1_Pass = DetailAndConclusion.GetTotalPassH2_1_Cnt()
        highH2_2 = DetailAndConclusion.GetTotalH2_2_Cnt()
        highH2_2_Pass = DetailAndConclusion.GetTotalPassH2_2_Cnt()

    totalHigh2Cnt = 0
    totalHigh2PassCnt = 0
    if DetailAndConclusion.IsFormat2_HighFormat():
        totalHigh2Cnt = DetailAndConclusion.GetTotalMP2Cnt()
        totalHigh2PassCnt = DetailAndConclusion.GetTotalPassMP2Cnt()
        highH2_1 += DetailAndConclusion.GetTotalH2_3_Cnt()
        highH2_1_Pass += DetailAndConclusion.GetTotalPassH2_3_Cnt()

    totalHighMpCnt = totalHigh1Cnt + totalHigh2Cnt
    totalHighMpPassCnt = totalHigh1PassCnt + totalHigh2PassCnt
    totalHighMpFailCnt = totalHighMpCnt - totalHighMpPassCnt

    highH2_1_Fail = highH2_1 - highH2_1_Pass
    highH2_2_Fail = highH2_2 - highH2_2_Pass
    #数据准备充分，填写4-6行
    ws['D11'] = totalHighMpCnt
    ws['E11'] = totalHighMpPassCnt
    ws['F11'] = totalHighMpFailCnt
    tmpRate = ''
    if totalHighMpCnt != 0:
        tmpRate = "%.2f%%" % (float(totalHighMpPassCnt)*100/float(totalHighMpCnt))
    ws['G11'] = tmpRate

    ws['D12'] = highH2_1
    ws['E12'] = highH2_1_Pass
    ws['F12'] = highH2_1_Fail
    tmpRate = ''
    if highH2_1 != 0:
        tmpRate = "%.2f%%" % (float(highH2_1_Pass)*100/float(highH2_1))
    ws['G12'] = tmpRate

    ws['D13'] = highH2_2
    ws['E13'] = highH2_2_Pass
    ws['F13'] = highH2_2_Fail
    tmpRate = ''
    if highH2_2 != 0:
        tmpRate = "%.2f%%" % (float(highH2_2_Pass)*100/float(highH2_2))
    ws['G13'] = tmpRate

    ws['D14'] = 0
    ws['E14'] = 0
    ws['F14'] = 0

"""     totalH2VerifyCnt = DetailAndConclusion.GetTotalH2_VERIFY_Cnt()
    totalH2VerifyPassCnt = DetailAndConclusion.GetTotalPassH2_VERIFY_Cnt()
    totalH2VerifyFailCnt = totalH2VerifyCnt - totalH2VerifyPassCnt
    ws['D14'] = totalH2VerifyCnt
    ws['E14'] = totalH2VerifyPassCnt
    ws['F14'] = totalH2VerifyFailCnt
    tmpRate = ''
    if totalH2VerifyCnt != 0:
        tmpRate = "%.2f%%" % (float(totalH2VerifyPassCnt)*100/float(totalH2VerifyCnt))
    ws['G14'] = tmpRate """

def ProPfmSummary(ws,workBook):
    wsTmp = workBook['性能测试']
    targetLineNo = 15
    MIN_LINE = 19
    MAX_LINE = 500
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'U',wsTmp)
    #colList = ['H']
    dataList = GetSpecialDataList(MIN_LINE,MAX_LINE,'U',wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['D%d'%targetLineNo] = totalSampleCnt

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt = GetFailCnt(dataList,validResultList)
    ws['F%d'%targetLineNo] = totalFailCnt
    totalPassCnt = totalSampleCnt - totalFailCnt
    ws['E%d'%targetLineNo] = totalPassCnt
    tmpRate = ''
    if totalSampleCnt != 0:
        tmpRate = "%.2f%%" % (float(totalPassCnt)*100/float(totalSampleCnt))
    ws['G%d'%targetLineNo] = tmpRate

def ProPCCopyfileSummary(ws,workBook):
    wsTmp = workBook['PC端文件拷贝与比对测试']
    targetLineNo = 15
    MIN_LINE = 18
    MAX_LINE = 500
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'H',wsTmp)
    #colList = ['H']
    dataList = GetSpecialDataList(MIN_LINE,MAX_LINE,'H',wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['D%d'%targetLineNo] = totalSampleCnt

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt = GetFailCnt(dataList,validResultList)
    ws['F%d'%targetLineNo] = totalFailCnt
    totalPassCnt = totalSampleCnt - totalFailCnt
    ws['E%d'%targetLineNo] = totalPassCnt
    tmpRate = ''
    if totalSampleCnt != 0:
        tmpRate = "%.2f%%" % (float(totalPassCnt)*100/float(totalSampleCnt))
    ws['G%d'%targetLineNo] = tmpRate

def Properformance_full(ws,workBook):
    cnt = 0
    wsTmp = workBook['性能-Full']
    targetLineNo = 24
    MIN_LINE = 6
    MAX_LINE = 16
    for item in range(MIN_LINE,MAX_LINE,2):
        celPosSample = '%s%d'%(get_column_letter(item), 3)
        celValue = wsTmp[celPosSample].value
        if celValue != '' and celValue != None:
            cnt += 1
 
    if cnt < 1:
        return

    ws['D%d'%targetLineNo] = cnt

def Properformance_simple(ws,workBook):
    cnt = 0
    wsTmp = workBook['性能-Simple']
    targetLineNo = 23
    MIN_LINE = 6
    MAX_LINE = 16
    for item in range(MIN_LINE,MAX_LINE,2):
        celPosSample = '%s%d'%(get_column_letter(item), 3)
        celValue = wsTmp[celPosSample].value
        if celValue != '' and celValue != None:
            cnt += 1
 
    if cnt < 1:
        return

    ws['D%d'%targetLineNo] = cnt

def ProRTBurnInSummary(ws,workBook):
    wsTmp = workBook['BurnInTest']
    targetLineNo = 19
    MIN_LINE = 17
    MAX_LINE = 32
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    #colList = ['H']
    dataList = GetSpecialDataList(MIN_LINE,MAX_LINE,'K',wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['D%d'%targetLineNo] = totalSampleCnt

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt = GetFailCnt(dataList,validResultList)
    ws['F%d'%targetLineNo] = totalFailCnt
    totalPassCnt = totalSampleCnt - totalFailCnt
    ws['E%d'%targetLineNo] = totalPassCnt
    tmpRate = ''
    if totalSampleCnt != 0:
        tmpRate = "%.2f%%" % (float(totalPassCnt)*100/float(totalSampleCnt))
    ws['G%d'%targetLineNo] = tmpRate

def ProHighBurnInSummary(ws,workBook):
    wsTmp = workBook['BurnInTest']
    targetLineNo = 20
    MIN_LINE = 36
    MAX_LINE = 51
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    #colList = ['H']
    dataList = GetSpecialDataList(MIN_LINE,MAX_LINE,'K',wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['D%d'%targetLineNo] = totalSampleCnt

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt = GetFailCnt(dataList,validResultList)
    ws['F%d'%targetLineNo] = totalFailCnt
    totalPassCnt = totalSampleCnt - totalFailCnt
    ws['E%d'%targetLineNo] = totalPassCnt
    tmpRate = ''
    if totalSampleCnt != 0:
        tmpRate = "%.2f%%" % (float(totalPassCnt)*100/float(totalSampleCnt))
    ws['G%d'%targetLineNo] = tmpRate

def ProLowBurnInSummary(ws,workBook):
    wsTmp = workBook['BurnInTest']
    targetLineNo = 21
    MIN_LINE = 55
    MAX_LINE = 70
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    #colList = ['H']
    dataList = GetSpecialDataList(MIN_LINE,MAX_LINE,'K',wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['D%d'%targetLineNo] = totalSampleCnt

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt = GetFailCnt(dataList,validResultList)
    ws['F%d'%targetLineNo] = totalFailCnt
    totalPassCnt = totalSampleCnt - totalFailCnt
    ws['E%d'%targetLineNo] = totalPassCnt
    tmpRate = ''
    if totalSampleCnt != 0:
        tmpRate = "%.2f%%" % (float(totalPassCnt)*100/float(totalSampleCnt))
    ws['G%d'%targetLineNo] = tmpRate



def ProDataPfmMarsH2Summary(ws,workBook):
    wsTmp = workBook['Performance-Full']
    targetLineNo = 19
    MIN_LINE = 37
    MAX_LINE = 41
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'
    ws['K%d'%targetLineNo] = 'NA'

def ProDataPfmThirdpartH2Summary(ws,workBook):
    wsTmp = workBook['Performance-Full']
    targetLineNo = 20
    MIN_LINE = 62
    MAX_LINE = 66
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)

    wsTmp_simple = workBook['Performance-Simple']
    MIN_LINE = 33
    MAX_LINE = 37
    totalSampleCnt_simple = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp_simple)

    totalSampleCnt += totalSampleCnt_simple

    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'
    ws['K%d'%targetLineNo] = 'NA'

def ProDataPfmCDMSummary(ws,workBook):
    wsTmp = workBook['Performance-Full']
    targetLineNo = 21
    MIN_LINE = 50
    MAX_LINE = 54
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)

    wsTmp_simple = workBook['Performance-Simple']
    MIN_LINE = 21
    MAX_LINE = 25
    totalSampleCnt_simple = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp_simple)

    totalSampleCnt += totalSampleCnt_simple
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'
    ws['K%d'%targetLineNo] = 'NA'

def ProDataPfmHDTuneSummary(ws,workBook):
    wsTmp = workBook['Performance-Full']
    targetLineNo = 22
    MIN_LINE = 89
    MAX_LINE = 93
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'
    ws['K%d'%targetLineNo] = 'NA'

def ProDataPfmATTOSummary(ws,workBook):
    wsTmp = workBook['Performance-Full']
    targetLineNo = 23
    MIN_LINE = 103
    MAX_LINE = 107
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'
    ws['K%d'%targetLineNo] = 'NA'

def ProDataPfmHDBenchSummary(ws,workBook):
    wsTmp = workBook['Performance-Full']
    targetLineNo = 24
    MIN_LINE = 115
    MAX_LINE = 119
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'
    ws['K%d'%targetLineNo] = 'NA'

def ProDataPfmIometerSummary(ws,workBook):
    wsTmp = workBook['Performance-Full']
    targetLineNo = 25
    MIN_LINE = 75
    MAX_LINE = 79
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'C',wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'
    ws['K%d'%targetLineNo] = 'NA'

def ProDataFunctionCopyFileSummary(ws,workBook):
    wsTmp = workBook['MP_Function']
    targetLineNo = 18
    MIN_LINE = 16
    MAX_LINE = 500
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'L',wsTmp)
    colList = ['B','L']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

def ProDataSporSummary(ws,workBook):
    wsTmp = workBook['Power Off Test']
    targetLineNo = 27
    MIN_LINE = 43
    MAX_LINE = 74
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','H','L','N','Q','U','Y','AL']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

def ProDataPorSummary(ws,workBook):
    wsTmp = workBook['Power Off Test']
    targetLineNo = 28
    MIN_LINE = 7
    MAX_LINE = 38
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','H','L','N','Q','U','Y','AL']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

#返回失败的数量，失败的汇总信息,可能不包含样本编号。
def GetFailCnt(dataList,validResultList):
    failCnt = 0
    for line in dataList:
        strResult = line
        if strResult == None or (strResult.upper() not in validResultList):
            failCnt += 1
    return failCnt

#返回失败的数量，失败的汇总信息,默认第一个是样本编号，第二个是测试结果
def GetFailInfo(dataList,validResultList):
    failCnt = 0
    failInfo = ''
    for line in dataList:
        for idx in range(1,len(line)):
            strResult = line[idx]
            if strResult == None or (strResult.upper() not in validResultList):
                failCnt += 1
                if strResult == None:
                    strResult = ''
                strTempErrInfo = '[' + str(line[0]) + ']' + strResult
                failInfo += strTempErrInfo + ','
                break #只记录最先遇到的错误
    failInfo = failInfo[:-1]
    return failCnt,failInfo

#返回量产失败的数量，失败的汇总信息,默认第一个是样本编号，第二个是测试结果（因为量产结果列要么填的是容量要么填的是错误码，比较特殊。单独写函数处理）
def GetFailInfoByRex(dataList,rexExpression):
    failCnt = 0
    failInfo = ''
    for line in dataList:
        for idx in range(1,len(line)):
            strResult = line[idx]
            if strResult == None or (not re.match(rexExpression,strResult)):
                failCnt += 1
                if strResult == None:
                    strResult = ''
                strTempErrInfo = '[' + str(line[0]) + ']' + strResult
                failInfo += strTempErrInfo + ','
                break #只记录最先遇到的错误
    failInfo = failInfo[:-1]
    return failCnt,failInfo

def ProDataCoverageSummary(ws,workBook):
    wsTmp = workBook['Data coverage']

    targetLineNo = 43
    MIN_LINE = 5
    MAX_LINE = 12
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','H']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

def ProEnvironmentDataRentionSummary(ws,workBook):
    wsTmp = workBook['Environment Test']

    targetLineNo = 36
    MIN_LINE = 7
    MAX_LINE = 46
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','G','J']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

def ProEnvironmentReadDisturbSummary(ws,workBook):
    wsTmp = workBook['Environment Test']

    targetLineNo = 37
    MIN_LINE = 51
    MAX_LINE = 66
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','I']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo


def ProEmptyChunkSummary(ws,workBook):
    wsTmp = workBook['Empty chunk']

    targetLineNo = 34
    MIN_LINE = 7
    MAX_LINE = 38
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','H','J','N','Q']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

def GetSpecialDataCnt(_startRow,_endRow,_colName,ws):
    cnt = 0
    if _startRow > _endRow:
        return 0
    for rowNo in range(_startRow,_endRow+1):
        celPosSample = '%s%d'%(_colName, rowNo)
        celValue = ws[celPosSample].value
        if celValue != '' and celValue != None:
            cnt += 1
    return cnt



#BIT测试数据汇总包括：RT
def ProBurinRTSummary(ws,workBook):
    wsTmp = workBook['BurnIn']

    #获取RT BIT汇总数据
    targetLineNo = 29
    MIN_LINE = 7
    MAX_LINE = 38
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','G','J','L','P','S','V','AI']
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

#BIT测试数据汇总包括：HT
def ProBurinHTSummary(ws,workBook):
    wsTmp = workBook['BurnIn']

    #获取HT BIT汇总数据
    targetLineNo = 30
    MIN_LINE = 43
    MAX_LINE = 58
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','G','K','N','S']#,'AF' 未填写，暂时不适用
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo

#BIT测试数据汇总包括：LT
def ProBurinLTSummary(ws,workBook):
    wsTmp = workBook['BurnIn']

    #获取HT BIT汇总数据
    targetLineNo = 31
    MIN_LINE = 63
    MAX_LINE = 78
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE,MAX_LINE,'B',wsTmp)
    colList = ['B','G','K','N','S'] #,'AF' 未填写，暂时不适用
    dataList = GetSpecialMultiDataList(MIN_LINE,MAX_LINE,colList,wsTmp)
 
    if totalSampleCnt < 1:
        return

    ws['H%d'%targetLineNo] = totalSampleCnt
    ws['J%d'%targetLineNo] = '完成'

    totalFailCnt = 0
    failInfo = ''
    validResultList = ['PASS','TRUE']
    totalFailCnt,failInfo = GetFailInfo(dataList,validResultList)
    ws['I%d'%targetLineNo] = totalFailCnt
    if totalFailCnt <= 0:
        ws['K%d'%targetLineNo] = 'Pass'
        return

    ws['K%d'%targetLineNo] = 'Fail'
    ws['L%d'%targetLineNo] = failInfo


#获取指定范围的数据内容，结果数据是列表形式
def GetSpecialDataList(_startRow,_endRow,_colName,ws):
    dic = []
    if _startRow > _endRow:
        return dic
    for rowNo in range(_startRow,_endRow+1):
        celPosSample = '%s%d'%(_colName, rowNo)
        celValue = ws[celPosSample].value
        if celValue != '' and celValue != None:
            dic.append(celValue)
    return dic


#获取最长测试时间
def GetMaxTime(dic):
    maxTimeInSeconds = 0
    for data in dic:
        timestr = data
        if timestr != '' and timestr != None:
            if timestr.find(':') != -1:
                #00:00:00
                timedata = timestr.split(':')
                totalSecond = int(timedata[0])*3600 + int(timedata[1])*60 + int(timedata[2])
                if totalSecond > maxTimeInSeconds:
                    maxTimeInSeconds = totalSecond
            else:
                #000h 03m 07s
                timedata = timestr.split(' ')
                hour = timedata[0][0:-1]
                minutes = timedata[1][0:-1]
                seconds = timedata[2][0:-1]
                totalSecond = int(hour)*3600 + int(minutes)*60 + int(seconds)
                if totalSecond > maxTimeInSeconds:
                    maxTimeInSeconds = totalSecond

    if maxTimeInSeconds == 0:
        return ''

    hour = int(maxTimeInSeconds/3600)
    lefSeconds = maxTimeInSeconds%3600
    minutes = int(lefSeconds/60)
    seconds = lefSeconds%60
    strTime = '%d:%d:%d'%(hour,minutes,seconds)
    return strTime

#获取指定范围的数据内容，结果数据是列表形式,且只加入有效的数据
def GetSpecialMultiDataList(_startRow,_endRow,_colNameList,ws):
    dic = []
    if _startRow > _endRow:
        return dic
    for rowNo in range(_startRow,_endRow+1):
        oneRow = []
        for _colName in _colNameList:
            celPosSample = '%s%d'%(_colName, rowNo)
            celValue = ws[celPosSample].value
            oneRow.append(celValue)

        if IsValidData(oneRow):
            dic.append(oneRow)
    return dic

#判定数据是否为无效数据
def IsValidData(dataLine):
    if len(dataLine) < 2:
        return False
    
    isValid = False
    for idx in range(1,len(dataLine)):
        if dataLine[idx] != None:
            isValid = True

    return isValid
  
#获取最长测试时间
def GetCombinedMaxTime(dic):
    maxTimeInSeconds = 0
    for data in dic:
        if len(data) == 1:
            #简单时间信息
            timestr = data
            if timestr != '' and timestr != None:
                if timestr.find(':') != -1:
                    #00:00:00
                    timedata = timestr.split(':')
                    totalSecond = int(timedata[0])*3600 + int(timedata[1])*60 + int(timedata[2])
                    if totalSecond > maxTimeInSeconds:
                        maxTimeInSeconds = totalSecond
                else:
                    #000h 03m 07s
                    timedata = timestr.split(' ')
                    hour = timedata[0][0:-1]
                    minutes = timedata[1][0:-1]
                    seconds = timedata[2][0:-1]
                    totalSecond = int(hour)*3600 + int(minutes)*60 + int(seconds)
                    if totalSecond > maxTimeInSeconds:
                        maxTimeInSeconds = totalSecond
        else:
            oneRowTimeList = data
            perRowTotalTime = 0
            for perTime in oneRowTimeList:
                if perTime != '' and perTime != None:
                    timestr = perTime
                    if timestr.find(':') != -1:
                        #00:00:00
                        timedata = timestr.split(':')
                        totalSecond = int(timedata[0])*3600 + int(timedata[1])*60 + int(timedata[2])
                        perRowTotalTime += totalSecond
                    else:
                        #000h 03m 07s
                        timedata = timestr.split(' ')
                        hour = timedata[0][0:-1]
                        minutes = timedata[1][0:-1]
                        seconds = timedata[2][0:-1]
                        totalSecond = int(hour)*3600 + int(minutes)*60 + int(seconds)
                        perRowTotalTime += totalSecond
            
            if perRowTotalTime > maxTimeInSeconds:
                            maxTimeInSeconds = perRowTotalTime


    if maxTimeInSeconds == 0:
        return ''

    hour = int(maxTimeInSeconds/3600)
    lefSeconds = maxTimeInSeconds%3600
    minutes = int(lefSeconds/60)
    seconds = lefSeconds%60
    strTime = '%d:%d:%d'%(hour,minutes,seconds)
    return strTime

#从字符中直接解析出时间的值
def GetTimeValue(dic):
    timeValue = 0
    endTimeStr = dic[2]
    startTimeStr = dic[1]
    if '' == endTimeStr or '' == startTimeStr:
        timeValue = 0
    else:
        endtime = datetime.strptime(endTimeStr, '%Y-%m-%d %H:%M:%S')
        starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
        totalSecond = timedelta.total_seconds(endtime-starttime)
        timeValue = totalSecond
    return timeValue
         

#获取开始和结束的时间
def GetTotalTimeStr():
    #测试时间
    strTime = ''
    totalTimeValue = 0
    for key in dicData:
        childDic = dicData[key]
        if 'MPTOOL' not in childDic:
            continue
        dic = childDic['MPTOOL']
        totalTimeValue += GetTimeValue(dic)

    totalSecond = int(totalTimeValue)
    hour = int(totalSecond/3600)
    lefSeconds = totalSecond%3600
    minutes = int(lefSeconds/60)
    seconds = lefSeconds%60
    strTime = '%d:%d:%d'%(hour,minutes,seconds)
        
    return strTime

   


def DrawTable(ws):
    totalRowCnt = len(dicData)
    STARTLINE = 13
    #serialNo = 1
    for rowNo in range(STARTLINE,STARTLINE+totalRowCnt):
        for ColNo in range(1,1+5):
            ws['%s%d'%(get_column_letter(ColNo), rowNo)].alignment = PublicFuc.alignment
            ws['%s%d'%(get_column_letter(ColNo), rowNo)].border = PublicFuc.my_border('thin', 'thin', 'thin', 'thin')
    
    #合并单元格
    if totalRowCnt > 0:
        ws['%s%d'%(get_column_letter(1), STARTLINE)] = '48H高格'
        ws.merge_cells(start_row=STARTLINE, start_column=1, end_row=STARTLINE+totalRowCnt-1, end_column=1)
