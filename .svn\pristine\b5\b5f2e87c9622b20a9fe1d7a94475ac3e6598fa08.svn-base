import os,sys,logging,traceback,time,json,MTT,MTT_PV_Report,Mars_hr,BLX_hr,xu4_tbw_um,xu4_sw_speed,xu4_longtime_rw_sw,terminalreport,xu4_cmdq

curpath = os.path.split(sys.argv[0])[0]
reportPath = curpath
bRmsRun = False
argInfo = ''

productType = '' #分EMMC和IND_EMMC
productRev = '' #分0 - alpha 和 1 - release

if len(sys.argv) > 3:
    #Rms调用报告统计，需要有个独占文件，防止多人同时合并报告
    reportPath = sys.argv[1]
    productType = sys.argv[2]
    productRev = sys.argv[3]
    for i in range(len(sys.argv)):
        argInfo += sys.argv[i]
        argInfo += "\n"

#reportPath = r'\\172.18.2.249\00文件中转-每周日删除\Ace\YSXU4_MP080116_Alpha00_Alpha_4_32GB_20250328'
#productType = 'EMMC'
#productRev = 1

curtime = time.strftime("%Y%m%d%H%M%S", time.localtime())
logname = os.path.join(reportPath, 'EMMC_Report_' + curtime + '.log')
#日志配置
logging.basicConfig(filename = logname,
                    filemode='w',
                    format = '%(asctime)s-%(name)s-%(levelname)s-%(module)s: %(message)s',
                    datefmt = '%Y-%m-%d %H:%M:%S %p',
                    level = logging.INFO)


try:
    import shutil,tempfile
    import requests
    import openpyxl
    from openpyxl.styles import Alignment
    import PublicFuc,XU4Merger,SummaryInfo
    import urllib.parse

    if len(sys.argv) > 1:
        testNo = os.path.split(reportPath)[1]
        enTestNo = urllib.parse.quote(testNo)
        onlyPath = os.path.join(reportPath, 'public.txt')
        jsonData = {}
        jsonData['token'] = 'd7faec554b2d83ff9592d5bb507e12b1d9915f21a29490638f3a735117ba6076'
        jsonData['secret'] = 'SEC4afd2f150ce4a5808c2a6337d59de4bc098c0a37f2140d4ea741519ef75e09da'
        jsonData['atLst'] = '18312006726'
        rmsUrl = 'http://ereport.yeestor.com/sendDingTalkMsg' 
        bRmsRun = True

    logging.info('程序开始运行！')
    logging.info(argInfo)
    PublicFuc.GetAllFile(reportPath)
    wb = None
    alignment = Alignment(horizontal='center',vertical='center')

    strTempDirRoot = os.path.join(tempfile.gettempdir(), 'mtt_emmc_report_temp')
    if not os.path.exists(strTempDirRoot):
        os.mkdir(strTempDirRoot)
    PublicFuc.strTempDir = strTempDirRoot


    #MTT.Run(reportPath, wb, alignment) 去掉原来旧的
    BLX_hr.Run(reportPath)
    if bRmsRun:
        XU4rtemplateFile = os.path.join(curpath, 'XU4_template.xlsx')
        wb = openpyxl.load_workbook(filename = XU4rtemplateFile)
        alignment = Alignment(horizontal='center',vertical='center') 
        XU4Merger.Run(wb, alignment, testNo)
        SummaryInfo.WriteSummaryInfo(wb, alignment, testNo)
        try:
            MTT_PV_Report.Run(curpath,reportPath, wb, alignment,bRmsRun)
            xu4_cmdq.Run(reportPath, wb)
            xu4_tbw_um.Run(reportPath, wb)
            xu4_sw_speed.Run(reportPath, wb)
            xu4_longtime_rw_sw.Run(reportPath, wb)
        except:
            print(traceback.format_exc())
            logging.error(traceback.format_exc())
        XU4resultFileName = 'XU4汇总报告.xlsx'
        XU4resultFile = os.path.join(reportPath, XU4resultFileName)
        wb.save(XU4resultFile)
    if bRmsRun:
        jsonData['type'] = 'link'
        jsonData['title'] = 'eReport报告合并完成通知'
        jsonData['text'] = '测试单号：%s \r\n报告合并成功，请前往查看！'%testNo
        jsonData['url'] = 'http://ereport.yeestor.com/eb/report/download?nid=%s&key=%s&type=%s'%(productType,enTestNo,productRev)
        response = requests.request('POST', rmsUrl, data=jsonData)
        if os.path.exists(onlyPath):
            os.remove(onlyPath)
        #工单系统回调
        strUrl = 'http://ereport.yeestor.com/report/file_download/?product=%s&testNo=%s&type=%s&file='%(productType,enTestNo,productRev)
        woDic = {}
        woDic['orderNo'] = testNo
        woDic['reportInfoList'] = []
        if os.path.exists(XU4resultFile):
            tempDic = {}
            tempDic['name'] = XU4resultFileName
            tempDic['type'] = 'report'
            tempDic['url'] = strUrl+XU4resultFileName
            woDic['reportInfoList'].append(tempDic)

        #补充HR报告到列表中
        HRResultFileName = 'BLX_HR数据_check汇总.xlsx'
        HRResultFile = os.path.join(reportPath, HRResultFileName)
        if os.path.exists(HRResultFile):
            tempDic = {}
            tempDic['name'] = HRResultFileName
            tempDic['type'] = 'report'
            tempDic['url'] = strUrl+HRResultFileName
            woDic['reportInfoList'].append(tempDic)
        woUrl = "http://ebuildin.yeestor.com:8789/qa/reportReply" #http://gateway.yeestor.com:8789/wo/report/status
        headers = {'Content-Type': 'application/json'}
        querystring = {"testNo":woDic['orderNo']}
        requests.request("POST", woUrl, headers=headers, json=woDic['reportInfoList'],params=querystring)
    logging.info('结束！')

except:
    print(traceback.format_exc())
    logging.error(traceback.format_exc())
    if bRmsRun:
        if os.path.exists(onlyPath):
            os.remove(onlyPath)
        jsonData['type'] = 'text'
        jsonData['text'] = 'eReport报告合并异常！@18312006726\r\n测试单号：%s'%testNo
        response = requests.request('POST', rmsUrl, data=jsonData)
        



