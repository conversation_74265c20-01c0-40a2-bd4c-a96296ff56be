import PublicFuc
from openpyxl.utils import get_column_letter
import configparser
import csv,time
import os,re
from openpyxl.utils import get_column_letter,column_index_from_string
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors
from datetime import datetime,timedelta

dicPlan22 = {}
dicPlan4 = {}
dicPlan5 = {}
dicPlan6 = {}

h2_column_name_map = {} #DUT的h2测试结果列映射关系

def Run(curpath, workBook, alignment):
    ws = workBook['Function']
    ws.alignment = alignment
    ProData(curpath, ws)
    FillData(ws)
    PublicFuc.WriteReportTime(ws,'E',2)
    PublicFuc.WriteReportOperator(ws,'I',2)

def ProData(curpath, worksheet):
    #得到plan22的数据
    pattern = '.+\\\\Plan22\\\\T_GE_SD_C7\\\\H2_1\\\\.+.csv$'
    rawH2FirstDataList = [] #第一次H2原始数据
    ReadRawCsvData(curpath,pattern,rawH2FirstDataList)
    InitH2CsvColumnNameMap(h2_column_name_map)
    InitH2RawDataMap(dicPlan22,rawH2FirstDataList)
    pattern = '.+\\\\Plan22\\\\T_GE_SD_C8\\\\Mars_文件拷贝\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    #PublicFuc.ReadMarsIniData(curpath, pattern, dicPlan22, 'AT_H2', 'MH2_1', marsH2Key, 'Mars.bmp',0)
    marsCopyKey = ['Cap','MMS_PC','TEST_RESULT','START_TIME','END_TIME']
    ReadMarsIniDataLocal(curpath, pattern, dicPlan22, 'AT_CopyFile', 'MARS_COPYFILE', marsCopyKey, '',0)

    #得到plan4的数据
    pattern = '.+\\\\Plan4\\\\T_GE_SD_C7\\\\Mars_H2_1_1st\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    marsCopyKey = ['Cap','MMS_PC','AVERAGE_WRITE_VEL','AVERAGE_READ_VEL','START_TIME','END_TIME','TEST_RESULT']
    ReadMarsIniDataLocal(curpath, pattern, dicPlan4, 'AT_H2', 'H2_1', marsCopyKey, '',0)

    pattern = '.+\\\\Plan4\\\\T_GE_SD_C8\\\\AT_CopyFile\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    marsCopyKey = ['Cap','MMS_PC','TEST_RESULT','START_TIME','END_TIME']
    ReadMarsIniDataLocal(curpath, pattern, dicPlan4, 'AT_CopyFile', 'MARS_COPYFILE', marsCopyKey, '',0)

    
    #得到plan5的数据
    pattern = '.+\\\\Plan5\\\\T_GE_SD_C7\\\\Mars_H2_1_1st\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    marsCopyKey = ['Cap','MMS_PC','AVERAGE_WRITE_VEL','AVERAGE_READ_VEL','START_TIME','END_TIME','TEST_RESULT']
    ReadMarsIniDataLocal(curpath, pattern, dicPlan5, 'AT_H2', 'H2_1', marsCopyKey, '',0)

    pattern = '.+\\\\Plan5\\\\T_GE_SD_C8\\\\AT_CopyFile\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    marsCopyKey = ['Cap','MMS_PC','TEST_RESULT','START_TIME','END_TIME']
    ReadMarsIniDataLocal(curpath, pattern, dicPlan5, 'AT_CopyFile', 'MARS_COPYFILE', marsCopyKey, '',0)

    #得到plan6的数据
    pattern = '.+\\\\Plan6\\\\T_GE_SD_C7\\\\Mars_H2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    marsCopyKey = ['Cap','MMS_PC','AVERAGE_WRITE_VEL','AVERAGE_READ_VEL','START_TIME','END_TIME','TEST_RESULT']
    ReadMarsIniDataLocal(curpath, pattern, dicPlan6, 'AT_H2', 'H2_1', marsCopyKey, '',0)

    pattern = '.+\\\\Plan6\\\\T_GE_SD_C8\\\\AT_CopyFile\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    marsCopyKey = ['Cap','MMS_PC','TEST_RESULT','START_TIME','END_TIME']
    ReadMarsIniDataLocal(curpath, pattern, dicPlan6, 'AT_CopyFile', 'MARS_COPYFILE', marsCopyKey, '',0)


def FillData(worksheet):
    DrawTable(worksheet)
    startLine = 12
    h2colLst = ['B','C','D','E','F','G','H']
    copyfilecolLst = ['B','C','D','I','J']
    WriteDataDUTH2(worksheet, startLine,dicPlan22,'H2_1', h2colLst)
    WriteDataMarsCopyFile(worksheet, startLine,dicPlan22,'MARS_COPYFILE', copyfilecolLst)

    startLine+= len(dicPlan22)
    WriteDataMarsH2(worksheet, startLine,dicPlan4,'H2_1', h2colLst)
    WriteDataMarsCopyFile(worksheet, startLine,dicPlan4,'MARS_COPYFILE', copyfilecolLst)

    startLine+= len(dicPlan4)
    WriteDataMarsH2(worksheet, startLine,dicPlan5,'H2_1', h2colLst)
    WriteDataMarsCopyFile(worksheet, startLine,dicPlan5,'MARS_COPYFILE', copyfilecolLst)

    startLine+= len(dicPlan5)
    WriteDataMarsH2(worksheet, startLine,dicPlan6,'H2_1', h2colLst)
    WriteDataMarsCopyFile(worksheet, startLine,dicPlan6,'MARS_COPYFILE', copyfilecolLst)

    WriteDataConclusion(worksheet)

def WriteDataConclusion(worksheet):
    STARTLINE = 12
    totalRowCnt = len(dicPlan22) + len(dicPlan4) + len(dicPlan5) + len(dicPlan6)
    for rowNo in range(STARTLINE,STARTLINE+totalRowCnt):
        h2Result = worksheet['%s%d'%('H', rowNo)].value
        copyfileResult = worksheet['%s%d'%('I', rowNo)].value
        if copyfileResult == None or h2Result == None:
            worksheet['%s%d'%('S', rowNo)] = 'FAIL'
            worksheet['%s%d'%('S', rowNo)].fill = PublicFuc.warnFill
        else:
            if h2Result.upper() == 'PASS' and copyfileResult.upper() == 'PASS':
                worksheet['%s%d'%('S', rowNo)] = 'PASS'
            else:
                worksheet['%s%d'%('S', rowNo)] = 'FAIL'
                worksheet['%s%d'%('S', rowNo)].fill = PublicFuc.warnFill

def GetDUTH2ResultStr(line):
    str = 'PASS'
    if len(line) < 6:
        str = 'FAIL'
        return str

    #写速度
    wspeed = float(line[2])
    rspeed = float(line[3])
    rawResult = line[5].upper()
    rawResult = rawResult.strip()
    if rawResult == 'PASS' or rawResult == 'TRUE':
        #进一步判定是否有速度超标
        if wspeed < 10 and rspeed < 50:
            str = "speed err"
        elif wspeed < 10 and rspeed >= 50:
            str = "write speed err"
        elif wspeed >= 10 and rspeed < 50:
            str = "read speed err"
        else:
            str = 'PASS'
        return str

    else:
        #原样打印测试工具报告中的错误描述
        str = line[5]

    return str


def GetMarsH2ResultStr(line):
    str = 'PASS'
    if len(line) < 7:
        str = 'FAIL'
        return str

    #写速度
    WspeedStr = line[2]
    RspeedStr = line[3]
    wspeed = 0
    if WspeedStr != '':
        wspeed = float(line[2])
    rspeed = 0
    if RspeedStr != '':
        rspeed = float(line[3])
    
    rawResult = line[6].upper()
    rawResult = rawResult.strip()
    if rawResult == 'PASS' or rawResult == 'TRUE':
        #进一步判定是否有速度超标
        if wspeed < 10 and rspeed < 50:
            str = "speed err"
        elif wspeed < 10 and rspeed >= 50:
            str = "write speed err"
        elif wspeed >= 10 and rspeed < 50:
            str = "read speed err"
        else:
            str = 'PASS'
        return str
    elif rawResult == '':
        str = 'FAIL'
    else:
        #原样打印测试工具报告中的错误描述
        str = line[6]

    return str
    



#写一个dic
def WriteDataDUTH2(worksheet, startLine,dataDic, caseKey, colLst):
    curLine = startLine
    keySortLst = sorted(dataDic.keys(), reverse=False)
    unitLst = ['M']
    for key in keySortLst:
        if caseKey not in dataDic[key]:
            curLine += 1
            continue
        line = dataDic[key][caseKey]
        for index,col in enumerate(colLst):
            if 0 == index:
                worksheet['%s%d'%(col, curLine)] = key
            else:
                if index == 1:
                    #去除单位
                    value = line[index-1].upper()
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    worksheet['%s%d'%(col, curLine)] = value
                elif index == 6:
                    #记录测试结果描述
                    resultStr = GetDUTH2ResultStr(line)
                    worksheet['%s%d'%(col, curLine)]= resultStr
                    if resultStr != 'PASS':
                        worksheet['%s%d'%(col, curLine)].fill = PublicFuc.warnFill
                    
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
        curLine += 1

#写一个dic
def WriteDataMarsH2(worksheet, startLine,dataDic, caseKey, colLst):
    curLine = startLine
    keySortLst = sorted(dataDic.keys(), reverse=False)
    unitLst = ['M']
    for key in keySortLst:
        if caseKey not in dataDic[key]:
            curLine += 1
            continue
        line = dataDic[key][caseKey]
        for index,col in enumerate(colLst):
            if 0 == index:
                worksheet['%s%d'%(col, curLine)] = key
            else:
                if index == 1:
                    #去除单位
                    value = line[index-1].upper()
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    worksheet['%s%d'%(col, curLine)] = int(float(value)*1024) #转换为M
                elif index == 6:
                    #记录测试结果描述
                    resultStr = GetMarsH2ResultStr(line)
                    worksheet['%s%d'%(col, curLine)]= resultStr
                    if resultStr != 'PASS':
                        worksheet['%s%d'%(col, curLine)].fill = PublicFuc.warnFill
                elif index == 5:
                    #记录的起始时间，需要将时间进行转换
                    startTimeStr = line[index-1]
                    endtimeStr = line[index]
                    if '' == startTimeStr or '' == endtimeStr:
                        worksheet['%s%d'%(col, curLine)] = ''
                    else:
                        try:
                            endtime = datetime.strptime(endtimeStr, '%Y-%m-%d %H:%M:%S')
                            starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
                            totalSecond = timedelta.total_seconds(endtime-starttime)
                            totalSecond = int(totalSecond)
                            hour = int(totalSecond/3600)
                            lefSeconds = totalSecond%3600
                            minutes = int(lefSeconds/60)
                            seconds = lefSeconds%60
                            timeStr = '%d:%d:%d'%(hour,minutes,seconds)
                            worksheet['%s%d'%(col, curLine)] = timeStr
                        except:
                            worksheet['%s%d'%(col, curLine)] = ''
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
        curLine += 1

#写一个dic
def WriteDataMarsCopyFile(worksheet, startLine,dataDic, caseKey, colLst):
    curLine = startLine
    keySortLst = sorted(dataDic.keys(), reverse=False)
    for key in keySortLst:
        if caseKey not in dataDic[key]:
            curLine += 1
            continue
        line = dataDic[key][caseKey]
        for index,col in enumerate(colLst):
            if 0 == index:
                worksheet['%s%d'%(col, curLine)] = key
            elif 1 == index:
                if worksheet['%s%d'%(col, curLine)].value == None or worksheet['%s%d'%(col, curLine)].value == '':
                    worksheet['%s%d'%(col, curLine)] = int(float(line[index-1])*1024)
            elif 3== index:
                #汇总结果
                result = line[index-1]
                result = result.strip().upper()
                if result == 'PASS' or result == 'TRUE':
                    result = 'PASS'
                else:
                    result = line[index-1]
                worksheet['%s%d'%(col, curLine)] = result
                if result != 'PASS':
                    worksheet['%s%d'%(col, curLine)].fill = PublicFuc.warnFill
            elif 4==index:
                #计算时间后直接break，因为5是结束时间，不用再填
                startTimeStr = line[index-1]
                endtimeStr = line[index]
                if '' == startTimeStr or '' == endtimeStr:
                    worksheet['%s%d'%(col, curLine)] = ''
                else:
                    try:
                        endtime = datetime.strptime(endtimeStr, '%Y-%m-%d %H:%M:%S')
                        starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
                        totalSecond = timedelta.total_seconds(endtime-starttime)
                        totalSecond = int(totalSecond)
                        hour = int(totalSecond/3600)
                        lefSeconds = totalSecond%3600
                        minutes = int(lefSeconds/60)
                        seconds = lefSeconds%60
                        timeStr = '%d:%d:%d'%(hour,minutes,seconds)
                        #hours = timedelta.total_seconds(endtime-starttime)//(60*60)
                        worksheet['%s%d'%(col, curLine)] = timeStr
                    except:
                        worksheet['%s%d'%(col, curLine)] = ''
                break
            else:
                worksheet['%s%d'%(col, curLine)] = line[index-1]
        curLine += 1

def DrawTable(ws):
    totalRowCnt = len(dicPlan22) + len(dicPlan4) + len(dicPlan5) + len(dicPlan6)
    STARTLINE = 12
    serialNo = 1
    for rowNo in range(STARTLINE,STARTLINE+totalRowCnt):
        for ColNo in range(1,1+19):
            ws['%s%d'%(get_column_letter(ColNo), rowNo)].alignment = PublicFuc.alignment
            ws['%s%d'%(get_column_letter(ColNo), rowNo)].border = PublicFuc.my_border('thin', 'thin', 'thin', 'thin')
        ws['%s%d'%(get_column_letter(1), rowNo)] = serialNo
        serialNo += 1

def ReadMarsIniDataLocal(curpath, pattern, dataDic, caseName, caseKey, keyLst, imageSuffix, diskCnt = 0):
    unitLst = ['M/s']
    config = configparser.RawConfigParser()
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        if 'HWCONFIG' not in config.sections():
            continue
        keyName = config['HWCONFIG']['mms_flash']
        if keyName not in dataDic:
            #0表示样片数量不确定，需要获取全部数据
            if diskCnt == len(dataDic) and 0 != diskCnt:
                continue
            dataDic[keyName] = {}
        if caseName not in config.sections():
            continue
        dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']
        if caseKey not in dataDic[keyName]:
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            tempLst = []
            for key in keyLst:
                if 'Cap' == key:
                    cap = config['HWCONFIG']['capacity']
                    if '' == cap:
                        tempLst.append('')
                    else:
                        tempLst.append(str(int(float(cap))))
                    continue
                if 'MMS_PC' == key:
                    tempLst.append(config['HWCONFIG']['MMS_PC'])
                    continue
                if key.lower() in config[caseName]:
                    value = config[caseName][key.lower()]
                    #去除单位
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    tempLst.append(value)
                else:
                    tempLst.append('')

            resultStr = PublicFuc.GetValueFromDic(dataDic[keyName],'test_result')
            if 'TRUE'== resultStr:
                dataDic[keyName]['test_result'] = 'TRUE'
            else:
                dataDic[keyName]['test_result'] = resultStr
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                PublicFuc.errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],resultStr,strTime])

            #imageSuffix为空不需要截图，只需要数据
            if '' != imageSuffix:
                pos = file.rfind('\\report')
                imagepath = file[:pos]
                image = ''
                for i in os.listdir(imagepath):
                    if i.endswith('.bmp'):
                        image = os.path.join(imagepath,i)
                        break
                if os.path.isfile(image):
                    tempLst.append(image)
                else:
                    tempLst.append('')
            dataDic[keyName][caseKey] = tempLst

def InitH2CsvColumnNameMap(columnNameMap):
    columnNameMap.clear()
    columnNameMap['Flash编号'] = 3
    columnNameMap['(H2)写速度'] = 14
    columnNameMap['(H2)读速度'] = 15
    columnNameMap['(H2)错误'] = 17 #PASS,or 错误码
    columnNameMap['test_time'] = 29
    columnNameMap['cap'] = 10
    columnNameMap['pc_no'] = 5

#获取H2数据的字典
def InitH2RawDataMap(h2Dic,rawDataList):
    h2Dic.clear()
    for row in rawDataList:
        tmpRow = ['']*(len(h2_column_name_map)-1) #减1是不再记录sampleNo，因为key已经记录
        tmpRow[0] = row[h2_column_name_map['cap']]
        tmpRow[1] = row[h2_column_name_map['pc_no']]
        tmpRow[2] = row[h2_column_name_map['(H2)写速度']]
        tmpRow[3] = row[h2_column_name_map['(H2)读速度']]     
        tmpRow[4] = row[h2_column_name_map['test_time']]
        tmpRow[5] = row[h2_column_name_map['(H2)错误']]
        if row[h2_column_name_map['Flash编号']] not in h2Dic:
            h2Dic[row[h2_column_name_map['Flash编号']]] = {}
        h2Dic[row[h2_column_name_map['Flash编号']]]['H2_1'] = tmpRow

def ReadRawCsvData(curpath,pattern,dataDic):
    #fileIdx = 1
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除

        #file_data = []
        with open(file,mode='r', buffering=-1, encoding='cp936') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            birth_header = next(csv_reader)  # 读取第一行每一列的标题
            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中
                dataDic.append(row)