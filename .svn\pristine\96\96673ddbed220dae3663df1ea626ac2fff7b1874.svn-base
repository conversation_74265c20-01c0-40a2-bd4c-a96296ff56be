
from openpyxl.styles import PatternFill, colors, Font, Alignment, Border, Side
from openpyxl.utils import get_column_letter
spealist = ['Front_end_1','Front_end_2','Front_end_3','Front_end_4','Front_end_5','Front_end_6','Front_end_7','Front_end_8']
def Run(workBook, data,tempws, title,time):
    ws = workBook['Test summary']
    set_sheetstyle(time*4+5,ws,ws.max_row)
    set_sheettile(time*4+5,time*4+8,ws,title)
    (Category_1,Category_2_1,Category_2_2,Script) = ('','','','')
    result = [['',0,0,0,'','finished'] for index in range(ws.max_row)]
    line = 0
    for row in tempws:
        if row[0] != '':
            Category_1 = row[0]  
        if row[1] != '':
            Category_2_1 = row[1]  
        if row[2] != '':
            Category_2_2 = Category_2_1 +';'+row[2] 
        if row[3] != '':
            Script = row[3] 
        for item in data:
            if Category_1 == 'FV' and item['Category_1'] == 'Full_Test' and item['Category_2'] in spealist and item['Script'] == Script:
                if item['Category_2'] == Category_2_1:
                    if item['Result'] == 'fail':
                        result[line][0] = 'fail'
                        result[line][2] += 1
                        caselist = item['case'].split('\n')
                        for case in caselist:
                            case = case + '\n'
                            if case not in result[line][4]: 
                                result[line][4] += case
                    elif item['Result'] == 'pass':
                        if result[line][0] != 'fail':
                            result[line][0] = 'pass'
                        result[line][1] += 1
                    elif item['Result'] == 'ongoing':
                        result[line][5] = 'ongoing'
                    result[line][3] += 1
            if item['Category_1'] == Category_1 and item['Script'] == Script:
                if item['Category_2'] == '' or item['Category_2'] == Category_2_1 or item['Category_2'] == Category_2_2:
                    if item['Result'] == 'fail':
                        result[line][0] = 'fail'
                        result[line][2] += 1
                        caselist = item['case'].split('\n')
                        for case in caselist:
                            case = case + '\n'
                            if case not in result[line][4]:
                                result[line][4] += case
                    elif item['Result'] == 'pass':
                        if result[line][0] != 'fail':
                            result[line][0] = 'pass'
                        result[line][1] += 1
                    elif item['Result'] == 'ongoing':
                        result[line][5] = 'ongoing'
                    result[line][3] += 1
        line += 1
        if result[line-1] != ['',0,0,0,'','finished']:
            write_excel(result[line-1],ws,line,time*4+5)

def write_excel(data,worksheet,startline,startcol):
    worksheet.row_dimensions[startline].height = 14
    alignment=Alignment(horizontal='center',vertical='center')
    resultfont = Font('等线',size=12,color=colors.BLACK)
    num = str(data[2]) + '/' + str(data[1]) + '/' + str(data[3])
    if data[0] == 'fail':
        resultfont = Font('等线',size=12,color='00FF0000')
    worksheet.cell(row=startline, column=startcol, value=data[5]).alignment = alignment
    startcol += 1 
    worksheet.cell(row=startline, column=startcol, value=data[0]).alignment = alignment
    worksheet.cell(row=startline, column=startcol, value=data[0]).font = resultfont
    startcol += 1 
    worksheet.cell(row=startline, column=startcol, value=num).alignment = alignment
    worksheet.cell(row=startline, column=startcol, value=num).font = resultfont
    startcol += 1 
    worksheet.cell(row=startline, column=startcol, value=data[4]).alignment = Alignment(horizontal='left',vertical='center',wrap_text=True)
    worksheet.cell(row=startline, column=startcol, value=data[4]).font = resultfont


def set_sheettile(startcol,col,ws,title):
    font=Font('Calibri',size=12,color=colors.BLACK,bold=True)
    alignment=Alignment(horizontal='center',vertical='center')
    bd = Border(left=Side(border_style='thin'),
                right=Side(border_style='thin'))
    ws.merge_cells(start_row=1, start_column=startcol, end_row=1, end_column=col)
    cell = ws.cell(row=1, column=startcol,value = title) 
    cell.fill = PatternFill("solid", fgColor="B5C6EA")
    cell.font = font
    cell.alignment = alignment
    cell.border = bd

def set_sheetstyle(col,ws,allrows):
    cellist = ['State','Result','Num(fail/pass/all)','Fail case']
    font=Font('Calibri',size=12,color=colors.BLACK,bold=True)
    alignment=Alignment(horizontal='center',vertical='center')
    bd = Border(left=Side(border_style='thin'),
                right=Side(border_style='thin'),
                top=Side(border_style='thin'),
                bottom=Side(border_style='thin'))
    for row in ws.iter_rows(min_row=2, max_row=allrows, min_col=col, max_col=col+3):
        for cell in row:
            cell.border = bd
            cell.font = Font('等线',size=12,color=colors.BLACK)
    column_letter = get_column_letter(col+2)
    ws.column_dimensions[column_letter].width = 15
    column_letter = get_column_letter(col+3)
    ws.column_dimensions[column_letter].width = 30
    for item in cellist:
        cell = ws.cell(row=2, column=col, value=item)
        col += 1 
        cell.fill = PatternFill("solid", fgColor="B5C6EA")
        cell.font = font
        cell.alignment = alignment