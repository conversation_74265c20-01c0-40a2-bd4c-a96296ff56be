#include "stdafx.h"
#include <StatusUpload\IStatusUpload.h>
#include <assert.h>
#include "commonFunction.h"
#include "PcStatus.h"
#include "TestStatus.h"
#include "TestResult.h"
#include "UpLoadSleepReboot.h"

#ifdef _DEBUG
#pragma comment(lib,"LibPublicwin32_d.lib")
#else
#pragma comment(lib,"LibPublicwin32")
#endif

HWND g_hMainWnd = NULL;

CPcStatus g_pcStatus;
CTestStatus g_testStatus;
CTestResult g_testResult;
CUpLoadSleepReboot g_SleepRebootUploader;

STATUS_API void ReleaseResource()
{
	g_pcStatus.ReleaseResource();
	g_testStatus.ReleaseResource();
	g_testResult.ReleaseResource();
}

STATUS_API bool UpLoadTestResult(TEST_RESULT* _pStatus)
{
#ifndef ATER_TW
	return g_testResult.UpdateTestResult(_pStatus);
#else
	return true;
#endif
}

STATUS_API bool UpLoadTestStatus(TEST_STATUS* _pStatus)
{
#ifndef ATER_TW
	return g_testStatus.UpdateTestStatus(_pStatus);
#else
	return true;
#endif
}

STATUS_API bool UpLoadPcStatus(PC_STATUS* _pStatus)
{
#ifndef ATER_TW
	return g_pcStatus.UpdatePcStatus(_pStatus);
#else
	return true;
#endif
}

STATUS_API CString GetPcErrInfo()
{
	return g_pcStatus.GetErrInfo();
}

STATUS_API bool UpLoadRebootSleepStatus(SLEEP_REEBOOT_STATUS* _pStatus)
{
#ifndef ATER_TW
	return g_SleepRebootUploader.UpdateSleepReboot(_pStatus);
#else
	return true;
#endif
}

STATUS_API bool UpLoadCaseTestStatus(TEST_CASE_STATUS* _pStatus)
{
#ifndef ATER_TW
	return g_testStatus.UpdateCaseTestStatus(_pStatus);
#else
	return true;
#endif
}

STATUS_API bool UpLoadSATAHistoryInfo(SSD_DB_INFO* _pStatus)
{
#ifndef ATER_TW
	return g_testResult.UpLoadSATAHistoryInfo(_pStatus);
#else
	return true;
#endif
}

