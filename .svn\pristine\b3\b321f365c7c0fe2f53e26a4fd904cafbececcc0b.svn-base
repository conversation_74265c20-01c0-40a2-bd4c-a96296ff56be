﻿/**
 * @file  FlashControllerDef.h
 * <AUTHOR>
 * @version  Sim_Studio_V30.00.2.4.000
 * @date  2018-03-27
 * @brief  本文件为仿真平台Flash Model相关的类型定义
 * @details  
	 1, 平台内部的类型定义
	 2，平台导出的类型定义
 */
#include "TypeDef.h"
#ifndef FLASH_STRUCTURE_TYPE_DEF_H
#define FLASH_STRUCTURE_TYPE_DEF_H

/** 
    * @brief Flash Model 操作命令
*/
typedef enum _FLASH_OP_TYPE
{
	// 操作模式
	OP_TLC = 0x00000000,							///< TLC模式（缺省模式），对于MLC Flash，適用該模式
	OP_SLC = 0x00000001,							///< SLC模式，僅對TLC Flash支持該模式

	// 特殊命令
	OP_NOCACHE = 0x00000000,						///< 保留模式，不建議使用
	OP_CACHE = 0x00000002,							///< 保留模式，不建議使用
	OP_NOCOPYBACK = 0x00000000,						///< 保留模式，不建議使用
	OP_COPYBACK = 0x00000004,						///< 保留模式，不建議使用
	OP_LOG_SINGLEPLANE = 0x00000000,				///< 保留模式，不建議使用
	OP_SLC_LOG_TWOPLANE = 0x00000008,				///< 保留模式，不建議使用
	OP_PHY_SINGLEPLANE = 0x00000000,				///< 保留模式，不建議使用
	OP_PHY_TWOPLANE = 0x00000100,					///< 保留模式，不建議使用

	//// 操作类型
	OP_ERASE = 0x01000000,							///< 擦除命令
	OP_PROGRAM = 0x02000000,						///< 編程命令
	OP_READ = 0x04000000,							///< 讀命令

	//// 出错标识
	OP_ERR_TYPE = 0xFFFFFFFE						///< 出錯標記，不建議使用
} FLASH_OP_TYPE;

/** 
    * @brief Flash Model 块状态
*/
enum BLOCKTYPES
{
	Nomal = 0,			///< 正常塊
	NatureBad,			///< 量產壞塊
	OverWornBad,		///< 磨損壞塊
	RandTypeBad,		///< 隨機壞塊
};

/** 
    * @brief 操作後，當前頁（塊）的狀態
*/
typedef enum _FLASH_OP_STATUS
{
	PROGRAMED = 0,		///< 已編程頁
	READFAIL,			///< 讀取失敗
	PROGRAMFAIL,		///< 編程失敗
	ERASED,				///< 已擦除頁
	TIME_OUT,			///< 命令超時
	ERASEDFAIL,			///< 擦除失敗
	MEMORY_PRESSURE,  // 内存不足
} FLASH_OP_STATUS;

/** 
    * @brief 對Flash進行讀取時，該頁的狀態
*/
typedef enum _ECC_STATUS
{
	ECC_NORMAL = 0,									///< 读取正常
	ECC_ALL_FF,										///< 读取了全F页（未写入，直接读取）
	ECC_ALL_00,										///< 读取了全0页（坏块）

	ECC_READ_OVERFLOW,								///< ECC超过了Overflow阈值，详细参见Start.exe的配置信息
	ECC_READ_FAILED,								///< ECC超过了ReadFail阈值，详细参加Start.exe的配置信息
}ECC_STATUS;

/** 
    * @brief Flash在當前設備中的位置
*/
typedef struct STR_CE_ID
{
	U8 m_uChannelNo;								///< 通道
	U8 m_uChipNo;									///< 片选
	U8 m_uChipIndexInDevice;						///< 内部保留字段，不建议设置
} CE_ID;

//获取最大最小磨损次数结构体
typedef struct WornTime
{
	U32 m_MaxWorn;
	U32 m_MinWorn;
}WornTime;

/** 
    * @brief Flash Model數據通訊的結構
	* @note 平臺內結構體，不建議使用
*/
typedef struct _FLASH_OP_INFO
{
	U16 uSize;						///< [Required] 有效性判斷，需設置為：sizeof(_FLASH_OP_INFO)

	U8 uCurrOpChannelNo;			///< [in] 通道號
	U8 uCurrOpChipNo;				///< [in] 片選號
	U8 uCurrOpLunNo;				///< [Optional] LUN號，保留字段
	U32 uRowAddr;					///< [in] 行地址
	U8 uVirtualOpFlag;				///< [in] 數據類型，0 邏輯數據 1 表格數據 2 VMC數據
	U32 uDMALen;					///< [in] 每个DMA的有效长度，单位：字节

	FLASH_OP_STATUS eFlashStatus;	///< [out] 命令執行後，當前頁(塊)的狀態
	ECC_STATUS eECCStatus;			///< [out] 頁讀取的ECC
	U8 uReadStatus;					///< [out] 讀取後的狀態，保留值
	U16 uEcc;						///< [out] 当前读返回的ecc值

	U8* arrOpBuff;					///< [in/out] 編程/讀取的數據Buffer指針
	U16 uCurrOpFirstByteNo;			///< [in] 此次操作的起始地址
	U16 uCurrOpByteNum;				///< [in] 此次操作的數據長度，單位：字節（原則上應為512的整數倍）

	U8* arrUserData;				///< [in/out] userdata的數據指針
	U16 uUserDataLen;				///< [in] userdata數據長度，單位：字節
}FLASH_OP_INFO, *PFLASH_OP_INFO;

/** 
    * @brief 描述ED3Flash的頁類型
	* @note ED3類型的Flash，除了需要指定行地址外，還需要指定頁類型，此參數用於補充描述。
*/
typedef enum _WLPAGE_TYPE
{
	DEFAULT = 0,					///< 缺省值，對於非ED3類型的Flash，應設置為此值
	HIGH_PAGE = 1,					///< 高頁
	MIDDLE_PAGE = 2,				///< 中頁
	LOW_PAGE = 3,					///< 低頁
}WLPAGE_TYPE;

/** 
    * @brief 包含Flash命令的主要參數
*/
typedef struct _OP_SEND_INFO
{
	//// 有效性判斷
	U16 uSize;						///< [Required] 有效性判斷，需設置為：sizeof(OP_SEND_INFO)
	U8 uCurrOpLunNo;				///< [Optional] 當前LUN號，保留字段

	U32 uRowAddr;					///< [Required] 行地址

	U8 uVirtualOpFlag;				///< [Required] 數據類型，0 邏輯數據，1 表格數據，2 VMC數據
	U32 uDMALen;					///< [Optional] DMA數據長度，用於計算ECC

	WLPAGE_TYPE pageType;			///< [Required] 頁類型，仅ED3Flash TLC模式（或類似的3D Flash）需设置该值
	
	U8 arrUserData[256];			///< [Required] 當前頁的Userdata，最大能夠接受256字節
	U16 uUserDataLen;				///< [Required] 有效的Userdata長度，單位：字節
	U16 uRetryCnt;					///< [Required] 重讀的次數
	U16 uSeed;						///< [Optional] Start.exe中，開啟隨機化功能有效，用於校驗讀寫種子一致
} OP_SEND_INFO, *POP_SEND_INFO;

/** 
    * @brief 包含Flash命令的主要參數
*/
typedef struct _OP_GET_INFO
{
	//// 有效性判斷
	U16 uSize;						///< [Required] 传输前，應将此值赋值为sizeof(OP_GET_INFO)，作為數據有效性判斷

	FLASH_OP_STATUS eFlashStatus;	///< [out]操作后flash状态
	ECC_STATUS		eECCStatus;		///< [out]仅读取有效，表示读取的状态
	U8 uReadStatus;					///< [out] 保留值，用於描述讀取狀態
	U16 uEcc;						///< [out] 該頁的ECC
	U32 error;						///< [out] 異常碼,0 正常, 其他 錯誤， 如果是读取，则该字段的每个bit描述了一个扇区是否出错
	U8 arrUserData[256];			///< [out] 僅讀取有效
	U16 uUserDataLen;				///< [Required] Userdata的長度，僅讀取有效
} OP_GET_INFO, *POP_GET_INFO;

/** 
    * @brief 命令隊列緩存項
	* @note 平臺內部導出的結構體，不建議固件用戶使用
*/
typedef struct _CMD_RECORD_INFO
{
	CE_ID m_CEID;					///< 片選
	U32 m_opMode;					///< 操作模式
	OP_SEND_INFO m_opInfo;			///< 命令參數
}CMD_RECORD_INFO, *PCMD_RECORD_INFO;

/** 
    * @brief 命令隊列
	* @note 平臺內部導出的結構體，不建議固件用戶使用
*/
typedef struct _CMD_RECORD_QUEUE
{
	PCMD_RECORD_INFO m_CmdArray;	///< 命令內容
	U32 m_uIdex;					///< 命令索引
}CMD_RECORD_QUEUE, *PCMD_RECORD_QUEUE;

/** 
    * @brief Cache存儲類型
	* @note 平臺內部導出的結構體，不建議固件用戶使用
*/
enum CACHE_MODE
{
	SECTOR_MODE = 0,				///< 512字節模式
	MAP_SECTOR_MODE = 1,			///< 6字節模式
};

#endif