import os
import re
import openpyxl
from openpyxl.styles import PatternFill, colors, Font, Alignment, Border, Side
from openpyxl.utils import get_column_letter
flag = True
alignment = Alignment(horizontal='center',vertical='center')
strList = ['Failed ','Passed ','.log']
colname_60 = ['FBB','RTBB_UDA','RTBB_EUDA','Run_time_bad_block_count_erase_fail','Run_time_bad_block_count_program_fail','Run_time_bad_block_count_read_UECC','UECC_Count','HECC_Count','EUDA_read_retry_count','UDA_read_retry_count',
              'Read_disturb_refresh_EUDA_VB_Count','Read_disturb_refresh_UDA_VB_Count','Total_EC_Num_in_EUDA','Min_EC_Num_in_EUDA','Max_EC_Num_in_EUDA',
              'Ave_EC_Num_in_EUDA','Total_EC_Num_in_UDA','Min_EC_Num_in_UDA','Max_EC_Num_in_UDA','Ave_EC_Num_in_UDA','Cumulative_NAND_Read','Cumulative_Host_Write','History_Host_Total_Read','History_Host_Total_Write','WearLeveling_count_in_EUDA','WearLeveling_count_in_UDA',
              'WAF_Total','WAF_UDA','Total_SLC_EC_in_UDA','Total_TLC_EC_in_UDA','History_min_Ctrl_NAND_temp','History_max_Ctrl_NAND_temp','Min_temperature_counter','Max_temperature_counter','Current_temperature_for_Controller',
              'Current_temperature_for_NAND_CE0','Current_temperature_for_NAND_CE1','Current_temperature_for_NAND_CE2','Current_temperature_for_NAND_CE3','First_read_issue_count','Vccq_VDT_count','Vcc_VDT_count','SPO_recovery_fail_VB_count','SPO_count','SPO_recovery_success_count',
              'SPO_recovery_faiount','Fatal_error_code_and_temp','Error Log 0','Error Log 1','Error Log 2','SRAM_FRSC','SRAM_FC','SRAM_FRFC','EUDA_VB_block_size','UDA_VB_block_size']

collist_60 = ['[3:0]     Factory bad block count','[15:12]   Run time bad block count (UDA)','[19:16]   Run time bad block count (EUDA)','[23:20]   Run time bad block count (erase fail)','[27:24]   Run time bad block count (program fail)','[31:28]   Run time bad block count (read UECC)',
           '[35:32]   UECC count','[39:36]   HECC count','[215:212] EUDA read retry count','[219:216] UDA read retry count','[171:168] Read disturb refresh EUDA VB Count','[175:172] Read disturb refresh UDA VB Count','[75:72]   Total EC Num in EUDA','[79:76]   Min_EC_Num in EUDA',
           '[83:80]   Max EC Num in EUDA','[87:84]   Ave EC Num in EUDA','[91:88]   Total EC Num in UDA','[95:92]   Min EC Num in UDA','[99:96]   Max EC Num in UDA','[103:100] Ave EC Num in UDA','[107:104] Cumulative NAND Read','[111:108] Cumulative Host Write',
           '[1291:1288] History Host Total Read','[1295:1292] History Host Total Write','[1209:1208] WearLeveling count in EUDA','[1211:1210] WearLeveling count in UDA','[923:920] WAF total (Write Amplication Factor)','[119:116] WAF_UDA',
           '[191:188] Total SLC EC in UDA','[195:192] Total TLC EC in UDA','[127:124] History min Ctrl/NAND temp','[131:128] History max Ctrl/NAND temp','[199:196] min temperature counter','[203:200] max temperature counter','[1230:1230] Current temperature for Controller',
           '[1100:1100] Current temperature for NAND CE0','[1101:1101] Current temperature for NAND CE1','[1102:1102] Current temperature for NAND CE2','[1103:1103] Current temperature for NAND CE3','[1467:1464] First read issue count',
           '[59:56]   Vccq VDT count','[55:52]   Vcc VDT count','[1059:1056] SPO recovery fail VB count','[63:60]   SPO count','[67:64]   SPO recovery success count','[71:68]   SPO recovery fail count','[1483:1480] Fatal error code and temp',
           '[1487:1484] YS Error Log 0','[1491:1488] YS Error Log 1','[1495:1492] YS Error Log 2','[1519:1516] SRAM_FRSC','[1523:1520] SRAM_FC','[1527:1524] SRAM_FRFC','[687:684] EUDA VB block size','[691:688] UDA VB block size']

colname_56 = ['FBB','RTBB_UDA','RTBB_EUDA','Run_time_bad_block_count_erase_fail','Run_time_bad_block_count_program_fail','Run_time_bad_block_count_read_UECC','UECC_Count','HECC_Count','EUDA_read_retry_count','UDA_read_retry_count',
              'Read_disturb_refresh_EUDA_VB_Count','Read_disturb_refresh_UDA_VB_Count','Total_EC_Num_in_EUDA','Min_EC_Num_in_EUDA','Max_EC_Num_in_EUDA',
              'Ave_EC_Num_in_EUDA','Total_EC_Num_in_UDA','Min_EC_Num_in_UDA','Max_EC_Num_in_UDA','Ave_EC_Num_in_UDA','Cumulative_NAND_Read','Cumulative_Host_Write','History_Host_Total_Read','History_Host_Total_Write','WearLeveling_count_in_EUDA','WearLeveling_count_in_UDA',
              'WAF_Total','WAF_UDA','Total_SLC_EC_in_UDA','Total_TLC_EC_in_UDA','History_min_Ctrl_NAND_temp','History_max_Ctrl_NAND_temp','Min_temperature_counter','Max_temperature_counter','Current_temperature_for_Controller',
              'Current_temperature_for_NAND_CE0','Current_temperature_for_NAND_CE1','Current_temperature_for_NAND_CE2','Current_temperature_for_NAND_CE3','First_read_issue_count','Vccq_VDT_count','Vcc_VDT_count','SPO_recovery_fail_VB_count','SPO_count','SPO_recovery_success_count',
              'SPO_recovery_faiount','Fatal_error_code_and_temp','Error Log 0','Error Log 1','Error Log 2','SRAM_FRSC','SRAM_FC','SRAM_FRFC']

collist_56 = ['Factory bad block count','Run time bad block count (UDA)','Run time bad block count (EUDA)','Run time bad block count (erase fail)','Run time bad block count (program fail)','Run time bad block count (read UECC)',
           'UECC count','HECC count','EUDA read retry count','UDA read retry count','Read disturb refresh EUDA VB Count','Read disturb refresh UDA VB Count','Total EC Num in EUDA','Min_EC_Num in EUDA','Max EC Num in EUDA','Ave EC Num in EUDA',
           'Total EC Num in UDA','Min EC Num in UDA','Max EC Num in UDA','Ave EC Num in UDA','Cumulative NAND Read','Cumulative Host Write','History Host Total Read','History Host Total Write',
           'WearLeveling count in EUDA','WearLeveling count in UDA','WAF total (Write Amplication Factor)','WAF_UDA','Total SLC EC in UDA','Total TLC EC in UDA','History min Ctrl/NAND temp','History max Ctrl/NAND temp',
           'min temperature counter','History max Ctrl/NAND temp','Current temperature for Controller','Current temperature for NAND CE0','Current temperature for NAND CE1','Current temperature for NAND CE2','Current temperature for NAND CE3','First read issue count',
           'VDT drop count','Vcc VDT count','VCCQ SPO count','VCC SPO Count','SPO recovery success count','SPO recovery fail count','LVD_Vccq','YS Error Log 0','YS Error Log 1','YS Error Log 2','SRAM_FRSC','SRAM_FC','SRAM_FRFC']

class filedata:
    def __init__(self, path, folder, time, case):
        self.path = path
        self.folder = folder
        self.time = time
        self.case = case
Capacity = 0
def Run(curpath):
    orgcurpath = curpath
    curpath = os.path.join(curpath, "XU4")
    MarsresultFileName = 'BLX_HR数据_check汇总.xlsx'
    MarsresultFile = os.path.join(orgcurpath, MarsresultFileName)
    wb = openpyxl.Workbook()
    global category
    category = []
    data = []
    if os.path.isdir(curpath):
        for item in os.listdir(curpath):
        # 拼接子文件夹路径
            sub_folder_path = os.path.join(curpath, item)
            
            # 判断是否为文件夹
            if os.path.isdir(sub_folder_path):
                file_start = item.split('-')[0]
                hr_merger_path = os.path.join(sub_folder_path, file_start)
                if os.path.isdir(hr_merger_path):
                    file_path = os.path.join(hr_merger_path, 'verify_env.ttl')
                    if os.path.exists(file_path):
                        with open(file_path, "r",errors='ignore') as file:
                            text = file.read()
                        pattern = r'GetHR_mode\s*=\s*"(\d+)"'
                        match = re.search(pattern, text)
                        pattern = r'GetHR_mode\s*=\s*(\d+)' #兼容没有引号的情况
                        match1 = re.search(pattern, text)
                        if match or match1:
                            hr_key = '0'
                            if match:
                                hr_key = match.group(1)  # 获取模式值
                            else:
                                hr_key = match1.group(1) # 获取模式值
                            if hr_key != '0':
                                for subitem in os.listdir(sub_folder_path):
                                    if subitem.endswith('.log') and ('Passed' in subitem or 'Failed' in subitem) and ' env ' not in subitem:
                                        inipath = os.path.join(sub_folder_path,subitem)
                                        time = os.path.getmtime(inipath)
                                        text = subitem
                                        for seg in strList:
                                            text = text.replace(seg,'')
                                        data.append(filedata(inipath,item,time,text))
        data.sort(key=lambda x: (x.folder, x.time))
        load_data(data,wb)
        if len(wb.sheetnames) != 1:
            sheet = wb["Sheet"]
            wb.remove(sheet)
        wb.save(MarsresultFile)

def insertdata(remindata,coltype,sample,lineimt,title,realdata):
    if coltype == 1:
        title[sample] = colname_56
    else:
        title[sample] = colname_60
    if sample in realdata:
        realdata[sample].append(getData(remindata,lineimt,coltype))
    else:
        realdata[sample] = [getData(remindata,lineimt,coltype)]

def insertNull(realdata,sample,item,coltype,title):
    if coltype == 0:
        coltype = 1
    Nullimt = {}
    Nullimt['color'] = ''
    Nullimt['folder'] = item.folder
    Nullimt['case'] = item.case
    if coltype == 1:
        for item in colname_56:
            Nullimt[item] = 'NA'
        title[sample] = colname_56
    else:
        for item in colname_60:
            Nullimt[item] = 'NA'
        title[sample] = colname_60
    if sample in realdata:
        realdata[sample].append(Nullimt)
    else:
        realdata[sample] = [Nullimt]


def load_data(data,wb):
    coltype = 0 
    title = {}
    realdata = {}
    key = 0
    remindata = []
    nulllist = []
    tempcolor = ''
    for item in data:
        pattern = r'-(.*?)-(.*?)_'
        match = re.search(pattern, item.folder)
        if re.search(pattern, item.folder):
            sample = match.group(2)
        with open(item.path, "r",errors='ignore') as file:
            lines = file.readlines()
            remindata = []
            key = 0
            for line in lines:
                if 'Health Report Monitor: by ' in line:
                    key = 2
                    if 'CMD56' in line:
                        coltype = 1
                    if 'CMD60' in line:
                        coltype = 2
                    if '<9999>' in line:
                        tempcolor = 'red'
                if '[Info] mmc restore factory setting...' in line and tempcolor != 'red':
                    tempcolor = 'blue'
                if '[Info] >>>mmc MP erase good...' in line and tempcolor != 'red':
                    tempcolor = 'yellow'
                if '[Info] >>>mmc MP erase all...' in line and tempcolor != 'red':
                    tempcolor = 'green'
                if key > 1:
                    remindata.append(line)
                if "==================================================================================" in line:
                    lineimt = {}
                    lineimt['color'] = tempcolor
                    lineimt['folder'] = item.folder
                    lineimt['case'] = item.case
                    if remindata != []:
                        insertdata(remindata,coltype,sample,lineimt,title,realdata)
                        tempcolor = ''
                    remindata = []
                    key = 1
            if key == 0:
                nulllist.append([sample,item])
    for item in nulllist:
        insertNull(realdata,item[0],item[1],coltype,title)
    
    rowline = 1
    for sample in realdata:
        sheet = wb.create_sheet(title=sample)
        sheet.cell(row=1, column=1).fill =  PatternFill(start_color="FF5959", end_color="FF5959", fill_type="solid")
        sheet.cell(row=2, column=1).fill =  PatternFill(start_color="F6E424", end_color="F6E424", fill_type="solid")
        sheet.cell(row=3, column=1).fill =  PatternFill(start_color="5B91FF", end_color="5B91FF", fill_type="solid")
        sheet.cell(row=4, column=1).fill =  PatternFill(start_color="ADADAD", end_color="ADADAD", fill_type="solid")
        sheet.cell(row=5, column=1).fill =  PatternFill(start_color="8B8BE2", end_color="8B8BE2", fill_type="solid")
        sheet.cell(row=6, column=1).fill =  PatternFill(start_color="B9DBAC", end_color="B9DBAC", fill_type="solid")
        sheet.cell(row=1, column=2, value='红色：需要报错反馈')
        sheet.cell(row=2, column=2, value='黄色：需要警告')
        sheet.cell(row=3, column=2, value='蓝色：恢复出厂设置')
        sheet.cell(row=4, column=2, value='灰色：case fail')
        sheet.cell(row=5, column=2, value='紫色：erase good MP')
        sheet.cell(row=6, column=2, value='绿色：erase all MP')
        sheet.column_dimensions['A'].width = 35
        sheet.column_dimensions['B'].width = 15
        colline = 1
        rowline = 9
        for val in ['测试模块','case']:
            sheet.cell(row=rowline, column=colline, value=val)
            colline += 1
        for val in title[sample]:
            sheet.cell(row=rowline, column=colline, value=val)
            colline += 1
        sheet.cell(row=rowline, column=colline, value='Delta_WAF')
        colline += 1
        sheet.cell(row=rowline, column=colline, value='EUDA_EC_Gap')
        colline += 1
        sheet.cell(row=rowline, column=colline, value='UDA_EC_Gap')
        for item in realdata[sample]:
            colline = 1
            rowline += 1
            for val in ['folder','case']:
                sheet.cell(row=rowline, column=colline, value=item[val])
                colline += 1
            for val in title[sample]:
                if val in item:
                    sheet.cell(row=rowline, column=colline, value=item[val])
                else:
                    sheet.cell(row=rowline, column=colline, value='NA')
                colline += 1
            for col in range(3, len(title[sample]) + 3):
                cell = sheet.cell(row=rowline, column=col)
                coloc(cell,item['color'])
        consheet(sheet)
        judgedata(sheet)


def getData(remindata,lineimt,coltype):
    if coltype == 1:
        collist = collist_56
        colname = colname_56
    if coltype == 2:
        collist = collist_60
        colname = colname_60
    for ind,case in enumerate(collist):
        for line in remindata:
            if case in line:
                if '   :' not in line:
                     lineimt[colname[ind]]= -1
                else:
                    last_digits = line.split(":")[-1].strip()
                    if last_digits == '0xFFFFFFFF':
                        last_digits = '0'
                    if last_digits == '0x00000000':
                        last_digits = '0'
                    if coltype == 1:
                        lineimt[colname[ind]]=(int(last_digits,16))
                    else:
                        lineimt[colname[ind]]=(int(last_digits,16))
    return lineimt

def consheet(sheet):
    sheet.column_dimensions['A'].width = 30
    sheet.column_dimensions['B'].width = 50
    #添加颜色
    sheet.freeze_panes='C10'
    name_range_dict = {}
    for row in sheet.iter_rows(min_row=10, min_col=1, max_col=1):
        name = row[0].value
    # 如果名字已存在于字典中，则更新合并范围的结束行号
        if name in name_range_dict:
            name_range_dict[name]['end_row'] = row[0].row
        else:
            # 否则，在字典中创建新的合并范围条目
            name_range_dict[name] = {'start_row': row[0].row, 'end_row': row[0].row}

# 合并相同名字的单元格
    for name, range_dict in name_range_dict.items():
        align = Alignment(horizontal='center', vertical='center')
        sheet['A%d'%range_dict['start_row']].alignment = align
        start_row = range_dict['start_row']
        end_row = range_dict['end_row']
        sheet.merge_cells(f"A{start_row}:A{end_row}")
    

def coloc(cell,color):
    if color == 'red':
        cell.fill = PatternFill(start_color="ADADAD", end_color="ADADAD", fill_type="solid")
    if color == 'green':
        cell.fill = PatternFill(start_color="B9DBAC", end_color="B9DBAC", fill_type="solid")
    if color == 'yellow':
        cell.fill = PatternFill(start_color="8B8BE2", end_color="8B8BE2", fill_type="solid")
    if color == 'blue':
        cell.fill = PatternFill(start_color="5B91FF", end_color="5B91FF", fill_type="solid") 

def judgedata(sheet):
    global rowline
    rowline = sheet.max_row
    for item in ['C','D','E','F','G','H']:
        judgecol_one(sheet,item)
    judgecol_two(sheet,'I')
    judgecol_there(sheet,'J')
    judgecol_four(sheet,'K',1000,-1)
    judgecol_four(sheet,'L',10000,-1)
    for item in ['M','AP']:
        judgecol_four(sheet,item,0,-1)
    for item in ['AC','AD']:
        judgecol_four_ex(sheet,item,6,10)
    for item in ['AS','AV','AW','AX','AY','AZ','BA','BB','BC']:
        judgecol_four(sheet,item,-1,0)
    judgecol_five(sheet,'R')
    judgecol_six(sheet,'S')
    judgecol_seven(sheet,'P','Q','O')
    judgecol_seven(sheet,'T','U','S')
    judgecol_eight(sheet)
    #judgecol_nine(sheet,'Y','AA')
    #judgecol_nine(sheet,'Z','AB')
    judgecol_ten(sheet)
    judgecol_elven(sheet)
def judgecol_one(sheet,col):#同一个样片的第1行为初始值，测试过程中同一个样片的初始数值发生变化（变大或者变小都需要），需要标红
    firstnum = -1
    column = sheet[col]
    # 遍历列中的每个单元格
    firstnum = sheet[f'{col}{10}'].value
    for cell in column[9:rowline+1]:
        if sheet[f'{col}{cell.row}'].value == None or sheet[f'{col}{cell.row}'].value == 'NA':
            continue
            # 合并前面的单元格
        if firstnum != sheet[f'{col}{cell.row}'].value:
                sheet[f'{col}{cell.row}'].fill = PatternFill(start_color="FF5959", end_color="FF5959", fill_type="solid")
def judgecol_two(sheet,col):#1.同一个样片的第1行为初始值，测试过程中同一个样片的初始数值发生变化（变大或者变小都需要），需要标红       2. 同一个样片，每一行都需要：UECC Count > Run time bad block count (read UECC)，需要标红
    firstnum = -1
    column = sheet[col]
    # 遍历列中的每个单元格
    for cell in column[9:rowline+1]:
        if sheet[f'{col}{cell.row}'].value == None or sheet[f'{col}{cell.row}'].value == 'NA' or sheet[f'{"I"}{cell.row}'].value == 'NA' or sheet[f'{"H"}{cell.row}'].value == 'NA':
            continue
        if sheet[f'{col}{10}'].value != None:
            firstnum = sheet[f'{col}{cell.row}'].value
            # 合并前面的单元格
        if firstnum != sheet[f'{col}{cell.row}'].value:
            sheet[f'{col}{cell.row}'].fill = PatternFill(start_color="FF5959", end_color="FF5959", fill_type="solid")
        if sheet[f'{col}{cell.row}'].value > sheet[f'{"H"}{cell.row}'].value:
            sheet[f'{col}{cell.row}'].fill = PatternFill(start_color="FF5959", end_color="FF5959", fill_type="solid")

def judgecol_there(sheet,col):#1.>同一个样片的第1行为初始值，测试过程中同一个样片的初始数值发生变化（变大或者变小都需要），需要标红  2.同一个样片，每一行都需要：HECC Count  EUDA read retry count+UDA read retry count之和，需要标红
    firstnum = -1
    column = sheet[col]
    # 遍历列中的每个单元格
    for cell in column[9:rowline+1]:
        if sheet[f'{col}{cell.row}'].value == None or sheet[f'{col}{cell.row}'].value == 'NA' or sheet[f'{"D"}{cell.row}'].value == 'NA' or sheet[f'{"K"}{cell.row}'].value or sheet[f'{"L"}{cell.row}'].value:
            continue
        if sheet[f'{col}{10}'].value != None:
            firstnum = sheet[f'{col}{cell.row}'].value
            # 合并前面的单元格
        if firstnum != sheet[f'{col}{cell.row}'].value:
            sheet[f'{col}{cell.row}'].fill = PatternFill(start_color="FF5959", end_color="FF5959", fill_type="solid")
        if sheet[f'{col}{cell.row}'].value > sheet[f'{"K"}{cell.row}'].value + sheet[f'{"L"}{cell.row}'].value:
            sheet[f'{col}{cell.row}'].fill = PatternFill(start_color="FF5959", end_color="FF5959", fill_type="solid")
            
def judgecol_four(sheet,col,yellow,red):#同一个样片，每一行都需关注：大于yellow需要标黄，大于red报错，需要标红
    column = sheet[col]
    # 遍历列中的每个单元格
    for cell in column[9:rowline+1]:
        if sheet[f'{col}{cell.row}'].value == None or sheet[f'{col}{cell.row}'].value == 'NA':
            continue
            # 合并前面的单元格
        if yellow != -1 and float(sheet[f'{col}{cell.row}'].value) > yellow:
                sheet[f'{col}{cell.row}'].fill = PatternFill(start_color="F6E424", end_color="F6E424", fill_type="solid")
        if red != -1 and float(sheet[f'{col}{cell.row}'].value) > red:
                sheet[f'{col}{cell.row}'].fill = PatternFill(start_color="FF5959", end_color="FF5959", fill_type="solid")

def judgecol_four_ex(sheet,col,yellow,red):#同一个样片，每一行都需关注：大于yellow需要标黄，大于red报错，需要标红
    column = sheet[col]
    # 遍历列中的每个单元格
    for cell in column[9:rowline+1]:
        if sheet[f'{col}{cell.row}'].value == None or sheet[f'{col}{cell.row}'].value == 'NA':
            continue
            # 合并前面的单元格
        if yellow != -1 and float(sheet[f'{col}{cell.row}'].value)/1000 > yellow:
                sheet[f'{col}{cell.row}'].fill = PatternFill(start_color="F6E424", end_color="F6E424", fill_type="solid")
        if red != -1 and float(sheet[f'{col}{cell.row}'].value)/1000 > red:
                sheet[f'{col}{cell.row}'].fill = PatternFill(start_color="FF5959", end_color="FF5959", fill_type="solid")

def judgecol_five(sheet,col):#1.同一个样片，每一行都需关注： Min EC Num in EUDA <= Ave EC Num in EUDA <= Max EC Num in EUDA,不符合需要标红
    column = sheet[col]
    # 遍历列中的每个单元格
    for cell in column[9:rowline+1]:
        if sheet[f'{col}{cell.row}'].value == None or sheet[f'{col}{cell.row}'].value == 'NA' or sheet[f'{"P"}{cell.row}'].value == 'NA' or sheet[f'{"Q"}{cell.row}'].value == 'NA':
            continue
            # 合并前面的单元格
        if float(sheet[f'{col}{cell.row}'].value) < float(sheet[f'{"P"}{cell.row}'].value) or float(sheet[f'{col}{cell.row}'].value) > float(sheet[f'{"Q"}{cell.row}'].value):
            sheet[f'{col}{cell.row}'].fill = PatternFill(start_color="FF5959", end_color="FF5959", fill_type="solid")

def judgecol_six(sheet,col):#1.同一个样片，每一行都需关注：Total SLC EC in UDA + Total TLC EC in UDA = Total EC Num in UDA,不符合需要标红
    column = sheet[col]
    # 遍历列中的每个单元格
    for cell in column[9:rowline+1]:
        if sheet[f'{col}{cell.row}'].value == None or sheet[f'{col}{cell.row}'].value == 'NA' or sheet[f'{"AE"}{cell.row}'].value == 'NA' or sheet[f'{"AF"}{cell.row}'].value == 'NA':
            continue
            # 合并前面的单元格
        if float(sheet[f'{col}{cell.row}'].value) != float(sheet[f'{"AE"}{cell.row}'].value) + float(sheet[f'{"AF"}{cell.row}'].value):
            sheet[f'{col}{cell.row}'].fill = PatternFill(start_color="FF5959", end_color="FF5959", fill_type="solid")

def judgecol_seven(sheet,col_fir,col_sec,col_key):#1.同一个样片，每一行都需关注：I  Max EC Num in EUDA-Min EC Num in EUDA  I差值大于150，且同一个样片相邻两行 Total EC Num in EUDA的差值大于6的时候，需要标红
    column = sheet[col_fir]
    # 遍历列中的每个单元格
    for cell in column[9:rowline+1]:
        if sheet[f'{"E"}{cell.row}'].value == None:
            continue
        delnum = 0
        if sheet[f'{col_fir}{cell.row}'].value == None or sheet[f'{col_sec}{cell.row}'].value == None or sheet[f'{col_fir}{cell.row}'].value == 'NA' or sheet[f'{col_sec}{cell.row}'].value == 'NA':
            continue
        if cell.row != 10 and sheet[f'{col_key}{cell.row}'].value != 'NA' and sheet[f'{col_key}{cell.row-1}'].value != 'NA':
            delnum = abs(float(sheet[f'{col_key}{cell.row}'].value) - float(sheet[f'{col_key}{cell.row-1}'].value))
            # 合并前面的单元格
        if delnum > 6 and abs(float(sheet[f'{col_fir}{cell.row}'].value) - float(sheet[f'{col_sec}{cell.row}'].value)) > 150:
            sheet[f'{col_fir}{cell.row}'].fill = PatternFill(start_color="FF5959", end_color="FF5959", fill_type="solid")
            sheet[f'{col_sec}{cell.row}'].fill = PatternFill(start_color="FF5959", end_color="FF5959", fill_type="solid")

def judgecol_eight(sheet): #1..同一个样片，每一行都需关注： Min_EC_Num inUDA <= Ave EC Num in UDA <= Max EC Num in UDA,不符合需要标红2.同一个样片，每一行都需关注：当（delta Ave EC Num in UDA ）*磁盘容量*1024 / delta Cumulative Host Write 大于10，需要标红
    column = sheet['V']
    global Capacity
    # 遍历列中的每个单元格
    NAlist = ['U','T','V','C','AA']
    key = 0
    for cell in column[9:rowline+1]:
        key = 0
        if sheet[f'{"E"}{cell.row}'].value == None:
            key = 1
        for item in NAlist:
            if sheet[f'{item}{cell.row}'].value == 'NA':
                key = 1
        if key == 1:
            continue
        delnum_ave = 0
        delnum_cum = 0
        if float(sheet[f'{"U"}{cell.row}'].value) < float(sheet[f'{"T"}{cell.row}'].value) or float(sheet[f'{"V"}{cell.row}'].value) > float(sheet[f'{"U"}{cell.row}'].value):
            sheet[f'{"V"}{cell.row}'].fill = PatternFill(start_color="FF5959", end_color="FF5959", fill_type="solid")
        if cell.row != 10 and sheet[f'{"V"}{cell.row}'].value != 'NA' and sheet[f'{"V"}{cell.row-1}'].value != 'NA' and sheet[f'{"AA"}{cell.row}'].value != 'NA' and sheet[f'{"AA"}{cell.row-1}'].value != 'NA':
            delnum_ave = abs(float(sheet[f'{"V"}{cell.row}'].value) - float(sheet[f'{"V"}{cell.row-1}'].value))
            delnum_cum = abs(float(sheet[f'{"AA"}{cell.row}'].value) - float(sheet[f'{"AA"}{cell.row-1}'].value))
        if float(sheet[f'{"V"}{cell.row}'].value) < float(sheet[f'{"T"}{cell.row}'].value) or float(sheet[f'{"V"}{cell.row}'].value) > float(sheet[f'{"U"}{cell.row}'].value):
            sheet[f'{"V"}{cell.row}'].fill = PatternFill(start_color="FF5959", end_color="FF5959", fill_type="solid")
        if Capacity != 0 and delnum_cum != 0:
            if delnum_ave * Capacity * 1024 * 1024 / delnum_cum > 10:
                sheet[f'{"V"}{cell.row}'].fill = PatternFill(start_color="FF5959", end_color="FF5959", fill_type="solid")

def judgecol_nine(sheet,col_fir,col_sec):#1.同一个样片，每一行都需关注： 判断与Cumulative NAND Read增长差值是否一致相邻两行：delta History Host Total Read =  delta Cumulative NAND Read不一致，需要标红

    column = sheet[col_fir]
    # 遍历列中的每个单元格
    for cell in column[9:rowline+1]:
        if sheet[f'{col_fir}{cell.row-1}'].value == 'NA' or sheet[f'{col_sec}{cell.row-1}'].value == 'NA' or cell.row == 10:
            continue
        if sheet[f'{col_fir}{cell.row}'].value == 'NA' or sheet[f'{col_sec}{cell.row}'].value == 'NA':
            continue
        delnum_ave = abs(float(sheet[f'{col_fir}{cell.row}'].value) - float(sheet[f'{col_fir}{cell.row-1}'].value))
        delnum_host = abs(float(sheet[f'{col_sec}{cell.row}'].value) - float(sheet[f'{col_sec}{cell.row-1}'].value))
            # 合并前面的单元格
        if delnum_ave != delnum_host:
            sheet[f'{col_sec}{cell.row}'].fill = PatternFill(start_color="FF5959", end_color="FF5959", fill_type="solid")

def judgecol_ten(sheet):#1.同一个样片，每一行都需关注：Total SLC EC in UDA + Total TLC EC in UDA = Total EC Num in UDA,不符合需要标红
    column = sheet['F']
    align = Alignment(horizontal='right', vertical='center')
    waf_col = 'BD'
    slc_size = 100
    tlc_size = 300
    if 'EUDA_VB_block_size' in sheet[f'{"BD"}{9}'].value:
        waf_col = 'BF'
    # 遍历列中的每个单元格
    for cell in column[9:rowline+1]:
        sheet[f'{waf_col}{cell.row}'].alignment = align
        sheet[f'{waf_col}{cell.row}'].value = 1
        if sheet[f'{"AE"}{cell.row}'].value == 'NA' or sheet[f'{"BD"}{cell.row}'].value == 'NA' or sheet[f'{"BE"}{cell.row}'].value == 'NA' or sheet[f'{"AF"}{cell.row}'].value == 'NA' or sheet[f'{"X"}{cell.row}'].value == 'NA' or sheet[f'{"AE"}{cell.row -1}'].value == 'NA' or sheet[f'{"AF"}{cell.row -1}'].value == 'NA' or sheet[f'{"X"}{cell.row -1 }'].value == 'NA' or cell.row == 10:
            continue
        if sheet[f'{"BD"}{cell.row}'].value != None and float(sheet[f'{"BD"}{cell.row}'].value) > 0:
            slc_size = float(sheet[f'{"BD"}{cell.row}'].value)/1024/1024
        if sheet[f'{"BE"}{cell.row}'].value != None and float(sheet[f'{"BE"}{cell.row}'].value) > 0:
            tlc_size = float(sheet[f'{"BE"}{cell.row}'].value)/1024/1024
        delnum_slc = abs(float(sheet[f'{"AE"}{cell.row}'].value) - float(sheet[f'{"AE"}{cell.row-1}'].value))
        delnum_tlc = abs(float(sheet[f'{"AF"}{cell.row}'].value) - float(sheet[f'{"AF"}{cell.row-1}'].value))
        delnum_cum = abs(float(sheet[f'{"X"}{cell.row}'].value) - float(sheet[f'{"X"}{cell.row-1}'].value))
        if delnum_cum == 0 or (delnum_slc == 0 and delnum_tlc == 0) or delnum_slc < 0 or delnum_tlc < 0 or delnum_cum < 0:
            WAF = 1
        else:
            WAF = round((delnum_slc*slc_size + delnum_tlc*tlc_size)/delnum_cum,3)  
        sheet[f'{waf_col}{cell.row}'].value = WAF
            # 合并前面的单元格
        if WAF>10:
            sheet[f'{waf_col}{cell.row}'].fill = PatternFill(start_color="FF5959", end_color="FF5959", fill_type="solid")

def judgecol_elven(sheet):
    column = sheet['F']
    align = Alignment(horizontal='right', vertical='center')
    waf_col_1 = 'BE'
    waf_col_2 = 'BF'
    if 'EUDA_VB_block_size' in sheet[f'{"BD"}{9}'].value:
        waf_col_1 = 'BG'
        waf_col_2 = 'BH'
    # 遍历列中的每个单元格
    for cell in column[9:rowline+1]:
        sheet[f'{waf_col_1}{cell.row}'].alignment = align
        sheet[f'{waf_col_2}{cell.row}'].alignment = align
        if sheet[f'{"P"}{cell.row}'].value == None or sheet[f'{"Q"}{cell.row}'].value == None or sheet[f'{"T"}{cell.row}'].value == None or sheet[f'{"U"}{cell.row}'].value == None :
            continue
        if sheet[f'{"P"}{cell.row}'].value == 'NA' or sheet[f'{"Q"}{cell.row}'].value == 'NA' or sheet[f'{"T"}{cell.row}'].value == 'NA' or sheet[f'{"U"}{cell.row}'].value == 'NA' :
            continue
        EUDA_EC_Gap = float(sheet[f'{"Q"}{cell.row}'].value) - float(sheet[f'{"P"}{cell.row}'].value)
        UDA_EC_Gap = float(sheet[f'{"U"}{cell.row}'].value) - float(sheet[f'{"T"}{cell.row}'].value)
        sheet[f'{waf_col_1}{cell.row}'].value = EUDA_EC_Gap
        sheet[f'{waf_col_2}{cell.row}'].value = UDA_EC_Gap
        if EUDA_EC_Gap>10:
            sheet[f'{waf_col_1}{cell.row}'].fill = PatternFill(start_color="f6e424", end_color="f6e424", fill_type="solid")
        if EUDA_EC_Gap>15 or EUDA_EC_Gap<0:
            sheet[f'{waf_col_1}{cell.row}'].fill = PatternFill(start_color="ff5959", end_color="ff5959", fill_type="solid")
        if UDA_EC_Gap>65:
            sheet[f'{waf_col_2}{cell.row}'].fill = PatternFill(start_color="f6e424", end_color="f6e424", fill_type="solid")
        if UDA_EC_Gap>70 or UDA_EC_Gap<0:
            sheet[f'{waf_col_2}{cell.row}'].fill = PatternFill(start_color="ff5959", end_color="ff5959", fill_type="solid")