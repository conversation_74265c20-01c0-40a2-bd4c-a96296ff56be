﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ReleaseTW|Win32">
      <Configuration>ReleaseTW</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="ReleaseTW|x64">
      <Configuration>ReleaseTW</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{6BDDC9C2-4488-49D3-9B6C-B341D194CC53}</ProjectGuid>
    <RootNamespace>AterEx</RootNamespace>
    <Keyword>MFCProj</Keyword>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>Dynamic</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>true</UseDebugLibraries>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>Dynamic</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>Dynamic</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseTW|Win32'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>Dynamic</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>Dynamic</UseOfMfc>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseTW|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
    <CharacterSet>MultiByte</CharacterSet>
    <UseOfMfc>Dynamic</UseOfMfc>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseTW|Win32'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseTW|x64'" Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)bin\</OutDir>
    <IntDir>$(SolutionDir)obj\$(ProjectName)\$(Configuration)\</IntDir>
    <TargetName>$(ProjectName)_d</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <LinkIncremental>true</LinkIncremental>
    <OutDir>$(SolutionDir)bin</OutDir>
    <IntDir>$(SolutionDir)obj\$(ProjectName)\$(Configuration)\</IntDir>
    <TargetName>$(ProjectName)_d</TargetName>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)bin\</OutDir>
    <IntDir>$(SolutionDir)obj\$(ProjectName)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseTW|Win32'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)bin\</OutDir>
    <IntDir>$(SolutionDir)obj\$(ProjectName)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)bin</OutDir>
    <IntDir>$(SolutionDir)obj\$(ProjectName)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseTW|x64'">
    <LinkIncremental>false</LinkIncremental>
    <OutDir>$(SolutionDir)bin</OutDir>
    <IntDir>$(SolutionDir)obj\$(ProjectName)\$(Configuration)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_WINDOWS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(SolutionDir)Include\;$(SolutionDir)Include\pyinclude\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>$(SolutionDir)Lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>LibPublicwin32_d.lib;python38_d.lib;LibPhyDevice_d.lib;LibDecompression_d.lib;json_d.lib</AdditionalDependencies>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
      <ProgramDatabaseFile>$(SolutionDir)obj\$(ProjectName)\$(TargetName).pdb</ProgramDatabaseFile>
      <DelayLoadDLLs>UDiskMap_d.dll</DelayLoadDLLs>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <ValidateAllParameters>true</ValidateAllParameters>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0409</Culture>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <WarningLevel>Level4</WarningLevel>
      <Optimization>Disabled</Optimization>
      <PreprocessorDefinitions>WIN32;_WINDOWS;_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(SolutionDir)Include\;$(SolutionDir)Include\pyinclude\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <AdditionalLibraryDirectories>$(SolutionDir)Lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>LibPublicwin32_d.lib;python37_d.lib</AdditionalDependencies>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0409</Culture>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <Optimization>Disabled</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;_WINDOWS;SMART_DLL_API;NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(SolutionDir)Include\;$(SolutionDir)Include\pyinclude\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalLibraryDirectories>$(SolutionDir)Lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>LibPublicwin32.lib;python38.lib;LibPhyDevice.lib;LibDecompression.lib;json.lib</AdditionalDependencies>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
      <ProgramDatabaseFile>$(SolutionDir)obj\$(ProjectName)\$(TargetName).pdb</ProgramDatabaseFile>
      <DelayLoadDLLs>UDiskMap.dll</DelayLoadDLLs>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <ValidateAllParameters>true</ValidateAllParameters>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0409</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseTW|Win32'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <Optimization>Disabled</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;ATER_TW;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(SolutionDir)Include\;$(SolutionDir)Include\pyinclude\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalLibraryDirectories>$(SolutionDir)Lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>LibPublicwin32.lib;python38.lib;LibPhyDevice.lib;LibDecompression.lib;json.lib</AdditionalDependencies>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
      <ProgramDatabaseFile>$(SolutionDir)obj\$(ProjectName)\$(TargetName).pdb</ProgramDatabaseFile>
      <DelayLoadDLLs>UDiskMap.dll</DelayLoadDLLs>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <ValidateAllParameters>true</ValidateAllParameters>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0409</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(SolutionDir)Include\;$(SolutionDir)Include\pyinclude\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalLibraryDirectories>$(SolutionDir)Lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>LibPublicwin32.lib;python37.lib</AdditionalDependencies>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0409</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='ReleaseTW|x64'">
    <ClCompile>
      <WarningLevel>Level4</WarningLevel>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <Optimization>MaxSpeed</Optimization>
      <FunctionLevelLinking>true</FunctionLevelLinking>
      <IntrinsicFunctions>true</IntrinsicFunctions>
      <PreprocessorDefinitions>WIN32;_WINDOWS;NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(SolutionDir)Include\;$(SolutionDir)Include\pyinclude\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ClCompile>
    <Link>
      <SubSystem>Windows</SubSystem>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <EnableCOMDATFolding>true</EnableCOMDATFolding>
      <OptimizeReferences>true</OptimizeReferences>
      <AdditionalLibraryDirectories>$(SolutionDir)Lib;%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalDependencies>LibPublicwin32.lib;python37.lib</AdditionalDependencies>
      <UACExecutionLevel>RequireAdministrator</UACExecutionLevel>
    </Link>
    <Midl>
      <MkTypLibCompatible>false</MkTypLibCompatible>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </Midl>
    <ResourceCompile>
      <Culture>0x0409</Culture>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>$(IntDir);%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <None Include="ReadMe.txt" />
    <None Include="res\AterEx.ico" />
    <None Include="res\AterEx.rc2" />
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="ATADevInfoHelper.h" />
    <ClInclude Include="AterEx.h" />
    <ClInclude Include="AterExDlg.h" />
    <ClInclude Include="AutoLock.h" />
    <ClInclude Include="AutoStatus.h" />
    <ClInclude Include="BitMapButton.h" />
    <ClInclude Include="BurnInTest9_1.h" />
    <ClInclude Include="CaseAtomicPort.h" />
    <ClInclude Include="CaseAtomicPortDef.h" />
    <ClInclude Include="CFGMgr.h" />
    <ClInclude Include="CListCtrl\HeaderCtrlCl.h" />
    <ClInclude Include="CListCtrl\ListCtrlExClEdit.h" />
    <ClInclude Include="ColdStartTest.h" />
    <ClInclude Include="CommonInfo.h" />
    <ClInclude Include="cpu-z.h" />
    <ClInclude Include="CRC.h" />
    <ClInclude Include="DeviceInfoQuery.h" />
    <ClInclude Include="DialogTransarentNotify.h" />
    <ClInclude Include="EMMCConfigDlg.h" />
    <ClInclude Include="FillMaterialNoTool.h" />
    <ClInclude Include="Format.h" />
    <ClInclude Include="h2test.h" />
    <ClInclude Include="MainDlg.h" />
    <ClInclude Include="MultiPartitionTest1.h" />
    <ClInclude Include="MultiPartitionTest3Partition.h" />
    <ClInclude Include="pc_sleeper.h" />
    <ClInclude Include="ProErrDisk.h" />
    <ClInclude Include="PublicDlg.h" />
    <ClInclude Include="RaidFormat.h" />
    <ClInclude Include="RebootByTool.h" />
    <ClInclude Include="Resource.h" />
    <ClInclude Include="SATAGenManager.h" />
    <ClInclude Include="SATASpeedDownDetection.h" />
    <ClInclude Include="ServerReport.h" />
    <ClInclude Include="SkynetServiceCtrol.h" />
    <ClInclude Include="SleepByTool.h" />
    <ClInclude Include="SpeedDownDetectionDlg.h" />
    <ClInclude Include="StatusListBox\StatusListBox.h" />
    <ClInclude Include="stdafx.h" />
    <ClInclude Include="TabSheet.h" />
    <ClInclude Include="targetver.h" />
    <ClInclude Include="TemperatureMgr.h" />
    <ClInclude Include="TestDiskChecker.h" />
    <ClInclude Include="TestPlanDlg.h" />
    <ClInclude Include="TestUtility\BusHound.h" />
    <ClInclude Include="TestUtility\MultiBit9Test.h" />
    <ClInclude Include="TestUtility\MultiIOMonkeyTest.h" />
    <ClInclude Include="TestUtility\MultiStressTest.h" />
    <ClInclude Include="TestUtility\PowerController.h" />
    <ClInclude Include="TestUtility\RAID0Format.h" />
    <ClInclude Include="TestUtility\RAID1Format.h" />
    <ClInclude Include="TestUtility\RAID5Format.h" />
    <ClInclude Include="TestUtility\SleepWakeUp.h" />
    <ClInclude Include="TestUtility\VideoAutoRecord.h" />
    <ClInclude Include="tool_base.h" />
    <ClInclude Include="UDPTemperatureBoxCtrl\UDP.h" />
    <ClInclude Include="UDPTemperatureBoxCtrl\UDPTemperatureBoxCtrl.h" />
    <ClInclude Include="UpdateDlg.h" />
    <ClInclude Include="USBConfigDlg.h" />
    <ClInclude Include="Utility.h" />
    <ClInclude Include="VTETest.h" />
    <ClInclude Include="WSDPowerBox\CRC.h" />
    <ClInclude Include="WSDPowerBox\PowerMgr.h" />
    <ClInclude Include="WSDPowerBox\SerialPowerCtrl\SerialPort.h" />
    <ClInclude Include="WSDPowerBox\SerialPowerCtrl\SerialPowerCtrl.h" />
    <ClInclude Include="WSDPowerBox\SerialPowerCtrl\SerialPowerCtrlWSD.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="ATADevInfoHelper.cpp" />
    <ClCompile Include="AterEx.cpp" />
    <ClCompile Include="AterExDlg.cpp" />
    <ClCompile Include="AutoLock.cpp" />
    <ClCompile Include="AutoStatus.cpp" />
    <ClCompile Include="BitMapButton.cpp" />
    <ClCompile Include="BurnInTest9_1.cpp" />
    <ClCompile Include="CaseAtomicPort.cpp" />
    <ClCompile Include="CFGMgr.cpp" />
    <ClCompile Include="CListCtrl\HeaderCtrlCl.cpp" />
    <ClCompile Include="CListCtrl\ListCtrlExClEdit.cpp" />
    <ClCompile Include="ColdStartTest.cpp" />
    <ClCompile Include="CommonInfo.cpp" />
    <ClCompile Include="cpu-z.cpp" />
    <ClCompile Include="CRC.cpp" />
    <ClCompile Include="DeviceInfoQuery.cpp" />
    <ClCompile Include="DialogTransarentNotify.cpp" />
    <ClCompile Include="EMMCConfigDlg.cpp" />
    <ClCompile Include="FillMaterialNoTool.cpp" />
    <ClCompile Include="Format.cpp" />
    <ClCompile Include="h2test.cpp" />
    <ClCompile Include="MainDlg.cpp" />
    <ClCompile Include="MultiPartitionTest1.cpp" />
    <ClCompile Include="MultiPartitionTest3Partition.cpp" />
    <ClCompile Include="pc_sleeper.cpp" />
    <ClCompile Include="ProErrDisk.cpp" />
    <ClCompile Include="PublicDlg.cpp" />
    <ClCompile Include="RaidFormat.cpp" />
    <ClCompile Include="RebootByTool.cpp" />
    <ClCompile Include="SATAGenManager.cpp" />
    <ClCompile Include="SATASpeedDownDetection.cpp" />
    <ClCompile Include="ServerReport.cpp" />
    <ClCompile Include="SkynetServiceCtrol.cpp" />
    <ClCompile Include="SleepByTool.cpp" />
    <ClCompile Include="SpeedDownDetectionDlg.cpp" />
    <ClCompile Include="StatusListBox\StatusListBox.cpp" />
    <ClCompile Include="stdafx.cpp">
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ReleaseTW|Win32'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Create</PrecompiledHeader>
      <PrecompiledHeader Condition="'$(Configuration)|$(Platform)'=='ReleaseTW|x64'">Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="TabSheet.cpp" />
    <ClCompile Include="TemperatureMgr.cpp" />
    <ClCompile Include="TestDiskChecker.cpp" />
    <ClCompile Include="TestPlanDlg.cpp" />
    <ClCompile Include="TestUtility\BusHound.cpp" />
    <ClCompile Include="TestUtility\MultiBit9Test.cpp" />
    <ClCompile Include="TestUtility\MultiIOMonkeyTest.cpp" />
    <ClCompile Include="TestUtility\MultiStressTest.cpp" />
    <ClCompile Include="TestUtility\PowerController.cpp" />
    <ClCompile Include="TestUtility\RAID0Format.cpp" />
    <ClCompile Include="TestUtility\RAID1Format.cpp" />
    <ClCompile Include="TestUtility\RAID5Format.cpp" />
    <ClCompile Include="TestUtility\SleepWakeUp.cpp" />
    <ClCompile Include="TestUtility\VideoAutoRecord.cpp" />
    <ClCompile Include="tool_base.cpp" />
    <ClCompile Include="UDPTemperatureBoxCtrl\UDP.cpp" />
    <ClCompile Include="UDPTemperatureBoxCtrl\UDPTemperatureBoxCtrl.cpp" />
    <ClCompile Include="UpdateDlg.cpp" />
    <ClCompile Include="USBConfigDlg.cpp" />
    <ClCompile Include="Utility.cpp" />
    <ClCompile Include="VTETest.cpp" />
    <ClCompile Include="WSDPowerBox\CRC.cpp" />
    <ClCompile Include="WSDPowerBox\PowerMgr.cpp" />
    <ClCompile Include="WSDPowerBox\SerialPowerCtrl\SerialPort.cpp" />
    <ClCompile Include="WSDPowerBox\SerialPowerCtrl\SerialPowerCtrl.cpp" />
    <ClCompile Include="WSDPowerBox\SerialPowerCtrl\SerialPowerCtrlWSD.cpp" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="AterEx.rc" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
  <ProjectExtensions>
    <VisualStudio>
      <UserProperties RESOURCE_FILE="AterEx.rc" />
    </VisualStudio>
  </ProjectExtensions>
</Project>