from openpyxl.utils import get_column_letter
import configparser
import csv,time
import os,re
from openpyxl.utils import get_column_letter,column_index_from_string
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors
from datetime import datetime,timedelta
from xml.dom.minidom import parse,parseString

from lxml import etree
#gbk_parser = xml.dom.minidom.XMLParser(encoding='utf-8')
def Run(curpath, ws, alignment):
    ws.alignment = alignment

    xml_file = etree.parse(curpath)
    root_node = xml_file.getroot()
    planInfoNode = root_node

    dicCaseID = {}
    for planNode in planInfoNode:
        for caseNode in planNode:
            #获取case的名称
            caseID = caseNode.attrib['id']
            caseUrl = ''
            if 'url' in caseNode.attrib:
                caseUrl = caseNode.attrib['url']
            dicCaseID[caseID] = caseUrl #字典的key唯一

    #对字典排序输出到worksheet中
    listDicKeys = sorted(dicCaseID.keys())
    curLine = 1
    for key in listDicKeys:
        ws['%s%d'%('A', curLine)] = key #写用例ID
        ws['%s%d'%('B', curLine)] = dicCaseID[key] #写URL
        curLine += 1


    #with open(curpath,"r",encoding="gbk") as f:
        #datasource=f.read()
        #f.close()

    #domTree = parseString(datasource)
    #文档根元素
    #rootNode = domTree.documentElement

	# 所有顾客
    #planNodes = rootNode.childNodes
    #customers = rootNode.getElementsByTagName("customer")