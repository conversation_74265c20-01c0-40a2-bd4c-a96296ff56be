from ntpath import exists
import re
import os
import MTT_PV_Report
from openpyxl.styles import  Alignment
from openpyxl.chart import Line<PERSON><PERSON>,Reference

def parse_cmdq_data(log_content):
    dicCmdData = {}
    
    # 匹配cmdq区块的正则
    pattern = r'\*{4,}(cmdq\s+(write|read)\s+id ready time)\*{4,}(.*?)\*{20,}end\*{20,}'
    matches = re.finditer(pattern, log_content, re.DOTALL)
    
    for match in matches:
        cmd_type = match.group(2).lower()
        block_content = match.group(3)
        
        # 初始化数据结构
        cmd_data = {
            'data': [],
            '99%': 0,
            'max_time': 0
        }
        
        # 提取时间分布数据
        time_pattern = r'.*?(\d+~\d+|>=\d+)\s+ms:\s+(\d+)\s+([\d.]+)%'
        for line in block_content.split('\n'):
            # 处理99%数据
            if 'More than 99%' in line:
                cmd_data['99%'] = int(re.search(r'(\d+)\s+ms', line).group(1))
            # 处理max_time
            elif 'max_time:' in line:
                cmd_data['max_time'] = int(re.search(r'(\d+)\s+ms', line).group(1))
            # 处理时间分布数据
            time_match = re.search(time_pattern, line.strip())
            if time_match:
                time_range, count, percent = time_match.groups()
                cmd_data['data'].append([time_range, int(count), float(percent)])
        
        dicCmdData[cmd_type] = cmd_data
    
    return dicCmdData


def WriteOneFileResult2Excel(dicCmdData, ws, row, col, filefullpath):
    # 写入文件路径
    ws.cell(row=row, column=col).value = filefullpath
    
    # 初始化写入位置
    current_row = row + 1
    start_col = col
    
    # 处理read数据
    if dicCmdData.get('read') and dicCmdData['read']['data']:
        # 写入read标题
        ws.cell(row=current_row, column=start_col).value = 'Time Range(ms)'
        ws.cell(row=current_row, column=start_col+1).value = 'read id ready time'
        ws.cell(row=current_row, column=start_col+2).value = 'confidence'
        
        current_row += 1
        
        # 写入data内容
        for time_range, count, percent in dicCmdData['read']['data']:
            ws.cell(row=current_row, column=start_col).value = time_range
            ws.cell(row=current_row, column=start_col+1).value = count
            ws.cell(row=current_row, column=start_col+2).value = f'{percent}%'
            current_row += 1
        
        # 写入99%和max_time
        ws.cell(row=current_row, column=start_col).value = '99% ready time'
        ws.cell(row=current_row, column=start_col+1).value = dicCmdData['read']['99%']
        current_row += 1
        
        ws.cell(row=current_row, column=start_col).value = 'max_time'
        ws.cell(row=current_row, column=start_col+1).value = dicCmdData['read']['max_time']
    
    # 处理write数据（列偏移3列）
    write_start_col = col + 3
    if dicCmdData.get('write') and dicCmdData['write']['data']:
        # 写入write标题
        ws.cell(row=row+1, column=write_start_col).value = 'Time Range(ms)'
        ws.cell(row=row+1, column=write_start_col+1).value = 'write id ready time'
        ws.cell(row=row+1, column=write_start_col+2).value = 'confidence'
        
        write_row = row + 2
        
        # 写入data内容
        for time_range, count, percent in dicCmdData['write']['data']:
            ws.cell(row=write_row, column=write_start_col).value = time_range
            ws.cell(row=write_row, column=write_start_col+1).value = count
            ws.cell(row=write_row, column=write_start_col+2).value = f'{percent}%'
            write_row += 1
        
        # 写入99%和max_time
        ws.cell(row=write_row, column=write_start_col).value = '99% ready time'
        ws.cell(row=write_row, column=write_start_col+1).value = dicCmdData['write']['99%']
        write_row += 1
        
        ws.cell(row=write_row, column=write_start_col).value = 'max_time'
        ws.cell(row=write_row, column=write_start_col+1).value = dicCmdData['write']['max_time']

def GetPVDir():
    lPath = MTT_PV_Report.lPath
    listDir=[]
    key = 1
    for obj in lPath:
        strXu4Dir=os.path.join(obj,'XU4')
        for dirpath,dirnames,filenames in os.walk(strXu4Dir):
            for dir in dirnames:
                listDir.append([os.path.join(dirpath,dir),key])
        key += 1
    return listDir

def Run(filepath, wb):
    try:

        filelist = GetPVDir()
        g_filePosDic   = {}
        for file_path in filelist:
            for dirpath,dirnames,filenames in os.walk(file_path[0]):
                for item in filenames:
                    speed_top = []
                    speed_end = []
                    Type = 'x1'
                    if '128G' in file_path[0]:
                        Type = 'x2'
                    if '256G' in file_path[0]:
                        Type = 'x4'
                    if'_cmdq_' in item and '.log' in item:
                        col_type = item.split(' ')[-1].split('.')[0]
                        onefileResult = {}

                        if os.path.exists(os.path.join(file_path[0],item)) == False:
                            continue

                        with open(os.path.join(file_path[0],item), "r",errors='ignore',encoding='utf-8') as f:
                            log_content = f.read()
                        onefileResult = parse_cmdq_data(log_content)
                        #写数据到excel中
                        if Type not in g_filePosDic:
                            g_filePosDic[Type] = {}
                            g_filePosDic[Type]['row'] = 1
                            g_filePosDic[Type]['col'] = 1
                        sheetName =  Type + '_' + 'cmdq'


                        WriteOneFileResult2Excel(onefileResult, wb[sheetName], g_filePosDic[Type]['row'], g_filePosDic[Type]['col'], os.path.join(file_path[0],item))
                        g_filePosDic[Type]['row'] += 40

    except Exception as e:
        print("Error:", e)



# 修改后（正确）
if __name__ == '__main__':
    # 使用示例
    with open('G:\TOOLWORKS\AterEX\EmmcMTTReport\EmmcReport\YS8297_E09T(SEMMC)_010100_release_Release_2_128GB_20250401\XU4\IO_stress-COM23-A185_1749710830\Passed verify RV IO_stress test_cmdq_WR_durnig_dataTrans_random_not_align.log', 'r', encoding='utf-8') as f:
        log_content = f.read()
        
    result = parse_cmdq_data(log_content)
    print(result)