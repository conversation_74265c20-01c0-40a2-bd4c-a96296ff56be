import PublicFuc,re,os
import configparser
import csv,time
from openpyxl.drawing.image import Image

from Coroutines import startLine

config = configparser.RawConfigParser()
config1 = configparser.RawConfigParser()

def Run(curpath, workBook, alignment):
    ws = workBook['Card Reader Performance']
    ws.alignment = alignment
    # ProPlan9(curpath, ws)
    ProPlan10(curpath, ws)
    ProPlan11(curpath, ws)
    ProPlan12(curpath, ws)
    PublicFuc.WriteReportTime(ws,'E',3)
    PublicFuc.WriteReportOperator(ws,'J',3)
 
#1个样片多条记录
def LocalWriteData(worksheet, startLine, dataDic, colLst, keyLst,combineColList,combineRowNo,lineCnt = 1):
    curLine = startLine
    for key in dataDic:
        for line in dataDic[key]:
            for index,col in enumerate(colLst):
                try:
                    cellRow = curLine
                    if col in combineColList:
                        cellRow = combineRowNo
                    if 0 == index:
                        #第一列是编号，直接填key
                        worksheet['%s%d'%(col, cellRow)] = key
                    else:
                        worksheet['%s%d'%(col, cellRow)] = line[index-1]
                        if 'A5-A6' == keyLst[index-1] and line[index-1] != '':
                            if line[index-1] >= 400:
                                worksheet['%s%d'%(col, cellRow)].fill = warnFill
                    if 0 != index and 'SmartInfo'== keyLst[index-1]:
                        worksheet['%s%d'%(col, cellRow)].alignment = PublicFuc.alignmentNewLine
                    else:
                        worksheet['%s%d'%(col, cellRow)].alignment = PublicFuc.alignment
                #合并的单元格只能写一次，需要捕获异常
                except(AttributeError):
                    continue
            worksheet['%s%d'%('A', combineRowNo)] = PublicFuc.GetDate()
            curLine += lineCnt
            startLine += 1
    return startLine


#1个样片多条记录,并且多个样片preDataCnt指各个有效数据前面有些不需要的公共数据的个数
def LocalWriteDataEx(worksheet, startLine, dataDic,caseLst,colLst,preDataCnt,MaxDiskCnt = 1,sampleRowCnt = 3):
    sampleBeginLine = startLine
    #commonInfoLine = startLine #写公共消息的行
    writeDiskCnt = 0
    for key in dataDic:
        if writeDiskCnt >= MaxDiskCnt:
            break
        writeDiskCnt += 1
        curLine = sampleBeginLine
        #写公共信息
        commonInfo = dataDic[key]['common_info']
        worksheet['I%d'%(curLine)] = key #样品编号
        worksheet['D%d'%(curLine)] = commonInfo[0] #容量
        worksheet['J%d'%(curLine)] = commonInfo[1] #PC
        worksheet['K%d'%(curLine)] = commonInfo[2] #FILESystem
        worksheet['A%d'%(curLine)] = PublicFuc.GetDate()
        if commonInfo[3] != '':
            worksheet['A%d'%(curLine)] = commonInfo[3]
        #curLine += 1
        for caseKey in caseLst:
            if caseKey not in dataDic[key]:
                curLine += 1
                continue
            caseData = dataDic[key][caseKey][0] #这里其实简单的写的此样片第一条测试数据信息，样片测试多次的只能通过时间来解决。
            if caseData == None or caseData == []:
                curLine += 1
                continue
            for index,col in enumerate(colLst):
                try:
                    worksheet['%s%d'%(col, curLine)] = caseData[index+preDataCnt]                       
                    worksheet['%s%d'%(col, curLine)].alignment = PublicFuc.alignment
                except(AttributeError):
                    continue
            curLine += 1
        sampleBeginLine += sampleRowCnt
            

def LocalWriteDataEx2(worksheet, startLine, dataDic,caseLst,colLst,preDataCnt,MaxDiskCnt = 1):
    curLine = startLine
    #commonInfoLine = startLine #写公共消息的行
    writeDiskCnt = 0
    for key in dataDic:
        if writeDiskCnt >= MaxDiskCnt:
            break
        writeDiskCnt += 1
        #写公共信息
        commonInfo = dataDic[key]['common_info']
        worksheet['I%d'%(curLine)] = key #样品编号
        worksheet['D%d'%(curLine)] = commonInfo[0] #容量
        worksheet['J%d'%(curLine)] = commonInfo[1] #PC
        worksheet['A%d'%(curLine)] = PublicFuc.GetDate()
        if commonInfo[2] != '':
            worksheet['A%d'%(curLine)] = commonInfo[2]
        #curLine += 1
        for caseKey in caseLst:
            if caseKey not in dataDic[key]:
                curLine += 1
                continue
            caseData = dataDic[key][caseKey][0] #这里其实简单的写的此样片第一条测试数据信息，样片测试多次的只能通过时间来解决。
            if caseData == None or caseData == []:
                curLine += 1
                continue
            for index,col in enumerate(colLst):
                try:
                    worksheet['%s%d'%(col, curLine)] = caseData[index+preDataCnt]
                    worksheet['%s%d'%(col, curLine)].alignment = PublicFuc.alignment
                except(AttributeError):
                    continue
            curLine += 1


def proImt(curpath,caseLst,dicImtTotal):
    imtKey = ['Cap_MB', 'pc_no','512K_SEQ_100R_MiBps_BIN', '512B_SEQ_100W_MiBps_BIN','512K_SEQ_100R_Iops','512B_SEQ_100W_Iops','4KALG_Random_100R_MiBps_BIN','4KALG_Random_100W_MiBps_BIN','4KALG_Random_100R_Iops','4KALG_Random_100W_Iops']

    for case in caseLst:
        pattern = '.+\\\\Plan11\\\\T_EM_NA_C19\\\\%s\\\\\d{14}\\\\report.ini$'%case
        imtDic = {}
        PublicFuc.ReadQaIniData(curpath, pattern, imtDic, imtKey, '', 1, 0)
        for key in imtDic:
            if key not in dicImtTotal:
                dicImtTotal[key] = {}
                dicImtTotal[key][case] = imtDic[key]
                dicImtTotal[key]['common_info'] = []
                if len(imtDic[key]) >= 1 and len(imtDic[key][0]) >= 2:
                    dicImtTotal[key]['common_info'] = imtDic[key][0][:2]
                    dateDic = GetPlanStartDateIdxBySampleNo(pattern)
                    if key in dateDic:
                        dicImtTotal[key]['common_info'].append(dateDic[key])
            else:
                if case not in dicImtTotal[key] or dicImtTotal[key][case] == None or dicImtTotal[key][case] == {} or dicImtTotal[key][case] == []:
                    dicImtTotal[key][case] = imtDic[key]
                    if dicImtTotal[key]['common_info'] == None or dicImtTotal[key]['common_info'] == []:
                        if len(imtDic[key]) >= 1 and len(imtDic[key][0]) >= 2:
                            dicImtTotal[key]['common_info'] = imtDic[key][0][:2]
                            dateDic = GetPlanStartDateIdxBySampleNo(pattern)
                            if key in dateDic:
                                dicImtTotal[key]['common_info'].append(dateDic[key])

def proMarsRndWrAndRd(curpath, ws, pattern, startLine,picCol):
    marsRWRKey = ['Cap','mms_pc','TEST_RESULT']
    marsRWRCol = ['I','D','J','AC']
    marsRWRDic = {}
    PublicFuc.ReadMarsIniDataWithPicture(curpath, pattern, marsRWRDic, 'RndWrAndRd', 'MRWR', marsRWRKey, '',0)
    for key in marsRWRDic:
        dataDic = marsRWRDic[key]
        if 'MRWR' in dataDic:
            dataList = dataDic['MRWR']
            if dataList[-1].upper() == 'TRUE':
                dataList[-1] = 'PASS'
            else:
                dataList[-1] = 'FAIL'
    imgWidth = 526
    imgHeight = 476
    WriteMarsDataAndImage(ws, startLine, marsRWRDic, marsRWRCol, picCol,'MRWR',imgWidth, imgHeight)

def proCdm(curpath,planName,caseLst,dicCdmTotal):
    cdmKey = ['Cap_MB','pc_no','format', 'SEQ1MQ8T1_Read', 'SEQ1MQ8T1_Write','SEQ1MQ1T1_Read','SEQ1MQ1T1_Write','RND4KQ32T1_Read','RND4KQ32T1_Write','RND4KQ1T1_Read','RND4KQ1T1_Write']

    for case in caseLst:
        pattern = '.+\\\\%s\\\\T_EM_NA_C17\\\\%s\\\\\d{14}\\\\report.ini$'%(planName,case)
        cdmDic = {}
        PublicFuc.ReadQaIniData(curpath, pattern, cdmDic, cdmKey, '',10,10)
        for key in cdmDic:
            if key not in dicCdmTotal:
                dicCdmTotal[key] = {}
                dicCdmTotal[key][case] = cdmDic[key]
                dicCdmTotal[key]['common_info'] = []                
                if len(cdmDic[key]) >= 1 and len(cdmDic[key][0]) >= 3:
                    dicCdmTotal[key]['common_info'] = cdmDic[key][0][:3]
                    dateDic = GetPlanStartDateIdxBySampleNo(pattern)
                    if key in dateDic:
                        dicCdmTotal[key]['common_info'].append(dateDic[key])
            else:
                if case not in dicCdmTotal[key] or dicCdmTotal[key][case] == None or dicCdmTotal[key][case] == {} or dicCdmTotal[key][case] == []:
                    dicCdmTotal[key][case] = cdmDic[key]
                    if dicCdmTotal[key]['common_info'] == None or dicCdmTotal[key]['common_info'] == []:
                        if len(cdmDic[key]) >= 1 and len(cdmDic[key][0]) >= 3:
                            dicCdmTotal[key]['common_info'] = cdmDic[key][0][:3]
                            dateDic = GetPlanStartDateIdxBySampleNo(pattern)
                            if key in dateDic:
                                dicCdmTotal[key]['common_info'].append(dateDic[key])

def proCdmWithPlan(curpath, ws, startLine, caseLst,colList,planName,combineRowNo):
    cdmKey = ['Cap_MB','pc_no','format', 'SEQ1MQ8T1_Read', 'SEQ1MQ8T1_Write','SEQ1MQ1T1_Read','SEQ1MQ1T1_Write','RND4KQ32T1_Read','RND4KQ32T1_Write','RND4KQ1T1_Read','RND4KQ1T1_Write']
    cdmCol = colList
    strDate = ''
    for case in caseLst:
        pattern = '.+\\\\'+planName+'\\\\T_EM_NA_C17\\\\%s\\\\\d{14}\\\\report.ini$'%case
        cdmDic = {}
        PublicFuc.ReadQaIniData(curpath, pattern, cdmDic, cdmKey, '')
        if strDate == '':
            strDate = GetPlanStartDate(pattern)
        combineColLst = ['I','D','J','K']
        LocalWriteData(ws, startLine, cdmDic, cdmCol, cdmKey,combineColLst,combineRowNo)
        startLine+=1
        if strDate != '':
            ws['%s%d'%('A', 69)] = strDate #获取Plan开始的时间

def proH2testw(curpath,planName,caseLst,dicTotal):
    h2Key = ['Cap_MB','pc_no','format','write speed','read speed']
    for case in caseLst:
        pattern = '.+\\\\%s\\\\T_EM_NA_C18\\\\%s\\\\\d{14}\\\\report.ini$'%(planName,case)
        tempDic = {}
        PublicFuc.ReadQaIniData(curpath, pattern, tempDic, h2Key, '',3,3)
        for key in tempDic:
            if key not in dicTotal:
                dicTotal[key] = {}
                dicTotal[key][case] = tempDic[key]
                dicTotal[key]['common_info'] = []                
                if len(tempDic[key]) >= 1 and len(tempDic[key][0]) >= 3:
                    dicTotal[key]['common_info'] = tempDic[key][0][:3]
                    dateDic = GetPlanStartDateIdxBySampleNo(pattern)
                    if key in dateDic:
                        dicTotal[key]['common_info'].append(dateDic[key])
            else:
                if case not in dicTotal[key] or dicTotal[key][case] == None or dicTotal[key][case] == {} or dicTotal[key][case] == []:
                    dicTotal[key][case] = tempDic[key]
                    if dicTotal[key]['common_info'] == None or dicTotal[key]['common_info'] == []:
                        if len(tempDic[key]) >= 1 and len(tempDic[key][0]) >= 3:
                            dicTotal[key]['common_info'] = tempDic[key][0][:3]
                            dateDic = GetPlanStartDateIdxBySampleNo(pattern)
                            if key in dateDic:
                                dicTotal[key]['common_info'].append(dateDic[key])
        

def proH2testwWithPlan(curpath, ws, startLine, caseLst,colList,planName,combineRowNo):
    h2Key = ['Cap_MB','pc_no','format','write speed','read speed']
    h2Col = colList
    strDate = ''
    for case in caseLst:
        pattern = '.+\\\\'+planName+'\\\\T_EM_NA_C18\\\\%s\\\\\d{14}\\\\report.ini$'%case
        h2Dic = {}
        PublicFuc.ReadQaIniData(curpath, pattern, h2Dic, h2Key, '')
        if strDate == '':
            strDate = GetPlanStartDate(pattern)
        combineColLst = ['I','D','J','K']
        LocalWriteData(ws, startLine, h2Dic, h2Col, h2Key,combineColLst,combineRowNo)
        startLine+=1
        if strDate != '':
            ws['%s%d'%('A', combineRowNo)] = strDate #获取Plan开始的时间

def proMarsH2(curpath, ws, pattern, startLine,picCol):
    marsH2Key = ['Cap','mms_pc','format'] #mars不清楚是否已经有生成格式化类型。
    marsH2Col = ['I','D','J','K']
    marsH2Dic = {}
    PublicFuc.ReadMarsIniDataWithPicture_CAP_MB(curpath, pattern, marsH2Dic, 'AT_H2', 'MH2', marsH2Key, 'Mars.bmp',0)
   
    imgWidth = 500
    imgHeight = 446
    WriteMarsDataAndImage(ws, startLine, marsH2Dic, marsH2Col, picCol,'MH2',imgWidth, imgHeight)

def WriteMarsDataAndImage(worksheet, startLine, dataDic, colLst, imageCol, caseName,imgWidth, imgHeight):
    curLine =  startLine
    for key in dataDic:
        if caseName not in dataDic[key]:
            continue
        line = dataDic[key][caseName]
        for index,col in enumerate(colLst):
            try:
                if 0 == index:
                    worksheet['%s%d'%(col, curLine)] = key
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
            #合并的单元格只能写一次，需要捕获异常
            except(AttributeError):
                continue
        # 列表最后一项是图片路径
        if '' != line[-1] and line[-1].find('\\') != -1:
            img = Image(line[-1])
            img.width = imgWidth
            img.height = imgHeight
            worksheet.add_image(img, '%s%d'%(imageCol, startLine))
        worksheet['%s%d'%('A', curLine)] = PublicFuc.GetDate()

def proAtto(curpath, ws, pattern, startLine,picCol):
    attoKey = ['Cap_MB']
    attoCol = ['I','D']
    attoDic = {}
    PublicFuc.ReadQaIniData(curpath, pattern, attoDic, attoKey, 'ATTO4_0_MBps.bmp')
    imgWidth = 320
    imgHeight = 295
    PublicFuc.WriteDataAndImage(ws, startLine, attoDic, attoCol, picCol, imgWidth, imgHeight)

def proAttoWithData(curpath, caseLst, dicAttoTotal):
    attoKey = ['Cap_MB','pc_no','format','512 B_Write','1 KB_Write','2 KB_Write','4 KB_Write','8 KB_Write','16 KB_Write','32 KB_Write','64 KB_Write','128 KB_Write','256 KB_Write',
               '512 KB_Write','1 MB_Write','2 MB_Write','4 MB_Write','8 MB_Write','12 MB_Write','16 MB_Write','24 MB_Write','32 MB_Write','48 MB_Write','64 MB_Write','Cap_MB','pc_no','format',
               '512 B_Read','1 KB_Read','2 KB_Read','4 KB_Read','8 KB_Read','16 KB_Read','32 KB_Read','64 KB_Read','128 KB_Read','256 KB_Read','512 KB_Read','1 MB_Read','2 MB_Read','4 MB_Read',
               '8 MB_Read','12 MB_Read','16 MB_Read','24 MB_Read','32 MB_Read','48 MB_Read','64 MB_Read']
    for case in caseLst:
        pattern = '.+\\\\Plan11\\\\T_EM_NA_C21\\\\%s\\\\\d{14}\\\\report.ini$'%case
        dicAtto = {}
        PublicFuc.ReadQaIniData(curpath, pattern, dicAtto, attoKey, '',1,0)
        for key in dicAtto:
            if key not in dicAttoTotal:
                dicAttoTotal[key] = {}
                dicAttoTotal[key][case] = dicAtto[key]
                dicAttoTotal[key]['common_info'] = []
                if len(dicAtto[key]) >= 1 and len(dicAtto[key][0]) >= 3:
                    dicAttoTotal[key]['common_info'] = dicAtto[key][0][:3]
                    dateDic = GetPlanStartDateIdxBySampleNo(pattern)
                    if key in dateDic:
                        dicAttoTotal[key]['common_info'].append(dateDic[key])
            else:
                if case not in dicAttoTotal[key] or dicAttoTotal[key][case] == None or dicAttoTotal[key][case] == {} or dicAttoTotal[key][case] == []:
                    dicAttoTotal[key][case] = dicAtto[key]
                    if dicAttoTotal[key]['common_info'] == None or dicAttoTotal[key]['common_info'] == []:
                        if len(dicAtto[key]) >= 1 and len(dicAtto[key][0]) >= 3:
                            dicAttoTotal[key]['common_info'] = dicAtto[key][0][:3]
                            dateDic = GetPlanStartDateIdxBySampleNo(pattern)
                            if key in dateDic:
                                dicAttoTotal[key]['common_info'].append(dateDic[key])

def proHdtune(curpath, caseLst, dicHteTotal):
    hdKey = ['Cap_MB','pc_no','format','min spped','max spped','avg spped','acess time','sundden trans rate','cpu usage']
    for case in caseLst:
        readCase = case[0]
        pattern = '.+\\\\Plan11\\\\T_EM_NA_C20\\\\%s\\\\\d{14}\\\\report.ini$'%readCase
        hdRDic = {}
        PublicFuc.ReadQaIniData(curpath, pattern, hdRDic, hdKey, '',1,0)
        writeCase = case[1]
        pattern = '.+\\\\Plan11\\\\T_EM_NA_C20\\\\%s\\\\\d{14}\\\\report.ini$'%writeCase
        hdWDic = {}
        PublicFuc.ReadQaIniData(curpath, pattern, hdWDic, hdKey, '', 1, 0)
        rwDic = {}
        for key in hdRDic:
            if key not in hdWDic:
                continue
            readRecord = hdRDic[key][0]
            writeRecord = hdWDic[key][0]
            # 容量等共同表头信息只需要记录一次
            readRecord.extend(writeRecord[3:])
            rwDic[key] = []
            rwDic[key].append(readRecord)
        for key in rwDic:
            if key not in dicHteTotal:
                dicHteTotal[key] = {}
                dicHteTotal[key][readCase] = rwDic[key]
                dicHteTotal[key]['common_info'] = []
                if len(rwDic[key]) >= 1 and len(rwDic[key][0]) >= 3:
                    dicHteTotal[key]['common_info'] = rwDic[key][0][:3]
                    dateDic = GetPlanStartDateIdxBySampleNo(pattern)
                    if key in dateDic:
                        dicHteTotal[key]['common_info'].append(dateDic[key])
            else:
                if readCase not in dicHteTotal[key] or dicHteTotal[key][readCase] == None or dicHteTotal[key][readCase] == {} or dicHteTotal[key][readCase] == []:
                    dicHteTotal[key][readCase] = rwDic[key]
                    if dicHteTotal[key]['common_info'] == None or dicHteTotal[key]['common_info'] == []:
                        if len(rwDic[key]) >= 1 and len(rwDic[key][0]) >= 3:
                            dicHteTotal[key]['common_info'] = rwDic[key][0][:3]
                            dateDic = GetPlanStartDateIdxBySampleNo(pattern)
                            if key in dateDic:
                                dicHteTotal[key]['common_info'].append(dateDic[key])


#获取真实的测试plan启动日期
def GetPlanStartDate(pattern):
    date = ''
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        else:
            idx = file.find('\\Plan')
            if idx != -1:
                commoninfofile = file[:idx]
                commoninfofile += '\\common_info.ini'
                if os.path.exists(commoninfofile):
                    config.clear()
                    config.read(commoninfofile,encoding = 'gbk')
                    if 'COMMON_INFO' not in config.sections():
                        continue
                    if 'PLAN_TEST_BEGIN_TIME' in config['COMMON_INFO']:
                        strData = config['COMMON_INFO']['PLAN_TEST_BEGIN_TIME']
                        if len(strData) >= 8:
                            strYear = strData[:4]
                            strMonth = strData[4:6]
                            strDay = strData[6:8]
                            date = strYear + '-' + strMonth + '-' + strDay
                            break
    return date

#依据文件路径获取启动测试开始日期
def GetPlanStartDateByFileName(file):
    startDate = ''
    idx = file.find('\\Plan')
    if idx != -1:
        commoninfofile = file[:idx]
        commoninfofile += '\\common_info.ini'
        if os.path.exists(commoninfofile):
            config.clear()
            config.read(commoninfofile,encoding = 'gbk')
            if 'COMMON_INFO' not in config.sections():
                startDate = ''
            if 'PLAN_TEST_BEGIN_TIME' in config['COMMON_INFO']:
                strData = config['COMMON_INFO']['PLAN_TEST_BEGIN_TIME']
                if len(strData) >= 8:
                    strYear = strData[:4]
                    strMonth = strData[4:6]
                    strDay = strData[6:8]
                    startDate = strYear + '-' + strMonth + '-' + strDay
    return startDate
                    

#获取真实的测试plan启动日期
def GetPlanStartDateIdxBySampleNo(pattern):
    date = ''
    dataDic = {}
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        else:
            config1.clear()
            config1.read(file,encoding = 'gbk')
            for sec in config1.sections():
                if sec not in dataDic:
                    dataDic[sec] = ''
                date = GetPlanStartDateByFileName(file)
                if date != '':
                    dataDic[sec] = date               
    return dataDic
                    

# def ProPlan9(curpath, ws):
    #marsH2
    #startLine = 7
    #pattern = '.+\\\\Plan11\\\\T_EM_NA_C8\\\\Mars_H2_1\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    #proMarsH2(curpath, ws, pattern, startLine,'M')
    #strDate = GetPlanStartDate(pattern)
    #pattern = '.+\\\\Plan11\\\\T_EM_NA_C8\\\\Mars_H2_2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    #proMarsH2(curpath, ws, pattern, startLine,'T')
    #if strDate == '':
    #    strDate = GetPlanStartDate(pattern)
    #strDate = GetPlanStartDate(pattern)
    #pattern = '.+\\\\Plan11\\\\T_EM_NA_C8\\\\Mars_H2_3\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    #proMarsH2(curpath, ws, pattern, startLine,'AA')
    #if strDate == '':
    #    strDate = GetPlanStartDate(pattern)

    #if strDate != '':
    #    ws['%s%d'%('A', startLine)] = strDate #获取Plan开始的时间

def ProPlan11(curpath, ws):
    #iometer
    dicImtTotal = {}
    caseLst1 = ['IOmeter_1G_1_SLC', 'IOmeter_1G_2_SLC', 'IOmeter_1G_3_SLC']
    caseLst2 = ['IOmeter_1G_1', 'IOmeter_1G_2', 'IOmeter_1G_3']
    caseLst3 = ['IOmeter_FULL_1', 'IOmeter_FULL_2', 'IOmeter_FULL_3']
    caseLst = caseLst1 + caseLst2 + caseLst3
    proImt(curpath, caseLst, dicImtTotal)

    startLine = 41
    imtCol = ['M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T']
    LocalWriteDataEx2(ws, startLine, dicImtTotal, caseLst1, imtCol, 2, 3)
    imtCol = ['U', 'V', 'W', 'X', 'Y', 'Z', 'AA', 'AB']
    LocalWriteDataEx2(ws, startLine, dicImtTotal, caseLst2, imtCol, 2, 3)
    imtCol = ['AD', 'AE', 'AF', 'AG', 'AH', 'AI', 'AJ', 'AK']
    LocalWriteDataEx2(ws, startLine, dicImtTotal, caseLst3, imtCol, 2, 3)

    patterns = []
    for case in caseLst:
        pattern = '.+\\\\%s\\\\T_EM_NA_C19\\\\%s\\\\\d{14}\\\\result.txt$' % ('Plan11', case)
        patterns.append(pattern)
    PublicFuc.GetTimeRange(curpath, patterns, 'Plan11_Iometer')

    #hdtune
    dicHteTotal = {}
    caseLst1 = [['HDTune_Read_1', 'HDTune_Write_1'], ['HDTune_Read_2', 'HDTune_Write_2'], ['HDTune_Read_3', 'HDTune_Write_3']]
    caseLst2 = [['HDTune_Read_1_SLC', 'HDTune_Write_1_SLC'], ['HDTune_Read_2_SLC', 'HDTune_Write_2_SLC'], ['HDTune_Read_3_SLC', 'HDTune_Write_3_SLC']]
    caseLst = caseLst1 + caseLst2
    proHdtune(curpath, caseLst, dicHteTotal)

    startLine = 55
    hteCol = ['M','N','O','P','Q','R','S','T','U','V','W','X']
    LocalWriteDataEx(ws, startLine, dicHteTotal, ['HDTune_Read_1_SLC', 'HDTune_Read_2_SLC', 'HDTune_Read_3_SLC'], hteCol, 3, 3)
    hteCol = ['Y','Z','AA','AB','AC','AD','AE','AF','AG','AH','AI','AJ']
    LocalWriteDataEx(ws, startLine, dicHteTotal, ['HDTune_Read_1', 'HDTune_Read_2', 'HDTune_Read_3'], hteCol, 3, 3)

    patterns = []
    for case in caseLst:
        pattern = '.+\\\\%s\\\\T_EM_NA_C20\\\\%s\\\\\d{14}\\\\result.txt$' % ('Plan11', case[0])
        patterns.append(pattern)
        pattern = '.+\\\\%s\\\\T_EM_NA_C20\\\\%s\\\\\d{14}\\\\result.txt$' % ('Plan11', case[1])
        patterns.append(pattern)
    PublicFuc.GetTimeRange(curpath, patterns, 'Plan11_HDTune')

    #atto
    dicAttoTotal = {}
    caseLst1 = ['ATTO_1', 'ATTO_2', 'ATTO_3']
    caseLst2 = ['ATTO_1_SLC', 'ATTO_2_SLC', 'ATTO_3_SLC']
    caseLst = caseLst1 + caseLst2
    proAttoWithData(curpath, caseLst, dicAttoTotal)

    dicAttoRead = {}
    dicAttoWrite = {}
    for sampleId, cases in dicAttoTotal.items():
        dicAttoRead[sampleId] = {}
        dicAttoWrite[sampleId] = {}
        for case, data_list in cases.items():
            if case != 'common_info':
                if len(data_list) >=1:
                    dicAttoRead[sampleId][case] = [data_list[0][:24]]
                    dicAttoWrite[sampleId][case] = [data_list[0][24:]]
                else:
                    dicAttoRead[sampleId][case] = [[]]
                    dicAttoWrite[sampleId][case] = [[]]
            else:
                dicAttoRead[sampleId][case] = data_list
                dicAttoWrite[sampleId][case] = data_list

    startLine = 68
    attoCol = ['M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z','AA','AB','AC','AD','AE','AF','AG']
    LocalWriteDataEx(ws, startLine, dicAttoRead, caseLst2, attoCol, 3, 3)
    attoCol = ['AH','AI','AJ','AK','AL','AM','AN','AO','AP','AQ','AR','AS','AT','AU','AV','AW','AX','AY','AZ','BA','BB']
    LocalWriteDataEx(ws, startLine, dicAttoRead, caseLst1, attoCol, 3, 3)

    startLine = 80
    attoCol = ['M','N','O','P','Q','R','S','T','U','V','W','X','Y','Z','AA','AB','AC','AD','AE','AF','AG']
    LocalWriteDataEx(ws, startLine, dicAttoWrite, caseLst2, attoCol, 3, 3)
    attoCol = ['AH','AI','AJ','AK','AL','AM','AN','AO','AP','AQ','AR','AS','AT','AU','AV','AW','AX','AY','AZ','BA','BB']
    LocalWriteDataEx(ws, startLine, dicAttoWrite, caseLst1, attoCol, 3, 3)

    patterns = []
    for case in caseLst:
        pattern = '.+\\\\%s\\\\T_EM_NA_C21\\\\%s\\\\\d{14}\\\\result.txt$' % ('Plan11', case)
        patterns.append(pattern)
    PublicFuc.GetTimeRange(curpath, patterns, 'Plan11_Atto')

def ProPlan10(curpath, ws):
    #cdm
    dicCdmTotal = {} #CDM所有层最上层字典
    caseLst1 = ['CDM_SLC_1', 'CDM_SLC_2', 'CDM_SLC_3']
    caseLst2 = ['CDM_TLC_1', 'CDM_TLC_2', 'CDM_TLC_3']
    caseLst3= ['CDM_After_BIT24_1', 'CDM_After_BIT24_2', 'CDM_After_BIT24_3']
    caseLst4= ['CDM_After_BIT48_1', 'CDM_After_BIT48_2', 'CDM_After_BIT48_3']
    caseLst = caseLst1 + caseLst2 + caseLst3 + caseLst4 
    proCdm(curpath,'Plan10',caseLst,dicCdmTotal)

    startLine = 15
    cdmCol = ['M', 'N','O','P','Q','R','S','T']
    LocalWriteDataEx(ws, startLine, dicCdmTotal,caseLst1,cdmCol,3,3)
    cdmCol = ['V','W', 'X','Y','Z','AA','AB','AC']
    LocalWriteDataEx(ws, startLine, dicCdmTotal,caseLst2,cdmCol,3,3)
    cdmCol = ['AE','AF','AG','AH','AI','AJ','AK','AL']
    LocalWriteDataEx(ws, startLine, dicCdmTotal,caseLst3,cdmCol,3,3)
    cdmCol = ['AN','AO','AP','AQ','AR','AS','AT','AU']
    LocalWriteDataEx(ws, startLine, dicCdmTotal,caseLst4,cdmCol,3,3)

    patterns = []
    for case in caseLst1 + caseLst2:
        pattern = '.+\\\\%s\\\\T_EM_NA_C17\\\\%s\\\\\d{14}\\\\result.txt$' % ('Plan10', case)
        patterns.append(pattern)
    PublicFuc.GetTimeRange(curpath, patterns, 'Plan10_CDM')

    #h2testw
    startLine = 28
    dicH2Total = {} #H2所有层最上层字典
    h2Col = ['M','N']
    caseLst1 = ['H2testw_SLC_1', 'H2testw_SLC_2', 'H2testw_SLC_3']
    proH2testw(curpath, 'Plan10',caseLst1,dicH2Total)
    LocalWriteDataEx(ws, startLine, dicH2Total,caseLst1,h2Col,3,3)

    h2Col = ['O','P']
    caseLst2 = ['H2testw_TLC_1', 'H2testw_TLC_2', 'H2testw_TLC_Del50']
    proH2testw(curpath, 'Plan10',caseLst2,dicH2Total)
    LocalWriteDataEx(ws, startLine, dicH2Total,caseLst2,h2Col,3,3)

    patterns = []
    for case in caseLst1 + caseLst2:
        pattern = '.+\\\\%s\\\\T_EM_NA_C18\\\\%s\\\\\d{14}\\\\result.txt$' % ('Plan10', case)
        patterns.append(pattern)
    PublicFuc.GetTimeRange(curpath, patterns, 'Plan10_H2Test')

def ProPlan12(curpath, ws):
    #cdm
    dicCdmTotal = {}
    caseLst1 = ['CDM_SLC_1', 'CDM_SLC_2', 'CDM_SLC_3', 'CDM_SLC_4', 'CDM_SLC_5', 'CDM_SLC_6', 'CDM_SLC_7', 'CDM_SLC_8', 'CDM_SLC_9', 'CDM_SLC_10']
    caseLst2 = ['CDM_TLC_1', 'CDM_TLC_2', 'CDM_TLC_3', 'CDM_TLC_4', 'CDM_TLC_5', 'CDM_TLC_6', 'CDM_TLC_7', 'CDM_TLC_8', 'CDM_TLC_9', 'CDM_TLC_10']
    caseLst = caseLst1 + caseLst2
    proCdm(curpath,'Plan12',caseLst,dicCdmTotal)

    startLine = 93
    cdmCol = ['M','N','O','P','Q','R','S','T']
    LocalWriteDataEx(ws, startLine, dicCdmTotal,caseLst1,cdmCol,3,10,10)
    cdmCol = ['W','X','Y','Z','AA','AB','AC','AD']
    LocalWriteDataEx(ws, startLine, dicCdmTotal,caseLst2,cdmCol,3,10,10)

    patterns = []
    for case in caseLst:
        pattern = '.+\\\\%s\\\\T_EM_NA_C17\\\\%s\\\\\d{14}\\\\result.txt$' % ('Plan12', case)
        patterns.append(pattern)
    PublicFuc.GetTimeRange(curpath, patterns, 'Plan12_CDM')

    #h2testw
    dich2TestTotal = {}
    caseLst = ['H2testw_TLC_1', 'H2testw_TLC_2', 'H2testw_TLC_Del50']
    h2Col = ['U','V']
    proH2testw(curpath, 'Plan12',caseLst,dich2TestTotal)
    LocalWriteDataEx(ws, startLine, dich2TestTotal,caseLst,h2Col,3,3,10)

    # #cdm
    # startLine = 69
    # caseLst = ['CDM_SLC_1', 'CDM_SLC_2', 'CDM_SLC_3', 'CDM_SLC_4', 'CDM_SLC_5', 'CDM_SLC_6', 'CDM_SLC_7', 'CDM_SLC_8', 'CDM_SLC_9', 'CDM_SLC_10']
    # cdmCol = ['I','D','J','K','M', 'N','O','P','Q','R','S','T']
    # proCdmWithPlan(curpath, ws, startLine, caseLst,cdmCol,'Plan12',startLine)
    # startLine = 69
    # caseLst = ['CDM_TLC_1', 'CDM_TLC_2', 'CDM_TLC_3', 'CDM_TLC_4', 'CDM_TLC_5', 'CDM_TLC_6', 'CDM_TLC_7', 'CDM_TLC_8', 'CDM_TLC_9', 'CDM_TLC_10']
    # cdmCol = ['I','D','J','K','W', 'X','Y','Z','AA','AB','AC','AD']
    # proCdmWithPlan(curpath, ws, startLine, caseLst,cdmCol,'Plan12',startLine)
    ##h2testw
    # startLine = 69
    # h2Col = ['I','D','J','K','U','V']
    # caseLst = ['H2testw_TLC_1', 'H2testw_TLC_2', 'H2testw_TLC_Del50']
    # proH2testwWithPlan(curpath, ws, startLine, caseLst,h2Col,'Plan12',startLine)