﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<!-- saved from url=(0029)http://www.helpandmanual.com/ -->
<head>
   <title>Warnings and Errors</title>
   <meta name="generator" content="Help &amp; Manual" />
   <meta name="keywords" content="" />
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <meta http-equiv="Content-Style-Type" content="text/css" />
   <link type="text/css" href="default.css" rel="stylesheet" />
<script type="text/javascript" src="helpman_topicinit.js"></script>
<!-- Redirect browser to frame page if page is not in the content frame. -->
<script type="text/javascript">
<!--
if (location.search.lastIndexOf("toc=0")<=0) {
  if (parent.frames.length==0) { parent.location.href="index.html?warnings_and_errors.htm"; }
  else { parent.quicksync('a4'); }
}
//-->
</script>
<script type="text/javascript" src="highlight.js"></script></head>
<body style="margin: 0px 0px 0px 0px; background: #FFFFFF;" onload="highlight();">
<div id="hmpopupDiv" style="visibility:hidden; position:absolute; z-index:1000; "></div>


<table width="100%" border="0" cellspacing="0" cellpadding="5" bgcolor="#649CCC">
  <tr valign="middle">
    <td align="left">
      <p class="p_Heading1"><span class="f_Heading1">Warnings and Errors</span></p>

    </td>
    <td align="right">
     <span style="font-size: 9pt">
     <a href="hid_overview.htm"><img src="nav_up_blue.gif" border=0 alt="Top"></a>&nbsp;
     <a href="hid_ui.htm"><img src="nav_left_blue.gif" border=0 alt="Previous"></a>&nbsp;
     <a href="hid_config.htm"><img src="nav_right_blue.gif" border=0 alt="Next"></a>
     </span>
    </td>
  </tr>
</table>


<!-- Placeholder for topic body. -->
<table width="100%" border="0" cellspacing="0" cellpadding="5"><tr valign="top"><td align="left">
<p><span style="font-weight: bold; text-decoration: underline;">Warnings</span></p>
<p>&nbsp;</p>
<p><span style="font-weight: bold;">Sleep time too long</span><br>
If the sleep is longer than expected duration, by more than 15 seconds for sleep states S1, S2 and S3 or more than 45 seconds for sleep state S4, then a warning will be logged.</p>
<p>&nbsp;</p>
<p><span style="font-weight: bold; text-decoration: underline;">Errors</span></p>
<p>&nbsp;</p>
<p><span style="font-weight: bold;">Sleep time too short</span></p>
<p> If the sleep is interrupted and returns earlier than the expected sleep time then an error will be logged</p>
<p>&nbsp;</p>
<p><span style="font-weight: bold;">Sleep error</span></p>
<p>The system call to start the sleep has failed, an error code will be reported and logged.</p>

</td></tr></table>

</body>
</html>
