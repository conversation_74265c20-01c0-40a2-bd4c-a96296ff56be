import PublicFuc
from openpyxl.drawing.image import Image
import csv
import matplotlib.pyplot as plt
import tempfile,os
from openpyxl.utils import get_column_letter

def Run(curpath, workBook, alignment):
    ws = workBook['写入放大因子和TBW测试']
    ws.alignment = alignment
    proIometerWAF(curpath, ws)

    PublicFuc.WriteReportTime(ws,'Q',2)
    PublicFuc.WriteReportOperator(ws,'D',2)

def WriteDataAndImageOfHdtune(worksheet, startLine, dataDic, colLst, imgWidth, imgHeight):
    imageLine = startLine+2
    curLine = startLine
    for key in dataDic:
        imageCol = 1
        for line in dataDic[key]:
            for index,col in enumerate(colLst):
                if 0 == index:
                    worksheet['%s%d'%(col, curLine)] = key
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
            curLine += 1
            # hdtune列表最后两项是图片路径(读和写)
            if '' != line[-2]:
                img = Image(line[-2])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
                imageCol += 8
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
            imageCol += 3
        curLine = startLine+1
        imageLine += 1

def WriteDataAndImageOfHdtuneFullWrite(worksheet, startLine, dataDic, colLst, imgWidth, imgHeight):
    imageLine = startLine+2
    curLine = startLine
    imageCol = 1
    for key in dataDic:
        for line in dataDic[key]:
            for index,col in enumerate(colLst):
                try:
                    if 0 == index:
                        worksheet['%s%d'%(col, curLine)] = key
                    else:
                        worksheet['%s%d'%(col, curLine)] = line[index-1]
                 #合并的单元格只能写一次，需要捕获异常
                except(AttributeError):
                    continue
            curLine += 1
            # 列表最后一项是图片路径
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
            imageCol += 8

def GetImagePath(strCsvFile, key):
    strPath = ''
    dataLst = []
    with open(strCsvFile, 'r', errors='ignore') as f:
        rowLst = list(csv.reader(f))
        for line in rowLst:
            if len(line) >= 14 and 'WORKER' == line[1]:
                dataLst.append(float(line[13]))
    if [] != dataLst:
        plt.figure(figsize=(13,5))
        plt.title('%s  Phy_Seq_1M_2H(MB/s)'%key)
        xLst = [x for x in range(len(dataLst))]
        plt.plot(xLst, dataLst)
        ax = plt.gca()
        ax.xaxis.set_major_locator(plt.MultipleLocator(120))
        plt.xticks(rotation=90)
        ax.yaxis.set_major_locator(plt.MultipleLocator(50))
        if not os.path.exists(PublicFuc.strSsdTempDir):
            os.mkdir(PublicFuc.strSsdTempDir)
        strPath = os.path.join(PublicFuc.strSsdTempDir, '%s_iometer.png'%key)
        plt.savefig(strPath, bbox_inches='tight')
        plt.close()  
    return strPath

def GetNewIoMeterDicLocal(oldDic, startPos, smartKey, bA23 = False):
    #startPos之后的数据为smart信息，需转换为模板所需数据[F1(G),F2(G),smart(非0),A5-A6]
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        for dataLst in oldDic[key]:
            newLst = dataLst[:startPos]
            smartLst = dataLst[startPos:]
            #F1、F2 1个单位为32M，需转化为G
            if smartLst[0] == '':
                newLst.append('')
            else:
                newLst.append((int(smartLst[0],16)*32)//1024)
            if smartLst[1] == '':
                newLst.append('')
            else:
                newLst.append((int(smartLst[1],16)*32)//1024)
            newLst.append(smartLst[-1])
            newDic[key].append(newLst)
        
    return newDic

def proIometerWAF(curpath, worksheet):
    imtKeyLst = ['pc_no', 'Cap', 'Phy_Seq_4KB_6H_Iops', 'Phy_Seq_4KB_6H_MiBps']
    pattern = '.+\\\\Plan81\\\\T-SS-SS-WAF_SEQ_4KB\\\\IOmeter\\\\\d{14}\\\\report.ini$'
    dataStartLine = 13
    imageStartLine = 33
    imageIdx = 1
    proIometerWAF_one_pattern(curpath, worksheet,imtKeyLst,pattern,dataStartLine,imageStartLine,imageIdx)

    imtKeyLst = ['pc_no', 'Cap', 'Phy_Seq_64KB_6H_Iops', 'Phy_Seq_64KB_6H_MiBps']
    pattern = '.+\\\\Plan81\\\\T-SS-SS-WAF_SEQ_64KB\\\\IOmeter\\\\\d{14}\\\\report.ini$'
    dataStartLine += 1
    imageIdx += 1
    proIometerWAF_one_pattern(curpath, worksheet,imtKeyLst,pattern,dataStartLine,imageStartLine,imageIdx)

    imtKeyLst = ['pc_no', 'Cap', 'Phy_Seq_512KB_6H_Iops', 'Phy_Seq_512KB_6H_MiBps']
    pattern = '.+\\\\Plan81\\\\T-SS-SS-WAF_SEQ_512KB\\\\IOmeter\\\\\d{14}\\\\report.ini$'
    dataStartLine += 1
    imageIdx += 1
    proIometerWAF_one_pattern(curpath, worksheet,imtKeyLst,pattern,dataStartLine,imageStartLine,imageIdx)

    imtKeyLst = ['pc_no', 'Cap', 'Phy_Seq_1MB_6H_Iops', 'Phy_Seq_1MB_6H_MiBps']
    pattern = '.+\\\\Plan81\\\\T-SS-SS-WAF_SEQ_1MB\\\\IOmeter\\\\\d{14}\\\\report.ini$'
    dataStartLine += 1
    imageIdx += 1
    proIometerWAF_one_pattern(curpath, worksheet,imtKeyLst,pattern,dataStartLine,imageStartLine,imageIdx)

    imtKeyLst = ['pc_no', 'Cap', 'Phy_Seq_4MB_6H_Iops', 'Phy_Seq_4MB_6H_MiBps']
    pattern = '.+\\\\Plan81\\\\T-SS-SS-WAF_SEQ_4MB\\\\IOmeter\\\\\d{14}\\\\report.ini$'
    dataStartLine += 1
    imageIdx += 1
    proIometerWAF_one_pattern(curpath, worksheet,imtKeyLst,pattern,dataStartLine,imageStartLine,imageIdx)


    imtKeyLst = ['pc_no', 'Cap', 'Phy_Rnd_4KB_6H_Iops', 'Phy_Rnd_4KB_6H_MiBps']
    pattern = '.+\\\\Plan81\\\\T-SS-SS-WAF_RAND_4KB\\\\IOmeter\\\\\d{14}\\\\report.ini$'
    dataStartLine += 1
    imageStartLine += 1
    imageIdx = 1
    proIometerWAF_one_pattern(curpath, worksheet,imtKeyLst,pattern,dataStartLine,imageStartLine,imageIdx)

    imtKeyLst = ['pc_no', 'Cap', 'Phy_Rnd_64KB_6H_Iops', 'Phy_Rnd_64KB_6H_MiBps']
    pattern = '.+\\\\Plan81\\\\T-SS-SS-WAF_RAND_64KB\\\\IOmeter\\\\\d{14}\\\\report.ini$'
    dataStartLine += 1
    imageIdx += 1
    proIometerWAF_one_pattern(curpath, worksheet,imtKeyLst,pattern,dataStartLine,imageStartLine,imageIdx)

    imtKeyLst = ['pc_no', 'Cap', 'Phy_Rnd_512KB_6H_Iops', 'Phy_Rnd_512KB_6H_MiBps']
    pattern = '.+\\\\Plan81\\\\T-SS-SS-WAF_RAND_512KB\\\\IOmeter\\\\\d{14}\\\\report.ini$'
    dataStartLine += 1
    imageIdx += 1
    proIometerWAF_one_pattern(curpath, worksheet,imtKeyLst,pattern,dataStartLine,imageStartLine,imageIdx)

    imtKeyLst = ['pc_no', 'Cap', 'Phy_Rnd_1MB_6H_Iops', 'Phy_Rnd_1MB_6H_MiBps']
    pattern = '.+\\\\Plan81\\\\T-SS-SS-WAF_RAND_1MB\\\\IOmeter\\\\\d{14}\\\\report.ini$'
    dataStartLine += 1
    imageIdx += 1
    proIometerWAF_one_pattern(curpath, worksheet,imtKeyLst,pattern,dataStartLine,imageStartLine,imageIdx)

    imtKeyLst = ['pc_no', 'Cap', 'Phy_Rnd_4MB_6H_Iops', 'Phy_Rnd_4MB_6H_MiBps']
    pattern = '.+\\\\Plan81\\\\T-SS-SS-WAF_RAND_4MB\\\\IOmeter\\\\\d{14}\\\\report.ini$'
    dataStartLine += 1
    imageIdx += 1
    proIometerWAF_one_pattern(curpath, worksheet,imtKeyLst,pattern,dataStartLine,imageStartLine,imageIdx)
    

def proIometerWAF_one_pattern(curpath, worksheet,imtKeyList,pattern,_dataStartLine,_imageStartLine,_imageIndex):
    smartKey = ['F5','F1']
    imtCol = ['C','B','E','K', 'M','O','P']
    imtKeyLst = imtKeyList
    pattern = pattern
    newKey = imtKeyLst+smartKey
    imtDic = {}
    PublicFuc.ReadQaIniData(curpath, pattern, imtDic, newKey, 'CrystalDiskInfo.bmp', 1)

    newDic = GetNewIoMeterDicLocal(imtDic, len(imtKeyLst), smartKey, True)
    #newKey = imtKeyLst+smartKeyNew
    startLine = _dataStartLine
    PublicFuc.WriteData(worksheet, startLine, newDic, imtCol, newKey)
    startLine = _imageStartLine
    imgWidth = 210
    imgHeight = 320
    for key in newDic:
        for line in newDic[key]:
            strPath = line[-1]
            if '' != strPath:
                img = Image(strPath)
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter((_imageIndex-1)*3+1),startLine))
                startLine += 1

