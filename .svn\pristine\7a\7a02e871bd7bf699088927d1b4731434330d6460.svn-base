import PublicFuc
import configparser
import csv,time
import os,re
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image

dicPfm = {}

def Run(curpath, workBook, alignment):
    ws = workBook['Performance-Simple']
    ws.alignment = alignment
    ProData(curpath, ws, 5)
    ProSumData(curpath, ws)
    PublicFuc.WriteReportTime(ws,'F',2)
    PublicFuc.WriteReportOperator(ws,'I',2)
    
def ProData(curpath, worksheet, maxDiskCnt):
    #cdm
    cdmKey = ['Cap_MB','pc_no','format','SeqQ32T1_Read','SeqQ32T1_Write','4KQ32T1_Read','4KQ32T1_Write','Seq_Read','Seq_Write','4K_Read','4K_Write']
    pattern = '.+\\\\Plan35\\\\T_GE_SD_C15\\\\CDM_1\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'CDM_1', cdmKey, 'CDM.bmp', maxDiskCnt)
    pattern = '.+\\\\Plan35\\\\T_GE_SD_C15\\\\CDM_2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'CDM_2', cdmKey, 'CDM.bmp', maxDiskCnt)
    startLine = 21
    cdmCol = ['C','D','A','N','E','F','G','H','I','J','K','L']
    WriteDataAndImageLocal(worksheet, startLine, startLine+5, dicPfm, 'CDM_1', cdmCol, cdmKey)
    cdmCol = ['C','D','A','N','O','P','Q','R','S','T','U','V']
    WriteDataAndImageLocal(worksheet, startLine, startLine+6, dicPfm, 'CDM_2', cdmCol, cdmKey)
    #h2testw
    h2Key = ['Cap_MB','pc_no','format','write speed','read speed']
    pattern = '.+\\\\Plan35\\\\T_GE_SD_C14\\\\H2testw_1\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'H2_1', h2Key, 'H2.bmp', maxDiskCnt)
    pattern = '.+\\\\Plan35\\\\T_GE_SD_C14\\\\H2testw_2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'H2_2', h2Key, 'H2.bmp', maxDiskCnt)
    startLine = 33
    h2Col = ['C','D','A','G','E','F']
    WriteDataAndImageLocal(worksheet, startLine, startLine+5, dicPfm, 'H2_1', h2Col, h2Key)
    h2Col = ['C','D','A','I','J','K']
    WriteDataAndImageLocal(worksheet, startLine, startLine+6, dicPfm, 'H2_2', h2Col, h2Key)

#'MMS_PC','FILE_SYSTEM'
def ReadMarsIniDataLocal(curpath, pattern, dataDic, caseName, caseKey, keyLst, imageSuffix, diskCnt = 6):
    unitLst = ['M/s']
    config = configparser.RawConfigParser()
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        if 'HWCONFIG' not in config.sections():
            continue
        keyName = config['HWCONFIG']['mms_flash']
        if keyName not in dataDic:
            #0表示样片数量不确定，需要获取全部数据
            if diskCnt == len(dataDic) and 0 != diskCnt:
                continue
            dataDic[keyName] = {}
        if caseName not in config.sections():
            continue

        dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']
        if caseKey not in dataDic[keyName]:
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            tempLst = []
            for key in keyLst:
                if 'Cap' == key:
                    cap = config['HWCONFIG']['capacity']
                    if '' == cap:
                        tempLst.append('')
                    else:
                        tempLst.append(str(int(float(cap))))
                    continue
                if 'MMS_PC' == key:
                    pcNo = ''
                    if 'MMS_PC' in config['HWCONFIG']:
                        pcNo = config['HWCONFIG']['MMS_PC']                   
                    tempLst.append(pcNo)
                    continue
                if 'FILE_SYSTEM' == key:
                    fs = ''
                    if 'FILE_SYSTEM' in config['HWCONFIG']:
                        fs = config['HWCONFIG']['FILE_SYSTEM']
                    tempLst.append(fs)
                    continue
                if key.lower() in config[caseName]:
                    value = config[caseName][key.lower()]
                    #去除单位
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    tempLst.append(value)
                else:
                    tempLst.append('')

            testResult =  PublicFuc.GetValueFromDic(dataDic[keyName],'test_result','FAIL')
            if 'TRUE'== testResult:
                dataDic[keyName]['test_result'] = 'TRUE'
            else:
                dataDic[keyName]['test_result'] = testResult
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                PublicFuc.errDiskLst.append([keyName,dataDic[keyName]['MMS_PC'],testResult,strTime])

            #imageSuffix为空不需要截图，只需要数据
            if '' != imageSuffix:
                pos = file.rfind('\\report')
                imagepath = file[:pos]
                image = ''
                for i in os.listdir(imagepath):
                    if i.endswith('.bmp'):
                        image = os.path.join(imagepath,i)
                        break
                if os.path.isfile(image):
                    tempLst.append(image)
                else:
                    tempLst.append('')
            dataDic[keyName][caseKey] = tempLst

def WriteDataAndImageLocal(worksheet, startLine, imageLine, dataDic, caseKey, colLst, keyLst, imgWidth = 260, imgHeight = 240, startCol = 2, colCnt = 3):
    curLine = startLine
    imageCol = startCol
    keySortLst = sorted(dataDic.keys(), reverse=False)
    for key in keySortLst:
        if caseKey not in dataDic[key]:
            curLine += 1
            continue
        line = dataDic[key][caseKey]
        for index,col in enumerate(colLst):
            if 0 == index:
                keyNo = key
                worksheet['%s%d'%(col, curLine)] = keyNo
            else:
                worksheet['%s%d'%(col, curLine)] = line[index-1]
        # 列表最后一项是图片路径
        if '' != line[-1]:
            img = Image(line[-1])
            img.width = imgWidth
            img.height = imgHeight
            worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
        curLine += 1
        imageCol += colCnt

def WriteData(worksheet, startLine, dataDic, caseKey, colNum, lineCnt, disk=''):
    if caseKey not in dataDic:
        return
    col = get_column_letter(colNum)
    curLine = startLine
    line = dataDic[caseKey]
    if '' != disk:
        worksheet['%s3'%col] = '%s-%sMB'%(disk, line[0])
    for data in line[3:3+lineCnt]:
        worksheet['%s%d'%(col, curLine)] = PublicFuc.Safe_float(data)
        curLine += 1

def ProSumData(curpath, ws):
    cleanLst = ['CDM_1', 'H2_1']
    dirtyLst = ['CDM_2', 'H2_2']
    lineLst = [8, 2]
    startCol = 6
    keyLst = sorted(dicPfm.keys(), reverse=False)
    for disk in keyLst:
        startLine = 5
        for idx,caseKey in enumerate(cleanLst):
            WriteData(ws, startLine, dicPfm[disk], caseKey, startCol, lineLst[idx], disk)
            startLine += lineLst[idx]
        startLine = 5
        startCol += 1
        for idx,caseKey in enumerate(dirtyLst):
            WriteData(ws, startLine, dicPfm[disk], caseKey, startCol, lineLst[idx])
            startLine += lineLst[idx]
        startCol += 1