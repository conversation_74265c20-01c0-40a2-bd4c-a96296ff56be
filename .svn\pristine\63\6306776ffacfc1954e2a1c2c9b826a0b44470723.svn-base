#pragma once
class IISQLOperate;
#include <StatusUpload/IStatusUpload.h>
#include <afxmt.h>

class CTestResult
{
public:
	CTestResult(void);
	virtual ~CTestResult(void);

	bool UpdateTestResult(TEST_RESULT* _pStatus);

	bool UpLoadSATAHistoryInfo(SSD_DB_INFO* _pStatus);

	void ReleaseResource();

protected:
	bool TryConnect();
private:
	IISQLOperate *m_pSqlOperator;
	IISQLOperate *mms_pSqlOperator;

	std::string m_strErrInfo;
	bool m_bConnected;
	CCriticalSection m_csLock;
};
