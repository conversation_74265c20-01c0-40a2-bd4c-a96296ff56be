﻿<!DOCTYPE html>
<html>
<head>
   <title>Configuration Options</title>
   <meta name="generator" content="Help &amp; Manual" />
   <meta name="keywords" content="Config,Configuration Options,Options" />
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <meta http-equiv="Content-Style-Type" content="text/css" />
   <link type="text/css" href="default.css" rel="stylesheet" />
   <script type="text/javascript" src="jquery.js"></script>
   <script type="text/javascript" src="helpman_settings.js"></script>
   <script type="text/javascript" src="helpman_topicinit.js"></script>

   <script type="text/javascript">
     HMSyncTOC("index.html", "hid_config.htm");
   </script>
   <script type="text/javascript" src="highlight.js"></script>
   <script type="text/javascript">
     $(document).ready(function(){highlight();});
   </script>
</head>
<body style="margin: 0px 0px 0px 0px; background: #FFFFFF;">


<table width="100%" border="0" cellspacing="0" cellpadding="5" bgcolor="#649CCC">
  <tr valign="middle">
    <td align="left">
      <p style="page-break-after: avoid; margin: 7px 0px 7px 0px;"><span style="font-size: 16pt; font-weight: bold; color: #ffffff;">Configuration Options</span></p>

    </td>
    <td align="right">
     <span style="font-size: 9pt">
     <a href="hid_overview.htm"><img src="nav_up_blue.gif" border=0 alt="Top"></a>&nbsp;
     <a href="hid_ui.htm"><img src="nav_left_blue.gif" border=0 alt="Previous"></a>&nbsp;
     <a href="hid_commandline.htm"><img src="nav_right_blue.gif" border=0 alt="Next"></a>
     </span>
    </td>
  </tr>
</table>


<!-- Placeholder for topic body. -->
<table width="100%" border="0" cellspacing="0" cellpadding="5"><tr valign="top"><td align="left">
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">Sleep durations</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">The length of time in seconds to carry out the requested sleep action for each individual sleep states.</span><br />
<span style="color: #010100;">Default values are 30 for S1, S2 and S3 and 60 for S4.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">State fallback (for sleep state that is not supported):</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Allow the user to specify what to do if a requested sleep state is not supported.</span><br />
<span style="color: #010100;">&nbsp;</span><br />
<span style="color: #010100;">Attempt lighter sleep: &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Attempt the next supported lighter sleep (e.g. S2 instead of S3)</span><br />
<span style="color: #010100;">Attempt deeper sleep: &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Attempt the next supported deeper sleep (e.g. S4 instead of S3)</span><br />
<span style="color: #010100;">Force sleep at requested level: &nbsp; &nbsp; &nbsp; &nbsp;Try and force requested sleep state</span><br />
<span style="color: #010100;">Skip sleep: &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Skip requested sleep state.</span><br />
<span style="color: #010100;">&nbsp;</span><br />
<span style="color: #010100;">The default value is “Skip Sleep”.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">Cycle Forever</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Select this to have Sleeper run the selected tests forever</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">Perform a Fixed number of Cycles</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Select this and enter a number in the Number of Cycles field to have Sleeper run the selected tests a set number of times.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">Number of Cycles</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">This is the number of times to run the selected test if the Perform a fixed number of cycles option is selected.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">Cycle Interval Duration</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">The length of time in seconds to wait between sleep actions when carrying out ‘Cycle through all supported states’ or a ‘Cycle through all states’.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">On Windows Vista the system may automatically return to its previous sleep state after several minutes if there is no user input, if this occurs the computer will not automatically wake again. To avoid this reduce the interval duration.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">Restart automatically after power cycle</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">This option will automatically restart Sleeper if there was a problem during the last sleep and the computer had to be rebooted.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">Stop and prompt user upon error</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">If this option is selected then if Sleeper is started and detects that it was in the middle of &nbsp;a sleep last time it was running then a dialog box will be displayed with the option to continue testing, stop the test or exit Sleeper.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">Sleep Forever</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Put the system into the requested sleep state until it is woken up by user action such as moving the mouse.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">Delay before first sleep</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">The length of time in seconds to countdown before entering the first requested sleep state.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">Log to file</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Select this option to save the logged messages from sleeper in a log file.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">Clear log file at start</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Select this option to clear the log file when Sleeper first starts testing.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">Append to log file</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Select this option to add new log entries to the existing log file without removing any existing entries.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">Log file</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">This is the filename of the log file to store the results in.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">Log level:</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">What level of detail you want in the log file.</span><br />
<span style="color: #010100;">&nbsp;</span><br />
<span style="color: #010100;">Normal: &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Sleeper activity log</span><br />
<span style="color: #010100;">Debug: &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;Sleeper activity log + trace log</span><br />
<span style="color: #010100;">Debug &nbsp;&amp; Events: &nbsp; &nbsp; &nbsp; &nbsp;Sleeper activity log + trace log + System or Application event log + Wake Source.</span><br />
<span style="color: #010100;">&nbsp;</span><br />
<span style="color: #010100;">The default value is “Normal”.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Note: Wake Source is available in Vista only and can describe the method the computer was awoken, eg &quot;ACPILid&quot; (open laptop) or &quot;PowerButton&quot;. Timed events, like those set by Sleeper will usually return no wake source as such these will be replaced with &quot;Unknown - Probably timer event&quot;.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">Call external application between suspends</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Select this option to run an application between suspends. The application location and name is entered in the File edit box below this option, the button will allow browsing the filesystem for the application.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">Call external application after last cycle</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Select this option to run an application after the last cycle has finished. The application location and name is entered in the File edit box below this option, the button will allow browsing the filesystem for the application.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">Load Config</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">A dialog will appear to allow user to choose configuration file to load. </span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">Save Config</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">A dialog will appear to allow user to save configuration settings as a configuration file.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #000000;">&nbsp;</span></p>

</td></tr></table>

</body>
</html>
