#pragma  once

#include <PublicWin32/PublicFunc/PublicFunc.h>
#include <PublicWin32/INI/IIniFile.h>
#include <PublicWin32/FileOp/FileOp.h>
#include <PublicWin32/FileOp/FileOpEx.h>

CString DLGGetPath(HWND _hParent);

CString SelectFloderPath();

CString BrowseDir(CWnd* pParent = NULL,const TCHAR* pszInitPath = NULL) ;

void RecordSpeedInfo(std::string _strLogInfo, HWND _hMainWnd = 0);

void DebugLogMsg(std::string _strLogInfo, HWND _hMainWnd = 0);