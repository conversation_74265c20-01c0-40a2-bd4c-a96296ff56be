﻿<!DOCTYPE html>
<html><head>
   <title>Rebooter by PassMark Software</title>
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <meta http-equiv="X-UA-Compatible" content="IE=edge" />

   <!-- This line includes the general project style sheet (not required) -->
   <link type="text/css" href="default.css" rel="stylesheet" />

   <style type="text/css">
       body { background:#FFF; }
       .navbar      { font-size: 120%; }

       #idx          { margin: 0; padding: 0; }     /* div tag that wraps the keyword index */
       #idx a        { font-color: #000; text-decoration: none; }  /* all links in index appear as text */

       #idx p             { margin: 2px; }       /* keywords and secondary keywords */
       #idx p.idxkeyword2 { margin-left: 20px }  /* indentation for secondary keywords */

       table.idxtable { background: #F4F4F4;
                        border: 1px solid #000000;
                        border-collapse: collapse;
                        -moz-box-shadow: 2px 2px 2px #B0B0B0;
                        -webkit-box-shadow: 2px 2px 2px #B0B0B0;
                        box-shadow: 2px 2px 2px #B0B0B0;
                        filter: progid:DXImageTransform.Microsoft.Shadow(color=B0B0B0, Direction=135, Strength=4); }
       td.idxtable    { background: #F4F4F4; }

       /* font definitions for keyword section, keywords and popup links */
       .idxsection  { font-family: Arial,Helvetica; font-weight: bold; font-size: 14pt; color: #000000; text-decoration: none;
                      margin-top: 15px; margin-bottom: 15px; }
       .idxkeyword  { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .idxkeyword2 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .idxlink     { font-family: Arial,Helvetica; font-weight: normal; font-size: 9pt; color: #000000; text-decoration: none; }

   </style>
   <script type="text/javascript" src="jquery.js"></script>
   <script type="text/javascript" src="helpman_settings.js"></script>
   <script type="text/javascript" src="hmcontextids.js"></script>
</head>
<body>
<p class="navbar"><a href="hmcontent.htm">Contents</a>
 | <b>Index</b>
 | <a href="hmftsearch.htm">Search</a>
</p>
<hr/>

  <!-- Place holder for the keyword index - this variable is REQUIRED! -->
  <script type="text/javascript">

  function hmInitHideLinks(cssCode) {
  	var styleElement = document.createElement("style");
  	styleElement.type = "text/css";
  	if (styleElement.styleSheet) {
    	styleElement.styleSheet.cssText = cssCode;
  	}
  	else {
    	styleElement.appendChild(document.createTextNode(cssCode));
  	}
  	document.getElementsByTagName("head")[0].appendChild(styleElement);
  }

  hmInitHideLinks("#idx div { display: none }");

  var currentdiv = null;
  var canhidelinks = true;

  function hmshowLinks(divID) {
    var thisdiv = document.getElementById(divID);
    canhidelinks = true;
    hmhideLinks();
    if (thisdiv) {
      currentdiv = thisdiv;
      $(currentdiv).show();
      $(currentdiv).mouseover(hmdivMouseOver).mouseout(hmdivMouseOut);
      $(document).mouseup(hmhideLinks);
    }
  }
  function hmdivMouseOver() { canhidelinks = false; };
  function hmdivMouseOut() { canhidelinks = true; };
  function hmhideLinks() {
    if (canhidelinks) {
      if (currentdiv) {
        $(currentdiv).hide();
        $(currentdiv).unbind("onmouseover", "onmouseout");
      }
      currentdiv = null;
      $(document).unbind("onmouseup");
    }
  }
</script>
<div id="idx" style="margin:0;padding:0;border:none">
<a name="A" id="A"></a><p class="idxsection">- A -</p>
<p class="idxkeyword"><a href="commandline.htm" target="hmcontent"><span class="idxkeyword">Arguments</span></a></p>
<p class="idxkeyword"><a href="forcetypes.htm" target="hmcontent"><span class="idxkeyword">Ask to close</span></a></p>
<p class="idxkeyword"><a href="autologin.htm" target="hmcontent"><span class="idxkeyword">Autologin</span></a></p>
<p class="idxkeyword"><a href="autologin.htm" target="hmcontent"><span class="idxkeyword">Auto-login</span></a></p>
<p class="idxkeyword"><a href="autorun.htm" target="hmcontent"><span class="idxkeyword">Auto-run</span></a></p>
<a name="C" id="C"></a><p class="idxsection">- C -</p>
<p class="idxkeyword"><a href="commandline.htm" target="hmcontent"><span class="idxkeyword">Command line</span></a></p>
<p class="idxkeyword"><a href="faq.htm" target="hmcontent"><span class="idxkeyword">Common Problems</span></a></p>
<p class="idxkeyword"><a href="commandline.htm" target="hmcontent"><span class="idxkeyword">Configuration mode</span></a></p>
<p class="idxkeyword"><a href="contacts.htm" target="hmcontent"><span class="idxkeyword">Contacting PassMark</span></a></p>
<p class="idxkeyword"><a href="copyright.htm" target="hmcontent"><span class="idxkeyword">Copyright notice</span></a></p>
<p class="idxkeyword"><a href="cycle.htm" target="hmcontent"><span class="idxkeyword">Cycle</span></a></p>
<a name="E" id="E"></a><p class="idxsection">- E -</p>
<p class="idxkeyword"><a href="faq.htm" target="hmcontent"><span class="idxkeyword">Errors</span></a></p>
<p class="idxkeyword"><a href="copyright.htm" target="hmcontent"><span class="idxkeyword">EULA</span></a></p>
<a name="F" id="F"></a><p class="idxsection">- F -</p>
<p class="idxkeyword"><a href="faq.htm" target="hmcontent"><span class="idxkeyword">FAQ</span></a></p>
<p class="idxkeyword"><a href="forcetypes.htm" target="hmcontent"><span class="idxkeyword">Force if Hung</span></a></p>
<p class="idxkeyword"><a href="forcetypes.htm" target="hmcontent"><span class="idxkeyword">Forced</span></a></p>
<p class="idxkeyword"><a href="faq.htm" target="hmcontent"><span class="idxkeyword">Frequently Asked Questions</span></a></p>
<a name="H" id="H"></a><p class="idxsection">- H -</p>
<p class="idxkeyword"><a href="forcetypes.htm" target="hmcontent"><span class="idxkeyword">Hung</span></a></p>
<a name="I" id="I"></a><p class="idxsection">- I -</p>
<p class="idxkeyword"><a href="commandline.htm" target="hmcontent"><span class="idxkeyword">Interactive mode</span></a></p>
<p class="idxkeyword"><a href="overview.htm" target="hmcontent"><span class="idxkeyword">Introduction to Rebooter</span></a></p>
<a name="L" id="L"></a><p class="idxsection">- L -</p>
<p class="idxkeyword"><a href="copyright.htm" target="hmcontent"><span class="idxkeyword">License (End user)</span></a></p>
<p class="idxkeyword"><a href="reboottypes.htm" target="hmcontent"><span class="idxkeyword">Logoff</span></a></p>
<a name="M" id="M"></a><p class="idxsection">- M -</p>
<p class="idxkeyword"><a href="systemreq.htm" target="hmcontent"><span class="idxkeyword">Minimum requirements</span></a></p>
<a name="O" id="O"></a><p class="idxsection">- O -</p>
<p class="idxkeyword"><a href="overview.htm" target="hmcontent"><span class="idxkeyword">Overview of Rebooter</span></a></p>
<a name="P" id="P"></a><p class="idxsection">- P -</p>
<p class="idxkeyword"><a href="contacts.htm" target="hmcontent"><span class="idxkeyword">PassMark on the Web</span></a></p>
<p class="idxkeyword"><a href="faq.htm" target="hmcontent"><span class="idxkeyword">Passwords</span></a></p>
<p class="idxkeyword"><a href="reboottypes.htm" target="hmcontent"><span class="idxkeyword">Power off</span></a></p>
<p class="idxkeyword"><a href="faq.htm" target="hmcontent"><span class="idxkeyword">Problems</span></a></p>
<a name="R" id="R"></a><p class="idxsection">- R -</p>
<p class="idxkeyword"><a href="javascript:void(0)" onclick="return hmshowLinks('k29')"><span class="idxkeyword">Reboot</span></a></p>
<div id="k29" style="position:relative;margin:0 0 0 20px;top:-3px">
<table class="idxtable" style="padding:4px"><tr><td class="idxtable"><table style="border:none;padding:0"><tr style="vertical-align:baseline"><td><img src="ciconidx.gif" style="border:none" alt="Icon"></td><td><a href="reboottypes.htm" target="hmcontent"><span class="idxlink">Reboot and Restart types</span></a></td></tr><tr style="vertical-align:baseline"><td><img src="ciconidx.gif" style="border:none" alt="Icon"></td><td><a href="cycle.htm" target="hmcontent"><span class="idxlink">Reboot cycling and looping</span></a></td></tr></table></td></tr></table></div>
<p class="idxkeyword"><a href="cycle.htm" target="hmcontent"><span class="idxkeyword">Reboot loop</span></a></p>
<p class="idxkeyword"><a href="autorun.htm" target="hmcontent"><span class="idxkeyword">Registry</span></a></p>
<p class="idxkeyword"><a href="reboottypes.htm" target="hmcontent"><span class="idxkeyword">Restart</span></a></p>
<a name="S" id="S"></a><p class="idxsection">- S -</p>
<p class="idxkeyword"><a href="faq.htm" target="hmcontent"><span class="idxkeyword">Security</span></a></p>
<p class="idxkeyword"><a href="autologin.htm" target="hmcontent"><span class="idxkeyword">Security risks</span></a></p>
<p class="idxkeyword"><a href="reboottypes.htm" target="hmcontent"><span class="idxkeyword">Shutdown</span></a></p>
<p class="idxkeyword"><a href="autorun.htm" target="hmcontent"><span class="idxkeyword">Starting applications automatically</span></a></p>
<p class="idxkeyword"><a href="cycle.htm" target="hmcontent"><span class="idxkeyword">Stress testing</span></a></p>
<p class="idxkeyword"><a href="contacts.htm" target="hmcontent"><span class="idxkeyword">Support</span></a></p>
<p class="idxkeyword"><a href="systemreq.htm" target="hmcontent"><span class="idxkeyword">System requirements</span></a></p>
<a name="V" id="V"></a><p class="idxsection">- V -</p>
<p class="idxkeyword"><a href="whats_new.htm" target="hmcontent"><span class="idxkeyword">Version history</span></a></p>
<a name="W" id="W"></a><p class="idxsection">- W -</p>
<p class="idxkeyword"><a href="contacts.htm" target="hmcontent"><span class="idxkeyword">Web page</span></a></p>
<p class="idxkeyword"><a href="whats_new.htm" target="hmcontent"><span class="idxkeyword">Whats new</span></a></p>
<p class="idxkeyword"><a href="systemreq.htm" target="hmcontent"><span class="idxkeyword">Windows requirements</span></a></p>
</div>


</body>
</html>

