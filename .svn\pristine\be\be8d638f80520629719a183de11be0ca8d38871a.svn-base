﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<!-- saved from url=(0029)http://www.helpandmanual.com/ --><head>
   <title>Rebooter by PassMark Software</title>
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />

   <!-- This line includes the general project style sheet (not required) -->
   <meta http-equiv="Content-Style-Type" content="text/css" />
   <link type="text/css" href="default.css" rel="stylesheet" />

   <style type="text/css">
       .navtitle    { font-size: 14pt; font-weight: bold; margin-bottom: 16px; }
       .navbar      { font-size: 10pt; }
       .idxsection  { font-family: Arial,Helvetica; font-weight: bold; font-size: 14pt; color: #000000; text-decoration: none;
                      margin-top: 15px; margin-bottom: 15px; }
       .idxkeyword  { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .idxkeyword2 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .idxlink     { font-family: Arial,Helvetica; font-weight: normal; font-size: 9pt; color: #000000; text-decoration: none; }

       TABLE.idxtable { background: #F4F4F4; border-width: 1px; border-color: #000000; border-collapse: collapse;
                        filter: progid:DXImageTransform.Microsoft.Shadow(color=B0B0B0, Direction=135, Strength=4); }
       TD.idxtable    { background: #F4F4F4; }
   </style>
</head>
<body bgcolor="#FFFFFF">
<p class="navtitle">Rebooter by PassMark Software</p>
<p class="navbar">
<a href="rebooter_content_dyn.html">Contents</a>
 | <b>Index</b>
 | <a href="rebooter_ftsearch.html">Search</a>
</p><hr size="1" />
<!-- Place holder for the keyword index - this variable is REQUIRED! -->
<script type="text/javascript">
var currentdiv = null;
var canhidelinks = true;
function hmshowLinks(divID) {
   var thisdiv = document.getElementById(divID);
   canhidelinks = true;
   hmhideLinks();
   if (thisdiv) {
      thisdiv.style.display = "block";
      currentdiv = thisdiv;
      thisdiv.onmouseover = divmouseover;
      thisdiv.onmouseout = divmouseout;
      document.onmouseup = hmhideLinks;
   }
}
function divmouseover() { canhidelinks = false; }
function divmouseout() { canhidelinks = true; }
function hmhideLinks() {
   if (canhidelinks) {
      if (currentdiv) {
         currentdiv.style.display = "none";
         currentdiv.onmouseover = null;
         currentdiv.onmouseout = null;
      }
      currentdiv = null;
      document.onmouseout = null;
   }
}

</script>
<a name="A"></a><p class="idxsection">- A -</p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="commandline.htm" target="hmcontent"><span class="idxkeyword">Arguments</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="forcetypes.htm" target="hmcontent"><span class="idxkeyword">Ask to close</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="autologin.htm" target="hmcontent"><span class="idxkeyword">Autologin</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="autologin.htm" target="hmcontent"><span class="idxkeyword">Auto-login</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="autorun.htm" target="hmcontent"><span class="idxkeyword">Auto-run</span></a></p>
<a name="C"></a><p class="idxsection">- C -</p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="commandline.htm" target="hmcontent"><span class="idxkeyword">Command line</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="faq.htm" target="hmcontent"><span class="idxkeyword">Common Problems</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="commandline.htm" target="hmcontent"><span class="idxkeyword">Configuration mode</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="contacts.htm" target="hmcontent"><span class="idxkeyword">Contacting PassMark</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="copyright.htm" target="hmcontent"><span class="idxkeyword">Copyright notice</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="cycle.htm" target="hmcontent"><span class="idxkeyword">Cycle</span></a></p>
<a name="E"></a><p class="idxsection">- E -</p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="faq.htm" target="hmcontent"><span class="idxkeyword">Errors</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="copyright.htm" target="hmcontent"><span class="idxkeyword">EULA</span></a></p>
<a name="F"></a><p class="idxsection">- F -</p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="faq.htm" target="hmcontent"><span class="idxkeyword">FAQ</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="forcetypes.htm" target="hmcontent"><span class="idxkeyword">Force if Hung</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="forcetypes.htm" target="hmcontent"><span class="idxkeyword">Forced</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="faq.htm" target="hmcontent"><span class="idxkeyword">Frequently Asked Questions</span></a></p>
<a name="H"></a><p class="idxsection">- H -</p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="forcetypes.htm" target="hmcontent"><span class="idxkeyword">Hung</span></a></p>
<a name="I"></a><p class="idxsection">- I -</p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="commandline.htm" target="hmcontent"><span class="idxkeyword">Interactive mode</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="overview.htm" target="hmcontent"><span class="idxkeyword">Introduction to Rebooter</span></a></p>
<a name="L"></a><p class="idxsection">- L -</p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="copyright.htm" target="hmcontent"><span class="idxkeyword">License (End user)</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="reboottypes.htm" target="hmcontent"><span class="idxkeyword">Logoff</span></a></p>
<a name="M"></a><p class="idxsection">- M -</p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="systemreq.htm" target="hmcontent"><span class="idxkeyword">Minimum requirements</span></a></p>
<a name="O"></a><p class="idxsection">- O -</p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="overview.htm" target="hmcontent"><span class="idxkeyword">Overview of Rebooter</span></a></p>
<a name="P"></a><p class="idxsection">- P -</p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="contacts.htm" target="hmcontent"><span class="idxkeyword">PassMark on the Web</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="faq.htm" target="hmcontent"><span class="idxkeyword">Passwords</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="reboottypes.htm" target="hmcontent"><span class="idxkeyword">Power off</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="faq.htm" target="hmcontent"><span class="idxkeyword">Problems</span></a></p>
<a name="R"></a><p class="idxsection">- R -</p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="javascript:void(0)" onclick="return hmshowLinks('k29')"><span class="idxkeyword">Reboot</span></a></p>
<div id="k29" style="position: relative; z-index: 1000; display: none; margin: 0px 0px 0px 20px; top: -3px">
<table border="1" cellpadding="4" cellspacing="0" class="idxtable"><tr><td class="idxtable"><table border="0" cellpadding="0" cellspacing="0"><tr valign="baseline"><td><img src="ciconidx.gif" border="0" alt="Icon"/></td><td><a href="reboottypes.htm" target="hmcontent"><span class="idxlink">Reboot and Restart types</span></a></td></tr><tr valign="baseline"><td><img src="ciconidx.gif" border="0" alt="Icon"/></td><td><a href="cycle.htm" target="hmcontent"><span class="idxlink">Reboot cycling and looping</span></a></td></tr></table></td></tr></table>
</div>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="cycle.htm" target="hmcontent"><span class="idxkeyword">Reboot loop</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="autorun.htm" target="hmcontent"><span class="idxkeyword">Registry</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="reboottypes.htm" target="hmcontent"><span class="idxkeyword">Restart</span></a></p>
<a name="S"></a><p class="idxsection">- S -</p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="faq.htm" target="hmcontent"><span class="idxkeyword">Security</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="autologin.htm" target="hmcontent"><span class="idxkeyword">Security risks</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="reboottypes.htm" target="hmcontent"><span class="idxkeyword">Shutdown</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="autorun.htm" target="hmcontent"><span class="idxkeyword">Starting applications automatically</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="cycle.htm" target="hmcontent"><span class="idxkeyword">Stress testing</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="contacts.htm" target="hmcontent"><span class="idxkeyword">Support</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="systemreq.htm" target="hmcontent"><span class="idxkeyword">System requirements</span></a></p>
<a name="V"></a><p class="idxsection">- V -</p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="whats_new.htm" target="hmcontent"><span class="idxkeyword">Version history</span></a></p>
<a name="W"></a><p class="idxsection">- W -</p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="contacts.htm" target="hmcontent"><span class="idxkeyword">Web page</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="whats_new.htm" target="hmcontent"><span class="idxkeyword">Whats new</span></a></p>
<p class="idxkeyword" style="margin: 0px 0px 0px 0px"><a href="systemreq.htm" target="hmcontent"><span class="idxkeyword">Windows requirements</span></a></p>


</body>
</html>

