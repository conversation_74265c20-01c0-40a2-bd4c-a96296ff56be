
// AterExDlg.cpp : implementation file
//

#include "stdafx.h"
#include "AterEx.h"
#include "AterExDlg.h"
#include "afxdialogex.h"
#include <Python.h>
#include "SkynetServiceCtrol.h"

#ifdef _DEBUG
#define new DEBUG_NEW
#endif

// CAboutDlg dialog used for App About

class CAboutDlg : public CDialogEx
{
public:
	CAboutDlg();

// Dialog Data
	enum { IDD = IDD_ABOUTBOX };

	protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support

// Implementation
protected:
	DECLARE_MESSAGE_MAP()
};

CAboutDlg::CAboutDlg() : CDialogEx(CAboutDlg::IDD)
{
}

void CAboutDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
}

BEGIN_MESSAGE_MAP(CAboutDlg, CDialogEx)
END_MESSAGE_MAP()


// CAterExDlg dialog




CAterExDlg::CAterExDlg(CWnd* pParent /*=NULL*/)
	: CDialogEx(CAterExDlg::IDD, pParent)
	, m_pCommunicateManger(NULL)
	, m_pCasePort(NULL)
	, bRunning(False)
{
	m_strIniPath.Format("%s\\config\\CommonConfig.ini", PublicW32::GetModulePath(NULL).c_str());
//	m_pIniFile = new PublicW32::IIniFile(m_strIniPath.GetString());
	m_hIcon = AfxGetApp()->LoadIcon(IDR_MAINFRAME);
	InitCommunicate();
	Py_Initialize();
	m_pCasePort = new CCaseAtomicPort(&m_mainDlg, m_pCommunicateManger);
}

CAterExDlg::~CAterExDlg()
{
	ReleaseCommunicate();
	Py_Finalize();
	SAFE_DELETE(m_pCasePort);
	CString strFmt;
	// ����ر�ʱ��Ҳ�ָ��±���ļ�
	Bool isRunning = CFG_MGR->ReadBool(ATEREX_COMMON, IS_RUNNING);
	CTime time = CTime::GetCurrentTime();
	CString strTime = time.Format("%Y-%m-%d %H-%M-%S");
	if (isRunning)
	{
		CFG_MGR->WriteStr(REBOOT_PARA, "debug1", strTime.GetString());
		std::string strLocalIni,strTempIni;
		strFmt.Format("%s\\%s", PublicW32::GetModulePath().c_str(), MATERIALNO_PATH);
		strLocalIni = strFmt;
		if (PublicW32::FileExists(strLocalIni))
		{
			S8 strTempPath[MAX_PATH] = {0};
			GetTempPath(MAX_PATH, strTempPath);
			strTempIni = strTempPath;
			strTempIni += MATERIALNO_PATH;
			PublicW32::CopyFileToFile(strLocalIni, strTempIni);
		}
		// ����Reboot����
		
		CFG_MGR->WriteStr(REBOOT_PARA, "debug", strTime.GetString());
		CFG_MGR->WriteBool(REBOOT_PARA, IS_AUTO_START, False);
		CFG_MGR->WriteInteger(REBOOT_PARA, CUR_CASE, 0);
		CFG_MGR->WriteInteger(REBOOT_PARA, CUR_ITEM, 0);
		CFG_MGR->WriteInteger(REBOOT_PARA, CUR_CYCLE, 0);
		CFG_MGR->WriteBool(REBOOT_PARA, IS_CONTINUE_TEST, False);//���ü������ԡ�
		CFG_MGR->WriteInteger(REBOOT_PARA, CUR_CASE_CYCLE, 0);
	}
	CFG_MGR->WriteStr(REBOOT_PARA, "debug2", strTime.GetString());
}

BOOL CAterExDlg::PreTranslateMessage(MSG* pMsg)
{
	// TODO: Add your specialized code here and/or call the base class
	if (pMsg->message == WM_KEYDOWN) 
	{
		switch(pMsg->wParam)  
		{  
			case VK_RETURN://���λس�     
				return   TRUE;  
			case VK_ESCAPE://����Esc  
				return   TRUE;  
		}  
	}

	return CDialog::PreTranslateMessage(pMsg);
}

LRESULT CAterExDlg::WindowProc(UINT uMsg, WPARAM wParam, LPARAM lParam )
{
	if (m_pCommunicateManger)
	{
		if (m_pCommunicateManger->ReceviedCommand(uMsg, static_cast<U32>(wParam)))
		{
			SGCommunicate::COMMUNICATE_USER_COMMAND_PACKAGE userCmdPackage;
			memset(&userCmdPackage,0 ,sizeof(userCmdPackage));
			m_pCommunicateManger->GetCommand(wParam, &userCmdPackage);	
			m_pCasePort->DealwithUserPackge(static_cast<DWORD>(wParam), &userCmdPackage);

			return 0;
		}
	}
	
	switch (uMsg)
	{
	case WM_POWERBROADCAST:
		{
			switch(wParam)
			{
			case WM_QUERYENDSESSION:         //˯�ߡ�����
				{

				}
				break;
			case PBT_APMSUSPEND:         //˯�ߡ�����
				{
					
				}
				break;
			case PBT_APMRESUMEAUTOMATIC: //˯�ߡ����߻ָ�
				{
					PITEM_DETAIL_INFO pCurItem = m_pCasePort->GetCurItemInfo();
					if (pCurItem == NULL)
					{
						//���������û�н��в��ԣ���ֱ�ӷ���
						m_mainDlg.AddLogInfo("�ǲ��Թ����е��Գ����޹�sleep");
						return CWnd::WindowProc(uMsg, wParam, lParam);
					}

					if (ATEREX_SLEEP == pCurItem->strTool || PC_SLEEPER == pCurItem->strTool || PC_REBOOTER == pCurItem->strTool)
					{
						return CWnd::WindowProc(uMsg, wParam, lParam);
					}				

					int nSleepCircle = CFG_MGR->ReadInteger(SLEEP_SECTION,SLEEP_CIRCLE);
					nSleepCircle += 1;

					CString cstrTmp;
					cstrTmp.Format("PBT��Ϣ��,��ǰȦ��%d",nSleepCircle);
					m_mainDlg.AddLogInfo(cstrTmp.GetString());

					if (nSleepCircle > m_pCasePort->m_nMaxSleepCycles)
					{
						//���������޹ص�sleep�¼��������ϱ�
						m_mainDlg.AddLogInfo("�����޹�sleep");
						return CWnd::WindowProc(uMsg, wParam, lParam);
					}

					//д���Ѵ���
					CFG_MGR->WriteInteger(SLEEP_SECTION,SLEEP_CIRCLE,nSleepCircle);
					CString strCycle;
					strCycle.Format("%d/%d", nSleepCircle, m_pCasePort->m_nMaxSleepCycles);
					::SendMessage(m_mainDlg.GetSafeHwnd(), WM_SET_CURINFO, (WPARAM)m_pCasePort->GetCurItemInfo(), (LPARAM)(LPCTSTR)strCycle);

					//�������ݸ�������
					bool bFinish = false;
					if (nSleepCircle == m_pCasePort->m_nMaxSleepCycles)
					{
						bFinish = true;
					}
					
					if (!m_pCasePort->UpdateSleepRebootStatus(true,bFinish))
					{
						CString cstrTmp;
						cstrTmp.Format("�ϴ�sleepȦ����Ϣʧ��,��ǰȦ��%d",nSleepCircle);
						 m_mainDlg.AddLogInfo(cstrTmp.GetString());
					}
					
				}
				break;
			}
		}
		break;
	}	  

	return CWnd::WindowProc(uMsg, wParam, lParam);
}

void CAterExDlg::InitCommunicate()
{
	//���ض�̬��
	CString strModulePath = PublicW32::GetModulePath(NULL).c_str();
#ifdef _DEBUG
	strModulePath += "\\Dll\\Communicate_d.dll";
#else
	strModulePath += "\\Dll\\Communicate.dll";
#endif

	HINSTANCE hInstance = LoadLibraryEx(strModulePath, NULL, LOAD_WITH_ALTERED_SEARCH_PATH);
	typedef SGCommunicate::CommunicateManger* (*PFunc)();
	PFunc pFunc = NULL;
	if (0 != hInstance)
	{
		pFunc = (PFunc)GetProcAddress(hInstance, "GetCommunicateManger");
		if (NULL != pFunc)
		{
			m_pCommunicateManger = pFunc();
		}
	}
}

void CAterExDlg::ReleaseCommunicate()
{
	//���ض�̬��
	CString strModulePath = PublicW32::GetModulePath(NULL).c_str();
#ifdef _DEBUG
	strModulePath += "\\Dll\\Communicate_d.dll";
#else
	strModulePath += "\\Dll\\Communicate.dll";
#endif
	HINSTANCE hInstance = LoadLibraryEx(strModulePath, NULL, LOAD_WITH_ALTERED_SEARCH_PATH);
	typedef void (*PFunc)();
	PFunc pFunc = NULL;
	if (0 != hInstance)
	{
		pFunc = (PFunc)GetProcAddress(hInstance, "ReleaseCommunicateManger");
		if (NULL != pFunc)
		{
			pFunc();
		}
	}
}

void CAterExDlg::DoDataExchange(CDataExchange* pDX)
{
	CDialogEx::DoDataExchange(pDX);
	DDX_Control(pDX, IDC_TAB1, m_tabSheet);
}

BEGIN_MESSAGE_MAP(CAterExDlg, CDialogEx)
	ON_WM_SYSCOMMAND()
	ON_WM_PAINT()
	ON_WM_QUERYDRAGICON()
	ON_NOTIFY(TCN_SELCHANGE, IDC_TAB1, &CAterExDlg::OnTcnSelchangedTab)
	ON_NOTIFY(TCN_SELCHANGING, IDC_TAB1, &CAterExDlg::OnTcnSelchangingTab)
	ON_MESSAGE(WM_ENABLE_UI, &CAterExDlg::OnEnableUI)
	ON_MESSAGE(WM_DISABLE_UI, &CAterExDlg::OnDisableUI)
	ON_MESSAGE(WM_UPDATE_TEST_NO, &CAterExDlg::OnUpdateTestNo)
	ON_MESSAGE(WM_UPDATE_TEST_OPERATOR, &CAterExDlg::OnUpdateTestOperator)
	ON_MESSAGE(WM_UPDATE_GROUP, &CAterExDlg::OnUpdateGroup)
	ON_MESSAGE(WM_UPDATE_PRODUCT, &CAterExDlg::OnUpdateProduct)
	ON_MESSAGE(WM_UPDATE_MARS_PATH, &CAterExDlg::OnUpdateMarsPath)
	ON_MESSAGE(WM_UPDATE_MP_PATH, &CAterExDlg::OnUpdateMpPath)
	ON_MESSAGE(WM_UPDATE_MARS_PLAN, &CAterExDlg::OnUpdateMarsPlanPath)
	ON_MESSAGE(WM_PRODUCT_TYPE_CHANGE,&CAterExDlg::OnProductTypeChange)
	ON_MESSAGE(WM_PLAN_CHANGE,&CAterExDlg::OnPlanChange)
	ON_MESSAGE(WM_SET_PLAN,&CAterExDlg::OnSetPlan)
	ON_MESSAGE(WM_MESSAGE_DEBUG,&CAterExDlg::OnDebugLog)
	ON_MESSAGE(WM_TESTNO_CHANGE,&CAterExDlg::OnTestNoChange)
	ON_MESSAGE(WM_MOVE_WND_TOP,&CAterExDlg::OnMoveWndTop)
	ON_MESSAGE(WM_LOAD_ATEREX_PLAN,&CAterExDlg::OnLoadAterExPlan)
	ON_WM_CLOSE()
END_MESSAGE_MAP()

// CAterExDlg message handlers

unsigned int WINAPI InstallSkynetServiceThreadFunc(void * Lparam)
{
	CAterExDlg* pMainDlg = (CAterExDlg*)Lparam;
	pMainDlg->InstallService();
	return 0;
}

unsigned int WINAPI AterExOnLineTreadFunc(void * Lparam)
{
	Sleep(3000);//�ȴ�AterEx���صļ����߳�������ԭ����RMS���յ���Ϣ������Ϣ��AterEx�����AterEx���صļ����߳�δ�򿪣����޷����յ�RMS�˵Ĳ�������
	CAterExDlg* pMainDlg = (CAterExDlg*)Lparam;

	std::string strIP;
	char buff[256] = {0};
	if (GetLocalIpAddr(buff))
	{
		strIP = buff;
	}

	Bool bAutoRun = CFG_MGR->ReadBool(REBOOT_PARA, IS_AUTO_START, False);
	if (bAutoRun)
	{
		return 0;//�˳��߳�
	}

	CString cstrMac;
	if (!GetMacAddress(cstrMac))
	{
		cstrMac = "";
	}
	
	
	for (int i = 0; i < 10; i++)
	{
		if (pMainDlg->IsRunning())
		{
			return 0;//�˳��߳�
		}

		if (UpdateOnlineStatus(cstrMac.GetString(),strIP))
		{
			return 0;
		}

		Sleep(1000);
	}
	
	return 0;
}

BOOL CAterExDlg::OnInitDialog()
{
	CDialogEx::OnInitDialog();

	// Add "About..." menu item to system menu.

	// IDM_ABOUTBOX must be in the system command range.
	ASSERT((IDM_ABOUTBOX & 0xFFF0) == IDM_ABOUTBOX);
	ASSERT(IDM_ABOUTBOX < 0xF000);

	CMenu* pSysMenu = GetSystemMenu(FALSE);
	if (pSysMenu != NULL)
	{
		BOOL bNameValid;
		CString strAboutMenu;
		bNameValid = strAboutMenu.LoadString(IDS_ABOUTBOX);
		ASSERT(bNameValid);
		if (!strAboutMenu.IsEmpty())
		{
			pSysMenu->AppendMenu(MF_SEPARATOR);
			pSysMenu->AppendMenu(MF_STRING, IDM_ABOUTBOX, strAboutMenu);
		}
	}
	ShowWindow(SW_NORMAL);
	// Set the icon for this dialog.  The framework does this automatically
	//  when the application's main window is not a dialog
	SetIcon(m_hIcon, TRUE);			// Set big icon
	SetIcon(m_hIcon, FALSE);		// Set small icon

	// TODO: Add extra initialization here
	InitDebugLog();

	LoadUDiskMapDll();

	std::string strTitle = "AterEx_50.22.2.0." +  CFG_MGR->ReadString(ATEREX_VER, SUB_VER);
	SetWindowText(strTitle.c_str());
	m_mainDlg.init(GetSafeHwnd(), m_pCommunicateManger, m_pCasePort);

	m_tabSheet.AddPage("������", &m_mainDlg, IDD_DIALOG_MAIN);
	m_tabSheet.AddPage("���Լƻ�", &m_planDlg, IDD_DIALOG_PLAN);
	m_tabSheet.AddPage("��������", &m_publicDlg, IDD_DIALOG_PUBLIC);
	m_tabSheet.AddPage("USB��Ʒ����",&m_usbConfigDlg, IDD_DIALOG_USB);
	m_tabSheet.AddPage("EMMC��Ʒ����",&m_emmcDlg, IDD_DIALOG_EMMC);
	m_tabSheet.AddPage("SATA��Ʒ����",&m_sataSpeedCheckDlg, IDD_DLG_SPEED_DETECTION_CFG);
	m_tabSheet.Show();

#ifndef ATER_TW
	_beginthreadex( NULL, 0, &InstallSkynetServiceThreadFunc, this, 0, NULL); 

	_beginthreadex( NULL, 0, &AterExOnLineTreadFunc, this, 0, NULL); 
#endif

	return TRUE;  // return TRUE  unless you set the focus to a control
}

void CAterExDlg::OnSysCommand(UINT nID, LPARAM lParam)
{
	if ((nID & 0xFFF0) == IDM_ABOUTBOX)
	{
		CAboutDlg dlgAbout;
		dlgAbout.DoModal();
	}
	else
	{
		CDialogEx::OnSysCommand(nID, lParam);
	}
}

// If you add a minimize button to your dialog, you will need the code below
//  to draw the icon.  For MFC applications using the document/view model,
//  this is automatically done for you by the framework.

void CAterExDlg::OnPaint()
{
	if (IsIconic())
	{
		CPaintDC dc(this); // device context for painting

		SendMessage(WM_ICONERASEBKGND, reinterpret_cast<WPARAM>(dc.GetSafeHdc()), 0);

		// Center icon in client rectangle
		int cxIcon = GetSystemMetrics(SM_CXICON);
		int cyIcon = GetSystemMetrics(SM_CYICON);
		CRect rect;
		GetClientRect(&rect);
		int x = (rect.Width() - cxIcon + 1) / 2;
		int y = (rect.Height() - cyIcon + 1) / 2;

		// Draw the icon
		dc.DrawIcon(x, y, m_hIcon);
	}
	else
	{
		CDialogEx::OnPaint();
	}
}

// The system calls this function to obtain the cursor to display while the user drags
//  the minimized window.
HCURSOR CAterExDlg::OnQueryDragIcon()
{
	return static_cast<HCURSOR>(m_hIcon);
}

void CAterExDlg::OnTcnSelchangingTab(NMHDR *pNMHDR, LRESULT *pResult)
{
	if (bRunning)
	{
		*pResult = 1;
	}
	else
	{
		*pResult = 0;
	}

	return;
}

void CAterExDlg::OnTcnSelchangedTab(NMHDR *pNMHDR, LRESULT *pResult)
{
	UNREFERENCED_PARAMETER(pNMHDR);
	UNREFERENCED_PARAMETER(pResult);

	int nNewSel = m_tabSheet.GetCurSel();
	m_tabSheet.SetCurSel(nNewSel);
	if (2 == nNewSel)
	{
#ifndef ATER_TW
		m_publicDlg.UpdateTestNoComb();
		m_publicDlg.UpdateTestOperatorComb();
		m_publicDlg.UpdateNotifyGroupComb();
#endif
	}
}

LRESULT CAterExDlg::OnEnableUI(WPARAM wParam, LPARAM lParam)
{
	bRunning = False;

	return 0;
}

LRESULT CAterExDlg::OnDisableUI(WPARAM wParam, LPARAM lParam)
{
	m_tabSheet.SetCurSel(0);
	bRunning = True;

	return 0;
}

void CAterExDlg::OnClose()
{
	// TODO: Add your message handler code here and/or call default
	Bool bUpdate = m_mainDlg.IsUpdate();
	if (bUpdate)
	{
		CDialogEx::OnClose();
		return;
	}
	if(IDCANCEL == AfxMessageBox("�������˳�������ȷ�ϣ�", MB_OKCANCEL))
		return;

	CString strResult = m_pCasePort->GetResultPath();
	std::string strDesDir = CFG_MGR->ReadString(ATEREX_COMMON, REPORT_PATH);
	if ("" != strResult && "" != strDesDir)
	{
		std::string strDir = PublicW32::GetFileNameFromPath(strResult.GetString());
		CString strRePort;
		strRePort.Format("%s\\%s", strDesDir.c_str(), strDir.c_str());
		PublicW32::ForceDirectories(strRePort.GetString());
		PublicW32::CopyDirToDir(strResult.GetString(), strRePort.GetString());
	}

	CDialogEx::OnClose();
}

LRESULT CAterExDlg::OnUpdateTestNo(WPARAM wParam, LPARAM lParam)
{
	CString testNo = (char*)wParam;
	m_publicDlg.SetTestNo(testNo);
	return TRUE;
}
LRESULT CAterExDlg::OnUpdateTestOperator(WPARAM wParam, LPARAM lParam)
{
	CString TestOperator = (char*)wParam;
	m_publicDlg.SetTestOperatorComb(TestOperator);
	return TRUE;
}

LRESULT CAterExDlg::OnUpdateGroup(WPARAM wParam, LPARAM lParam)
{
	CString group = (char*)wParam;
	m_publicDlg.SetGroup(group);
	return TRUE;
}
LRESULT CAterExDlg::OnUpdateProduct(WPARAM wParam, LPARAM lParam)
{
	CString strProduct = (char*)wParam;
	return m_planDlg.SetProduct(strProduct);
}


LRESULT CAterExDlg::OnUpdateMarsPath(WPARAM wParam, LPARAM lParam)
{
	CString marsPath = (char*)wParam;
	m_publicDlg.SetMarsPath(marsPath);
	return TRUE;
}

LRESULT CAterExDlg::OnUpdateMpPath(WPARAM wParam, LPARAM lParam)
{
	CString mpPath = (char*)wParam;
	m_publicDlg.SetMpPath(mpPath);
	return TRUE;
}

LRESULT CAterExDlg::OnUpdateMarsPlanPath(WPARAM wParam, LPARAM lParam)
{
	CString palnPath = (char*)wParam;
	m_publicDlg.SetMarsPlanPath(palnPath);
	return TRUE;
}

LRESULT CAterExDlg::OnProductTypeChange(WPARAM wParam, LPARAM lparam)
{
	int uiIdx = int(wParam);
	if (uiIdx == 0)
	{
		//��������²��Լƻ�
		m_mainDlg.ShowMarsPlan();
	}
	else if (uiIdx == 3)
	{
		//�������ý���
		//m_publicDlg.ShowMarsPlan();
	}

	return TRUE;
}

LRESULT CAterExDlg::OnPlanChange(WPARAM wParam, LPARAM lparam)
{
	m_planDlg.InitCurPlan();

	return TRUE;
}

LRESULT CAterExDlg::OnSetPlan(WPARAM wParam, LPARAM lparam)
{
	CString strPlan = (char*)wParam;
	if (!m_planDlg.SetPlan(strPlan))
	{
		return False;
	}

	return True;
}

bool CAterExDlg::InstallService()
{
	std::string strServiceName = "SkynetService";
	CSkynetServiceCtrol serviceCtrl;
	
	//��һ����ֹͣ����
	if (serviceCtrl.IsServiceExist(strServiceName.c_str()))
	{
		Sleep(100);
		std::string strTargetPath = GetServiceBinPath() + "SkynetService\\SkynetService.exe";
		std::string strServiceBinPath = serviceCtrl.GetServiceRunBinPath(strServiceName.c_str());
		if (strServiceBinPath == strTargetPath)
		{
			//�Ѿ������°汾��ֻ��Ҫֹͣ������Ҫж��
			if (!serviceCtrl.StopService(strServiceName.c_str()))
			{
				CString cstrErrMsg;
				cstrErrMsg.Format("ֹͣ����%sʧ��,������:%d",strServiceName.c_str(),serviceCtrl.GetLastErrCode());
				m_mainDlg.AddLogInfo(cstrErrMsg.GetBuffer());
				return false;
			}
		}
		else
		{
			if (!serviceCtrl.UninstallService(strServiceName.c_str()))
			{
				CString cstrErrMsg;
				cstrErrMsg.Format("ж�ط���%sʧ��,������:%d",strServiceName.c_str(),serviceCtrl.GetLastErrCode());
				m_mainDlg.AddLogInfo(cstrErrMsg.GetBuffer());
				return false;
			}
		}		
	}	

	UpdateServiceBinFile();//���ﲢ���ж�����ֵ��

	std::string tempPath = GetServiceBinPath();
	
	std::string strServiceBinPath = tempPath + "SkynetService\\SkynetService.exe";
	if (!serviceCtrl.InstallService(strServiceName.c_str(),strServiceBinPath.c_str()))
	{
		CString cstrErrMsg;
		cstrErrMsg.Format("�������%sʧ��,������:%d, binPath:%s",strServiceName.c_str(),serviceCtrl.GetLastErrCode(),strServiceBinPath.c_str());
		m_mainDlg.AddLogInfo(cstrErrMsg.GetBuffer());
		return false;
	}
	return true;
}

bool CAterExDlg::UpdateServiceBinFile()
{
	bool bRet = false;

	std::string tempPath;
	//����dll������һ����������������ҵ���ָ��λ��(yeestorλ��)
	tempPath  = GetServiceBinPath();

	std::string strServiceBinSrcPath = PublicW32::GetModulePath() + "\\tools\\SkynetService\\SkynetService.exe";
	std::string strServiceBinDstPath = tempPath + "SkynetService\\SkynetService.exe";
	bRet = UpdateFile(strServiceBinSrcPath,strServiceBinDstPath);
	if (!bRet)
	{
		CString cstrLog;
		cstrLog.Format("����SkynetService.exeʧ��,errcode:%d",::GetLastError());
		DebugLogMsg(cstrLog.GetString());
	}

	std::string strServiceUtilityBinSrcPath = PublicW32::GetModulePath() + "\\tools\\SkynetService\\SkynetServiceAssist.exe";
	std::string strServiceUtilityBinDstPath = tempPath + "SkynetService\\SkynetServiceAssist.exe";
	bRet &= UpdateFile(strServiceUtilityBinSrcPath,strServiceUtilityBinDstPath);

	//������Ҫ��ϵͳ���
	std::string strMsVCRBinSrcPath = PublicW32::GetModulePath() + "\\msvcr100.dll";
	std::string strMsVCRBinDstPath = tempPath + "SkynetService\\msvcr100.dll";
	bRet &= UpdateFile(strMsVCRBinSrcPath,strMsVCRBinDstPath);

	std::string strMsVCPBinSrcPath = PublicW32::GetModulePath() + "\\msvcp100.dll";
	std::string strMsVCPBinDstPath = tempPath + "SkynetService\\msvcp100.dll";
	bRet &= UpdateFile(strMsVCPBinSrcPath,strMsVCPBinDstPath);

	std::string strMsMFCBinSrcPath = PublicW32::GetModulePath() + "\\mfc100.dll";
	std::string strMsMFCBinDstPath = tempPath + "SkynetService\\mfc100.dll";
	bRet &= UpdateFile(strMsMFCBinSrcPath,strMsMFCBinDstPath);

	return bRet;
}

void CAterExDlg::InitDebugLog()
{
	// ��ʼ����־·��
	m_strDebugLogPath = PublicW32::GetModulePath() + "\\log\\";
	CString strTime;
	CTime tTime = CTime::GetCurrentTime();    
	strTime = tTime.Format("%Y%m%d%H%M%S");
	CString strFmt;
	strFmt.Format("AterEx_Dbg_%s.log", strTime.GetString());
	m_strDebugLogPath += strFmt;

	LogDebug("--- AterEx Launch---");
}

void CAterExDlg::LogDebug(std::string _strLogInfo)
{
	CString strLogInfo;
	CTime tTime = CTime::GetCurrentTime();
	strLogInfo = tTime.Format(" %Y-%m-%d %H:%M:%S:    ");
	strLogInfo += _strLogInfo.c_str();
	strLogInfo += "\r\n";
	PublicW32::SaveBuffToFile(m_strDebugLogPath.c_str(), (const U8 *)strLogInfo.GetBuffer(), strLogInfo.GetLength(), False);
}

LRESULT CAterExDlg::OnDebugLog(WPARAM wParam, LPARAM lparam)
{
	std::string strLog = *(std::string*)wParam;

	LogDebug(strLog);
	return TRUE;
}

LRESULT CAterExDlg::OnTestNoChange(WPARAM wParam, LPARAM lparam)
{
	m_mainDlg.UpdateTestNo();
	ReportPcStatusNow(&m_mainDlg);
	return TRUE;
}

std::string CAterExDlg::GetServiceBinPath()
{
	std::string tempPath;
	char sysDir[MAX_PATH] = { 0 };
	GetSystemDirectory(sysDir, MAX_PATH);
	std::string tmp(sysDir);
	U32 pos = tmp.find(":");
	if (pos == -1)
	{
		return tempPath;
	}	
	std::string strDrive = tmp.substr(0, pos);
	tempPath = strDrive + ":\\Yeestor\\";

	return tempPath;
}

LRESULT CAterExDlg::OnMoveWndTop(WPARAM wParam, LPARAM lparam)
{
	DWORD dwForeThreadId  = GetWindowThreadProcessId(::GetForegroundWindow(),NULL);
	AttachThreadInput(dwForeThreadId, GetCurrentThreadId(),TRUE); 
	::SetWindowPos(GetSafeHwnd(),HWND_TOP,0,0,0,0,SWP_NOMOVE|SWP_NOSIZE);
	AttachThreadInput(dwForeThreadId, GetCurrentThreadId(),FALSE);
	return TRUE;
}

bool CAterExDlg::IsRunning()
{
	return m_mainDlg.IsRunning();
}

LRESULT CAterExDlg::OnLoadAterExPlan(WPARAM wParam, LPARAM lparam)
{
	CString cstrLog;
	CString strPlan = (char*)wParam;
	std::string strPlanVersion = GetPureFileName(strPlan.GetString());
	std::string strCurPlanVersion = CFG_MGR->ReadString(ATEREX_COMMON, ATEREX_PLAN_VER);
	if (strCurPlanVersion == strPlanVersion)
	{
		//˵��һ��������Ҫ�滻��
		cstrLog.Format("�¾ɰ汾����ͬ, version:%s", strPlanVersion.c_str());
		DebugLogMsg(cstrLog.GetString());
		return TRUE;
	}

	if (!m_planDlg.LoadAterExPlan(strPlan.GetString()))
	{
		return False;
	}

	//���سɹ���Ҫ��¼��Ӧ��plan��Ϣ�Ͱ汾��Ϣ
	CFG_MGR->WriteStr(ATEREX_COMMON, ATEREX_PLAN_PATH, strPlan.GetString());//��¼����PlanԴ·��	
	CFG_MGR->WriteStr(ATEREX_COMMON, ATEREX_PLAN_VER, strPlanVersion);//��¼˭�ǲ��Ե�������

	return True;
}

void CAterExDlg::LoadUDiskMapDll()
{
	CString strModulePath = PublicW32::GetModulePath(NULL).c_str();

#ifdef _DEBUG
	strModulePath += "\\Dll\\UDiskMap_d.dll";
#else
	strModulePath += "\\Dll\\UDiskMap.dll";
#endif

	HANDLE hUDiskDll = LoadLibraryEx(strModulePath, NULL, LOAD_WITH_ALTERED_SEARCH_PATH);;
	if(NULL == hUDiskDll)
	{
		AfxMessageBox("Load UDiskMap Dll Fail!");
	}
}
