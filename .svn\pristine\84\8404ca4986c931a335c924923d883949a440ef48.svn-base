// stdafx.h : include file for standard system include files,
// or project specific include files that are used frequently, but
// are changed infrequently
//

#pragma once

#include "targetver.h"

#define WIN32_LEAN_AND_MEAN             // Exclude rarely-used stuff from Windows headers
// Windows Header Files:
#include <windows.h>
#include <Ntddscsi.h>
#include <dbt.h>



typedef unsigned __int64  U64; 
typedef unsigned long   U32;
typedef unsigned short  U16;
typedef unsigned char   U8;

typedef unsigned long long const    U64_C; 
typedef unsigned long const U32_C;
typedef unsigned short const    U16_C;
typedef unsigned char const U8_C;

typedef volatile unsigned long long U64_V; 
typedef volatile unsigned long  U32_V;
typedef volatile unsigned short U16_V;
typedef volatile unsigned char  U8_V;

#ifndef SAFE_DELETE 
#define SAFE_DELETE(p) { if(p) { delete (p); (p)=NULL; } } 
#endif 
#ifndef SAFE_DELETE_ARRAY 
#define SAFE_DELETE_ARRAY(p) { if(p) { delete[] (p); (p)=NULL; } } 
#endif 
#ifndef SAFE_RELEASE 
#define SAFE_RELEASE(p) { if(p) { (p)->Release(); (p)=NULL; } } 
#endif 
