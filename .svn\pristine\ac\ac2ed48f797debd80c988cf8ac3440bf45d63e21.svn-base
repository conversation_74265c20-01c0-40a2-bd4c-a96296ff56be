﻿<!DOCTYPE html>
<html>
<head>
   <title>Forcing a reboot</title>
   <meta name="generator" content="Help &amp; Manual" />
   <meta name="keywords" content="Forced,Ask to close,<PERSON> if Hung,Hung" />
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <meta http-equiv="Content-Style-Type" content="text/css" />
   <link type="text/css" href="default.css" rel="stylesheet" />
   <script type="text/javascript" src="jquery.js"></script>
   <script type="text/javascript" src="helpman_settings.js"></script>
   <script type="text/javascript" src="helpman_topicinit.js"></script>

   <script type="text/javascript">
     HMSyncTOC("index.html", "forcetypes.htm");
   </script>
   <script type="text/javascript" src="highlight.js"></script>
   <script type="text/javascript">
     $(document).ready(function(){highlight();});
   </script>
</head>
<body style="margin: 0px 0px 0px 0px; background: #FFFFFF;">


<table width="100%" border="0" cellspacing="0" cellpadding="5" bgcolor="#649CCC">
  <tr valign="middle">
    <td align="left">
      <p style="margin: 7px 0px 7px 0px;"><span style="font-size: 16pt; font-weight: bold;"> &nbsp;Forced reboot</span></p>

    </td>
    <td align="right">
     <span style="font-size: 9pt">
     <a href="overview.htm"><img src="..\..\..\..\Program Files (x86)\HelpandManual4\passmark\nav_up_blue.gif" border=0 alt="Top"></a>&nbsp;
     <a href="reboottypes.htm"><img src="..\..\..\..\Program Files (x86)\HelpandManual4\passmark\nav_left_blue.gif" border=0 alt="Previous"></a>&nbsp;
     <a href="commandline.htm"><img src="..\..\..\..\Program Files (x86)\HelpandManual4\passmark\nav_right_blue.gif" border=0 alt="Next"></a>
     </span>
    </td>
  </tr>
</table>


<!-- Placeholder for topic body. -->
<table width="100%" border="0" cellspacing="0" cellpadding="5"><tr valign="top"><td align="left">
<p style="margin: 7px 0px 7px 0px;">Three options are available.</p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold;">Ask to close</span></p>
<p style="margin: 7px 0px 7px 0px;">Default value. Each running application is given the chance to block the shutdown of Windows. Applications will typically block a shutdown if there is data that has not been saved to disk.</p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold;">Force to close</span></p>
<p style="margin: 7px 0px 7px 0px;">Forces processes to terminate. When option is selected, the system does <span style="font-weight: bold;">not</span> ask each running application for permission to shut down Windows. This can cause the applications to lose data. Therefore, you should only use this flag in an emergency.</p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold;">Force if hung</span></p>
<p style="margin: 7px 0px 7px 0px;">If applications do not respond to the query to ask for shutdown permission, then they are forced to close. This option is only taken into account in Windows2000 and XP. In other versions of Windows selecting this option is the same as selecting, “Force to close”.</p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold;">See also</span></p>
<p style="margin: 7px 0px 7px 0px;"><a href="overview.htm" class="topiclink">Overview</a></p>
<p style="margin: 7px 0px 7px 0px;"><a href="reboottypes.htm" class="topiclink">Reboot types</a></p>

</td></tr></table>

</body>
</html>
