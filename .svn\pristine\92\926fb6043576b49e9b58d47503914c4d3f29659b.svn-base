﻿<!DOCTYPE html>
<html>
<head>
   <title>Introduction and Overview</title>
   <meta name="generator" content="Help &amp; Manual" />
   <meta name="keywords" content="Introduction to Rebooter,Overview of Rebooter" />
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <meta http-equiv="Content-Style-Type" content="text/css" />
   <link type="text/css" href="default.css" rel="stylesheet" />
   <script type="text/javascript" src="jquery.js"></script>
   <script type="text/javascript" src="helpman_settings.js"></script>
   <script type="text/javascript" src="helpman_topicinit.js"></script>

   <script type="text/javascript">
     HMSyncTOC("index.html", "overview.htm");
   </script>
   <script type="text/javascript" src="highlight.js"></script>
   <script type="text/javascript">
     $(document).ready(function(){highlight();});
   </script>
</head>
<body style="margin: 0px 0px 0px 0px; background: #FFFFFF;">


<table width="100%" border="0" cellspacing="0" cellpadding="5" bgcolor="#649CCC">
  <tr valign="middle">
    <td align="left">
      <p style="margin: 7px 0px 7px 0px;"><span style="font-size: 16pt; font-weight: bold;"> Rebooter by PassMark Software - Overview</span></p>

    </td>
    <td align="right">
     <span style="font-size: 9pt">
     <a href="overview.htm"><img src="..\..\..\..\Program Files (x86)\HelpandManual4\passmark\nav_up_blue.gif" border=0 alt="Top"></a>&nbsp;
     
     <a href="reboottypes.htm"><img src="..\..\..\..\Program Files (x86)\HelpandManual4\passmark\nav_right_blue.gif" border=0 alt="Next"></a>
     </span>
    </td>
  </tr>
</table>


<!-- Placeholder for topic body. -->
<table width="100%" border="0" cellspacing="0" cellpadding="5"><tr valign="top"><td align="left">
<p><img alt="logo" width="50" height="50" style="margin:0;width:50px;height:50px;border:none" src="logo.png"/></p>
<p> Rebooter is a small utility program developed by PassMark<span style="font-size: 12pt;">­</span>™ Software to help automate the PC hardware testing process. It has been designed to work with PassMark BurnInTest but will also work with 3rd party application. Rebooter allows you to,</p>
<p style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 24px; margin: 7px 0px 7px 24px;"><span style="font-size:10pt; font-family: 'Arial Unicode MS','Lucida Sans Unicode','Arial';color:#000000;display:inline-block;width:24px;margin-left:-24px">-</span>Shutdown, Reboot or Logout of a PC.</p><p style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 24px; margin: 7px 0px 7px 24px;"><span style="font-size:10pt; font-family: 'Arial Unicode MS','Lucida Sans Unicode','Arial';color:#000000;display:inline-block;width:24px;margin-left:-24px">-</span>Reboot a PC from the command line</p><p style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 24px; margin: 7px 0px 7px 24px;"><span style="font-size:10pt; font-family: 'Arial Unicode MS','Lucida Sans Unicode','Arial';color:#000000;display:inline-block;width:24px;margin-left:-24px">-</span>Set a timer so that the PC will reboot after a certain amount of time</p><p style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 24px; margin: 7px 0px 7px 24px;"><span style="font-size:10pt; font-family: 'Arial Unicode MS','Lucida Sans Unicode','Arial';color:#000000;display:inline-block;width:24px;margin-left:-24px">-</span>Setup a reboot loop, to reboot a PC over an over again in a cycle.</p><p style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 24px; margin: 7px 0px 7px 24px;"><span style="font-size:10pt; font-family: 'Arial Unicode MS','Lucida Sans Unicode','Arial';color:#000000;display:inline-block;width:24px;margin-left:-24px">-</span>Force a shutdown or request a shutdown.</p><p style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 24px; margin: 7px 0px 7px 24px;"><span style="font-size:10pt; font-family: 'Arial Unicode MS','Lucida Sans Unicode','Arial';color:#000000;display:inline-block;width:24px;margin-left:-24px">-</span>Enable and disable the Windows auto-login feature. (NT/2000/XP/Vista/Window 7 only)</p><p style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 24px; margin: 7px 0px 7px 24px;"><span style="font-size:10pt; font-family: 'Arial Unicode MS','Lucida Sans Unicode','Arial';color:#000000;display:inline-block;width:24px;margin-left:-24px">-</span>Include reboots into your hardware stress testing plan, (when used with BurnInTest).</p><p style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 24px; margin: 7px 0px 7px 24px;"><span style="font-size:10pt; font-family: 'Arial Unicode MS','Lucida Sans Unicode','Arial';color:#000000;display:inline-block;width:24px;margin-left:-24px">-</span>Run an external application between reboots.</p><p style="text-align: left; text-indent: 0px; padding: 0px 0px 0px 24px; margin: 7px 0px 7px 24px;"><span style="font-size:10pt; font-family: 'Arial Unicode MS','Lucida Sans Unicode','Arial';color:#000000;display:inline-block;width:24px;margin-left:-24px">-</span>Run an external application when all cycles have been completed</p><p style="margin: 7px 0px 7px 0px;"><span style="color: #000000;">&nbsp;</span></p>
<p style="margin: 7px 0px 7px 0px;">For more information click on one of the following topics.</p>
<p style="margin: 7px 0px 7px 0px;"><a href="reboottypes.htm" class="topiclink">Types of reboots and restarts</a>.</p>
<p style="margin: 7px 0px 7px 0px;"><a href="forcetypes.htm" class="topiclink">Forcing reboots</a></p>
<p style="margin: 7px 0px 7px 0px;"><a href="commandline.htm" class="topiclink">Command line options</a></p>
<p style="margin: 7px 0px 7px 0px;"><a href="autorun.htm" class="topiclink">Auto-run applications</a></p>
<p style="margin: 7px 0px 7px 0px;"><a href="autologin.htm" class="topiclink">Auto-login to Windows</a></p>
<p style="margin: 7px 0px 7px 0px;"><a href="cycle.htm" class="topiclink">Cyclic reboot looping</a></p>
<p style="margin: 7px 0px 7px 0px;"><a href="faq.htm" class="topiclink">Frequently asked questions</a></p>
<p style="margin: 7px 0px 7px 0px;"><a href="whats_new.htm" class="topiclink">What's new in this version</a></p>
<p style="margin: 7px 0px 7px 0px;"><a href="copyright.htm" class="topiclink">Copyright and license information</a></p>
<p style="margin: 7px 0px 7px 0px;"><a href="systemreq.htm" class="topiclink">System requirements</a></p>
<p style="margin: 7px 0px 7px 0px;"><a href="contacts.htm" class="topiclink">Contacting PassMark Software</a></p>

</td></tr></table>

</body>
</html>
