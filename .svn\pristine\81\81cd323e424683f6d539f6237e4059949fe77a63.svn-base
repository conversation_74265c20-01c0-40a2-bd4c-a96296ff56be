== Opus Audio Tools ==

This is opus-tools, a set of tools to encode, inspect, and decode
audio in the Opus format.

For more information on Opus see http://www.opus-codec.org/

There is basic documentation in the HTML files included in this archive.

-------------------------------------------------------------------------
These Win32 opus-tools binaries were cross-compiled with mingw from:

http://downloads.xiph.org/releases/opus/opus-tools-0.1.7.tar.gz
corrsponding to https://git.xiph.org/?p=opus-tools.git
commit: 7afc14217cf6a408829bd94a3372b69c3b08344b
tag: v0.1.7

opus 1.1 pre-release
corresponding to https://git.xiph.org/?p=opus.git
commit: 02fed471a4568852d6618e041c4f2af0d7730ee2

http://downloads.xiph.org/releases/flac/flac-1.3.0.tar.xz
http://downloads.xiph.org/releases/ogg/libogg-1.3.1.tar.xz

You can verify the files with:
 sha256sum -c SHA256SUMS.txt
 gpg --verify SHA256SUMS.txt.asc

These tools are available as part of cygwin: http://www.cygwin.com/
-------------------------------------------------------------------------

Please send any comments/bug reports on these tools
by email to Ralph Giles <<EMAIL>> and
Greg Maxwell <<EMAIL>>.
