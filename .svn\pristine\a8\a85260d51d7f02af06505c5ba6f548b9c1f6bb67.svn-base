import re,os
from collections import defaultdict
from openpyxl.styles import  <PERSON><PERSON><PERSON><PERSON>,Alignment,Border,Side,colors,Font
from openpyxl.utils import get_column_letter

def parse_saturation_data(file_path):
    # 匹配两种关键模式的正则表达式
    avg_pattern = r"\|.*clean average (\d+\.?\d*).*dirty average (\d+\.?\d*)"
    saturation_pattern = r"saturation_(\d+)"
    
    result = {}
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            for i, line in enumerate(lines):
                # 匹配平均值行
                avg_match = re.search(avg_pattern, line)
                if avg_match:
                    # 提取并构建单个饱和测试数据
                    dicOneSaturationData = {
                        'clean average': float(avg_match.group(1)),
                        'dirty average': float(avg_match.group(2))
                    }
                
                    # 检查下一行是否包含饱和度数值
                    if i+1 < len(lines):
                        sat_match = re.search(saturation_pattern, lines[i+1])
                        if sat_match:
                            saturation_key = sat_match.group(1)
                            result[saturation_key] = dicOneSaturationData
    except:
        print('open file fail file:'+file_path)
        result = {}
                        
    return result

def GetSaturationData(path:str):
    dataDic = {}
    lognamePass = 'Passed verify performance proxima check_SLC_perf_on_logic_saturation.log'
    lognameFail = 'Failed verify performance proxima check_SLC_perf_on_logic_saturation.log'
    lognameNormal ='verify performance proxima check_SLC_perf_on_logic_saturation.log'
    logPath = os.path.join(path,lognamePass)
    if not os.path.exists(logPath):
        logPath = os.path.join(path,lognameFail)
    if not os.path.exists(logPath):  # Fixed indentation
        logPath = os.path.join(path,lognameNormal)
    if not os.path.exists(logPath):
        print("log not exist")
        return {}
    dataDic = parse_saturation_data(logPath)
        
    return dataDic

#写数据
def WriteSaturationData(wb,strType:str,lData:dict,rgb="00000000",alignment=Alignment(horizontal='center',vertical='center')):
    dataDicRows = {'x1':4,'x2':17,'x4':30}
    strWsName= 'pv_saturation'
    ws=wb[strWsName]
    logdataStartRow = dataDicRows[strType]
    if lData is None:
        return
    for Index,Saturation in enumerate(lData): 
        ws['%s%d'%(get_column_letter(1),logdataStartRow)] = int(Saturation)
        for rowIdx,DataName in enumerate(lData[Saturation]):
            nStartCol = 2
            if 'clean average' == DataName:
                ws['%s%d'%(get_column_letter(nStartCol),logdataStartRow)] = lData[Saturation]['clean average']
                ws['%s%d'%(get_column_letter(nStartCol), logdataStartRow)].alignment=alignment
                ws['%s%d'%(get_column_letter(nStartCol), logdataStartRow)].font=rgb
            if 'dirty average' == DataName:
                ws['%s%d'%(get_column_letter(nStartCol+1),logdataStartRow)] = lData[Saturation]['dirty average']
                ws['%s%d'%(get_column_letter(nStartCol+1), logdataStartRow)].alignment=alignment
                ws['%s%d'%(get_column_letter(nStartCol+1), logdataStartRow)].font=rgb
        logdataStartRow+=1

# 使用示例
if __name__ == "__main__":
    dicAllSaturationData = GetSaturationData(r"D:\LocalReportData\YS8297_E09T(2.5V)_010100_Release_2_64GB_20250415\XU4\PV-COM136-A056_X2_HALT_1745822582")
    WriteSaturationData(None,'x2',dicAllSaturationData)
    print(dicAllSaturationData)