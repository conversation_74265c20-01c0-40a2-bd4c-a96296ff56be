import os,sys,logging,traceback,time,shutil,tempfile,json

curpath = os.path.split(sys.argv[0])[0]
reportPath = curpath
bRmsRun = False
xl = None
if len(sys.argv) > 1:
    #Rms调用报告统计，需要有个独占文件，防止多人同时合并报告
    reportPath = sys.argv[1]

curtime = time.strftime("%Y%m%d%H%M%S", time.localtime())
logname = os.path.join(reportPath, 'SSD_Report_' + curtime + '.log')
#日志配置
logging.basicConfig(filename = logname,
                    filemode='w',
                    format = '%(asctime)s-%(name)s-%(levelname)s-%(module)s: %(message)s',
                    datefmt = '%Y-%m-%d %H:%M:%S %p',
                    level = logging.INFO)


try:
    import openpyxl,requests
    from openpyxl.styles import Alignment
    import PublicFuc,Performance,RS_Performance,Stability,Reliability,CRCTest,NormalBit,NewItem,PerformanceDingRong,Format,FactoryWAF,Summary,IdlePerformance,Performance2
    import Flash_Reliability,Flash_InitPerformance,FlashSummary
    from win32com.client import Dispatch
    import urllib.parse

    strSsdTempDirRoot = os.path.join(tempfile.gettempdir(), 'ssd_report_temp')
    if not os.path.exists(strSsdTempDirRoot):
        os.mkdir(strSsdTempDirRoot)
    PublicFuc.strSsdTempDir = strSsdTempDirRoot

    templateFile = os.path.join(curpath, 'ssd_template.xlsx')
    resultFileName = 'YS908x_版本全面测试用例及报告汇总.xlsx'
    resultFile = os.path.join(reportPath, resultFileName) 
    errDiskFile = os.path.join(reportPath, 'ErrDisk.txt') 
    addpath = os.path.join(reportPath,'ManReport')
    if len(sys.argv) > 1:
        testNo = os.path.split(reportPath)[1]
        enTestNo = urllib.parse.quote(testNo)
        onlyPath = os.path.join(reportPath, 'public.txt') 
        jsonData = {}
        jsonData['token'] = '7d4eb91cfcf8f07e660f731704919d1078a11b170e5a6edbbe7817773d43d8b5'
        jsonData['secret'] = 'SECc552cffafd69496974edab7350652d4c4dd8410a47e9d91422d6f21a2dfe9d61'
        jsonData['atLst'] = '18312006726'
        rmsUrl = 'http://ereport.yeestor.com/sendDingTalkMsg'
        bRmsRun = True

    logging.info('程序开始运行！')
    PublicFuc.GetAllFile(reportPath)
    if bRmsRun:
        PublicFuc.testno = testNo
        PublicFuc.strSsdTempDir += '\\' + testNo #如果是RMS启动的则加上测试单号路径。
    logging.info('PublicFuc.testno:'+PublicFuc.testno)
    wb = openpyxl.load_workbook(filename = templateFile)
    alignment = Alignment(horizontal='center',vertical='center')
    RS_Performance.Run(reportPath, wb, alignment)
    PerformanceDingRong.Run(reportPath, wb, alignment)
    Performance.Run(reportPath, wb, alignment)
    Performance2.Run(reportPath, wb, alignment)
    FactoryWAF.Run(reportPath, wb, alignment)
    Stability.Run(reportPath, wb, alignment)
    Reliability.Run(reportPath, wb, alignment)
    CRCTest.Run(reportPath, wb, alignment)
    NormalBit.Run(reportPath, wb, alignment)
    NewItem.Run(reportPath, wb, alignment)
    Format.Run(reportPath, wb, alignment)
    IdlePerformance.Run(reportPath, wb, alignment)
    PublicFuc.WriteErrDiskFile(errDiskFile)

    Summary.Run(reportPath, wb, alignment)
    wb.save(resultFile)
    
    #if os.path.exists(addpath):
    #    excelLst = ['xls','xlsx']
    #    dst = resultFile
    #    xl = Dispatch("Excel.Application")
    #    wb2 = xl.Workbooks.Open(Filename=dst,UpdateLinks=0)
    #    oldPos = len(wb2.Worksheets)-1
    #    for src_plan in os.listdir(addpath):
    #        src_plan = os.path.join(addpath,src_plan)
    #        src_time = os.listdir(src_plan)
    #        src_time = sorted(src_time,reverse=True)
    #        src_time = os.path.join(src_plan,src_time[0])
    #        for src_xls in os.listdir(src_time):
    #            pos = src_xls.rfind('.')
    #            if -1 != pos:
    #                suffix = src_xls[pos+1:]
    #                if suffix.lower() in excelLst:
    #                    src_xls = os.path.join(src_time,src_xls)
    #                    wb1 = xl.Workbooks.Open(Filename=src_xls,UpdateLinks=0)
    #                    for shes in wb1.sheets: 
    #                        ws1 = wb1.Worksheets(shes.name)
    #                        ws1.Copy(wb2.Worksheets[len(wb2.Worksheets)-1])
    #                    wb1.Close()
    #    #Copy无法复制到最后一个sheet，因此要把最后一个sheet移到原有的位置
    #    wb2.Sheets[len(wb2.Worksheets)-1].Move(wb2.Sheets[oldPos])
    #    wb2.Worksheets[0].Activate()
    #    wb2.Close(SaveChanges=True) 
    #    xl.Quit()

    #此处做Flash品质测试的报告统计
    flash_templateFile = os.path.join(curpath, 'ssd_flash_quality_template.xlsx')
    flash_resultFileName = 'Flash品质测试用例及报告汇总.xlsx'
    flash_resultFile = os.path.join(reportPath, flash_resultFileName) 
    flash_wb = openpyxl.load_workbook(filename = flash_templateFile)
    Flash_Reliability.Run(reportPath, flash_wb, alignment)
    Flash_InitPerformance.Run(reportPath, flash_wb, alignment)
    FlashSummary.Run(reportPath, flash_wb, alignment)
    
    flash_wb.save(flash_resultFile)

    if os.path.exists(PublicFuc.strSsdTempDir):
        shutil.rmtree(PublicFuc.strSsdTempDir)
    if bRmsRun:
        jsonData['type'] = 'link'
        jsonData['title'] = 'eReport报告合并完成通知'
        jsonData['text'] = '测试单号：%s \r\n报告合并成功，请前往查看！'%testNo
        jsonData['url'] = 'http://ereport.yeestor.com/report/download/?nid=SSD&key=%s'%enTestNo
        response = requests.request('POST', rmsUrl, data=jsonData)
        if os.path.exists(onlyPath):
            os.remove(onlyPath)
        #工单系统回调
        strUrl = 'http://ereport.yeestor.com/report/file_download/?product=%s&testNo=%s&file='%('SSD',enTestNo)
        woDic = {}
        woDic['orderNo'] = testNo
        woDic['reportInfoList'] = []
        if os.path.exists(resultFile):
            tempDic = {}
            tempDic['name'] = resultFileName
            tempDic['type'] = 'report'
            tempDic['url'] = strUrl+resultFileName
            woDic['reportInfoList'].append(tempDic)
        woUrl = "http://gateway.yeestor.com:8789/wo/report/status"
        headers = {'Content-Type': 'application/json','ORDER-NO':enTestNo}
        requests.request("POST", woUrl, headers=headers, data=json.dumps(woDic))
    #del xl
    logging.info('结束！')

except:
    print(traceback.format_exc())
    logging.error(traceback.format_exc())
    if bRmsRun:
        jsonData['type'] = 'text'
        jsonData['text'] = 'eReport报告合并异常！@18312006726\r\n测试单号：%s'%testNo
        response = requests.request('POST', rmsUrl, data=jsonData)
        if os.path.exists(onlyPath):
            os.remove(onlyPath)
        if None != xl:
            wb2.Close(False)
            xl.Quit()
            del xl

