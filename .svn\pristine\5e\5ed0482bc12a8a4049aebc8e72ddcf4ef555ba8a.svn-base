#include "StdAfx.h"
#include "AutoStatus.h"

CAutoStatus::CAutoStatus(Bool* _pStatus, Bool _bValue)
	:m_pCurBoolStatus(NULL)
	,m_pCurboolStatus(NULL)
{
	m_pCurBoolStatus = _pStatus;
	*m_pCurBoolStatus = _bValue;
}

CAutoStatus::CAutoStatus(bool* _pStatus, bool _bValue)
	:m_pCurBoolStatus(NULL)
	,m_pCurboolStatus(NULL)
{
	m_pCurboolStatus = _pStatus;
	*m_pCurboolStatus = _bValue;
}


CAutoStatus::~CAutoStatus(void)
{
	if (m_pCurBoolStatus)
	{
		*m_pCurBoolStatus = (*m_pCurBoolStatus)? False : True;
	}

	if (m_pCurboolStatus)
	{
		*m_pCurboolStatus = !(*m_pCurboolStatus);
	}
}
