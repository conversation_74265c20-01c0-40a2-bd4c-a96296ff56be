﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">
<html>
<!-- saved from url=(0029)http://www.helpandmanual.com/ --><head>
   <title>Rebooter by PassMark Software</title>
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />

   <!-- This line includes the general project style sheet (not required) -->
   <meta http-equiv="Content-Style-Type" content="text/css" />
   <link type="text/css" href="default.css" rel="stylesheet" />

   <!-- This block defines the styles of the TOC headings, change them as needed -->
   <style type="text/css">
       .navtitle { font-size: 14pt; font-weight: bold; margin-bottom: 16px; }
       .navbar   { font-size: 10pt; }

       .heading1 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading2 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading3 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading4 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading5 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }
       .heading6 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #000000; text-decoration: none; }

       .hilight1 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight2 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight3 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight4 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight5 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }
       .hilight6 { font-family: Arial,Helvetica; font-weight: normal; font-size: 10pt; color: #FFFFFF; background: #002682; text-decoration: none; }

       TD.toc { padding-bottom: 2px; padding-right: 4px }
   </style>
</head>
<body style="background: #FFFFFF; url(null) fixed no-repeat" onload="parent.loadstate(document.getElementById('tree'));" onunload="parent.savestate(document.getElementById('tree'));">
<p class="navtitle">Rebooter by PassMark Software</p>
<p class="navbar">
<b>Contents</b>
 | <a href="rebooter_kwindex_dyn.html">Index</a>
 | <a href="rebooter_ftsearch.html">Search</a>
</p><hr size="1" />

<!-- Place holder for the TOC - this variable is REQUIRED! -->
<div id="tree">
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a id="a1" href="overview.htm" target="hmcontent" onclick="return parent.hilight('s1')" ondblclick="javascript:parent.toggle('div1')"><span id="s1" class="heading1">Introduction and Overview</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a id="a2" href="reboottypes.htm" target="hmcontent" onclick="return parent.hilight('s2')" ondblclick="javascript:parent.toggle('div2')"><span id="s2" class="heading1">Reboot and Restart types</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a id="a3" href="forcetypes.htm" target="hmcontent" onclick="return parent.hilight('s3')" ondblclick="javascript:parent.toggle('div3')"><span id="s3" class="heading1">Forcing a reboot</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a id="a4" href="commandline.htm" target="hmcontent" onclick="return parent.hilight('s4')" ondblclick="javascript:parent.toggle('div4')"><span id="s4" class="heading1">Command line options</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a id="a5" href="autorun.htm" target="hmcontent" onclick="return parent.hilight('s5')" ondblclick="javascript:parent.toggle('div5')"><span id="s5" class="heading1">Auto-run applications</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a id="a6" href="autologin.htm" target="hmcontent" onclick="return parent.hilight('s6')" ondblclick="javascript:parent.toggle('div6')"><span id="s6" class="heading1">Auto-login to Windows</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a id="a7" href="cycle.htm" target="hmcontent" onclick="return parent.hilight('s7')" ondblclick="javascript:parent.toggle('div7')"><span id="s7" class="heading1">Reboot cycling and looping</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a id="a8" href="whats_new.htm" target="hmcontent" onclick="return parent.hilight('s8')" ondblclick="javascript:parent.toggle('div8')"><span id="s8" class="heading1">What's new</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a id="a9" href="faq.htm" target="hmcontent" onclick="return parent.hilight('s9')" ondblclick="javascript:parent.toggle('div9')"><span id="s9" class="heading1">Problems and FAQ</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a id="a10" href="systemreq.htm" target="hmcontent" onclick="return parent.hilight('s10')" ondblclick="javascript:parent.toggle('div10')"><span id="s10" class="heading1">System requirements</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a id="a11" href="contacts.htm" target="hmcontent" onclick="return parent.hilight('s11')" ondblclick="javascript:parent.toggle('div11')"><span id="s11" class="heading1">Contacting PassMark Software</span></a></td></tr></table>
<table class="toc" border="0" cellpadding="0" cellspacing="0"><tr valign="top"><td class="toc" width="20" align="right"><span class="heading1"><img class="icon" src="cicon9.gif" border="0" alt="Icon"/></span></td><td class="toc" align="left"><a id="a12" href="copyright.htm" target="hmcontent" onclick="return parent.hilight('s12')" ondblclick="javascript:parent.toggle('div12')"><span id="s12" class="heading1">Copyright and License</span></a></td></tr></table>
</div>
<script type="text/javascript">
parent.preloadicons('cicon9.gif');</script>

<hr size="1" /><p style="font-size: 8pt">Rebooter 1.3</p>
</body>
</html>
