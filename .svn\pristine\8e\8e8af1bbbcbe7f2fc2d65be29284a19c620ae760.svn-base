import PublicFuc
import configparser
import csv
import os,re
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors,Font
from datetime import datetime,timedelta
import ErrDiskInfo
from PublicFuc import machaine_info

mp_column_name_map = {} #量产工具低格高格测试结果的列映射关系
dicPfm = {} #存放性能相关数据
dicPcCopyfile ={} #存放文件拷贝相关数据
dicPcCopyfileIdxByPcNo = {} #存放按照电脑索引的数据，由dicPcCopyfile筛选而来。
dicBurnIn ={} #存放burnin相关数据
dicBurnInIdxByPcNo = {} #存放按照电脑索引的数据，由dicBurnIn筛选而来。
h2_column_name_map = {} #DUT的h2测试结果列映射关系
totalDataDic = {} #所有数据的汇总
Excel_Info={}

config = configparser.RawConfigParser()

global g_totalValidCopyFileDataCnt
g_totalValidCopyFileDataCnt = 0

PC_COPY_DATA_START_LINE = 18
PFM_DATA_START_LINE = 6

BURNIN_DATA_START_LINE = 17
global g_totalValidBurInDataCnt
g_totalValidBurInDataCnt = 0

FW_VERSION = '' #主控版本
MP_VERSION = '' #量产工具版本
FLASH_ID = ''  #flash的ID
FLASH_NAME = ''  #flash的型号
FLASH_MCU = '' #主控版本号
TEST_TIME = '' #测试时间
global nPcType 
nPcType = 0        #plan33的电脑类型，0 台式机；1 工控主板；2 笔记本，用于使用对应的worksheet

def Run(curpath, workBook, alignment):

    ws_conclusion = workBook['PCIE-USB扩展卡']
    ws_conclusion.alignment = alignment
    ProDetailInfo(curpath, ws_conclusion)


################################################################
# Plan 35

def ProDetailInfo(curpath, worksheet):
    InitH2CsvColumnNameMap(h2_column_name_map)
    #读Plan35 H2的数据
    pattern = '.+\\\\Plan35\\\\T_GE_U3_C37\\\\H2-1\\\\.+.csv$' 
    ReadDUTH2CsvData(curpath,pattern,totalDataDic,'H2-1')
    pattern = '.+\\\\Plan35\\\\T_GE_U3_C37\\\\H2-2\\\\.+.csv$'
    ReadDUTH2CsvData(curpath,pattern,totalDataDic,'H2-2')

    pattern = '.+\\\\Plan35\\\\T_GE_U3_C37\\\\space_compare_1\\\\.+.ini$'
    ReadResultIniData(curpath,pattern,totalDataDic,'space_compare_1')
    pattern = '.+\\\\Plan35\\\\T_GE_U3_C37\\\\H2-FAT32\\\\.+.csv$'
    ReadDUTH2FormatCsvData(curpath,pattern,totalDataDic,'H2-FAT32')
    pattern = '.+\\\\Plan35\\\\T_GE_U3_C37\\\\space_compare_2\\\\.+.ini$'
    ReadResultIniData(curpath,pattern,totalDataDic,'space_compare_2')

    pattern = '.+\\\\Plan35\\\\T_GE_U3_C37\\\\H2-3\\\\.+.csv$'
    ReadDUTH2FormatCsvData(curpath,pattern,totalDataDic,'H2-3')
    pattern = '.+\\\\Plan35\\\\T_GE_U3_C37\\\\space_compare_3\\\\.+.ini$'
    ReadResultIniData(curpath,pattern,totalDataDic,'space_compare_3')
    pattern = '.+\\\\Plan35\\\\T_GE_U3_C37\\\\H2-4\\\\.+.csv$'
    ReadDUTH2FormatCsvData(curpath,pattern,totalDataDic,'H2-4')
    pattern = '.+\\\\Plan35\\\\T_GE_U3_C37\\\\space_compare_4\\\\.+.ini$'
    ReadResultIniData(curpath,pattern,totalDataDic,'space_compare_4')
    pattern = '.+\\\\Plan35\\\\T_GE_U3_C37\\\\H2-5\\\\.+.csv$' 
    ReadDUTH2CsvData(curpath,pattern,totalDataDic,'H2-5')

    #读Plan35 重启的数据
    pattern = '.+\\\\Plan35\\\\T_GE_U3_C37\\\\RebootAndCheck\\\\.+.ini$'
    ReadSleepOrRebootAndCheckIniData(curpath,pattern,totalDataDic,'RebootAndCheck')
    #读Plan35 安全移除的结果
    pattern = '.+\\\\Plan35\\\\T_GE_U3_C37\\\\EjectUSB\\\\.+.ini$'
    ReadResultIniData(curpath,pattern,totalDataDic,'EjectUSB')

    pattern = '.+\\\\Plan35\\\\T_GE_U3_C37\\\\.+$'
    GetPcInfo(curpath,pattern,totalDataDic,'DevInfo')

    WriteDetailData2WorkSheet(worksheet)

    return True

def GetPcInfo(curpath,patternMapNo,dataDic,caseKey):
    listHaveDeal=[]
    for file in PublicFuc.fileLst:
        if not re.match(patternMapNo, file):
            continue
        pos = file.rfind('Plan35')
        dir = file[:pos]
        if dir not in listHaveDeal:
            listHaveDeal.append(dir)
        else:
            continue
        path=dir+'common_info.ini'
        tempRow=[]
        for strInfo in PublicFuc.mach_info:
            tempRow.append(PublicFuc.GetIniInfoByFilePath(path,'COMMON_INFO',strInfo))
            
        path = dir + 'AterEx_MapNo.ini'
        nDiskCnt=int(PublicFuc.GetIniInfoByFilePath(path,'PUBLIC_MATERIALNO_INFO','materialcnt'))
        pcNo=PublicFuc.GetIniInfoByFilePath(path,'PUBLIC_MATERIALNO_INFO','pcNo')
        for nIndex in range(nDiskCnt):
            strSec='material_port'+str(nIndex)
            strSample=PublicFuc.GetIniInfoByFilePath(path,strSec,'materialno_0')  
            strTestBoarddDNo=PublicFuc.GetIniInfoByFilePath(path,strSec,'testBoardNo')  
            tempRow[machaine_info.TestBoardNo.value]=strTestBoarddDNo            
            
            if strSample not in dataDic:
                    dataDic[strSample] = {}
            if caseKey not in dataDic[strSample]:
                dataDic[strSample][caseKey] = tempRow
            else:
                #如果已经有数据，看是否是新数据，新数据才覆盖
                tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
                oldFileName = dataDic[strSample][tmpItemKey]
                oldTime = os.path.getmtime(oldFileName)
                fileMdTime = os.path.getmtime(file)
                if fileMdTime < oldTime:
                    continue#数据不是新的，不做读取覆盖

                dataDic[strSample][caseKey] = tempRow #新数据覆盖

            tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_pcnoKey)
            dataDic[strSample][tmpItemKey] = pcNo
            tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
            dataDic[strSample][tmpItemKey] = path


def ReadMPRawCsvData(curpath,pattern,dataDic,caseKey):
    #fileIdx = 1
    csvHeaderColumn = []
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除

        #file_data = []
        with open(file,mode='r', buffering=-1, encoding='cp936') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            birth_header = next(csv_reader)  # 读取第一行每一列的标题
            if csvHeaderColumn == []:
                csvHeaderColumn = birth_header
                InitMPCsvColumnNameMap(mp_column_name_map,csvHeaderColumn)

            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中
                key = row[mp_column_name_map['FlashNO']]
                flashID = row[mp_column_name_map['FlashID']]
                cap = row[mp_column_name_map['LogCap']]
                errcode = row[mp_column_name_map['MP_Result']]
                mpTime = row[mp_column_name_map['MP_Time']]
                badBlk = row[mp_column_name_map['BadBlkNum']]
                mpMode = row[mp_column_name_map['MPStatus']]
                tempRow = [flashID,cap,errcode,mpTime,badBlk,mpMode]

                #begin此段为独立的记录公共信息的代码块
                global FW_VERSION
                global MP_VERSION
                global FLASH_ID
                global FLASH_NAME
                global FLASH_MCU
                if FW_VERSION == '':
                    FW_VERSION = row[mp_column_name_map['fw_version']]
                if MP_VERSION == '':
                    MP_VERSION = row[mp_column_name_map['mp_version']]
                if FLASH_ID == '':
                    FLASH_ID = flashID
                if FLASH_NAME == '':
                    FLASH_NAME = row[mp_column_name_map['FlashName']]
                if FLASH_MCU == '':
                    FLASH_MCU = row[mp_column_name_map['ControlName']]
                #end
               
                
                if key not in dataDic:
                    dataDic[key] = {}
                if 'cap' not in dataDic[key]:
                    dataDic[key]['cap'] = ''
                if cap != '':
                    dataDic[key]['cap'] = cap
                if caseKey not in dataDic[key]:
                    dataDic[key][caseKey] = tempRow
                else:
                    #如果已经有数据，看是否是新数据，新数据才覆盖
                    tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
                    oldFileName = dataDic[key][tmpItemKey]
                    oldTime = os.path.getmtime(oldFileName)
                    fileMdTime = os.path.getmtime(file)
                    if fileMdTime < oldTime:
                        continue#数据不是新的，不做读取覆盖

                    dataDic[key][caseKey] = tempRow #新数据覆盖

                pcNo = row[mp_column_name_map['PC_Name']]
                tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_pcnoKey)
                dataDic[key][tmpItemKey] = pcNo
                tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
                dataDic[key][tmpItemKey] = file
  

def InitH2CsvColumnNameMap(columnNameMap):
    columnNameMap.clear()
    columnNameMap['Flash编号'] = 3
    columnNameMap['USB协议'] = 1
    columnNameMap['(H2)写速度'] = 14
    columnNameMap['(H2)读速度'] = 15
    columnNameMap['(H2)错误'] = 17 #PASS,or 错误码
    columnNameMap['first_err_offset'] = 18
    columnNameMap['测试PC编号'] = 5
    columnNameMap['测试架编号'] = 7

def ReadDUTH2CsvData(curpath,pattern,dataDic,caseKey):
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]

        #file_data = []
        with open(file,mode='r', buffering=-1, encoding='cp936') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            birth_header = next(csv_reader)  # 读取第一行每一列的标题
            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中
                key = row[h2_column_name_map['Flash编号']]
                usbVersion = row[h2_column_name_map['USB协议']]
                wSpeed = row[h2_column_name_map['(H2)写速度']]
                rSpeed = row[h2_column_name_map['(H2)读速度']]
                result = row[h2_column_name_map['(H2)错误']]
                firstErr = row[h2_column_name_map['first_err_offset']]
                strTestPosition=row[h2_column_name_map['测试架编号']]
                tempRow = [usbVersion,wSpeed,rSpeed,result,firstErr,strTestPosition]
                if key not in dataDic:
                    dataDic[key] = {}
                if caseKey not in dataDic[key]:
                    dataDic[key][caseKey] = tempRow
                else:
                    #如果已经有数据，看是否是新数据，新数据才覆盖
                    tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
                    oldFileName = dataDic[key][tmpItemKey]
                    oldTime = os.path.getmtime(oldFileName)
                    fileMdTime = os.path.getmtime(file)
                    if fileMdTime < oldTime:
                        continue#数据不是新的，不做读取覆盖

                    dataDic[key][caseKey] = tempRow

                pcNo = row[h2_column_name_map['测试PC编号']]
                tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_pcnoKey)
                dataDic[key][tmpItemKey] = pcNo
                tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
                dataDic[key][tmpItemKey] = file

def ReadDUTH2FormatCsvData(curpath,pattern,dataDic,caseKey):
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]

        #file_data = []
        with open(file,mode='r', buffering=-1, encoding='cp936') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            birth_header = next(csv_reader)  # 读取第一行每一列的标题
            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中
                key = row[h2_column_name_map['Flash编号']]
                usbVersion = row[h2_column_name_map['USB协议']]
                result = row[10]            #exFAT和NTFS格式化的结构在第10列，K列
                tempRow = [usbVersion,result]
                if key not in dataDic:
                    dataDic[key] = {}
                if caseKey not in dataDic[key]:
                    dataDic[key][caseKey] = tempRow
                else:
                    #如果已经有数据，看是否是新数据，新数据才覆盖
                    tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
                    oldFileName = dataDic[key][tmpItemKey]
                    oldTime = os.path.getmtime(oldFileName)
                    fileMdTime = os.path.getmtime(file)
                    if fileMdTime < oldTime:
                        continue#数据不是新的，不做读取覆盖

                    dataDic[key][caseKey] = tempRow

                pcNo = row[h2_column_name_map['测试PC编号']]
                tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_pcnoKey)
                dataDic[key][tmpItemKey] = pcNo
                tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
                dataDic[key][tmpItemKey] = file

def ReadSleepOrRebootAndCheckIniData(curpath,pattern,dataDic,caseKey):
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]

        config.clear()
        config.read(file,encoding = 'gbk')
        nDiskCnt = int(config['Info']['DiskCount'])
        nCycle = int(config['Info']['Cycles'])
        bSecondPlan=False
        for nDiskIndex in range(nDiskCnt):
            strDisk = 'Disk' + str(nDiskIndex)
            if strDisk in config:
                MaterialNo=config[strDisk]['MaterialNo']
                strResult = 'PASS'
                for nTestRount in range(nCycle):
                    strTestRount='TestCount_'+str(nTestRount)
                    if strTestRount in config[strDisk]:
                        if '0' == config[strDisk][strTestRount]:
                            strResult='fail 识别不到盘'
                            break
                    else:
                        strResult='fail 识别不到盘'
            else: 
                bSecondPlan=True    
                break
            tempRow=[strResult]
            if MaterialNo not in dataDic:
                    dataDic[MaterialNo] = {}
            if caseKey not in dataDic[MaterialNo]: 
                dataDic[MaterialNo][caseKey] = tempRow
            else:
                #如果已经有数据，看是否是新数据，新数据才覆盖
                tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
                oldFileName = dataDic[MaterialNo][tmpItemKey]
                oldTime = os.path.getmtime(oldFileName)
                fileMdTime = os.path.getmtime(file)
                if fileMdTime < oldTime:
                    continue#数据不是新的，不做读取覆盖
                dataDic[MaterialNo][caseKey] = tempRow
            pos = file.rfind('Plan35')
            dir = file[:pos]         
            path = dir + 'AterEx_MapNo.ini'
            pcNo=PublicFuc.GetIniInfoByFilePath(path,'PUBLIC_MATERIALNO_INFO','pcNo')
            tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_pcnoKey)
            dataDic[MaterialNo][tmpItemKey] = pcNo
            tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
            dataDic[MaterialNo][tmpItemKey] = file
        if True==bSecondPlan:
                for item in config:
                        if 'Info' == item or "DEFAULT"==item:
                            continue
                        else:
                            MaterialNo=item
                            strResult = 'PASS'
                            if nCycle != len(config[item]):
                                strResult='fail 识别不到盘'
                            else:
                                for val in config[item].values():
                                    if val == '0':
                                        strResult='fail 识别不到盘'
                        tempRow=[strResult]
                        if MaterialNo not in dataDic:
                                dataDic[MaterialNo] = {}
                        if caseKey not in dataDic[MaterialNo]: 
                            dataDic[MaterialNo][caseKey] = tempRow
                        else:
                            #如果已经有数据，看是否是新数据，新数据才覆盖
                            tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
                            oldFileName = dataDic[MaterialNo][tmpItemKey]
                            oldTime = os.path.getmtime(oldFileName)
                            fileMdTime = os.path.getmtime(file)
                            if fileMdTime < oldTime:
                                continue#数据不是新的，不做读取覆盖
                            dataDic[MaterialNo][caseKey] = tempRow
                        pos = file.rfind('Plan35')
                        dir = file[:pos]         
                        path = dir + 'AterEx_MapNo.ini'
                        pcNo=PublicFuc.GetIniInfoByFilePath(path,'PUBLIC_MATERIALNO_INFO','pcNo')
                        tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_pcnoKey)
                        dataDic[MaterialNo][tmpItemKey] = pcNo
                        tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
                        dataDic[MaterialNo][tmpItemKey] = file

# def ReadSleepOrRebootAndCheckIniData(curpath,pattern,dataDic,caseKey):
#     for file in PublicFuc.fileLst:
#         if not re.match(pattern, file):
#             continue
#         pos = file.rfind('\\')
#         imagepath = file[:pos]

#         config.clear()
#         config.read(file,encoding = 'gbk')
#         nDiskCnt = int(config['Info']['DiskCount'])
#         nCycle = int(config['Info']['Cycles'])
#         for nDiskIndex in range(nDiskCnt):
#             strDisk = 'Disk' + str(nDiskIndex)
#             MaterialNo=config[strDisk]['MaterialNo']
#             strResult = 'PASS'
#             for nTestRount in range(nCycle):
#                 strTestRount='TestCount_'+str(nTestRount)
#                 if strTestRount in config[strDisk]:
#                     if '0' == config[strDisk][strTestRount]:
#                         strResult='NA'
#                         break
#                 else:
#                     strResult='NA'
#             tempRow=[strResult]
#             if MaterialNo not in dataDic:
#                     dataDic[MaterialNo] = {}
#             if caseKey not in dataDic[MaterialNo]: 
#                 dataDic[MaterialNo][caseKey] = tempRow
#             else:
#                 #如果已经有数据，看是否是新数据，新数据才覆盖
#                 tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
#                 oldFileName = dataDic[MaterialNo][tmpItemKey]
#                 oldTime = os.path.getmtime(oldFileName)
#                 fileMdTime = os.path.getmtime(file)
#                 if fileMdTime < oldTime:
#                     continue#数据不是新的，不做读取覆盖
#                 dataDic[MaterialNo][caseKey] = tempRow
#             pos = file.rfind('Plan34')
#             dir = file[:pos]         
#             path = dir + 'AterEx_MapNo.ini'
#             pcNo=PublicFuc.GetIniInfoByFilePath(path,'PUBLIC_MATERIALNO_INFO','pcNo')
#             tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_pcnoKey)
#             dataDic[MaterialNo][tmpItemKey] = pcNo
#             tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
#             dataDic[MaterialNo][tmpItemKey] = file


def ReadResultIniData(curpath,pattern,dataDic,caseKey):
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]

        config.clear()
        config.read(file,encoding = 'gbk')
        strResult = 'PASS'
        for section in config.sections():
            if '0' == config[section]['result']:
                strResult = 'fail'
            tempRow=[strResult]
            if section not in dataDic:
                    dataDic[section] = {}
            if caseKey not in dataDic[section]: 
                dataDic[section][caseKey] = tempRow
            else:
                #如果已经有数据，看是否是新数据，新数据才覆盖
                tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
                oldFileName = dataDic[section][tmpItemKey]
                oldTime = os.path.getmtime(oldFileName)
                fileMdTime = os.path.getmtime(file)
                if fileMdTime < oldTime:
                    continue#数据不是新的，不做读取覆盖
                dataDic[section][caseKey] = tempRow
            pos = file.rfind('Plan34')
            dir = file[:pos]         
            path = dir + 'AterEx_MapNo.ini'
            pcNo=PublicFuc.GetIniInfoByFilePath(path,'PUBLIC_MATERIALNO_INFO','pcNo')
            tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_pcnoKey)
            dataDic[section][tmpItemKey] = pcNo
            tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
            dataDic[section][tmpItemKey] = file

letter=[['G','H','I','L','M','N','O','R','S','T','X','Y','U','V','W','P','Q']]

def WriteDetailData2WorkSheet(worksheet):
    InitReportTemplateInWorkSheet(worksheet)
    nStartRow=11
    curLine = nStartRow
    writen={}
    for key in totalDataDic:
        if len(totalDataDic[key])==0:
            continue
        strTmp=list(totalDataDic[key].keys())[0]
        tmpItemKey = ErrDiskInfo.GetCombinedKeyName(strTmp,ErrDiskInfo.g_pcnoKey)
        #tmpItemKey = ErrDiskInfo.GetCombinedKeyName('H2-1',ErrDiskInfo.g_pcnoKey)
        strPcNo=totalDataDic[key][tmpItemKey]
        curLine=Excel_Info[strPcNo][key]
        if strPcNo not in writen:
            writen[strPcNo]=0

        if 0 == writen[strPcNo]:
            writen[strPcNo]=1
            tmpItemKey = ErrDiskInfo.GetCombinedKeyName('H2-1',ErrDiskInfo.g_pcnoKey)
            strPcNo=totalDataDic[key][tmpItemKey]
            worksheet['%s%d'%(letter[nPcType][0],curLine)] = strPcNo
            if 'DevInfo' in totalDataDic[key]:
                worksheet['%s%d'%('A',curLine)] = totalDataDic[key]['DevInfo'][machaine_info.PcNum.value]
        if 'DevInfo' in totalDataDic[key]:  
            worksheet['%s%d'%(letter[nPcType][1],curLine)] = totalDataDic[key]['DevInfo'][machaine_info.TestBoardNo.value] 
        if 'H2-1' in totalDataDic[key]:  
            worksheet['%s%d'%(letter[nPcType][2],curLine)] = key
            worksheet['%s%d'%(letter[nPcType][3],curLine)] = totalDataDic[key]['H2-1'][1] 
            worksheet['%s%d'%(letter[nPcType][4],curLine)] = totalDataDic[key]['H2-1'][2] 
            worksheet['%s%d'%(letter[nPcType][5],curLine)] = totalDataDic[key]['H2-1'][3]
        if 'H2-2' in totalDataDic[key]: 
            worksheet['%s%d'%(letter[nPcType][6],curLine)] = totalDataDic[key]['H2-2'][3]

        if 'H2-3' in totalDataDic[key]: 
            if 'space_compare_3' in totalDataDic[key]:
                if totalDataDic[key]['H2-3'][1].lower() == 'pass':
                    worksheet['%s%d'%(letter[nPcType][7],curLine)] = totalDataDic[key]['space_compare_3'][0] 
                else:
                    worksheet['%s%d'%(letter[nPcType][7],curLine)] = 'fail 容量不符'
            else:
                worksheet['%s%d'%(letter[nPcType][7],curLine)] = 'fail 容量验证失败'
        
        if 'H2-4' in totalDataDic[key]: 
            if 'space_compare_4' in totalDataDic[key]:
                if totalDataDic[key]['H2-4'][1].lower() == 'pass':
                    worksheet['%s%d'%(letter[nPcType][8],curLine)] = totalDataDic[key]['space_compare_4'][0] 
                else:
                    worksheet['%s%d'%(letter[nPcType][8],curLine)] = 'fail 容量不符'
            else:
                worksheet['%s%d'%(letter[nPcType][8],curLine)] = 'fail 容量验证失败'

        if 'RebootAndCheck' in totalDataDic[key]:
            worksheet['%s%d'%(letter[nPcType][9],curLine)] = totalDataDic[key]['RebootAndCheck'][0] 
        if 'EjectUSB' in totalDataDic[key]:
            worksheet['%s%d'%(letter[nPcType][10],curLine)] = totalDataDic[key]['EjectUSB'][0]
        if 'H2-5' in totalDataDic[key]:
            worksheet['%s%d'%(letter[nPcType][12],curLine)] = totalDataDic[key]['H2-5'][1] 
            worksheet['%s%d'%(letter[nPcType][13],curLine)] = totalDataDic[key]['H2-5'][2] 
            worksheet['%s%d'%(letter[nPcType][14],curLine)] = totalDataDic[key]['H2-5'][3]

        if 'space_compare_1' in totalDataDic[key]:
            worksheet['%s%d'%(letter[nPcType][15],curLine)] = totalDataDic[key]['space_compare_1'][0]
        if 'H2-FAT32' in totalDataDic[key]:
            if 'space_compare_2' in totalDataDic[key]:
                if totalDataDic[key]['H2-FAT32'][1].lower() == 'pass':
                    worksheet['%s%d'%(letter[nPcType][16],curLine)] = totalDataDic[key]['space_compare_2'][0]
                else:
                    worksheet['%s%d'%(letter[nPcType][16],curLine)] = 'fail 容量不符'
            else:
                worksheet['%s%d'%(letter[nPcType][16],curLine)] = 'fail 容量验证失败'

        if 'H2-1' in totalDataDic[key] and 'H2-2' in totalDataDic[key] and 'H2-3' in totalDataDic[key]\
        and 'H2-4' in totalDataDic[key] and 'H2-5' in totalDataDic[key]and 'RebootAndCheck' in totalDataDic[key]\
        and 'space_compare_1' in totalDataDic[key] and 'space_compare_2' in totalDataDic[key] and 'space_compare_3' in totalDataDic[key]\
        and 'space_compare_4' in totalDataDic[key] and 'EjectUSB' in totalDataDic[key]:
            if 'pass' == totalDataDic[key]['H2-1'][3].lower() and 'pass' == totalDataDic[key]['H2-2'][3].lower()\
            and 'pass' == totalDataDic[key]['H2-3'][1].lower() and 'pass' ==  totalDataDic[key]['H2-4'][1].lower()\
            and 'pass' == totalDataDic[key]['RebootAndCheck'][0].lower() and 'pass' == totalDataDic[key]['H2-5'][3].lower()\
            and 'pass' == totalDataDic[key]['space_compare_1'][0].lower() and 'pass' ==  totalDataDic[key]['space_compare_2'][0].lower()\
            and 'pass' == totalDataDic[key]['space_compare_3'][0].lower() and 'pass' ==  totalDataDic[key]['space_compare_4'][0].lower()\
            and 'pass' == totalDataDic[key]['EjectUSB'][0].lower() and 'pass' == totalDataDic[key]['H2-FAT32'][1].lower():
                strPass = 'PASS'
            else:
                strPass = 'fail' 
        else:
            strPass = 'fail' 
        worksheet['%s%d'%(letter[nPcType][11],curLine)] = strPass
        for it in range(len(letter[nPcType])):
            strData=worksheet['%s%d'%(letter[nPcType][it],curLine)].value
            if strData!=None:
                if 'fail' in strData:
                    worksheet['%s%d'%(letter[nPcType][it],curLine)].font=PublicFuc.red_font
        curLine+=1
    return 

def InitReportTemplateInWorkSheet(worksheet):
    cellfont=Font('宋体',size=10,color=colors.BLACK,bold=False,italic=False)
    #得到数据总行数
    nStartRow=11
    nEndCol=26
    nMergeLine=7
    
    dictSortByPcNum={}
    for key,value in totalDataDic.items():
        if len(value)==0:
            continue
        nPcNum=0
        strTmp=list(value.keys())[0]
        tmpItemKey = ErrDiskInfo.GetCombinedKeyName(strTmp,ErrDiskInfo.g_pcnoKey)
        if value[tmpItemKey] not in Excel_Info:
            Excel_Info[value[tmpItemKey]]={}
        Excel_Info[value[tmpItemKey]][key]=1

        if 'DevInfo' in totalDataDic[key]:
            nPcNum = int(totalDataDic[key]['DevInfo'][machaine_info.PcNum.value])
        if value[tmpItemKey] not in dictSortByPcNum:
            dictSortByPcNum[value[tmpItemKey]]=nPcNum

    sorted_dict = {k: v for k, v in sorted(dictSortByPcNum.items(), key=lambda item: int(item[1]))}      
    nTmpStartRow=nStartRow
    nAllLine=0
    nNum=1
    for it in sorted_dict:
        if it in Excel_Info:
            for item in Excel_Info[it]:
                while int(sorted_dict[it])>nNum:
                    worksheet['%s%d'%('A',nTmpStartRow)] = str(nNum)
                    for rowIdx in range(2):
                        for col in range(nEndCol):
                            worksheet['%s%d'%(get_column_letter(col+1), nTmpStartRow+rowIdx)].alignment = PublicFuc.alignment
                            bottomLine = 'thin'
                            rightLine = 'thin'
                            worksheet['%s%d'%(get_column_letter(col+1), nTmpStartRow+rowIdx)].border = PublicFuc.my_border('thin',bottomLine,'thin',rightLine)
                            worksheet['%s%d'%(get_column_letter(col+1), nTmpStartRow+rowIdx)].font = cellfont
                        for col in range(nMergeLine):
                            worksheet.merge_cells(start_row=nTmpStartRow, start_column=col+1, end_row=nTmpStartRow****, end_column=col+1)
                    nNum+=1
                    nTmpStartRow+=2
                Excel_Info[it][item]=nTmpStartRow
                nTmpStartRow+=1
                nAllLine+=1
            nNum+=1


            nMergeCnt=nAllLine
            if 0 == nMergeCnt:
                return

            Line=list(Excel_Info[it].values())
            nStartRow=min(Line)
            for rowIdx in range(nMergeCnt):
                for col in range(nEndCol):
                    worksheet['%s%d'%(get_column_letter(col+1), nStartRow+rowIdx)].alignment = PublicFuc.alignment
                    bottomLine = 'thin'
                    rightLine = 'thin'
                    worksheet['%s%d'%(get_column_letter(col+1), nStartRow+rowIdx)].border = PublicFuc.my_border('thin',bottomLine,'thin',rightLine)
                    worksheet['%s%d'%(get_column_letter(col+1), nStartRow+rowIdx)].font = cellfont
        
            #合并单元格
            for listPcName in Excel_Info.values():
                nMergeCnt=len(listPcName)
                Line=list(listPcName.values())
                nStartRow=min(Line)
                for col in range(nMergeLine):
                    worksheet.merge_cells(start_row=nStartRow, start_column=col+1, end_row=nStartRow+nMergeCnt-1, end_column=col+1)


    # for listPcName in Excel_Info:
    #     for item in Excel_Info[listPcName]:
    #         Excel_Info[listPcName][item]=nTmpStartRow
    #         nTmpStartRow+=1
    #         nAllLine+=1


    # nMergeCnt=nAllLine
    # if 0 == nMergeCnt:
    #     return

    # for rowIdx in range(nMergeCnt):
    #     for col in range(nEndCol):
    #         worksheet['%s%d'%(get_column_letter(col+1), nStartRow+rowIdx)].alignment = PublicFuc.alignment
    #         bottomLine = 'thin'
    #         rightLine = 'thin'
    #         worksheet['%s%d'%(get_column_letter(col+1), nStartRow+rowIdx)].border = PublicFuc.my_border('thin',bottomLine,'thin',rightLine)
    #         worksheet['%s%d'%(get_column_letter(col+1), nStartRow+rowIdx)].font = cellfont
   
    # #合并单元格
    # for listPcName in Excel_Info.values():
    #     nMergeCnt=len(listPcName)
    #     Line=list(listPcName.values())
    #     nStartRow=min(Line)
    #     for col in range(nMergeLine):
    #         worksheet.merge_cells(start_row=nStartRow, start_column=col+1, end_row=nStartRow+nMergeCnt-1, end_column=col+1)

#######################################################################






def ProBurnIn(curpath, worksheet):
    pattern = '.+\\\\Plan2\\\\T_GE_U3_C7\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicBurnIn,'HighFormat')

    KeySet = ['Cap','pc_no','Duration','BitCycle','qa_err_msg']
    pattern = '.+\\\\Plan2\\\\T_GE_U3_C7\\\\BurnIn测试\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicBurnIn, 'BurnIn', KeySet, '',0)

    #plan11
    pattern = '.+\\\\Plan11\\\\T_GE_U3_C3\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicBurnIn,'HighFormat')
    pattern = '.+\\\\Plan19\\\\T_GE_U3_C3\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicBurnIn,'HighFormat')
    pattern = '.+\\\\Plan20\\\\T_GE_U3_C3\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicBurnIn,'HighFormat')

    pattern = '.+\\\\Plan11\\\\T_GE_U3_C7\\\\BurnIn测试_24H\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicBurnIn, 'BurnIn', KeySet, '',0)
    pattern = '.+\\\\Plan19\\\\T_GE_U3_C7\\\\BurnIn测试_24H\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicBurnIn, 'BurnIn', KeySet, '',0)
    pattern = '.+\\\\Plan20\\\\T_GE_U3_C7\\\\BurnIn测试_24H\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicBurnIn, 'BurnIn', KeySet, '',0)

    #plan12
    pattern = '.+\\\\Plan12\\\\T_GE_U3_C3\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicBurnIn,'HighFormat')

    pattern = '.+\\\\Plan12\\\\T_GE_U3_C29\\\\BurnIn测试_48H\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicBurnIn, 'BurnIn', KeySet, '',0)

    #plan13
    pattern = '.+\\\\Plan13\\\\T_GE_U3_C3\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicBurnIn,'HighFormat')

    pattern = '.+\\\\Plan13\\\\T_GE_U3_C30\\\\BurnIn测试_72H\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicBurnIn, 'BurnIn', KeySet, '',0)

    #plan24
    pattern = '.+\\\\Plan24\\\\T_GE_U3_C3\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicBurnIn,'HighFormat')

    pattern = '.+\\\\Plan24\\\\T_GE_U3_C29\\\\BurnIn测试_48H\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicBurnIn, 'BurnIn', KeySet, '',0)

    #plan25
    pattern = '.+\\\\Plan25\\\\T_GE_U3_C3\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicBurnIn,'HighFormat')

    pattern = '.+\\\\Plan25\\\\T_GE_U3_C30\\\\BurnIn测试_72H\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicBurnIn, 'BurnIn', KeySet, '',0)

    InitDicBurInDataByPcNo()
    startLine = BURNIN_DATA_START_LINE
    WriteBurnInDic(worksheet, startLine, dicBurnInIdxByPcNo)
    worksheet['%s%d'%('B', 9)] = FLASH_MCU
    worksheet['%s%d'%('F', 9)] = MP_VERSION
    worksheet['%s%d'%('F', 11)] = PublicFuc.GetDate()
    PublicFuc.WriteReportTime(worksheet,'N',1)
    PublicFuc.WriteReportOperator(worksheet,'P',1)

#得到按照PCno索引的字典
def InitDicBurInDataByPcNo():
    global g_totalValidBurInDataCnt
    for key in dicBurnIn:
        subDic = {}
        subDic[key] = dicBurnIn[key]
        if 'HighFormat' in dicBurnIn[key]:
            row = subDic[key]['HighFormat']
            pcNo = row[2]
            if pcNo not in dicBurnInIdxByPcNo:
                dicBurnInIdxByPcNo[pcNo] = []          
            dicBurnInIdxByPcNo[pcNo].append(subDic)
            g_totalValidBurInDataCnt += 1
            continue
        #如果没有量产数据，需求要求要输出BIT数据。因此按bit数据中的PC编号分组
        if 'BurnIn' in dicBurnIn[key]:
            row = subDic[key]['BurnIn']
            pcNo = row[1]
            if pcNo not in dicBurnInIdxByPcNo:
                dicBurnInIdxByPcNo[pcNo] = []          
            dicBurnInIdxByPcNo[pcNo].append(subDic)
            g_totalValidBurInDataCnt += 1


def WriteBurnInDic(worksheet, startLine, dataDic):
    InitBurnInReportTemplateInWorkSheet(worksheet)#绘制表格

    curLine = startLine
    serialNo = 1
    failcnt = 0
    for pcNo in dicBurnInIdxByPcNo:
        listRows = dicBurnInIdxByPcNo[pcNo]
        listRows.sort(key = lambda x: list(x.keys())[0])
        for dicSample in listRows:
            for key in dicSample:
                if 'HighFormat' in dicSample[key]:
                    rowFormat = dicSample[key]['HighFormat']
                    worksheet['%s%d'%('A', curLine)] = serialNo #填写flash编号
                    cap = rowFormat[0]
                    mode = rowFormat[1]
                    worksheet['%s%d'%('B', curLine)] = key
                    worksheet['%s%d'%('C', curLine)] = cap
                    worksheet['%s%d'%('D', curLine)] = mode
                    #PCNO已经在构造表格的时候写入，此处不必写入

                if 'BurnIn' in dicSample[key]:
                    rowBurIn = dicSample[key]['BurnIn']
                    costTime = rowBurIn[2]
                    runcircle = rowBurIn[3]
                    errMsg = rowBurIn[4]
                    worksheet['%s%d'%('G', curLine)] =  costTime
                    worksheet['%s%d'%('H', curLine)] = runcircle
                    bPass = True
                    if errMsg != '':
                        bPass = False

                    if bPass == True:
                        worksheet['%s%d'%('I', curLine)] = 'PASS'
                        worksheet['%s%d'%('K', curLine)] = 'PASS'
                    else:
                        worksheet['%s%d'%('I', curLine)] = errMsg
                        worksheet['%s%d'%('K', curLine)] = 'FAIL'
                        worksheet['%s%d'%('I', curLine)].fill = PublicFuc.warnFill
                        worksheet['%s%d'%('K', curLine)].fill = PublicFuc.warnFill
                        failcnt += 1
                        tmpItemKey = ErrDiskInfo.GetCombinedKeyName('BurnIn',ErrDiskInfo.g_filepathKey)
                        file = dicSample[key][tmpItemKey]
                        PublicFuc.AppendErrDiskInfo('常温老化测试_Err',key,errMsg,pcNo,file)

                else:
                    worksheet['%s%d'%('I', curLine)] = 'FAIL'
                    worksheet['%s%d'%('K', curLine)] = 'FAIL'
                    worksheet['%s%d'%('I', curLine)].fill = PublicFuc.warnFill
                    worksheet['%s%d'%('K', curLine)].fill = PublicFuc.warnFill
                    failcnt += 1
                    
                curLine += 1
                serialNo += 1
                    
    if g_totalValidBurInDataCnt > 0:
        passRatio = float(g_totalValidBurInDataCnt-failcnt)*100/float(g_totalValidBurInDataCnt)
        strDesc = 'BurnIn 24h测试：测试%dpcs,失败%dpcs,良率%.2f%%。'%(g_totalValidBurInDataCnt, failcnt,passRatio)
        worksheet['%s%d'%('A', 13)] = strDesc
    worksheet.merge_cells(start_row=13, start_column=1, end_row=13, end_column=9) 
    
#绘制BurnIn表格。
def InitBurnInReportTemplateInWorkSheet(worksheet):
    #titleFont=Font('宋体',size=11,color=colors.BLACK,bold=True,italic=False)
    cellfont=Font('宋体',size=10,color=colors.BLACK,bold=False,italic=False)
    for rowIdx in range(g_totalValidBurInDataCnt):
        for col in range(12):
            worksheet['%s%d'%(get_column_letter(col+1), BURNIN_DATA_START_LINE+rowIdx)].alignment = PublicFuc.alignment
            bottomLine = 'thin'
            rightLine = 'thin'
            worksheet['%s%d'%(get_column_letter(col+1), BURNIN_DATA_START_LINE+rowIdx)].border = PublicFuc.my_border('thin',bottomLine,'thin',rightLine)
            worksheet['%s%d'%(get_column_letter(col+1), BURNIN_DATA_START_LINE+rowIdx)].font = cellfont
   
    #合并单元格
    mergeStartLine = BURNIN_DATA_START_LINE
    mergeStartCol = 5
    curLine = mergeStartLine
    for pcNo in dicBurnInIdxByPcNo:
        listRows = dicBurnInIdxByPcNo[pcNo]
        rowCnt = len(listRows)
        bWritePcNo = False
        for dicSample in listRows:
            for key in dicSample:
                if 'HighFormat' in dicSample[key]:
                    rowFormat = dicSample[key]['HighFormat']
                    if bWritePcNo == False:
                        worksheet['%s%d'%('E', curLine)] = pcNo
                        bWritePcNo = True #确保只写一次，方便合并单元格。
                        if 'windows_version' in dicSample[key]:
                            worksheet['%s%d'%('F', curLine)] = dicSample[key]['windows_version']
                    curLine += 1
                    continue
                if 'BurnIn' in dicSample[key]:
                    rowFormat = dicSample[key]['BurnIn']
                    if bWritePcNo == False:
                        worksheet['%s%d'%('E', curLine)] = pcNo
                        bWritePcNo = True #确保只写一次，方便合并单元格。
                        if 'windows_version' in dicSample[key]:
                            worksheet['%s%d'%('F', curLine)] = dicSample[key]['windows_version']
                    curLine += 1

        worksheet.merge_cells(start_row=mergeStartLine, start_column=mergeStartCol, end_row=mergeStartLine+rowCnt-1, end_column=mergeStartCol)
        worksheet.merge_cells(start_row=mergeStartLine, start_column=mergeStartCol+1, end_row=mergeStartLine+rowCnt-1, end_column=mergeStartCol+1)
        mergeStartLine += rowCnt

def ProPcCopy(curpath, worksheet):
    pattern = '.+\\\\Plan33\\\\T_GE_U3_C35\\\\H2-1\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicPcCopyfile,'')#copyfile前面的格式化

    pattern = '.+\\\\Plan2\\\\T_GE_U3_C16\\\\copyfile\\\\DUTTest.+.csv$'
    ReadCopyFileCsvData(curpath,pattern,dicPcCopyfile,'copyfile')

    #plan10
    pattern = '.+\\\\Plan10\\\\T_GE_U3_C3\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicPcCopyfile,'HighFormat')#copyfile前面的格式化
    pattern = '.+\\\\Plan10\\\\T_GE_U3_C16\\\\copyfile\\\\DUTTest.+.csv$'
    ReadCopyFileCsvData(curpath,pattern,dicPcCopyfile,'copyfile')
    InitDicDataByPcNo()
    startLine = PC_COPY_DATA_START_LINE
    WriteCopyFileDic(worksheet, startLine, dicPcCopyfileIdxByPcNo)
    worksheet['%s%d'%('B', 9)] = FLASH_MCU
    worksheet['%s%d'%('B', 10)] = MP_VERSION
    worksheet['%s%d'%('E', 12)] = PublicFuc.GetDate()
    PublicFuc.WriteReportTime(worksheet,'O',1)
    PublicFuc.WriteReportOperator(worksheet,'R',1)


    
    
#得到按照PCno索引的编号
def InitDicDataByPcNo():
    global g_totalValidCopyFileDataCnt
    for key in dicPcCopyfile:
        subDic = {}
        subDic[key] = dicPcCopyfile[key]
        if 'HighFormat' in dicPcCopyfile[key]:
            row = subDic[key]['HighFormat']
            pcNo = row[2]
            if pcNo not in dicPcCopyfileIdxByPcNo:
                dicPcCopyfileIdxByPcNo[pcNo] = []          
            dicPcCopyfileIdxByPcNo[pcNo].append(subDic)
            g_totalValidCopyFileDataCnt += 1

def ProPfm(curpath, worksheet):
    #高格
    pattern = '.+\\\\Plan2\\\\T_GE_U3_C3\\\\高格\\\\.+.csv$'
    ReadFormatCsvData(curpath,pattern,dicPfm,'HighFormat')
    #h2testw
    h2Key = ['write speed','read speed','qa_err_msg']
    pattern = '.+\\\\Plan2\\\\T_GE_U3_C6\\\\满盘H2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'H2_1', h2Key, 'H2.bmp',0)
   
    #hdbench
    hdbKey = ['Read','Write','RRead','RWrite']
    pattern = '.+\\\\Plan2\\\\T_GE_U3_C6\\\\HDBench\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDB_1', hdbKey, 'HDBench.bmp',0)
    
    #cdm
    cdmKey = ['SeqQ32T1_Read','SeqQ32T1_Write','4KQ32T1_Read','4KQ32T1_Write','Seq_Read','Seq_Write','4K_Read','4K_Write']
    pattern = '.+\\\\Plan2\\\\T_GE_U3_C6\\\\CDM\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'CDM_1', cdmKey, 'CDM.bmp',0)

    #写数据
    startLine = PFM_DATA_START_LINE  
    WritePfmDic(worksheet,startLine,dicPfm)
    PublicFuc.WriteReportTime(worksheet,'B',1)
    PublicFuc.WriteReportOperator(worksheet,'E',1)

def InitMPCsvColumnNameMap(columnNameMap,listHeader):
    #columnNameMap['端口'] = 0
    PublicFuc.RemoveSpace(listHeader)
    if listHeader == []:
        return
    columnNameMap.clear()
    columnNameMap['FlashNO'] = PublicFuc.GetIndex('FlashNO',listHeader)
    columnNameMap['LogCap'] = PublicFuc.GetIndex('LogCap',listHeader)  #开卡容量
    columnNameMap['MP_Result'] = PublicFuc.GetIndex('MP_Result',listHeader) #通过Pass 否则错误码
    columnNameMap['MP_Time'] = PublicFuc.GetIndex('MP_Time(min)',listHeader) #量产时间
    columnNameMap['BadBlkNum'] = PublicFuc.GetIndex('BadBlkNum',listHeader)
    columnNameMap['MPStatus'] = PublicFuc.GetIndex('MPStatus',listHeader) #模式
    columnNameMap['FlashID'] = PublicFuc.GetIndex('FlashID',listHeader)
    columnNameMap['PC_Name'] = PublicFuc.GetIndex('PC_Name',listHeader)

    columnNameMap['mp_version'] = PublicFuc.GetIndex('mp_version',listHeader) #量产工具版本
    columnNameMap['fw_version'] = PublicFuc.GetIndex('fw_version',listHeader) #固件版本
    columnNameMap['FlashName'] = PublicFuc.GetIndex('FlashName',listHeader) #Flash型号
    columnNameMap['ControlName'] = PublicFuc.GetIndex('ControlName',listHeader) #主控

def ReadFormatCsvData(curpath,pattern,dataDic,caseKey):
    csvHeaderColumn = []

    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除

        #file_data = []
        with open(file,mode='r', buffering=-1, encoding='cp936') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            birth_header = next(csv_reader)  # 读取第一行每一列的标题
            if csvHeaderColumn == []:
                csvHeaderColumn = birth_header
                InitMPCsvColumnNameMap(mp_column_name_map,csvHeaderColumn)
            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中
                key = row[mp_column_name_map['FlashNO']]
                cap = row[mp_column_name_map['LogCap']]
                mode = row[mp_column_name_map['MPStatus']]
                pcNo = row[mp_column_name_map['PC_Name']]
                tempRow = [cap,mode,pcNo]
                if key not in dataDic:
                    dataDic[key] = {}
                if caseKey not in dataDic[key]:
                    dataDic[key][caseKey] = tempRow
                    fileMdTime = os.path.getmtime(file)
                    dataDic[key][PublicFuc.GetTimeKeyName(caseKey)] = fileMdTime
                else:
                    #查看是否是最新数据做覆盖处理。
                    oldTime = dataDic[key][PublicFuc.GetTimeKeyName(caseKey)]
                    fileMdTime = os.path.getmtime(file)
                    if fileMdTime < oldTime:
                        continue#数据不是新的，不做读取覆盖

                    dataDic[key][caseKey] = tempRow
                    dataDic[key][PublicFuc.GetTimeKeyName(caseKey)] = fileMdTime

                #begin此段为独立的记录公共信息的代码块
                global FW_VERSION
                global MP_VERSION
                global FLASH_ID
                global FLASH_NAME
                global FLASH_MCU
                if FW_VERSION == '':
                    FW_VERSION = row[mp_column_name_map['fw_version']]
                if MP_VERSION == '':
                    MP_VERSION = row[mp_column_name_map['mp_version']]
                if FLASH_ID == '':
                    FLASH_ID = row[mp_column_name_map['FlashID']]
                if FLASH_NAME == '':
                    FLASH_NAME = row[mp_column_name_map['FlashName']]
                if FLASH_MCU == '':
                    FLASH_MCU = row[mp_column_name_map['ControlName']]
                #end


def ReadCopyFileCsvData(curpath,pattern,dataDic,caseKey):
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除

        #file_data = []
        with open(file,mode='r', buffering=-1, encoding='cp936') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            birth_header = next(csv_reader)  # 读取第一行每一列的标题
            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中
                key = row[3]
                result = row[10]
                pcNo = row[5]
                tempRow = [result,pcNo]
                if key not in dataDic:
                    dataDic[key] = {}
                if caseKey not in dataDic[key]:
                    dataDic[key][caseKey] = tempRow
                else:
                    #判定是否有更新数据进行覆盖
                    tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
                    oldFileName = dataDic[key][tmpItemKey]
                    oldTime = os.path.getmtime(oldFileName)
                    fileMdTime = os.path.getmtime(file)
                    if fileMdTime < oldTime:
                        continue#数据不是新的，不做读取覆盖
                    dataDic[key][caseKey] = tempRow

                if 'windows_version' not in dataDic[key] or dataDic[key]['windows_version'] == '':
                    dataDic[key]['windows_version'] = totalDataDic[key]['DevInfo'][machaine_info.WINDOWS_VERSION]#PublicFuc.GetCommonInfoByFilePath(file,'WINDOWS_VERSION')
                dataDic[key][caseKey+'_file_path'] = file
                tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_pcnoKey)
                dataDic[key][tmpItemKey] = pcNo
                tmpItemKey = ErrDiskInfo.GetCombinedKeyName(caseKey,ErrDiskInfo.g_filepathKey)
                dataDic[key][tmpItemKey] = file

#绘制表格。
def InitPfmReportTemplateInWorkSheet(worksheet):
    #titleFont=Font('宋体',size=11,color=colors.BLACK,bold=True,italic=False)
    cellfont=Font('宋体',size=10,color=colors.BLACK,bold=False,italic=False)
    for rowIdx in range(len(dicPfm)):
        for col in range(20):
            worksheet['%s%d'%(get_column_letter(col+1), PFM_DATA_START_LINE+rowIdx)].alignment = PublicFuc.alignment
            worksheet['%s%d'%(get_column_letter(col+1), PFM_DATA_START_LINE+rowIdx)].border = PublicFuc.my_border('thin', 'thin', 'thin', 'thin')
            worksheet['%s%d'%(get_column_letter(col+1), PFM_DATA_START_LINE+rowIdx)].font = cellfont

#绘制表格。
def InitPcCopyFileReportTemplateInWorkSheet(worksheet):
    #titleFont=Font('宋体',size=11,color=colors.BLACK,bold=True,italic=False)
    cellfont=Font('宋体',size=10,color=colors.BLACK,bold=False,italic=False)
    for rowIdx in range(g_totalValidCopyFileDataCnt):
        for col in range(9):
            worksheet['%s%d'%(get_column_letter(col+1), PC_COPY_DATA_START_LINE+rowIdx)].alignment = PublicFuc.alignment
            bottomLine = 'thin'
            rightLine = 'thin'
            worksheet['%s%d'%(get_column_letter(col+1), PC_COPY_DATA_START_LINE+rowIdx)].border = PublicFuc.my_border('thin',bottomLine,'thin',rightLine)
            worksheet['%s%d'%(get_column_letter(col+1), PC_COPY_DATA_START_LINE+rowIdx)].font = cellfont
   
    #合并单元格
    mergeStartLine = PC_COPY_DATA_START_LINE
    mergeStartCol = 5
    curLine = mergeStartLine
    for pcNo in dicPcCopyfileIdxByPcNo:
        listRows = dicPcCopyfileIdxByPcNo[pcNo]
        rowCnt = len(listRows)
        bWritePcNo = False
        for dicSample in listRows:
            for key in dicSample:
                if 'HighFormat' in dicSample[key]:
                    rowFormat = dicSample[key]['HighFormat']
                    if bWritePcNo == False:
                        worksheet['%s%d'%('E', curLine)] = pcNo
                        bWritePcNo = True #确保只写一次，方便合并单元格。
                        if 'windows_version' in dicSample[key]:
                            worksheet['%s%d'%('F', curLine)] = dicSample[key]['windows_version']
                    curLine += 1

        worksheet.merge_cells(start_row=mergeStartLine, start_column=mergeStartCol, end_row=mergeStartLine+rowCnt-1, end_column=mergeStartCol)
        worksheet.merge_cells(start_row=mergeStartLine, start_column=mergeStartCol+1, end_row=mergeStartLine+rowCnt-1, end_column=mergeStartCol+1)
        mergeStartLine += rowCnt




def WritePfmDic(worksheet, startLine, dataDic,imgWidth = 360, imgHeight = 300):
    InitPfmReportTemplateInWorkSheet(worksheet)#绘制表格

    curLine = startLine
    imageStartLine = startLine + len(dataDic)+2
    imageLine = imageStartLine
    imageCol = 1
    keySortLst = sorted(dataDic.keys(), reverse=False)
    for idx,key in enumerate(keySortLst):
        imageCol = 1+(idx%2)*16
        imageLine = imageStartLine + (idx//2)*18

        worksheet['%s%d'%('A', curLine)] = key #填写flash编号

        caseKey = 'HighFormat'
        if caseKey in dataDic[key]:
            line = dataDic[key][caseKey]
            colLst = ['B','C','D']
            for index,col in enumerate(colLst):
                worksheet['%s%d'%(col, curLine)] = line[index]

        caseKey = 'H2_1'
        if caseKey in dataDic[key]:
            line = dataDic[key][caseKey]
            colLst = ['F','G','H']
            for index,col in enumerate(colLst):
                if col == 'H':
                    if line[index] == '':
                        worksheet['%s%d'%(col, curLine)] = 'TRUE'
                    else:
                        worksheet['%s%d'%(col, curLine)] = 'FALSE'                   
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index]

        caseKey = 'HDB_1'
        if caseKey in dataDic[key]:
            line = dataDic[key][caseKey]
            colLst = ['I','J','K','L']
            for index,col in enumerate(colLst):
                worksheet['%s%d'%(col, curLine)] = line[index]

        caseKey = 'CDM_1'
        if caseKey in dataDic[key]:
            line = dataDic[key][caseKey]
            colLst = ['M','N','O','P','Q','R','S','T']
            for index,col in enumerate(colLst):
                worksheet['%s%d'%(col, curLine)] = line[index]

        #写图片
        worksheet['%s%d'%(get_column_letter(imageCol), imageLine)] = key #填写flash编号
        imageLine += 1

        if 'H2_1' in dataDic[key]:
            line = dataDic[key]['H2_1']
            # 列表最后一项是图片路径
            if '' != line[-1]:
                img = Image(line[-1])
                if imageCol == 1:
                    img.width = imgWidth-20
                    img.height = imgHeight
                    worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
                    imageCol += 4
                else:
                    img.width = imgWidth
                    img.height = imgHeight
                    worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
                    imageCol += 5
        
        if 'HDB_1' in dataDic[key]:
            line = dataDic[key]['HDB_1']
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
                imageCol += 5

        if 'CDM_1' in dataDic[key]:
            line = dataDic[key]['CDM_1']
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))

        curLine += 1


def WriteCopyFileDic(worksheet, startLine, dataDic):
    InitPcCopyFileReportTemplateInWorkSheet(worksheet)#绘制表格

    curLine = startLine
    serialNo = 1
    failcnt = 0
    for pcNo in dicPcCopyfileIdxByPcNo:
        listRows = dicPcCopyfileIdxByPcNo[pcNo]
        listRows.sort(key = lambda x: list(x.keys())[0])
        for dicSample in listRows:
            for key in dicSample:
                if 'HighFormat' in dicSample[key]:
                    rowFormat = dicSample[key]['HighFormat']
                    worksheet['%s%d'%('A', curLine)] = serialNo #填写flash编号
                    cap = rowFormat[0]
                    mode = rowFormat[1]
                    worksheet['%s%d'%('B', curLine)] = key
                    worksheet['%s%d'%('C', curLine)] = cap
                    worksheet['%s%d'%('D', curLine)] = mode
                    #PCNO已经在构造表格的时候写入，此处不必写入

                    if 'copyfile' in dicSample[key]:
                        rowCopyFile = dicSample[key]['copyfile']
                        bPass = rowCopyFile[0].upper()
                        worksheet['%s%d'%('G', curLine)] = bPass
                        worksheet['%s%d'%('H', curLine)] = bPass
                        if bPass != 'PASS':
                            failcnt += 1
                            worksheet['%s%d'%('G', curLine)].fill = PublicFuc.warnFill
                            worksheet['%s%d'%('H', curLine)].fill = PublicFuc.warnFill
                            tmpItemKey = ErrDiskInfo.GetCombinedKeyName('copyfile',ErrDiskInfo.g_filepathKey)
                            file = dicSample[key][tmpItemKey]
                            errcode = bPass
                            PublicFuc.AppendErrDiskInfo('PC端文件拷贝比对测试_Err',key,errcode,pcNo,file)
                    else:
                        worksheet['%s%d'%('G', curLine)] = 'FAIL'
                        worksheet['%s%d'%('H', curLine)] = 'FAIL'
                        worksheet['%s%d'%('G', curLine)].fill = PublicFuc.warnFill
                        worksheet['%s%d'%('H', curLine)].fill = PublicFuc.warnFill
                        failcnt += 1
                    
                    curLine += 1
                    serialNo += 1

    if g_totalValidCopyFileDataCnt > 0:
        passRatio = float(g_totalValidCopyFileDataCnt-failcnt)*100/float(g_totalValidCopyFileDataCnt)
        strDesc = '文件拷贝比对测试：测试%dpcs,失败%dpcs,良率%.2f%%。'%(g_totalValidCopyFileDataCnt, failcnt,passRatio)
        worksheet['%s%d'%('A', 14)] = strDesc
    #worksheet.merge_cells(start_row=14, start_column=1, end_row=14, end_column=8)


      




                