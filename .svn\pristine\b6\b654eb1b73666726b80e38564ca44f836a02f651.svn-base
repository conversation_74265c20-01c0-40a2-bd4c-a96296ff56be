import PublicFuc,os
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  Pat<PERSON>Fill,Alignment,Border,Side,colors,Font
from datetime import datetime,timedelta

g_dicErrDisk = {'H2_Err':[],'Copy File_Err':[],'SPOR_Err':[],'POR_Err':[],'RT BIT_Err':[],'HT BIT_Err':[],'LT BIT_Err':[],'空快耗尽_Err':[],'Data Retention_Err':[],'Read Disturb_Err':[],'MP_Err':[]}

def Run(curpath, workBook, alignment):
    WriteDetailErrInfo(workBook,alignment)
    WriteSummaryErrInfo(workBook,alignment)

hyperLinkFont = Font( color='0000FF')
        
def WriteDetailErrInfo(workBook,alignment):
    for errType in g_dicErrDisk:
        errList = g_dicErrDisk[errType]
        if errList == []:
            continue
        worksheet = workBook[errType]
        worksheet.alignment = alignment
        START_LINE = 2
        START_COLUMN = 1
        curLine = START_LINE
        orderErrList = sorted(errList,key=PreProcessKey,reverse=False)
        for errDisk in orderErrList:          
            for columnIdx in range(len(errDisk)):
                if columnIdx == len(errDisk) - 1:
                    dirPathName = os.path.dirname(errDisk[columnIdx])
                    worksheet['%s%d'%(get_column_letter(columnIdx+1), curLine)] = dirPathName
                    #worksheet['%s%d'%(get_column_letter(columnIdx+1), curLine)].value ='= HYPERLINK("{}","{}")'.format(dirPathName,dirPathName)
                    #worksheet['%s%d'%(get_column_letter(columnIdx+1), curLine)].font = hyperLinkFont
                    worksheet['%s%d'%(get_column_letter(columnIdx+1), curLine)].hyperlink = dirPathName
                    worksheet['%s%d'%(get_column_letter(columnIdx+1), curLine)].style = "Hyperlink"
                else:
                    worksheet['%s%d'%(get_column_letter(columnIdx+1), curLine)] = errDisk[columnIdx]

            curLine += 1

def PreProcessKey(_rawKey):
        newKey = _rawKey[0]     
        return newKey

def WriteSummaryErrInfo(workBook,alignment):
    worksheet = workBook['Err汇总']
    worksheet.alignment = alignment

    COLUMN_IDX = 2
    START_LINE = 2
    errDiskList = g_dicErrDisk['H2_Err']
    worksheet['%s%d'%(get_column_letter(COLUMN_IDX), START_LINE)] = len(errDiskList)
    errDiskList = g_dicErrDisk['Copy File_Err']
    worksheet['%s%d'%(get_column_letter(COLUMN_IDX), START_LINE+1)] = len(errDiskList)
    errDiskList = g_dicErrDisk['SPOR_Err']
    worksheet['%s%d'%(get_column_letter(COLUMN_IDX), START_LINE+2)] = len(errDiskList)
    errDiskList = g_dicErrDisk['POR_Err']
    worksheet['%s%d'%(get_column_letter(COLUMN_IDX), START_LINE+3)] = len(errDiskList)
    errDiskList = g_dicErrDisk['RT BIT_Err']
    worksheet['%s%d'%(get_column_letter(COLUMN_IDX), START_LINE+4)] = len(errDiskList)
    errDiskList = g_dicErrDisk['HT BIT_Err']
    worksheet['%s%d'%(get_column_letter(COLUMN_IDX), START_LINE+5)] = len(errDiskList)
    errDiskList = g_dicErrDisk['LT BIT_Err']
    worksheet['%s%d'%(get_column_letter(COLUMN_IDX), START_LINE+6)] = len(errDiskList)
    errDiskList = g_dicErrDisk['空快耗尽_Err']
    worksheet['%s%d'%(get_column_letter(COLUMN_IDX), START_LINE+7)] = len(errDiskList)
    errDiskList = g_dicErrDisk['Data Retention_Err']
    worksheet['%s%d'%(get_column_letter(COLUMN_IDX), START_LINE+8)] = len(errDiskList)
    errDiskList = g_dicErrDisk['Read Disturb_Err']
    worksheet['%s%d'%(get_column_letter(COLUMN_IDX), START_LINE+9)] = len(errDiskList)
    errDiskList = g_dicErrDisk['MP_Err']
    worksheet['%s%d'%(get_column_letter(COLUMN_IDX), START_LINE+10)] = len(errDiskList)
            
    if PublicFuc.MPDATA_DIR != "":
        worksheet['%s%d'%(get_column_letter(COLUMN_IDX), START_LINE+11)] = PublicFuc.MPDATA_DIR
        worksheet['%s%d'%(get_column_letter(COLUMN_IDX), START_LINE+11)].hyperlink = PublicFuc.MPDATA_DIR
        worksheet['%s%d'%(get_column_letter(COLUMN_IDX), START_LINE+11)].style = "Hyperlink"
    if PublicFuc.REPORT_DIR != "":
        worksheet['%s%d'%(get_column_letter(COLUMN_IDX), START_LINE+12)] = PublicFuc.REPORT_DIR
        worksheet['%s%d'%(get_column_letter(COLUMN_IDX), START_LINE+12)].hyperlink = PublicFuc.REPORT_DIR
        worksheet['%s%d'%(get_column_letter(COLUMN_IDX), START_LINE+12)].style = "Hyperlink"


#返回失败的数量，失败的汇总信息,默认第一个是样本编号，第二个是测试结果
def GetFailInfo(dataList,validResultList):
    failCnt = 0
    failInfo = ''
    for line in dataList:
        for idx in range(1,len(line)):
            strResult = line[idx]
            if strResult == None or (strResult.upper() not in validResultList):
                failCnt += 1
                if strResult == None:
                    strResult = ''
                strTempErrInfo = '[' + str(line[0]) + ']' + strResult
                failInfo += strTempErrInfo + ','
                break #只记录最先遇到的错误
    failInfo = failInfo[:-1]
    return failCnt,failInfo





def GetSpecialDataCnt(_startRow,_endRow,_colName,ws):
    cnt = 0
    if _startRow > _endRow:
        return 0
    for rowNo in range(_startRow,_endRow+1):
        celPosSample = '%s%d'%(_colName, rowNo)
        celValue = ws[celPosSample].value
        if celValue != '' and celValue != None:
            cnt += 1
    return cnt


#获取指定范围的数据内容，结果数据是列表形式
def GetSpecialDataList(_startRow,_endRow,_colName,ws):
    dic = []
    if _startRow > _endRow:
        return dic
    for rowNo in range(_startRow,_endRow+1):
        celPosSample = '%s%d'%(_colName, rowNo)
        celValue = ws[celPosSample].value
        if celValue != '' and celValue != None:
            dic.append(celValue)
    return dic


#获取最长测试时间
def GetMaxTime(dic):
    maxTimeInSeconds = 0
    for data in dic:
        timestr = data
        if timestr != '' and timestr != None:
            if timestr.find(':') != -1:
                #00:00:00
                timedata = timestr.split(':')
                totalSecond = int(timedata[0])*3600 + int(timedata[1])*60 + int(timedata[2])
                if totalSecond > maxTimeInSeconds:
                    maxTimeInSeconds = totalSecond
            else:
                #000h 03m 07s
                timedata = timestr.split(' ')
                hour = timedata[0][0:-1]
                minutes = timedata[1][0:-1]
                seconds = timedata[2][0:-1]
                totalSecond = int(hour)*3600 + int(minutes)*60 + int(seconds)
                if totalSecond > maxTimeInSeconds:
                    maxTimeInSeconds = totalSecond

    if maxTimeInSeconds == 0:
        return ''

    hour = int(maxTimeInSeconds/3600)
    lefSeconds = maxTimeInSeconds%3600
    minutes = int(lefSeconds/60)
    seconds = lefSeconds%60
    strTime = '%d:%d:%d'%(hour,minutes,seconds)
    return strTime

#获取指定范围的数据内容，结果数据是列表形式,且只加入有效的数据
def GetSpecialMultiDataList(_startRow,_endRow,_colNameList,ws):
    dic = []
    if _startRow > _endRow:
        return dic
    for rowNo in range(_startRow,_endRow+1):
        oneRow = []
        for _colName in _colNameList:
            celPosSample = '%s%d'%(_colName, rowNo)
            celValue = ws[celPosSample].value
            oneRow.append(celValue)

        if IsValidData(oneRow):
            dic.append(oneRow)
    return dic

#判定数据是否为无效数据
def IsValidData(dataLine):
    if len(dataLine) < 2:
        return False
    
    isValid = False
    for idx in range(1,len(dataLine)):
        if dataLine[idx] != None:
            isValid = True

    return isValid
  
#获取最长测试时间
def GetCombinedMaxTime(dic):
    maxTimeInSeconds = 0
    for data in dic:
        if len(data) == 1:
            #简单时间信息
            timestr = data
            if timestr != '' and timestr != None:
                if timestr.find(':') != -1:
                    #00:00:00
                    timedata = timestr.split(':')
                    totalSecond = int(timedata[0])*3600 + int(timedata[1])*60 + int(timedata[2])
                    if totalSecond > maxTimeInSeconds:
                        maxTimeInSeconds = totalSecond
                else:
                    #000h 03m 07s
                    timedata = timestr.split(' ')
                    hour = timedata[0][0:-1]
                    minutes = timedata[1][0:-1]
                    seconds = timedata[2][0:-1]
                    totalSecond = int(hour)*3600 + int(minutes)*60 + int(seconds)
                    if totalSecond > maxTimeInSeconds:
                        maxTimeInSeconds = totalSecond
        else:
            oneRowTimeList = data
            perRowTotalTime = 0
            for perTime in oneRowTimeList:
                if perTime != '' and perTime != None:
                    timestr = perTime
                    if timestr.find(':') != -1:
                        #00:00:00
                        timedata = timestr.split(':')
                        totalSecond = int(timedata[0])*3600 + int(timedata[1])*60 + int(timedata[2])
                        perRowTotalTime += totalSecond
                    else:
                        #000h 03m 07s
                        timedata = timestr.split(' ')
                        hour = timedata[0][0:-1]
                        minutes = timedata[1][0:-1]
                        seconds = timedata[2][0:-1]
                        totalSecond = int(hour)*3600 + int(minutes)*60 + int(seconds)
                        perRowTotalTime += totalSecond
            
            if perRowTotalTime > maxTimeInSeconds:
                            maxTimeInSeconds = perRowTotalTime


    if maxTimeInSeconds == 0:
        return ''

    hour = int(maxTimeInSeconds/3600)
    lefSeconds = maxTimeInSeconds%3600
    minutes = int(lefSeconds/60)
    seconds = lefSeconds%60
    strTime = '%d:%d:%d'%(hour,minutes,seconds)
    return strTime

#从字符中直接解析出时间的值
def GetTimeValue(dic):
    timeValue = 0
    endTimeStr = dic[2]
    startTimeStr = dic[1]
    if '' == endTimeStr or '' == startTimeStr:
        timeValue = 0
    else:
        endtime = datetime.strptime(endTimeStr, '%Y-%m-%d %H:%M:%S')
        starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
        totalSecond = timedelta.total_seconds(endtime-starttime)
        timeValue = totalSecond
    return timeValue
         

   


def DrawTable(ws):
    totalRowCnt = len(dicData)
    STARTLINE = 13
    #serialNo = 1
    for rowNo in range(STARTLINE,STARTLINE+totalRowCnt):
        for ColNo in range(1,1+5):
            ws['%s%d'%(get_column_letter(ColNo), rowNo)].alignment = PublicFuc.alignment
            ws['%s%d'%(get_column_letter(ColNo), rowNo)].border = PublicFuc.my_border('thin', 'thin', 'thin', 'thin')
    
    #合并单元格
    if totalRowCnt > 0:
        ws['%s%d'%(get_column_letter(1), STARTLINE)] = '48H高格'
        ws.merge_cells(start_row=STARTLINE, start_column=1, end_row=STARTLINE+totalRowCnt-1, end_column=1)
