﻿; <PERSON><PERSON>vich's Delphi Localizer Language File.
; Generated by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 12.10.2019 5:02:05
; Version 1.0 (Victoria 5.37. 14 Oct 2021) 

[TAAMForm]
Caption='Керування акустичним шумом та енергоспоживанням'
Font.Name='Tahoma'
SeekGrp.Caption=' ТЕСТ МЕХАНІКИ ТА АКУСТИЧНОГО ШУМУ HDD'
SeekGrp.Font.Name='Tahoma'
Label5.Caption='Ліміт, мс:'
Label5.Font.Name='Tahoma'
SeekTimeLed.Hint='Середній час доступу'
SeekCntOut.Hint='Лічильник позиціювань'
SeekCntOut.Font.Name='Tahoma'
SeekStartLbaEdit.Hint='Початковій сектор'
SeekEndLbaEdit.Hint='Кінцевий сектор'
Button2.Hint='Скидання початкового сектора в нуль'
Button4.Hint='Встановлення кінцевого сектора до найбільш можливого значення'
Button4.Caption='MAX'
AAMLed.Hint='Величина AAM (рівень акустичного шуму HDD)'
AAMTrack.Hint='Точна підстройка рівня шуму'
AAMMinBtn.Hint='Ввімкнути найнижчий рівень шуму (найнижча швідкість роботи HDD)'
AAMOffBtn.Hint='Вимкнути AAM (найвищий рівень шуму та найвища швидкість роботи HDD)'
SeekBtn.Hint='Запустити тестування механіки та керування рівнем шуму'
SeekBtn.Caption='Старт'
STioEdit.Hint='Час реакції HDD (мілісекунд), при перевищенні якого тестування буде припинено'
SeekTmprtLed.Hint='Температура HDD'
TermoCtrlBox.Hint='Ввімкнути контроль температури'
TermoCtrlBox.Caption='t°'
SeekModeGrp.Hint='Метод позиціювання'
SeekModeGrp.Items.Strings='Seek','Verify','Read'
SeekLbaOut.Hint='Поточний сектор'
RzPanel1.Caption='ПОКРАЩЕНЕ КЕРУВАННЯ ЕНЕРГОЗБЕРЕЖЕННЯМ'
RzPanel1.Font.Name='Tahoma'
ApmTrack.Hint='Точне підстроювання рівня енергозбереження'
ApmOffBtn.Hint='Вимкнути APM. Найвища продуктивність HDD'
ApmLed.Hint='Рівень енергоспоживання'
WDIdlePanel.Caption='ТАЙМЕР ПАРКОВКИ ГОЛОВОК WD'
DisWdTimerBtn.Caption='Відключити'
SetWdTimerBtn.Caption='Встановити'
ReadWdTimBtn.Caption='Прочитати'
WdIdleTrack.Hint='Точна установка часу таймера'
RestartWDBtn.Caption='Restart WD'
RestartWDBtn.Hint='Перезапустіть прошивку WD-диска, щоб застосувати зміни таймера без перезавантаження. На завантажувальному HDD та деяких системах може виникнути збій.'


[TDCOForm]
Caption='Керування властивостями HDD (DCO)'

[TDCOFrame]
DcoPanel.Font.Name='Tahoma'
Label14.Caption='Версія:'
DcoBit5Chk.Caption='Read/Write DMA Queued'
DcoBit4Chk.Caption='Power-up in Standby'
DcoChkSecu.Caption='Security'
DcoBit2Chk.Caption='SMART error log'
DcoBit1Chk.Caption='SMART self-test'
DcoChkSMART.Caption='SMART'
DcoBit9Chk.Caption='Streaming feature set'
DCoAppBtn.Caption='Застосувати'
RzBitBtn27.Caption='Відновити'
RzBitBtn28.Caption='Визначити'
DcoBit11Chk.Caption='Forced Unit Access'
DcoBit12Chk.Caption='SMART Selective selftest'
DcoBit13Chk.Caption='SMART Conveyance selftest'
DcoBit14Chk.Caption='Write-Read-Verify'
DcoNvCacheChk.Caption='Support for NV Cache'
DcoNvCachePmChk.Hint='Підтримка команд керування енергоспоживанням енергонезалежного кеша - дозволено'
DcoNvCachePmChk.Caption='Support for NV Cache PM'
DcoWRUNChk.Caption='Write Uncorrectable'
DcoTChk.Caption='Trusted Computing'
DcoFreezeBtn.Caption='Заморозити'
DcoSataB7.Caption='Bit 7'
DcoSataB6.Caption='Bit 6'
DcoSataB5.Caption='Bit 5'
DcoSataB3.Caption='Bit 3'
DcoSataB2.Caption='Bit 2'
DcoSataB4.Caption='Bit 4'
DcoSataB0.Caption='NCQ'
DcoSataB1.Caption='Bit 1'
DcoRightPanel.Caption='  ІНСТРУКЦІЯ'


[TFeaForm]
Caption='функції накопичувача'
CacheBox.Caption=' КЕРУВАННЯ КЕШЕМ'
CacheBox.Font.Name='Tahoma'
RzBitBtn40.Caption='Кеш запису ВИМКНЕНИЙ'
RzBitBtn41.Caption='Кеш запису ВВІМКНЕНО'
CacheOFFbtn.Caption='Кеш читання ВИМКНЕНИЙ'
RdCacheBtn.Caption='Кеш читання ВВІМКНЕНО'


[TFm]
Font.Name='Tahoma'
StandBtn.Hint='Вибір накопичувача. Властивості накопичувача (паспорт)'
SmartPageBtn.Hint='Атрибути самодіагностики накопичувача'
BreakBtn.Hint='Зупинити всі процеси'
ReportBtn.Hint='Створити звіт'
SMLogPageBtn.Hint='Журнали самодіагностики накопичувача'
MainPauseBtn.Hint='Призупинити поточний тест'
TestPageBtn.Hint='Тестування та ремонт накопичувача'
AdvPageBtn.Hint='Робота з вмістом накопичувача'
DriLab.Caption='Інфо'
SmLab.Caption='S.M.A.R.T'
SmLogLab.Caption='Журнали'
DiskeditLab.Caption='Редактор'
TestRLab.Caption='Тестування'
ReportLab.Caption='Звіт'
PausLab.Caption=' Призупинити '
Brklab.Caption='Зупинити'
SSDLed.Hint='Частота обертів дисків HDD'
SataDevLed.Hint='Накопичувач с SATA під''єднанням'
RemDrvLed.Hint='Покажчик розмірів пристрою або змінного носія'
VirtDrvLed.Hint='Покажчик віртуального диску'
PspplusRstChk.Hint='Reset + ECh'
PspplusRstChk.Caption='reset+'
PASSBtn.Hint='Отримати паспорт накопичувача (тільки команда ECh)'
PASSBtn.Caption='Паспорт'
PASSBtnExt.Hint='Отримати додаткову інформацію з накопичувача:'#13'- Паспорт'#13'- Дійсний обсяг'#13'- Активність'#13'- Перевірити інтерфейс.'#13'(Докладніше дивись в "налаштуваннях")'
PASSBtnExt.Caption='EXT'
PspPanel.Psp.Hint='Натисніть кнопку ''Паспорт'' для перегляду информації про накопичувач'
ApiList.Columns.(2).Caption='Обсяг'
ApiList.Columns.(3).Caption=' Note / Оновити список'
ApiList.Columns.(4).Caption='Букви'
PciScanPanel.Caption='  КОНТРОЛЕР'
PciScanPanel.Font.Name='Tahoma'
PCIScanBtn.Hint='Сканування шини PCI, пошук додаткових ATA/SATA контролерів'
PCIScanBtn.Caption='PCI Scan'
PCIAllDevChk.Hint='Пошук всіх PCI/AGP пристроїв'
PCIAllDevChk.Caption='Всі'
MFDevChk.Hint='Знаходити багатофункціональні пристрої (бажано ввімкнути)'
MFDevChk.Caption='Б/Ф'
PortPanel.Caption='  ПОРТ'
BasePortEdit.Hint='Адрес базового порту'
AltPortEdit.Hint='Адрес альтернативного порту'
PortBox.Hint='Вибір порту'
slavechk.Hint='Вибір SLAVE IDE-пристрою'
slavechk.Caption='slave'
aschk.Hint='Застосувати альтернативный порт для контролю статусу'
aschk.Caption='alt-st'
ATA7Panel.Caption='ПІДТРИМУВАНІ НАКОПИЧУВАЧЕМ ФУНКЦІЇ:'
AAMInd.Caption='Керування акустичним шумом (AAM)'
SelfTestInd.Caption='Самотести S.M.A.R.T.'
ErrLogInd.Caption='Журнали помилок S.M.A.R.T.'
HpaInd.Caption='Захищена область користувача (HPA)'
ApmInd.Caption='Покращене керування енергозбереженням'
SecuInd.Caption='Керування безпекою'
EncrInd.Caption='Шифрування усіх даних користувача'
FreeFailInd.Caption='Датчик вільного падіння'
PECInd.Caption='Журнал лічильників подій інтерфейсу'
NcqInd.Caption='Підтримка керування чергою команд'
TMaxLab.Caption='МАКСИМАЛЬНА'
TMinLab.Caption='МІНІМАЛЬНА'
TCLab.Caption='ТЕМПЕРАТУРА'
SctTemp.Hint='Температура накопичувача'
SctMaxt.Hint='Максимальна температура за життя накопичувача'
SctMinT.Hint='Мінімальна температура за життя накопичувача'
SmartList.Hint='Подвійне клік по рядку - для перегляду зміни атрибута SMART'
SmartList.Columns.(1).Caption='ID  '
SmartList.Columns.(2).Caption=' Назва атрибута'
SmartList.Columns.(3).Caption='Значення'
SmartList.Columns.(4).Caption='Найгірше'
SmartList.Columns.(5).Caption='Поріг   '
SmartList.Columns.(6).Caption=' Абсолютне     '
SmartList.Columns.(7).Caption='Залишок'
SmrtStLabel.Hint='Стан ''здоров''я'', зчитаний з накопичувача'
SmartStatLabel.Caption='Статус:'
SmartBtn.Caption='Отримати атрибути SMART'
SmartBtn.Hint='Застаріла кнопка. Її цілком замінює клік по заголовку таблиці або автоматична дія'
HexRawChk.Caption='RAW відображати в HEX'
IBMSmartChk.Hint='Якщо накопичувач IBM або HGST, цей перемикач дозволяє отримати розширений перелік атрибутів SMART'
IBMSmartChk.Caption='IBM super smart:'
ErrorLog.Columns.(1).Caption='Параметр'
ErrorLog.Columns.(2).Caption='Вміст'
ErrorLog.Columns.(3).Caption='Коментар'
ErrorLog.Font.Name='Arial'
SmartLog.Columns.(1).Caption='ID '
SmartLog.Columns.(2).Caption='    Назва'
SmartLog.Columns.(3).Caption='Длина'
RzBitBtn5.Hint='Перечитати журнал з накопичувача'
RzBitBtn5.Caption='Оновити'
AutoReadLogChk.Hint='Автоматично отримувати журнал по кліку по його назві'
AutoReadLogChk.Caption='Автоотримання'
GetLogDirBtn.Hint='Прочитати перелік журналів з накопичувача'
GetLogDirBtn.Caption='Перелік'
SmIgnCrcChk.Caption='Ігнорувати КС'
SmIgnCrcChk.Hint='Дозволити відображення вмісту журналів з невірною контрольною сумою'
OldDriveLogChk.Hint='Читати журнал 28-бітними (застарілими) командами - для старих HDD без підтримки LBA48'
OldDriveLogChk.Caption='Застарілий'
grfwrchk.Hint='Ввімкнення/вимкнення графіка запису'
grfwrchk.Caption='Запис'
grfrdchk.Hint='Ввімкнення/вимкнення графіка читання'
grfrdchk.Caption='Читання'
Reclam2.Hint='Нажміть для отримання додаткової інформації...'
ScriptMeter.Hint='Прогрес-бар проходження сценарію/переліка дефектів'
Label60.Caption='[End LBA]'
Label60.Font.Name='Tahoma'
Label61.Caption='[Start LBA]'
Label61.Font.Name='Tahoma'
Label71.Caption='[ End time ]'
Label71.Font.Name='Tahoma'
Label58.Caption='[ timeout,ms ]'
Label58.Font.Name='Tahoma'
Label59.Caption='[ block size ]'
Label59.Font.Name='Tahoma'
MaxLbaButton.Hint='Встановити кінцевий LBA в максимальне значення'
MaxLbaButton.Caption='MAX'
ScanClZeroBtn.Hint='Встановити початковий LBA в нуль'
Qn.Hint='Стрибки під час сканування'
RstAftJmpChk.Hint='Подати команду Soft Reset в накопичувач перед стрибком (працює тільки в PIO)'
RndScTimeEdit.Hint='Тривалість випадкового читання (1min...23h:59m)'
StartLbaEdit.Hint='Start LBA'
EndLbaEdit.Hint='End LBA'
LbaOut.Hint='Поточний LBA'
ScBlockSizeBox.Hint='Розмір блока читання/запису. Один осередок карти дорівнює одному блоку. Рекомендуємо залишити автоматичний вибір'
ScanTimeEdit.Hint='Час доступу к блоку, за вичерпанням якого блок буде визнано дефектним'
LbaOut1.Hint='Поточний LBA-2 для тесту ''метелик'''
ScanStartBtn.Hint='Запустити/Зупинити тестування поверхні'
AutoSizeSC.Hint='Автоматичне настроювання розміру блока'
AutoSizeSC.Caption='[ auto ]'
QscanStartBtn.Hint='Запустити ШВИДКИЙ тест поверхні'
ScanCurBtn.Hint='Копіювати поточний LBA до початкового LBA'
ScanCurBtn.Caption='CUR'
EndScanVarBox.Items.Strings='Завершити','Зупинити вал','Зациклити тест','Вимкнути HDD','Вимкнути комп','Зняти скріншот','3 СШ+вимкнути','3 скріншоти'
EndScanVarBox.Hint='Вибір дії після закінчення сканування'
ScanCurEndBtn.Hint='Копіювати поточний LBA до кінцевого LBA'
ScanCurEndBtn.Caption='CUR'
ScanClrListBtn.Hint='Занулити лічильники'
ScTimeLed.Hint='Час, що залишився (при натисканні - буде відображено час початку сканування)'
DftMeter.Hint='Прогрес-бар для операцій с блоками'
ScanRWGrp.Items.Strings='Вериф.','Читн.','Запис'
ScanRWGrp.Hint='Метод доступу до накопичувача (УВАГА! Запис знищує дані користувача)'
ScDftChk.Hint='Аналізувати сектори у блоці, шукати дефектні та нестабільні сектори'
ScDftChk.Caption='дефектоскоп'
ScRemapGroup.Hint='Дія при виявленні дефекта'
ScanDefPanel.Hint='Статистика часу доступу (мілісекунди) до блоків'
ScRemapGroup.Items.Strings='Ігнор','Замінити','Стерти','Оновити'
ScanDirectionPanel.Hint='Напрямок сканування накопичувача'
ImgFWD.Hint='Лінійно уперед'
ImgREV.Hint='Лінійно в зворотьному напрямку'
ImgRND.Hint='За випадковими адресами'
ImgBTF.Hint='Метелик - від початку та кінця до середини'
ScanMbLed.Hint='Поточний адрес'
EnLwChk.Caption='Довгий запис'
SpeedLed.Hint='Відображення швидкості сканування'
ScNumLbaChk.Hint='Вписувати номери секторів на накопичувач'
ScNumLbaChk.Caption='Номери LBA'
DDDChk.Hint='Data Distortion Detect System (c) - виявлення спотворення даних при записі'
DDDChk.Caption='DDD (API)'
TioBad.Hint='Позначати сектор як дефектний, якщо час доступу перевищує встановлений'
TioBad.Caption='TIO-BAD'
DisGridChk.Hint='Перемикання режимів Карта / Графік'
DisGridChk.Caption='Grid'
ScanDefList.Columns.(1).Caption='Start LBA'
ScanDefList.Columns.(2).Caption='Block'
ScanDefList.Columns.(3).Caption='Comment'
PartBox.Hint='Спроба інтерпретації таблиці розділів формату MBR'
PartBox.Caption=' ТАБЛИЦЯ РОЗДІЛІВ'
PartBtn.Caption='Отримайте паспорт!'
PartAnalPassChk.Hint='Застосовувати паспортний об''єм накопичувача для виявлення вільного місця за межами розділів'
PartAnalPassChk.Caption='Об''єм з паспорту'
Use2CopyChk.Hint='Використовувати другу копію таблиць GPT замість основної'
Use2CopyChk.Caption='Викор. копію GPT'
MbrLed.Hint='Покажчик наявності відбитка 55AAh в завантажувальному секторі'
MbrOnBtn.Hint='Записати відбиток 55AAh в MBR'
MbrOnBtn.Caption='MBR ON'
MbrOffBtn.Hint='Замінити відбиток 55AAh на CC88h в MBR'
MbrOffBtn.Caption='MBR OFF'
HexNavigation.Caption='Press ''Open'''
HexEOppenBtn.Caption='Відкрити'
HexEOppenBtn.Hint='Встановити редактор на початок накопичувача'
HexESaveBtn.Caption='Збер.'
HexESaveBtn.Hint='Зберегти змінені дані на накопичувач'
AdvSyncHexPV.Caption='Синхронізувати з таблицею розділів'
AdvSyncHexPV.Hint='При виборі розділу в таблиці - встановити редактор на його початок'
HexTransLab.Caption='Відображення:'
HexTKBox.Hint='Переключення кодування символів у HEX-редакторі'
PartList.Columns.(1).Caption='N'
PartList.Columns.(2).Caption='Boot'
PartList.Columns.(3).Caption='Cистема'
PartList.Columns.(4).Caption='Start LBA  '
PartList.Columns.(5).Caption='End LBA   '
PartList.Columns.(6).Caption='Size     '
PartList.Columns.(7).Caption='Name'
HexE.Font.Name='Courier New'
BreakLed.Hint='Покажчик сигналізує про відміну усіх процесів'
PasspBtn.Hint='Альтернативна кнопка для отримання паспорту накопичувача'
PasspBtn.Caption='Passp'
RecBtn.Hint='Команда рекалібрування накопичувача (10h)'
RecBtn.Caption='Re&call'
APIRg.Items.Strings='API','PIO'
StopBtn.Hint='Відправити накопичувач до сну'
StopBtn.Caption='&Sleep'
ResetBtn.Hint='Програмний сброс накопичувача'
ResetBtn.Caption='&Reset'
WrIndLab.Hint='Покажчик операцій запису на тестований накопичувач'
RdIndLab.Hint='Покажчик операцій читання з тестованого накопичувача'
AltSndChk.Hint='Ввімкнути / Вимкнути звукові сповіщення'
AltSndChk.Caption='Звук'
HintChk.Caption='Hints'
HintChk.Hint='Відображати вспливаючі підказки для елементів'
IgnDRDYChk.Hint='Ігнорувати відсутність бита DRDY'
IgnDRDYChk.Caption='DRDY'
biglbaChk.Hint='Дозволити 48-бітну адресацію ATA-накопичувачів'
biglbaChk.Caption='48 bit'
Button1.Caption='wipe'
Button1.Font.Name='Tahoma'
PasStr.Items.Strings='1234','Отримання паспорту накопичувача','Помилка накопичувача','Помилка портів','При зміні диска - запис перемкнувся на читання!','Увага! Цей накопичувач не підтримує LBA !!!','Drive ATA passport','Passport USB','SCSI  Drive passport','З''ємний','Віртуальний','Немає носія','Жорсткий','Гнучкий диск','Невідомий не-ATA диск','Вал обертається.','unknown','LBA не підтримується','Функції','[Увага!] Необхідні права адміністратора!','Support','Model:','Size','Firmware','Диск','Serial','S.M.A.R.T','Cache','AAM Val','APM Val','Erase time','Security','Sector','Enabled','Disabled','Maximum','Minimum','Зупинений','Active','Frozen','ПОМИЛКА ІНТЕРФЕЙСУ!!!','Передано:','Прийнято:','УВАГА! ПОМИЛКА ІНТЕРФЕЙСУ!!!','Volume info. Partition','Total','Free','Таблиця розділів','Керування опціями безпеки','режим USB','Помилка паспорта!','Паспорт накопичувача ','Комент:','Errlog','Selftest','Unknown or unsupported','hours','minutes','Status','Passed','Failed','Power:','Locked','Цей накопичувач закрито ATA-паролем!','Unlocked','Expired','Не підтримується','SSD ext:','Emulate:','Interface:','Name:','FSystem:','Revice it!','Unk.size','Увага! Для вашої зручності застосунок містить багато автоматичних дій. Якщо ви використовуєте застосунок для відновлення даних та ці дії заважають - їх можно вимкнути в налаштуваннях','NVMe drive passport','Технічна інформація','Reserved'
StrCommon.Items.Strings='1234','не знайдено файл довідки для вибраної мови.','Файл перекладу не знайдено:','Перервано за бажанням користувача!','USB device connected.','USB device removed.','This is a new device. Сlick on the list, when it will be installed.','PAUSED','RESUMED','Warning! Main INI-file','not found, and writen default sets!','Program disabled. Please, use administrator rights.','Stop current operation before closing Victoria','Press F1 to About/HELP','Surface scan','Seek test','Вибраний накопичувач не підтримує технологію S.M.A.R.T.','Кнопка "Break" була натиснута чотири рази ! Як наслідок перемикач "Enable breaking internal loops" у налаштуваннях був автоматично ввімкнений','Приховати серійний номер','Відкрити серійний номер','Увага! Не вдалося активувати W-R-V на цьому накопичувачі. Операція скасована.','Reserved'
StrSmart.Items.Strings='1234','Помилки OTF','Частота прояву помилок читання','Продуктивність','Тривалість виходу в готовність','Тривалість розкрутки шпинделя','Кількість стартів або пробуджень','Кількість вилучених блоків','Кількість перепризначених секторів','Експлуатаційний запас каналу читання','Частота помилок позиціонування','Швидкодія системи позиціонування','Час напрацювання накопичувача','Кількість повторних спроб розкрутки','HP Лічильник мета-відновлень','Число повторів рекалібровки','Кількість включень живлення','Кількість запусків / зупинок','Частота ''м''яких'' помилок читання','Невдачі повторного тестування','Помилки захисту даних','Рівень заправки (Gas gauge)','Поточний рівень гелію','Average MRR (Maxtor)','Minimum MRR (Maxtor)','Maximum MRR (Maxtor)','Average FHC','Max Negative FHC','Цикли програмного стирання','Кількість циклів програмного стирання','Max positive FHC','Лічильник виходів з режиму сну','Контроль висоти польоту головок','Entering PS1 count','Перебудов таблиць транслятора','Нескоректована помилки читання / запису','Кількість справних запасних блоків','Кількість запасних блоків','Початкова кількість негідних блоків','Кількість стирань','Максимальну кількість стирань','Мінімальна кількість стирань','Середня кількість стирань','Максимальна кількість стираних NAND за специфікацією','Сумарна кількість бед-блоків / SMI залишок життя (%)','Кількість резервних блоків','Кількість програмних збоїв','Число невдало стертих блоків','Найгірший рівень зношених осередків','Несподівані провали живлення','Найгірше кількість програмних помилок SMI','Найгірше кількість помилок стирання','Діапазон зносу', 'Вирівнювання зносу, %','Використане кількість запасних блоків','Використано запасних блоків','Вільних запасних блоків','Кількість програмних помилок','Кількість помилок при стирання','Бед-блоків в процесі життя','Сумарне число помилок CRC','Стабільність магнітних головок','Виявлення вібрації','Нескоректована ECC-кодом помилки','Перевищення часу очікування після команди','Альтернативний датчик температури','Перевищення висоти польоту головки','Первинний термодатчик','Температура повітряного потоку','Кількість спрацьовувань датчика удару','Цикли аварійної парковки','Небезпечних відключень','Кількість раптових вимкнень живлення','Кількість парковок / распарковок','Температура контролера','Температура гермоблока','Помилки, підраховані на льоту','RAISE Recovered','Апаратно скориговані помилки','Спроб перепризначень секторів','Кандидатів на перепризначення','Дефектні сектори під час самотеста','Помилки передачі даних через DMA','Частота помилок запису (multi-zone errors)','Зарезервоване кількість блоків','Частота помилок запису','Невиправні програмні помилки читання','Detected TA count','Кількість невдач при корекції помилок','Відсоток використаного ресурсу','Перевищення теплового навантаження','Помилки адресних маркерів','Частота невдач стирання','Лічильник рівня зносу','Скасовані спроби читання','Кількість помилок ECC','Число ударів під час запису','Скориговані ECC-кодом помилки','Max PE count spec','Частота ударів під час запису','Частота перевищень температури','Min Erase Count','Висота польоту головок','Тривалість калібрування','Max erase count','Сила струму при розкручуванні диска','Середня кількість стирань','Кількість писків перед розкруткою','Що залишився термін служби за кількістю стирань, %','Продуктивність автономного пошуку','Вібрація під час запису','Струм при обертанні, mA','Вібрація під час читання','Кількість помилок SSM','Удари під час запису','Приховані помилки позиціонування','Приховане кількість помилок парковки','Ні-афішуються помилки розкрутки','Кількість раптових помилок','Unlock / Mis read count','Кількість корекцій даних ПЗУ','Зміщення дисків','Кількість ударів з сенсора','Час в распаркованном стані','Число повторних спроб запарковаться','Тертя в парковці','Сумарна кількість записаних LBA','Число циклів парковки','Індикатор брешемо ні зносу носія','Час в запаркованому стані','Відсоток робочого навантаження читанням','Компенсація крутного моменту','Workload timer','Лічильник відмін виключення живлення','Halt system ID, flash ID','Вимірювач висоти польоту головок','Статус самозахисту диска','Статус початку подиханню','Амплітуда тремтіння головок','Залишок життя SSD','Залишок життя SSD','Температура гермоблока','Вичерпання ресурсу по циклам записи','Доступне вільне місце','Записів у флеш-пам''ять','Час життя NAND по ресурсу записи','Частина, що залишилася життя','Пробіг, годин','Час життя по ресурсу записи користувачем','Average erase count and maximum erase Count','Раптові відключення живлення під час запису','Вторинний термодатчик','Час перебування головок над диском','Час життя по ресурсу записи','Всього записано GB','Всього записано секторів','Час життя по ресурсу читання','Всього прочитано GB','Всього прочитано секторів','Зниженій швидкості SATA','Статус температурного тротлінга','Індикатор зносу SSD','Прочитано LBA після розпакування','Тимчасова навантаження на носій','Timed Workload Host Read / Write Ratio','Timed Workload Timer','Залишок життя у відсотках','Залишок резервних блоків у відсотках','Частота спроб перечитування','Записів у флеш-пам''ять','Non-GuaTempHours (?)','Повідомлення про помилки','Лічильник вільного падіння','невідомий атрибут','Мінімальна температура накопичувача','Максимальна температура накопичувача','Кількість виключень живлення + раптові','SATA Interface Downshift','Enclosure temperature','RAIN Successful Recovery page count','Cumulative host sectors written','Host program page count','FTL program page count','Чим менше - тим краще','Чим більше - тим краще','Потрібно дорівнювати нулю','Оптимальне значення','невідомо','Неоптимальне значення, можливі проблеми','Лічильник подій','Самозбереження','Онлайн тест','Частота помилок','Продуктивність','Життєво важливий','Дата','Час','Відмітка','Поточне значення','Атрибут','База для графіків атрибутів SMART відсутня або ще не створена','База SMART оновлена.','База SMART містить','записів','Лічильник часу','Відкрийте пункт','як SMART','SMART-атрибути витягнуті з бази за','Збережіть точку до файла','Створіть резервну копію бази даних','Завантажте базу з файлу','Оновити базу незаплановано','База не була оновлена - файлова помилка або неправильний шлях до файлу!','Перезапустіть програму і перевірте шлях до файлу','Режим захисту SSD','Reserved'
StrNVMe.Items.Strings='1234','Critical Warning','Composite Temperature','Available Spare','Available Spare Threshold','Percentage Used','Data Units Read','Data Units Written','Host Read Commands','Host Write Commands','Controller Busy Time','Power Cycles','Power On Hours','Unsafe Shutdowns','Media Errors','Number of Error Information Log Entries','Warning Composite Temperature Time','Critical Composite Temperature Time','Temperature Sensor','Thermal Management Temperature 1 Transition Count','Thermal Management Temperature 2 Transition Count','Total Time For Thermal Management Temperature','Total Trotling Time','Unknown_nvme attribute','Warning','No warning','Available spare space has fallen below the threshold','Temperature has exceeded a critical threshold','Device reliability has been degraded due to significant media related errors or any internal error that degrades device reliability','Media has been placed in read only mode','Volatile memory backup device has failed','Persistent Memory Region has become read-only or unreliable','Number of Namespaces','Number of Power states','Best','Better','Good','Degraded','performance','Reserved'
IndPanel.Hint='Панель ATA-регистрів накопичувача'
HDPn1.Hint='Назва моделі накопичувача'
HdPn2.Hint='Серійний номер накопичувача'
HdPn3.Hint='Версія мікрокода (прошивки) накопичувача'
HdPn4.Hint='Об''єм накопичувача'
MmMenu.Caption='Меню  '
MmMenu.HLP.Caption='Довідка'
MmMenu.N1.Caption='Евентлог минулої сесії'
MmMenu.Quit.Caption='Аварійний вихід'
MmService.Caption='     Сервіс      '
MmAcoustic.Caption='Акустика та Енергозбереження (AAM / APM)'
MmHPA.Caption='Захищена частина (HPA)'
MmRotateSpeed.Caption='Швидкість обертання та інші тести'
MmSecurity.Caption='Керування безпековими властивостями накопичувача'
MmDCO.Caption='Керування конфігурацією накопичувача через DCO'
MmCache.Caption='Кеш та тимчасові функції'
MmSmart.Caption='SMART - тести та інші операції'
MmOtherFe.Caption='Енергонезалежні функції'
MmControl.Caption='       Дії       '
MmSurfTest.Caption='Повний скан поверхні з картою'
Fullsurfacescanwithgraph1.Caption='Повний скан поверхні з графіком'
Quicksurfacescan1.Caption='Швидкий тест поверхні з графіком'
MmSeek.Caption='Тестування механіки HDD'
g1.Caption='Подивитись атрибути S.M.A.R.T.'
MmShortSelfDiag.Caption='Швидка самодіагностика (дві хвилини)'
MmLongSelfDiag.Caption='Повна самодіагностика (довгий час)'
MMEraseAllData.Caption='Стерти всі дані з накопичувача'
MmEraseVerify.Caption='Стерти з перевіркою даних (W-R-V)'
Lng.Caption='     Language     '
Lng.DefLng.Caption='Internal'
MmSettings.Caption='     Налаштування    '
MmHelp.Caption='   Довідка   '
HLP1.Caption='Инструкція'
ABT.Caption='Про застосунок'
DNT.Caption='Підтримати проект '
Whatnew1.Caption='Що нового?'
MmBuffer.Caption='Перегляд буфера'
SetasStartLBA.Caption='Встановити як початковий LBA'
SetasEndLBA.Caption='Встановити як кінцевий LBA'
Exit1.Caption='Відміна'
Loadgraf.Caption='Завантажити графік з файлу'
Savegraf.Caption='Зберегти графік в файл'
SaveScreenshot1.Caption='Зберегти скріншот до файлу'
Cleargraf.Caption='Видалити  усі графіки'
SetasStartLBA1.Caption='Встановити як початковий LBA'
SetasEndLBA1.Caption='Встановити як кінцевий LBA'
Close2.Caption='Закрити меню'
MenuItem1.Caption='Копіювати все'
Copy1.Caption='Копіювати значення'
MenuItem3.Caption='Отримати паспорт'
MenuItem2.Caption='PCI Scan'
OpenBinBassport.Caption='Відкрити .BIN-файл паспорту'
BigSmall1.Caption='Міжрядна відстань'
OpenDefLog.Caption='Відкрити лог дефектів як сценарій тестування'
MenuItem4.Caption='Відкрити скріпт формату .VDS'
ScanDropBtn.Hint='Меню опцій ськана поверхні (запуск сценарію)'
MenuItem16.Caption='Виділити все рядки'
MenuItem17.Caption='Копіювати виділений текст'
LogCopyvalue.Caption='Копіювати значення'
Savetofile1.Caption='Зберегти лог до файлу'
C1.Caption='Очистити лог'
Openloginfolder1.Caption='Відкрити лог в папці'
Setasstart1.Caption='Встановити як початкове'
SetasEnd1.Caption='Встановити як кінцеве'
GetSMART1.Caption='Отримати S.M.A.R.T.   [ F9 ]'
CopySm.Caption='Копіювати все'
SmmCopySelStr.Caption='Копіювати виділене'
Clearscreen1.Caption='Очистити таблицю'
OpenBINfile1.Caption='Відкрити .BIN-файл'
Chk1.Caption='Міжрядкова відстань'
Smm0.Caption='Копіювати значення'
Smm1.Caption='Копіювати рядок'
Smm2.Caption='Копіювати увесь журнал'
Smm3.Caption='Встановити значення як початковий LBA'
Smm4.Caption='Встановити значення як кінцевий LBA'
Smm5.Caption='Створити сценарій для тестування поверхні'
Smd2.Caption='Читати та відкрити HEX-переглядачі'
Smd0.Caption='Зберегти журнал до файлу'
Smd1.Caption='Зберегти УСІ журнали до файлів'
Smd3.Caption='Зберегти журнал з файлу в накопичувач'
Smd6.Caption='Зберегти журнал з перерахунком КС'
O2.Caption='Відкрити .BIN-файл як SMART-журнал'
Smd4.Caption='Оновити перелік журналів'
Smd5.Caption='Копіювати перелік'
Fontsize71.Caption='Розмір шрифту = 7'
Fontsize81.Caption='Розмір шрифту = 8'
Fontsize9default1.Caption='Розмір шрифту = 9 (за замовченням)'
Fontsize121.Caption='Розмір шрифту = 12'
MenuItem5.Caption='Копіювати увесь текст'
C2.Caption='Копіювати значення:'
C3.Caption='Копіювати значення:'
MPartSetRange.Caption='Встановити як межі сканування'
Clear1.Caption='Очистити всі рядки'
MmRescan.Caption='Оновити перелік накопичувачів'
MMDft.Caption='HGST Vendor Specific'

[THeFm]
Caption='Перегляд буферів в реальному часі'
HexV.Font.Name='Courier New'
HVPauseBtn.Hint='Призупинити онлайн оновлення для ручного дослідження'
HexBufSw.Items.Strings='HDD','API, Copier','PIO'
HVBl.Hint='Розмір буфера'
HexeditIn.Hint='Зміщення даних в буфері'
Fontsize71.Caption='Розмір шрифту = 7'
Fontsize81.Caption='Розмір шрифту = 8'
Fontsize9default1.Caption='Розмір шрифту = 9 (за замовчуванням)'
LoadHexBtn.Caption='Load'
LoadHexBtn.Hint='Завантажити файл шаблону (512 байт), який буде використаний для запису на накопичувач в режимі "Запис / Стирання"'


[THelpForm]
HelpTab.Caption='Довідка'
HelpEdit.Lines.Strings='Файл довідки не знайдено!'
AboutTab.Caption='Про застосунок'
DonateTab.Caption='Підтримати'
NewsTab.Caption='Що нового?'
WhatNew.Lines.Strings='Відсутній файл довідки! Будь ласка, покладіть файл ''whatsnew.rtf'' в робочу теку застосунку Victoria, та натисніть F1.'

[THexForm]
LoadHex.Caption='FROM'#13#10'FILE'
SaveHex.Caption='SAVE'#13#10'TO FILE'
CreateHex.Caption='CREATE'#13#10'NEW'
HexReadBufBtn.Caption='READ'#13#10'BUFFER'
HexWrBufBtn.Caption='WRITE'#13#10'BUFFER'
SwapBtn.Caption='SWAP '#13#10'BYTES'

[THPAForm]
Caption='Керування HPA - захищений простір користувача'
HpaTrack.Hint='Візуальне керування HPA'
NHPABtn.Hint='Отримати справжній об''єм'
NHPABtn.Caption='RHPA'
HpaBtn.Hint='Встановити новий об''єм накопичувача'
HpaBtn.Caption='HPA'
L1.Hint='Можна вводити числа, одиниці об''єма та відсотки.'#13'Наприклад: '#13'131072134'#13'100 MB'#13'204G'#13'17%'#13'etc.'
HPATpChk.Hint='Встановити HPA тимчасово (до вимкнення живлення)'
HPATpChk.Caption='Тимчасовий HPA'
HpaMbBox.Hint='Режим відображення: MB або LBA'
HpaMbBox.Caption='LBA/MB'
HpaExtpassChk.Hint='Оновити розширений паспорт після зміни HPA'
HpaExtpassChk.Caption='Паспорт після HPA'

[TLogFM]
Caption='Журнал подій'


[TminitestsForm]
Caption='Міні-тести'
RPMBox.Caption=' Вимірювання RPM '
RPMBox.Hint='Вимірювання швидкості обертання дисків HDD'
RzBitBtn43.Caption='Старт'


[TSecuForm]
Caption='Керування властивостями безпеки'
SecuNoteLab.Caption='Увага! Не використовуйте ці функції на WD Passport !'
PwdLab.Caption='Введення паролю:'
LockBtn.Hint='Встановлення ATA-паролю на накопичувач'
LockBtn.Caption='Встановити'
UnlockBtn.Hint='Видалення пароля з накопичувача. Необхідний вірний User- або Master-пароль (якщо рівень = High)'
UnlockBtn.Caption='Видалити'
PwdTypeGrp.Hint='Тип паролю: Master- або User-'
PwdTypeGrp.Caption=' Тип паролю '
PwdTypeGrp.Items.Strings='Master','User'
PwdLevGrp.Hint='Рівень паролювання.'#13'Впливає на спосіб подальшого розблокування накопичувача master-паролем.'#13'Якщо рівень Maximum - дані користувача будуть знищені'
PwdLevGrp.Caption=' Рівень '
PwdLevGrp.Items.Strings='High','Max'
ErasePanel.Caption='Видалення даних користувача'
PwdEraseBtn.Hint='Запустити процес видалення даних за допомогою методів безпеки'
PwdEraseBtn.Caption='Стирання данних'
EraseBeepChk.Hint='Звуковий сигнал при закінченні процесу видалення'
EraseBeepChk.Caption='Сигнал при закінченні'
PwdEdit.Hint='Поле введення ATA-пароля. 32 літери максімум.'
FilePwdBtn.Caption='Пароль з файлу'


[TSettingsForm]
Caption='Налаштуванні застосунку'
TabSheet3.Caption='Загальні'
CommonSetBox.Font.Name='Tahoma'
WColorLab.Caption='Колір вікон:'
ELogLab.Caption='Колір логу:'
RPanelLab.Caption='Колір правої панелі:'
FootPanelLab.Caption='Колір підвалу:'
SmFontLab.Caption='Шрифт в SMART:'
SmBoldChk.Caption='жирний'
PassFontLab.Caption='Шрифт в паспорті:'
PassBoldChk.Caption='жирний'
NonDestrChk.Hint='Якщо перемикач включено - застосунок забороняє операції запису на накопичувач'
NonDestrChk.Caption='Заборонити небезпечні функції (не рекомендується)'
GrLinesChk.Caption='Включити лінії в переліках'
RussianChk.Caption='''I speak Russian'''
IfApiTab1.Caption='Якщо включити режим API - відкривати паспорт'
AutoSetFocusChk.Caption='Автоматично встановлювати фокус на переліки'
SoundBox.Caption=' Звук '
BellChk.Caption='Дзвоник'
SndChk.Caption='Задіяти звуки'
NightChk.Hint='Автоматично вимкнути звук в межах з 1 до 7 годин ранку'
NightChk.Caption='Нічний режим'
UseSpk.Caption='Застосовувати PC-динамік'#13'(Тільки Win x32)'
PrevHibChk.Caption='Заборонити перехід ПК до сплячого режиму під час тестування'
PrevHibChk.Hint='Prevent the computer from entering sleep mode while testing the drive'
PrevMonChk.Caption='Prevent the monitor sleep'
PrevMonChk.Hint='Prevent monitor from sleeping during tests'
LsGrpBox.Caption=' Налаштування логу: '
LogDiskBox.Hint='Літера диску в шляху збереження логу'#13'NUL - за замовченням зберегти в поточну теку застосунка'
LogSaveTofChk.Caption='Зберегати загальній лог в файл'
RzBitBtn2.Caption='Очистити файл'
NewDateDirChk.Caption='Оновлювати дату створення каталогу'
SepLogsChk.Caption='Окремі логи для кожного накопичувача'
dvintchk.Caption='Об''єм кратний 1000 (ввімк.) / 1024 (вимк.)'
ParamBarChk.Caption='Смуга з властивостями диску під головним меню'
ThSeparatorChk.Hint='Використовувати роздільник тисяч в полях введення та виведення'
ThSeparatorChk.Caption='Роздільник тисяч'
TabSheet1.Caption='API'
oldusbchk.Hint='Спробуйте, якщо ваш USB-накопичувач має міст MA6160 (A-DATA USB 2.0) та S.M.A.R.T. не працює через нього'
oldusbchk.Caption='Старий USB/SATL режим (12-байтні команди)'
UsbDetectChk.Caption='Авто-детект накопичувачів USB'
NoResetOnRfrChk.Caption='Не скидати настройки при оновленні списку пристроїв'
NoResetOnRfrChk.Hint='При оновленні списку API-пристроїв і при автодетекте USB-накопичувачів залишатимуться незмінними всі раніше змінені режими. Зокрема, не буде скидатися запис при скане поверхні (будьте уважні!), Не буде очищатися таблиця SMART і список журналів.'
ViewLettersChk.Caption='Відображати букви логічних дисків в списку фізичних пристроїв'
ROHandlerChk.Caption='Хендлер ''тільки читання'' для вирішення проблем з Windows 10. Не використовуйте без необхідності'
SctExtChk.Caption='Застосовувати SCT EXT-команди (API && PIO)'
SctExtChk.Hint='Протокол Smart Command Transport підтримує два типу команд. Перемикання може допомогти якщо є проблема з отриманням температури накопичувача'
ApiAtaIndChk.Caption='Ввімкнути покажчики ATA-регистрів в режимі API'#13'(не рекомендується, бо не всі USB-місти передають регістри)'
RemapSctChk.Caption='Використовувати SCT-команди для переназначення (повинно працювати без вiдключення MBR)'
SettingPages.TabSheet2.Caption='PIO'
SetupAtaBox.Caption='  Налаштування ATA-протокола '
ATATimerRG.Caption='      ATA-таймер:'
PspDblChk.Caption='Отримувати паспорт за двойним кліком по переліку PCI-пристроїв'
DntRmPcChk.Caption='Не запам''ятовувати первинний канал IDE'
TabSheet4.Caption='Паспорт'
ExtPasspBox.Caption='ДІЇ ПРИ НАТИСКАННІ КНОПКИ EXT-ПАСПОРТУ:'
DSFChk.Caption='Надіслати ATA-команду діагностики (03 0C 00 4F C2 A0 EF)'
PspExpaChk.Caption='Відключити додаткові команди від кнопки "Паспорт"'
PspExpaChk.Hint='Включення цієї галки відключає від кнопки "Паспорт" функції отримання температури і додаткової технічної інформації про HDD. Вони залишаються на кнопці "EXT"'
RSChk.Caption='Визначити реальний об''єм'
PWSCHK.Caption='Отримати статус активності'
BDChk.Caption='Виконати тест інтерфейсу'
PspSmrtChk.Caption='Дізнатися кількість записаніх даних до SSD'
OnlyScsiPassChk.Hint='Встановіть цей перемикач якщо декілька дисків повертають однаковий паспорт'
OnlyScsiPassChk.Caption='Використовувати тільки SCSI-команди для отримання паспорту'
dontgetdpchk.Caption='Не використовувати SCSI-команду 25h (не рекомендується)'
Dont9EChk.Caption='Не використовувати SCSI-команду 9Eh (не рекомендується)'
Dont9EChk.Hint='Set this checkbox if the program slows down on the detect of a device or incorrectly determines its capacity'
dontgetdpchk.Hint='Set this checkbox if the program slows down on the detect of a device or incorrectly determines its capacity'
LogicChk.Caption='Відображати логичні диски'
IgnoreFddChk.Caption='Ігнорувати: FDD'
IgnoreHDDChk.Hint='Ігнорувати тома жорстких дисків'
CHSPassp.Caption='Відображати CHS-властивості'
PassChk.Caption='Зберігати паспортні дані в .BIN файли'
TabSheet5.Caption='Тестування'
Label9.Caption='Кількість спроб довгого читання:'
SwapUpDnkeysChk.Hint='Поміняти місцями кнопки стрибків уперед-назад (малий шаг в швидкій навігації)'
SwapUpDnkeysChk.Caption='Поміняти місцями кнопки стрибків уперед-назад'
EnaBrkInCyclChk.Hint='Якщо цей перемикач включено - застосунок дозволяє переривання внутрішніх циклів за клавішею «Стоп» для щвидкої зупинки, якщо тестований накопичувач зависає'
EnaBrkInCyclChk.Caption='Дозволити переривання внутрішніх циклів'
lbasyncChk.Hint='Якщо цей перемикач встановлено - застосунок оновлює максимальний LBA після кожного отримання паспорту. В іншому разі оновлення виконується виключно якщо накопичувач було змінено'
lbasyncChk.Caption='Оновлювати LBA перед кожною операцією'
ResBetwLoopScanChk.Caption='Відсилати сброс після кожного старту циклу сканування'
TestParamPanel.Caption=' Налаштування властивостей, які можуть впливати на тестування поверхні'
DontBlinkChk.Caption='Не миготіти кнопками'
CorrTimHDDChk.Caption='Налаштувати таймінги в залежності від накопичувача'
AutoRepQnChk.Caption='Автоповтор клавіш стрибків при утриманні, ms:'
AdjustCPUchk.Hint='Вимірювати частоту CPU перед кожним тестом'
AdjustCPUchk.Caption='Вимірювати частоту CPU перед кожним тестом'
TimerBox.Caption='Вимірювання часу через: (* - рекомендується)'
RTChk.Caption='  Скан: тільки PIO'
RTChk.Items.Strings='G.T.C. (Універсальний)','MMTimer (для NT)*'
ApiRGR.Caption='      Загальний таймер'
ApiRGR.Items.Strings='RDTSC (для одноядерних)','G.T.C.  (для багатоядерних)'
RecallBeforeChk.Hint='Виконувати рекалибрування HDD перед сканом (команда 10h)'
RecallBeforeChk.Caption='Виконувати рекалибрування перед сканом'
DelaySettings.Caption='Логувати затримки:'
ErLogBx50.Caption='Темно-сірі'
ErLogBx200.Caption='Зелені'
ErLogBx600.Caption='Помаранчеві'
ErLogBxbig.Caption='Красні'
ErLogBxErr.Caption='Дефекти'
StopFatalChk.Caption='Зупинити скан при фатальних помилках (тільки API)'
RecCellWidthChk.Caption='Перераховувати ширину комірок карти для щільної укладки'
TabSheet6.Caption='SMART'
DontSmStatChk.Caption='Не запитувати SMART-статус'
SmartStatusGrp.Caption='     Джерело отримання статусу SMART (тільки ATA-пристрої):'
SmartStatusGrp.Items.Strings='Накопичувач повідомляє про свій стан самостійно','Victoria програмно аналізує накопичувач'
AlwaysIBMChk.Caption='Завжди пробувати IBM супер-SMART'
SmartDiv3Chk.Caption='Розділяти RAW-значення на окремі числа'
SmrtChkFileSave.Caption='Зберегати атрибути в .BIN-файли'
GetSmartNsChk.Caption='Get NVMe Namespace SMART (not recommended)'
GetSmartNsChk.Hint='If this checkbox is checked, then SMART is getting from the namespace. Otherwise, from a physical device'
SmBaseChk.Caption='Зберігати базу даних SMART'
SmBaseIntervalChk.Caption='Інтервал оновлення бази даних (гг:мм)'
SmTrayChk.Caption='Звертати в трей замість закриття і автоматично опитувати SMART (API)'
SmHideFormChk.Caption='Згорнути програму в трей при старті (API)'
AutoGetSmartChk.Caption='Автоматично отримувати SMART при його відкритті (тільки API)'
AutoGetLogChk.Caption='Автоматично отримувати перелік SMART-журналів при його відкритті (тільки API)'
TabSheet7.Caption='Интерфейс'
LowColorChk.Caption='Використовувати колірну схему VGA (256 кольорів)'
FormResizeGrp.Caption=' Поведінка форми при зміні її розмірів '
ResizeFormChk.Caption='Розширювати всю форму захопленням нижньої межі вікна, а висоту евентлога регулювати сплиттером.'
ResizeLogChk.Caption='Розширювати евентлог захопленням нижньої межі вікна, а решта - сплиттером.'
UsbAtaRegBox.Caption='Не отримувати ATA-регістри через USB-міст'
UsbAtaRegBox.Hint='Деякі USB-мости зависають при отриманні регістрів. Установка перемикача вирішує цю проблему.'
HintTimeLab.Caption='Тривалість відображення підказок, c:'
ModeltoFilenameChk.Caption='Включати назву моделі в ім''я файлу скріншота'
ModeltoFilenameChk.Hint='Приєднує назва моделі накопичувача і його серійний номер до імені файлу скріншота.'


[TSmForm]
Caption='S.M.A.R.T. - операції'
SmOperPanel.Caption='ТАБЛИЦЯ  SMART - ТЕСТІВ:'
SmartTimerGrp.Hint='Автоматично оновлювати інформацію про проходження поточного SMART-тесту'
SmartTimerGrp.Caption='ТАЙМЕР ОПИТУВАННЯ'
Label74.Caption='[ період, с ]'
SmTimerChk.Caption='ON'
SmartCtrlGrp.Caption=' КЕРУВАННЯ'
SmartAtsOFFBtn.Hint='Вимкнути автозбереження S.M.A.R.T.-атрибутів в накопичувачі'
SmartAtsOFFBtn.Caption='Disable attributes autosave'
SmartAtsOnBtn.Hint='Ввімкнути автозбереження S.M.A.R.T.-атрибутів в накопичувачі'
SmartAtsOnBtn.Caption='Auto Save attributes ON'
SmartOnBtn.Hint='Ввімкнути S.M.A.R.T. в накопичувачі'
SmartOnBtn.Caption='SMART ON'
SmartOnBtn.Font.Name='Tahoma'
SmartOFFBtn.Hint='Вимкнути S.M.A.R.T. в накопичувачі'
SmartOFFBtn.Caption='SMART OFF'
SmartTestGrp.Caption=' SMART - ТЕСТИ'
SmtMeter.Hint='Проходження поточного SMART-теста'
SmScanBox.Items.Strings='Off-line data collect','Off-line short test','Ext. off-line routine','Selective routine','Short test (captive)','Ext. test (captive)','Selective test (captive)','Vendor specific'
SmTestBtn.Hint='Запустити обраний SMART-тест'
SmTestBtn.Caption='Begin Test'
RzBitBtn29.Hint='Надіслати накопичувачу зупинки поточного SMART-тесту'
RzBitBtn29.Caption='Abort Test'
SmartBtn.Caption='Get SMART'
SmVSTestEdit.Hint='Номер SMART-тесту по ATA-стандарту, який потрібно запустити'#13'Також тут можна вказати нестандартні значення для запуску спеціальних тестов виробника' 
SmTestLba.Hint='Деякі накопичувачі (наприклад WD) в цьому полі виводять поточний LBA під час проходження тестів'
SmartTestList.Columns.(1).Caption='Parameters of SMART data collections tests / Status after tests:'
SmartTestList.Columns.(2).Caption='Value'

[TDFTForm]
Caption='IBM/HGST Vendor specific tool'
DFTMemo.Caption='These tools are for IBM / Hitachi / HGST drives only.'
DftSmartInitBtn.Caption='SMART Init'
DftSmartInitBtn.Hint='The button clears SMART attributes to the state of a new drive.'
DftLLFBtn.Caption='Low-Level Format'
DftLLFBtn.Hint='This function transfers the growing defect list to primary list, and then format the drive.'
DftSataSpeedGrp.Caption=' SATA Mode '
DftSataSpeedBtn.Caption='Set'
DftCacheOnBtn.Caption='Cache ON'
DftCacheOFFBtn.Caption='Cache OFF'

[TWriteWarn]
Caption='Увага!'
Label1.Caption='Частина диску, яку ви вказали,'#13'буде стерта. Будь ласка, перевірте'#13'введені дані ще раз та скоректуйте'
Label2.Caption='НАКОПИЧУВАЧ БУВ ЗАМІНЕНИЙ'
StartLbaEdit.Hint='Start LBA'
EndLbaEdit.Hint='End LBA'

[TSmartgfForm]
SmGfChangePanel.Caption='Маштаб по:'
FromNulChk.Caption='Y від нуля'
FromNulChk.Hint='Якщо цей перемикач не включений, то графік будується від мінімального значення функції. Інакше - від нуля.'

[ResourceStrings]

