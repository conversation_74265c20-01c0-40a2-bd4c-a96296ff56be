import PublicFuc
import configparser
import csv
import os,re
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  <PERSON><PERSON>Fill,Alignment,Border,Side,colors,Font
from datetime import datetime,timedelta

def Run(curpath, workBook, alignment):
    ws = workBook['Empty chunk']
    ws.alignment = alignment
    ProChunckTable1(curpath, ws,32)
    ProImtCase(curpath,ws,8)
    PublicFuc.WriteReportTime(ws,'E',2)
    PublicFuc.WriteReportOperator(ws,'H',2)

def ProImtCase(curpath, worksheet,diskCnt):
    #仿真监控测试
    imtCol = ['B','C','D','F','G','H','I']
    imtKeyLst = ['cap','pc_no','4K_Random_100Write_Iops', '4K_Random_100Write_MiBps','4K_Random_100Read_Iops', '4K_Random_100Read_MiBps']
    pattern = '.+\\\\Plan13\\\\T_GE_SD_C29\\\\Iometer4K-12H\\\\\d{14}\\\\report.ini$'
    imtDic = {}
    PublicFuc.ReadQaIniDataEx(curpath, pattern, imtDic, imtKeyLst, '',1,diskCnt)
    startLine = 44
    InitIOMeterReportTemplateInWorkSheet(worksheet, startLine, imtDic)
    PublicFuc.WriteData(worksheet, startLine, imtDic, imtCol, imtKeyLst)


 #按照实际数据条数生成表格样式
def InitIOMeterReportTemplateInWorkSheet(worksheet,startLine,imtDic):
    cellfont=Font('微软雅黑',size=10,color=colors.BLACK,bold=False,italic=False)
    for rowIdx in range(len(imtDic)):
        for col in range(10):
            worksheet['%s%d'%(get_column_letter(col+1), startLine+rowIdx)].alignment = PublicFuc.alignment
            worksheet['%s%d'%(get_column_letter(col+1), startLine+rowIdx)].border = PublicFuc.my_border('thin', 'thin', 'thin', 'thin')
            worksheet['%s%d'%(get_column_letter(col+1), startLine+rowIdx)].font = cellfont
            if col == 4:
                worksheet['%s%d'%(get_column_letter(col+1), startLine+rowIdx)] = 'NTFS'

def ProChunckTable1(curpath,worksheet,recordCnt):
    pattern = '.+\\\\Plan6\\\\T_GE_SD_C7\\\\Mars_H2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2'
    caseDicH2 = {} 
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseDicH2, caseName, recordCnt)

    pattern = '.+\\\\Plan6\\\\T_GE_SD_C8\\\\AT_CopyFile\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    #marsCopyKey = ['TEST_RESULT','START_TIME','END_TIME']
    #ReadMarsIniDataLocal(curpath, pattern, dicMarsData, 'AT_CopyFile', 'MARS_COPYFILE', marsCopyKey, '',0)
    caseName = 'AT_CopyFile'
    caseCopyFile = {} 
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseCopyFile, caseName, recordCnt)

    pattern = '.+\\\\Plan6\\\\T_GE_SD_C22\\\\BIT\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'BurnIn' 
    caseDicBIT = {}
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseDicBIT, caseName, recordCnt)

    pattern = '.+\\\\Plan6\\\\T_GE_SD_C11\\\\SPOR\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'SPOR'
    caseDicSPOR = {}
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseDicSPOR, caseName, recordCnt)


    keyLst = ['cap','pc_no_H2','wavg_H2','ravg_H2','test_time_H2','result_H2',
             'test_time_CopyFile','result_CopyFile',
             'test_time_BIT','test_time_BIT','circle_BIT','result_BIT',
             'test_time_SPOR','circle_SPOR','result_SPOR',
             'RetryCnt','SLCBadBlock_New','TLCBadBlock_New','WL_SLC_MAX','WL_SLC_MIN','WL_SLC_AVG','WL_TLC_MAX','WL_TLC_MIN','WL_TLC_AVG','SLC_Diff','TLC_Diff','PowerUpCnt']  #第一次也显示磨损差值

    #GetNewMarsDic把ini读出来的dic按照keyLst转换为模板所需数据列表
    newDic = GetNewChunkReportDic(caseDicH2,caseCopyFile,caseDicBIT,caseDicSPOR)

    colLst = ['B','C','D','E','F','G','H',
              'I','J','K','L','M','N',
              'O','P','Q','R','S',
              'T','U','V','W', 'X','Y','Z','AA','AB','AC']

    startLine = 7
   
    #写内容
    resultColumnList = ['H','J','N','Q']
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst)

#获取测试时间
def GetTestTime(dic):
    #测试时间
    endTimeStr = PublicFuc.GetValueFromDic(dic,'end_time')
    startTimeStr = PublicFuc.GetValueFromDic(dic,'start_time')
    if '' == endTimeStr or '' == startTimeStr:
        return ''
    else:
        try:
            endtime = datetime.strptime(endTimeStr, '%Y-%m-%d %H:%M:%S')
            starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
            totalSecond = timedelta.total_seconds(endtime-starttime)
            totalSecond = int(totalSecond)
            hour = int(totalSecond/3600)
            lefSeconds = totalSecond%3600
            minutes = int(lefSeconds/60)
            seconds = lefSeconds%60
            timeStr = '%d:%d:%d'%(hour,minutes,seconds)
            return timeStr
        except:
            return ''      

def GetNewChunkReportDic(caseDicH2,caseCopyFile,caseDicBIT,caseDicSPOR):
    newDic = {}
    for key in caseDicH2:
        newDic[key] = []
        dic = caseDicH2[key]  
        
        #容量取整数G
        if '' == dic['capacity']:
            newDic[key].append('')
        else:
            newDic[key].append(int(float(dic['capacity'])*1024)) #转化为M为单位
        
        #PC编号
        if '' == dic['MMS_PC']:
            newDic[key].append('')
        else:
            newDic[key].append(dic['MMS_PC'])
        
        newDic[key].append(PublicFuc.GetValueFromDic(dic,'average_write_vel'))
        newDic[key].append(PublicFuc.GetValueFromDic(dic,'average_read_vel'))

        #测试时间
        testTime = GetTestTime(dic)
        newDic[key].append(testTime)

        #测试结果
        h2Result = PublicFuc.GetValueFromDic(dic,'test_result')
        newDic[key].append(h2Result)
        if h2Result != '' and h2Result.upper() != 'PASS' and h2Result.upper() != 'TRUE':
            PublicFuc.AppendErrDiskInfo('H2_Err',key,h2Result,dic['MMS_PC'],dic['file_path'])

        #CopyFile
        if key in caseCopyFile:
            dic = caseCopyFile[key]
            testTime = GetTestTime(dic)
            newDic[key].append(testTime)
            strResult = PublicFuc.GetValueFromDic(dic,'test_result')
            newDic[key].append(strResult)
            
            if strResult != '' and strResult.upper() != 'PASS' and strResult.upper() != 'TRUE':
                PublicFuc.AppendErrDiskInfo('Copy File_Err',key,strResult,dic['MMS_PC'],dic['file_path'])
        else:
            newDic[key].append('')
            newDic[key].append('')

        #BIT
        if key in caseDicBIT:#如果键值在里面
            dic = caseDicBIT[key]       
            newDic[key].append('25℃')
            #测试时间
            testTime = GetTestTime(dic)
            newDic[key].append(testTime)
            #圈数
            testCycle = PublicFuc.GetValueFromDic(dic,'teset_circle')
            if '' == testCycle:
                newDic[key].append('')
            else:
                newDic[key].append(int(testCycle))

            #测试结果
            testResult =  PublicFuc.GetValueFromDic(dic,'test_result','FAIL')
            newDic[key].append(testResult)
            if testResult != '' and testResult.upper() != 'PASS' and testResult.upper() != 'TRUE':
                PublicFuc.AppendErrDiskInfo('RT BIT_Err',key,testResult,dic['MMS_PC'],dic['file_path'])

        else:
            newDic[key].append('25℃') #温度
            newDic[key].append('') #测试时间
            newDic[key].append('') #圈数
            newDic[key].append('') #测试结果


        #SPOR
        if key in caseDicSPOR:#如果键值在里面
            dic = caseDicSPOR[key]       

            #测试时间
            testTime = GetTestTime(dic)
            newDic[key].append(testTime)
            #圈数
            testCycle = PublicFuc.GetValueFromDic(dic,'powerdown_cnt')
            if '' == testCycle:
                newDic[key].append('')
            else:
                newDic[key].append(int(testCycle))

            #测试结果
            testResult =  PublicFuc.GetValueFromDic(dic,'test_result','FAIL')
            newDic[key].append(testResult)
            if testResult != '' and testResult.upper() != 'PASS' and testResult.upper() != 'TRUE':
                PublicFuc.AppendErrDiskInfo('空快耗尽_Err',key,testResult,dic['MMS_PC'],dic['file_path'])

        else:
            newDic[key].append('') #测试时间
            newDic[key].append('') #圈数
            newDic[key].append('') #测试结果
        
        
        #smart信息
        WL_SLC_MAX = ''
        WL_SLC_MIN = ''
        WL_TLC_MAX = ''
        WL_TLC_MIN = ''
        if key in caseDicSPOR:#如果键值在里面
            dic = caseDicSPOR[key]
            if 'RetryCnt'.lower() in dic:
                if ''== dic['RetryCnt'.lower()]:
                    newDic[key].append('')
                else:
                    newDic[key].append(dic['RetryCnt'.lower()])
            else:
                newDic[key].append('')
          
            if 'SLCBadBlock_New'.lower() in dic:
                if ''== dic['SLCBadBlock_New'.lower()]:
                    newDic[key].append('')
                else:
                    newDic[key].append(dic['SLCBadBlock_New'.lower()])
            else:
                newDic[key].append('')

            if 'TLCBadBlock_New'.lower() in dic:
                if ''== dic['TLCBadBlock_New'.lower()]:
                    newDic[key].append('')
                else:
                    newDic[key].append(dic['TLCBadBlock_New'.lower()])
            else:
                newDic[key].append('')

            if 'WL_SLC_MAX'.lower() in dic or 'WL SLC MAX'.lower() in dic:
                if 'WL_SLC_MAX'.lower() in dic:
                    WL_SLC_MAX = dic['WL_SLC_MAX'.lower()]
                    newDic[key].append(dic['WL_SLC_MAX'.lower()])
                else:
                    WL_SLC_MAX = dic['WL SLC MAX'.lower()]
                    newDic[key].append(dic['WL SLC MAX'.lower()])
            else:
                newDic[key].append('')

            if 'WL_SLC_MIN'.lower() in dic or 'WL SLC MIN'.lower() in dic:
                if 'WL_SLC_MIN'.lower() in dic:
                    WL_SLC_MIN = dic['WL_SLC_MIN'.lower()]
                    newDic[key].append(dic['WL_SLC_MIN'.lower()])
                else:
                    WL_SLC_MIN = dic['WL SLC MIN'.lower()]
                    newDic[key].append(dic['WL SLC MIN'.lower()])
            else:
                newDic[key].append('')

            if 'WL_SLC_AVG'.lower() in dic or 'WL SLC AVG'.lower() in dic:
                if 'WL_SLC_AVG'.lower() in dic:
                    newDic[key].append(dic['WL_SLC_AVG'.lower()])
                else:
                    newDic[key].append(dic['WL SLC AVG'.lower()])
            else:
                newDic[key].append('')

            if 'WL_TLC_MAX'.lower() in dic or 'WL TLC MAX'.lower() in dic:
                if 'WL_TLC_MAX'.lower() in dic:
                    WL_TLC_MAX = dic['WL_TLC_MAX'.lower()]
                    newDic[key].append(dic['WL_TLC_MAX'.lower()])
                else:
                    WL_TLC_MAX = dic['WL TLC MAX'.lower()]
                    newDic[key].append(dic['WL TLC MAX'.lower()])
            else:
                newDic[key].append('')

            if 'WL_TLC_MIN'.lower() in dic or 'WL TLC MIN'.lower() in dic:
                if 'WL_TLC_MIN'.lower() in dic:
                    WL_TLC_MIN = dic['WL_TLC_MIN'.lower()]
                    newDic[key].append(dic['WL_TLC_MIN'.lower()])
                else:
                    WL_TLC_MIN = dic['WL TLC MIN'.lower()]
                    newDic[key].append(dic['WL TLC MIN'.lower()])
            else:
                newDic[key].append('')

            if 'WL_TLC_AVG'.lower() in dic or 'WL TLC AVG'.lower() in dic:
                if 'WL_TLC_AVG'.lower() in dic:
                    newDic[key].append(dic['WL_TLC_AVG'.lower()])
                else:
                    newDic[key].append(dic['WL TLC AVG'.lower()])
            else:
                newDic[key].append('')

            if WL_SLC_MAX == '' or WL_SLC_MIN == '':
                newDic[key].append('')
            else:
                newDic[key].append(str(int(WL_SLC_MAX[2:], 16) - int(WL_SLC_MIN[2:], 16)))

            if WL_TLC_MAX == '' or WL_TLC_MIN == '':
                newDic[key].append('')
            else:
                newDic[key].append(str(int(WL_TLC_MAX[2:], 16) - int(WL_TLC_MIN[2:], 16)))

            if 'PowerUpCnt'.lower() in dic:
                if ''== dic['PowerUpCnt'.lower()]:
                    newDic[key].append('')
                else:
                    newDic[key].append(dic['PowerUpCnt'.lower()])
            else:
                newDic[key].append('')
        else:
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')

    return newDic 


def GetNewAutomEmptyChunkReportDic(caseDicH2,caseCopyFile,caseDicEmptyChunk):
    newDic = {}
    for key in caseDicH2:
        newDic[key] = []
        dic = caseDicH2[key]  
        
        #容量取整数G
        if '' == dic['capacity']:
            newDic[key].append('')
        else:
            newDic[key].append(int(float(dic['capacity'])*1024)) #转化为M为单位
        
        #PC编号
        if '' == dic['MMS_PC']:
            newDic[key].append('')
        else:
            newDic[key].append(dic['MMS_PC'])
        
        newDic[key].append(PublicFuc.GetValueFromDic(dic,'average_write_vel'))
        newDic[key].append(PublicFuc.GetValueFromDic(dic,'average_read_vel'))

        #测试时间
        testTime = GetTestTime(dic)
        newDic[key].append(testTime)

        #测试结果
        h2Result = PublicFuc.GetValueFromDic(dic,'test_result')
        newDic[key].append(h2Result)
        if h2Result != '' and h2Result.upper() != 'PASS' and h2Result.upper() != 'TRUE':
            PublicFuc.AppendErrDiskInfo('H2_Err',key,h2Result,dic['MMS_PC'],dic['file_path'])

        #CopyFile
        if key in caseCopyFile:
            dic = caseCopyFile[key]
            testTime = GetTestTime(dic)
            newDic[key].append(testTime)
            strResult = PublicFuc.GetValueFromDic(dic,'test_result')
            newDic[key].append(strResult)
            
            if strResult != '' and strResult.upper() != 'PASS' and strResult.upper() != 'TRUE':
                PublicFuc.AppendErrDiskInfo('Copy File_Err',key,strResult,dic['MMS_PC'],dic['file_path'])
        else:
            newDic[key].append('')
            newDic[key].append('')


        #EmptyChunk
        if key in caseDicEmptyChunk:#如果键值在里面
            dic = caseDicEmptyChunk[key]       

            #测试时间
            testTime = GetTestTime(dic)
            newDic[key].append(testTime)

            #写读速度
            newDic[key].append(PublicFuc.GetValueFromDic(dic,'average_write_vel'))
            newDic[key].append(PublicFuc.GetValueFromDic(dic,'average_read_vel'))

            #掉电圈数
            testCycle = PublicFuc.GetValueFromDic(dic,'powerdown_cnt')
            if '' == testCycle:
                newDic[key].append('')
            else:
                newDic[key].append(int(testCycle))

            #测试结果
            testResult =  PublicFuc.GetValueFromDic(dic,'test_result','FAIL')
            newDic[key].append(testResult)
            if testResult != '' and testResult.upper() != 'PASS' and testResult.upper() != 'TRUE':
                PublicFuc.AppendErrDiskInfo('空快耗尽_Err',key,testResult,dic['MMS_PC'],dic['file_path'])

        else:
            newDic[key].append('') #测试时间
            newDic[key].append('') #写平均速度
            newDic[key].append('') #读平均速读
            newDic[key].append('') #掉电圈数
            newDic[key].append('') #测试结果
        
        
        #smart信息
        if key in caseDicEmptyChunk:#如果键值在里面
            dic = caseDicEmptyChunk[key]
            if 'RetryCnt'.lower() in dic:
                if ''== dic['RetryCnt'.lower()]:
                    newDic[key].append('')
                else:
                    newDic[key].append(dic['RetryCnt'.lower()])
            else:
                newDic[key].append('')
          
            if 'SLCBadBlock_New'.lower() in dic:
                if ''== dic['SLCBadBlock_New'.lower()]:
                    newDic[key].append('')
                else:
                    newDic[key].append(dic['SLCBadBlock_New'.lower()])
            else:
                newDic[key].append('')

            if 'TLCBadBlock_New'.lower() in dic:
                if ''== dic['TLCBadBlock_New'.lower()]:
                    newDic[key].append('')
                else:
                    newDic[key].append(dic['TLCBadBlock_New'.lower()])
            else:
                newDic[key].append('')

            if 'WL_SLC_MAX'.lower() in dic or 'WL SLC MAX'.lower() in dic:
                if 'WL_SLC_MAX'.lower() in dic:
                    newDic[key].append(dic['WL_SLC_MAX'.lower()])
                else:
                    newDic[key].append(dic['WL SLC MAX'.lower()])
            else:
                newDic[key].append('')

            if 'WL_SLC_MIN'.lower() in dic or 'WL SLC MIN'.lower() in dic:
                if 'WL_SLC_MIN'.lower() in dic:
                    newDic[key].append(dic['WL_SLC_MIN'.lower()])
                else:
                    newDic[key].append(dic['WL SLC MIN'.lower()])
            else:
                newDic[key].append('')

            if 'WL_SLC_AVG'.lower() in dic or 'WL SLC AVG'.lower() in dic:
                if 'WL_SLC_AVG'.lower() in dic:
                    newDic[key].append(dic['WL_SLC_AVG'.lower()])
                else:
                    newDic[key].append(dic['WL SLC AVG'.lower()])
            else:
                newDic[key].append('')

            if 'WL_TLC_MAX'.lower() in dic or 'WL TLC MAX'.lower() in dic:
                if 'WL_TLC_MAX'.lower() in dic:
                    newDic[key].append(dic['WL_TLC_MAX'.lower()])
                else:
                    newDic[key].append(dic['WL TLC MAX'.lower()])
            else:
                newDic[key].append('')

            if 'WL_TLC_MIN'.lower() in dic or 'WL TLC MIN'.lower() in dic:
                if 'WL_TLC_MIN'.lower() in dic:
                    newDic[key].append(dic['WL_TLC_MIN'.lower()])
                else:
                    newDic[key].append(dic['WL TLC MIN'.lower()])
            else:
                newDic[key].append('')

            if 'WL_TLC_AVG'.lower() in dic or 'WL TLC AVG'.lower() in dic:
                if 'WL_TLC_AVG'.lower() in dic:
                    newDic[key].append(dic['WL_TLC_AVG'.lower()])
                else:
                    newDic[key].append(dic['WL TLC AVG'.lower()])
            else:
                newDic[key].append('')

            if 'PowerUpCnt'.lower() in dic:
                if ''== dic['PowerUpCnt'.lower()]:
                    newDic[key].append('')
                else:
                    newDic[key].append(dic['PowerUpCnt'.lower()])
            else:
                newDic[key].append('')
        else:
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')

    return newDic 
