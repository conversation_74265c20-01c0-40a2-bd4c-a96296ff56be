import openpyxl
import json
from openpyxl.styles import Border
from openpyxl.styles import Pattern<PERSON>ill

def find_table_start(ws):
    """查找包含No.的单元格作为表头起始"""
    for row in ws.iter_rows():
        for cell in row:
            if cell.value and "No." in str(cell.value):
                return cell
    raise ValueError("未找到包含No.的表头起始标记")

def has_left_border(cell):
    """检测单元格是否有左边框"""
    return cell.border and cell.border.left.style is not None

def has_right_border(cell):
    """检测单元格是否有右边框""" 
    return cell.border and cell.border.right.style is not None

def has_header_color(cell):
    """检测单元格是否有表头背景色"""
    if cell.fill:
        return cell.fill.start_color.index not in [0, 'FFFFFF']  # 过滤默认白色/无色
    return False

def parse_excel_tables(file_path, sheet_name, output_json):
    wb = openpyxl.load_workbook(file_path, data_only=True)
    ws = wb[sheet_name]
    
    # 改为查找所有表格起始位置
    table_starts = [cell for row in ws.iter_rows() for cell in row 
                   if cell.value and str(cell.value).startswith("No.")]
    
    all_tables = []
    
    for header_cell in table_starts:
        # 获取单个表格结构（保留原有解析逻辑）
        merged_range = next((m for m in ws.merged_cells if header_cell.coordinate in m), None)
        header_rows = merged_range.min_row if merged_range else header_cell.row
        
        # 确定表头列范围
        start_col = header_cell.column_letter
        end_col = start_col
        
        # 构建表头结构
        header_structure = []
        parent_stack = []  # 用于跟踪各级父节点
        
        # 首先构建完整的表头结构
        for row_idx in range(header_rows, (merged_range.max_row if merged_range else header_rows) + 1):
            current_parents = []
            for cell in ws[row_idx]:
                if cell.column < openpyxl.utils.column_index_from_string(start_col) or not cell.value:
                    continue
                    
                merged = next((m for m in ws.merged_cells.ranges if cell.coordinate in m), None)
                
                # 创建当前节点
                from openpyxl.utils import get_column_letter
                start_col_num = merged.min_col if merged else cell.column
                end_col_num = merged.max_col if merged else cell.column
                node = {
                    "name": cell.value.strip() if cell.value else "",
                    "start_col": get_column_letter(start_col_num),
                    "end_col": get_column_letter(end_col_num),
                    "start_col_num": start_col_num,  # 新增数字列号
                    "end_col_num": end_col_num,      # 新增数字列号
                    "children": []
                }
        
                # 优化父节点查找逻辑
                parent = None
                if parent_stack:
                    # 查找包含当前节点列范围的最近父节点
                    for p in reversed(parent_stack[-1]):
                        if (start_col_num >= p["start_col_num"] and
                            end_col_num <= p["end_col_num"]):
                            parent = p
                            break
                
                if parent:
                    parent["children"].append(node)
                else:
                    header_structure.append(node)
                    
                current_parents.append(node)
            
            # 更新父节点栈
            parent_stack.append(current_parents)
        
        # 递归查找最大列（移动到header_structure初始化之后）
        def find_max_col(node):
            current_max = openpyxl.utils.column_index_from_string(node["end_col"])
            for child in node["children"]:
                if isinstance(child, dict):
                    child_max = find_max_col(child)
                    current_max = max(current_max, child_max)
            return current_max
        
        # 获取所有根节点的最大列
        max_col = max([find_max_col(node) for node in header_structure]) if header_structure else openpyxl.utils.column_index_from_string(start_col)
        end_col = openpyxl.utils.get_column_letter(max_col)
        # for cell in ws[header_cell.row]:
        #     if cell.fill.start_color.index in ['FFFFFF', '00000000']:
        #         end_col = openpyxl.utils.get_column_letter(cell.column - 1)
        #         break
                
        # 获取表格名称（表头上一行的文字）
        table_name = ws[f"{header_cell.column_letter}{header_cell.row - 1}"].value
        
        result = {
            "table_name": table_name,
            "content_start": merged_range.max_row + 1 if merged_range else header_cell.row + 1,
            "column_range": f"{start_col}-{end_col}",
            "row_range": f"{header_rows}-{merged_range.max_row}" if merged_range else str(header_rows),
            "header_structure": header_structure
        }
        
        # 添加表格边界检测
        if header_cell.row > 1:
            prev_cell = ws[f"{header_cell.column_letter}{header_cell.row-1}"]
            result["table_name"] = prev_cell.value
        
        all_tables.append(result)
    
    # 保存所有表格数据
    with open(output_json, 'w') as f:
        json.dump({"tables": all_tables}, f, indent=2)

    return all_tables

# 在load_table_coordinates中同步修改
class TableCoordinate:
    def __init__(self, data):
        self.tables = [{
            'table_name': t['table_name'],
            'header_structure': t['header_structure']
        } for t in data.get('tables', [])]


def load_table_coordinates(json_file):
    """从JSON文件加载表格数据"""
    try:
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
            # 新版多表格结构处理
            if 'tables' in data:
                return [{
                    'table_name': table.get('table_name'),
                    'content_start': table.get('content_start', 1),
                    'column_range': table.get('column_range', 'A-Z').split('-'),
                    'row_range': table.get('row_range', '1-1').split('-'),
                    'header_structure': table.get('header_structure', [])
                } for table in data['tables']]
            
            # 旧版单表格结构兼容
            return [{
                'table_name': data.get('table_name'),
                'content_start': data.get('content_start', 1),
                'column_range': data.get('column_range', 'A-Z').split('-'),
                'row_range': data.get('row_range', '1-1').split('-'),
                'header_structure': data.get('header_structure', [])
            }]
    except Exception as e:
        print(f"加载文件错误: {str(e)}")
        return []

def get_column_start(coordinates, *names):
    """
    根据嵌套结构获取列起始位置
    :param coordinates: load_table_coordinates返回的字典
    :param names: 层级名称序列（支持多个参数）
    :return: 列字母或None
    """
    def find_node(nodes, name):
        for node in nodes:
            if isinstance(node, dict) and node.get('name') == name:
                return node
        return None
    
    current_level = coordinates.get('header_structure', [])
    
    # 参数校验
    if not names or not current_level:
        return None
        
    for i, name in enumerate(names):
        node = find_node(current_level, name)
        if not node:
            return None
            
        # 检查是否到达最后层级
        if i == len(names) - 1:
            if node.get('children', []):
                print(f"警告：'{'.'.join(names)}' 路径未到达最终层级")
                return None
            return node.get('start_col')
            
        # 继续深入子层级
        current_level = node.get('children', [])
        
    return None

if __name__ == "__main__":
    # 使用示例
    result = parse_excel_tables(
        "sd_consumer_report_template.xlsx",
        "Power Off Test",
        "table_coordinates.json"
    )

    # 调用示例保持兼容
    coordinates = load_table_coordinates('table_coordinates.json')
    
    # 修正后代码
    first_table = coordinates[0] if coordinates else None  # 直接访问列表第一个元素
    
    # 后续调用示例
    if first_table:
        print(f"表格名称：{first_table['table_name']}")  # 直接访问字典元素
        print(f"列范围：{first_table['column_range']}")
    
    # 其他需要调整的代码
    col = get_column_start(first_table, "No.")  # 保持参数传递单个表格字典
    col12 = get_column_start(first_table, "POR Test","Cyle")
    # 多参数示例 
    col1 = get_column_start(first_table, "Smart信息", "Sample wear information", "S_Wear\n(Avg)")
    print(f"列起始位置：{col,col1,col12}")
    # # 在构建完成后添加列号数字转换
    # def add_column_numbers(node):
    #     node["start_col_num"] = openpyxl.utils.column_index_from_string(node["start_col"])
    #     node["end_col_num"] = openpyxl.utils.column_index_from_string(node["end_col"])
    #     for child in node.get("children", []):
    #         if isinstance(child, dict):
    #             add_column_numbers(child)
    
    # for root_node in header_structure:
    #     add_column_numbers(root_node)

    