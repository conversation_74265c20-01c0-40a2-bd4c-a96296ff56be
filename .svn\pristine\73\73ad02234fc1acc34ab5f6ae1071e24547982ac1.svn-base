import re
import time

import PublicFuc
from openpyxl.drawing.image import Image
import csv
import matplotlib.pyplot as plt
import tempfile,os
from openpyxl.utils import get_column_letter

def Run(curpath, workBook, alignment):
    ws = workBook['初始_性能测试 ']
    ws.alignment = alignment
    ProCdm(curpath, ws)  
    ProAssd(curpath, ws)
    ProAtto(curpath, ws)
    proHdtune(curpath, ws)
    proIometer(curpath, ws)
    proVictoria(curpath, ws)
    proHdtuneFileBase(curpath, ws)
    PublicFuc.WriteReportTime(ws,'G',2)
    PublicFuc.WriteReportOperator(ws,'L',2)

def ProCdm(curpath, worksheet):
    #配置文件中的键
    cdmKey = ['pc_no', 'Cap', 'qa_err_msg','SeqQ32T1_Read','SeqQ32T1_Write','4KQ32T1_Read','4KQ32T1_Write','Seq_Read','Seq_Write','4K_Read','4K_Write']
    #excel中键对应填充的列，第一个是编号的列，其他列应与键顺序一一对应
    cdmCol = ['C','B', 'E', 'P','G','H','I','J','K','L','M','N']
    cdmDic = {}
    pattern = '.+\\\\Plan31\\\\T-SS-SS-A17\\\\CrystalDiskMark\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, cdmDic, cdmKey, 'CDM.bmp')
    PublicFuc.GetMaxOrMinValueLst(cdmKey, cdmDic)
    startLine = 25
    imgWidth = 250
    imgHeight = 240
    PublicFuc.WriteDataAndImage(worksheet, startLine, cdmDic, cdmCol, cdmKey, imgWidth, imgHeight)

def ProAssd(curpath, worksheet):   
    assdKey = ['pc_no', 'Cap', 'qa_err_msg','Seq Read','Seq Write','4K Read','4K Write','4K 64Thrd Read','4K 64Thrd Write','Read Acc Time','Write Acc Time','Read Score','Write Score','Total Score']
    assdCol = ['C','B','E','S','G','H','I','J','K','L','M','N','O','P','Q']
    assdDic = {}
    pattern = '.+\\\\Plan31\\\\T-SS-SS-A18\\\\AS SSD Benchmarks\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, assdDic, assdKey, 'AS_SSD.bmp')
    PublicFuc.GetMaxOrMinValueLst(assdKey, assdDic)
    startLine = 50
    imgWidth = 250
    imgHeight = 240
    PublicFuc.WriteDataAndImage(worksheet, startLine, assdDic, assdCol, assdKey, imgWidth, imgHeight)
    
def ProAtto(curpath, worksheet):
    attoKey = ['pc_no', 'Cap', 'qa_err_msg','64 MB_Write','64 MB_Read']
    attoCol = ['C','B','E','M','G','I']
    attoDic = {}
    pattern = '.+\\\\Plan31\\\\T-SS-SS-A19\\\\ATTO Disk Benchmark\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, attoDic, attoKey, 'ATTO.bmp')
    PublicFuc.GetMaxOrMinValueLst(attoKey, attoDic)
    startLine = 75
    imgWidth = 250
    imgHeight = 330
    PublicFuc.WriteDataAndImage(worksheet, startLine, attoDic, attoCol, attoKey, imgWidth, imgHeight)

def WriteDataAndImageOfHdtune(worksheet, startLine, dataDic, colLst, imgWidth, imgHeight):
    imageLine = startLine+2
    curLine = startLine
    for key in dataDic:
        imageCol = 1
        for line in dataDic[key]:
            for index,col in enumerate(colLst):
                if 0 == index:
                    worksheet['%s%d'%(col, curLine)] = key
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
            curLine += 1
            # hdtune列表最后两项是图片路径(读和写)
            if '' != line[-2]:
                img = Image(line[-2])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
                imageCol += 8
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
            imageCol += 3
        curLine = startLine+1
        imageLine += 1

def WriteDataAndImageOfHdtuneFullWrite(worksheet, startLine, dataDic, colLst, imgWidth, imgHeight):
    imageLine = startLine+2
    curLine = startLine
    imageCol = 1
    for key in dataDic:
        for line in dataDic[key]:
            for index,col in enumerate(colLst):
                try:
                    if 0 == index:
                        worksheet['%s%d'%(col, curLine)] = key
                    else:
                        worksheet['%s%d'%(col, curLine)] = line[index-1]
                 #合并的单元格只能写一次，需要捕获异常
                except(AttributeError):
                    continue
            curLine += 1
            # 列表最后一项是图片路径
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
            imageCol += 8

def proHdtune(curpath, worksheet):
    hdtuneKey = ['pc_no', 'Cap', 'qa_err_msg', 'min spped', 'max spped', 'avg spped', 'acess time', 'sundden trans rate', 'cpu usage']
    hdtuneCol = ['C', 'B', 'E', 'T', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R']

    # hdtune 64K读写
    readDic = {}
    pattern = '.+\\\\Plan31\\\\T-SS-SS-A20\\\\HDTunePro\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, readDic, hdtuneKey, 'HDTune.bmp', 1)
    writeDic = {}
    pattern = '.+\\\\Plan31\\\\T-SS-SS-A21\\\\HDTunePro\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, writeDic, hdtuneKey, 'HDTune.bmp', 1)

    rwDic = {}
    for key in readDic:
        if key not in writeDic:
            continue
        readRecord = readDic[key][0]
        writeRecord = writeDic[key][0]
        readRecord[2] += writeRecord[2]
        rwRecord = []
        for i in range(len(readRecord)):
            rwRecord.append(readRecord[i])
            #容量只需要记录一次
            if i>2:
                rwRecord.append(writeRecord[i])
        rwDic[key] = []
        rwDic[key].append(rwRecord)
    startLine = 107
    imgWidth = 450
    imgHeight = 350
    WriteDataAndImageOfHdtune(worksheet, startLine, rwDic, hdtuneCol, imgWidth, imgHeight)

    # hdtune 64K满盘读写
    readDic = {}
    pattern = '.+\\\\Plan31\\\\T-SS-SS-E39\\\\HDTunePro\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, readDic, hdtuneKey, 'HDTune.bmp', 1)
    writeDic = {}
    pattern = '.+\\\\Plan31\\\\T-SS-SS-A22\\\\HDTunePro\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, writeDic, hdtuneKey, 'HDTune.bmp', 1)

    rwDic = {}
    for key in readDic:
        if key not in writeDic:
            continue
        readRecord = readDic[key][0]
        writeRecord = writeDic[key][0]
        readRecord[2] += writeRecord[2]
        rwRecord = []
        for i in range(len(readRecord)):
            rwRecord.append(readRecord[i])
            # 容量只需要记录一次
            if i > 2:
                rwRecord.append(writeRecord[i])
        rwDic[key] = []
        rwDic[key].append(rwRecord)
    startLine = 114
    imgWidth = 450
    imgHeight = 350
    #WriteDataAndImageOfHdtuneFullWrite(worksheet, startLine, writeDic, hdtuneCol, imgWidth, imgHeight)
    WriteDataAndImageOfHdtune(worksheet, startLine, rwDic, hdtuneCol, imgWidth, imgHeight)

    # hdtune 1M读写
    readDic = {}
    pattern = '.+\\\\Plan31\\\\T-SS-SS-E31\\\\HDTunePro\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, readDic, hdtuneKey, 'HDTune.bmp', 1)
    writeDic = {}
    pattern = '.+\\\\Plan31\\\\T-SS-SS-E32\\\\HDTunePro\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, writeDic, hdtuneKey, 'HDTune.bmp', 1)

    rwDic = {}
    for key in readDic:
        if key not in writeDic:
            continue
        readRecord = readDic[key][0]
        writeRecord = writeDic[key][0]
        readRecord[2] += writeRecord[2]
        rwRecord = []
        for i in range(len(readRecord)):
            rwRecord.append(readRecord[i])
            # 容量只需要记录一次
            if i > 2:
                rwRecord.append(writeRecord[i])
        rwDic[key] = []
        rwDic[key].append(rwRecord)
    startLine = 121
    imgWidth = 450
    imgHeight = 350
    WriteDataAndImageOfHdtune(worksheet, startLine, rwDic, hdtuneCol, imgWidth, imgHeight)

    # hdtune 1M满盘读写
    readDic = {}
    pattern = '.+\\\\Plan31\\\\T-SS-SS-E33\\\\HDTunePro\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, readDic, hdtuneKey, 'HDTune.bmp', 1)
    writeDic = {}
    pattern = '.+\\\\Plan31\\\\T-SS-SS-E34\\\\HDTunePro\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, writeDic, hdtuneKey, 'HDTune.bmp', 1)

    rwDic = {}
    for key in readDic:
        if key not in writeDic:
            continue
        readRecord = readDic[key][0]
        writeRecord = writeDic[key][0]
        readRecord[2] += writeRecord[2]
        rwRecord = []
        for i in range(len(readRecord)):
            rwRecord.append(readRecord[i])
            # 容量只需要记录一次
            if i > 2:
                rwRecord.append(writeRecord[i])
        rwDic[key] = []
        rwDic[key].append(rwRecord)
    startLine = 128
    imgWidth = 450
    imgHeight = 350
    WriteDataAndImageOfHdtune(worksheet, startLine, rwDic, hdtuneCol, imgWidth, imgHeight)

def proHdtuneFileBase(curpath, worksheet):
    hdtuneKey = ['pc_no', 'Cap', 'qa_err_msg','sequential read speed','sequential write speed','4kb read speed','4kb write speed','4kb queue read speed','4kb queue write speed','data mode','file length']
    hdtuneCol = ['C','B','E','T','G','I','K','M','O','P','Q','R']
    dataDic = {}
    pattern = '.+\\\\Plan31\\\\T-SS-SS-C43\\\\HDTunePro\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dataDic, hdtuneKey, 'HDTune.bmp', 1)

    for keyNo in dataDic:
        keySet = dataDic[keyNo]
        for line in keySet:
            if line[2] == '':
                line[2] = 'PASS'

    imgWidth = 666
    imgHeight = 666
    #WriteDataAndImageOfHdtune(worksheet, startLine, rwDic, hdtuneCol, imgWidth, imgHeight)
    # hdtune满盘写
    startLine = 135
    WriteDataAndImageOfHdtuneFullWrite(worksheet, startLine, dataDic, hdtuneCol, imgWidth, imgHeight)

def GetImagePath(strCsvFile, key):
    strPath = ''
    dataLst = []
    try:
        with open(strCsvFile, 'r', errors='ignore') as f:
            rowLst = list(csv.reader(f))
            for line in rowLst:
                if len(line) >= 14 and 'WORKER' == line[1]:
                    dataLst.append(float(line[13]))
    except:
        return ''
    if [] != dataLst:
        #自适应纵坐标刻度值
        maxVerticalNum = 20 #只有20个左右的刻度时纵坐标刻度才易看清楚。
        maxValue = max(dataLst)
        verticalGap = maxValue/maxVerticalNum
        candidateGap = [10,20,30,50,100,150,200,300]
        detaList = [0]*len(candidateGap)
        for idx in range(len(candidateGap)):
            detaList[idx] = abs(candidateGap[idx]-verticalGap)
        minDeta = min(detaList)
        minIdx = detaList.index(minDeta)
        targetGap = candidateGap[minIdx]

        plt.figure(figsize=(13,5))
        plt.title('%s  Phy_Seq_1M_2H(MB/s)'%key)
        xLst = [x for x in range(len(dataLst))]
        plt.plot(xLst, dataLst)
        ax = plt.gca()
        ax.xaxis.set_major_locator(plt.MultipleLocator(120))
        plt.xlim(xLst[0]-30, None) #减掉30是为了右移留空白。
        plt.xticks(rotation=90)
        ax.yaxis.set_major_locator(plt.MultipleLocator(targetGap))
        if not os.path.exists(PublicFuc.strSsdTempDir):
            os.mkdir(PublicFuc.strSsdTempDir)
        strPath = os.path.join(PublicFuc.strSsdTempDir, '%s_iometer.png'%key)
        plt.savefig(strPath, bbox_inches='tight')
        plt.close()  
    return strPath

def proIometer(curpath, worksheet):
    smartKey = PublicFuc.commonSmartKey
    smartKeyNew = ['F1', 'F2', 'SmartInfo', 'A5-A6']
    imtCol = ['C','B','E','R', 'G','I','K','L','M','P']
    imtKeyLst = ['pc_no', 'Cap', 'qa_err_msg', 'Seq_1M_2H_Iops', 'Seq_1M_2H_MiBps']
    pattern = '.+\\\\Plan65\\\\T-SS-SS-A23\\\\IOmeter\\\\\d{14}\\\\report.ini$'
    newKey = imtKeyLst+smartKey
    imtDic = {}
    PublicFuc.ReadQaIniData(curpath, pattern, imtDic, newKey, 'IOmeter.bmp', 1)

    #plan65合并到了plan31中，plan65暂时保留，因此两个地方的结果都需要统计
    pattern = '.+\\\\Plan31\\\\T-SS-SS-A23\\\\IOmeter\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, imtDic, newKey, 'IOmeter.bmp', 1)

    newDic = PublicFuc.GetNewIoMeterDic(imtDic, len(imtKeyLst), smartKey, True)
    newKey = imtKeyLst+smartKeyNew
    startLine = 142
    PublicFuc.WriteData(worksheet, startLine, newDic, imtCol, newKey)
    startLine += 2
    imgWidth = 1000
    imgHeight = 320
    for key in newDic:
        for line in newDic[key]:
            strPath = GetImagePath(line[-1], key)
            if '' != strPath:
                img = Image(strPath)
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, 'G%d'%startLine)
                startLine += 1

def proVictoria(curpath, worksheet):
    vicKey = ['pc_no', 'Cap', 'qa_err_msg', 'MaxSpeed', 'AvgSpeed', 'MinSpeed']
    vicCol = ['C', 'B', 'E', 'T', 'G', 'K', 'O']
    vicDic = {}
    pattern = '.+\\\\Plan31\\\\T-SS-SS-E36\\\\Victoria\\\\\d{14}\\\\report.ini$'
    ReadQaIniDataEx(curpath, pattern, vicDic, vicKey, 'VIC.bmp')
    startLine = 100
    imgWidth = 580
    imgHeight = 480
    vicDic = GetNewVictoriaDic(vicDic)
    WriteDataAndImageOfHdtuneFullWrite(worksheet, startLine, vicDic, vicCol, imgWidth, imgHeight)

def GetNewVictoriaDic(dic):
    newDic = {}
    for key in dic:
        newDic[key] = []
        tempLst = []
        tempLst.append(dic[key][0][0])
        tempLst.append(dic[key][0][1])
        tempLst.append(dic[key][0][2])
        tempLst.append(dic[key][0][3])
        tempLst.append(dic[key][0][4])
        tempLst.append(dic[key][0][5])
        tempLst.append(dic[key][0][7])
        newDic[key].append(tempLst)
    return newDic

def ReadQaIniDataEx(curpath, pattern, dataDic, keyLst, imageSuffix, recordCnt = 10, diskCnt = 2):
    unitLst = ['MB/s', 'ms', '%']
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除
        PublicFuc.config.clear()
        PublicFuc.config.read(file, encoding ='gbk')
        for sec in PublicFuc.config.sections():
            if sec not in dataDic:
                #diskCnt为0表示统计所有磁盘数据
                if diskCnt == len(dataDic) and 0 != diskCnt:
                    continue
                dataDic[sec] = []
                tempLst = []
                pcNo = ''
                if 'pc_no' in PublicFuc.config[sec]:
                    pcNo = PublicFuc.config[sec]['pc_no']
                for key in keyLst:
                    if key.lower() in PublicFuc.config[sec]:
                        value = PublicFuc.config[sec][key.lower()]
                        #去除单位
                        for unit in unitLst:
                            if unit in value:
                                value = value[:value.find(unit)]
                                break
                        tempLst.append(value)
                        if 'qa_err_msg' == key:
                            filemt= time.localtime(os.stat(file).st_mtime)
                            strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                            PublicFuc.errDiskLst.append([sec,pcNo,value,strTime])
                    else:
                        tempLst.append('')
                fileMdTime = os.path.getmtime(file)
                if len(dataDic[sec]) < recordCnt and [] != tempLst:
                    tempLst.append(fileMdTime)
                    imgHasFound = False
                    image = '%s_%s'%(sec, imageSuffix)
                    for filename in os.listdir(imagepath):
                        if image in filename:
                            image = os.path.join(imagepath, filename)
                            tempLst.append(image)
                            imgHasFound = True
                            break
                    if not imgHasFound:
                            tempLst.append('')
                    dataDic[sec].append(tempLst)
            else:
                fileMdTime = os.path.getmtime(file)
                if dataDic[sec][0][-2] > fileMdTime:
                    continue
                dataDic[sec] = []
                tempLst = []
                pcNo = ''
                if 'pc_no' in PublicFuc.config[sec]:
                    pcNo = PublicFuc.config[sec]['pc_no']
                for key in keyLst:
                    if key.lower() in PublicFuc.config[sec]:
                        value = PublicFuc.config[sec][key.lower()]
                        #去除单位
                        for unit in unitLst:
                            if unit in value:
                                value = value[:value.find(unit)]
                                break
                        tempLst.append(value)
                        if 'qa_err_msg' == key:
                            filemt= time.localtime(os.stat(file).st_mtime)
                            strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                            PublicFuc.errDiskLst.append([sec,pcNo,value,strTime])
                    else:
                        tempLst.append('')
                if len(dataDic[sec]) < recordCnt and [] != tempLst:
                    tempLst.append(fileMdTime)
                    imgHasFound = False
                    image = '%s_%s' % (sec, imageSuffix)
                    for filename in os.listdir(imagepath):
                        if image in filename:
                            image = os.path.join(imagepath, filename)
                            tempLst.append(image)
                            imgHasFound = True
                            break
                    if not imgHasFound:
                        tempLst.append('')
                    dataDic[sec].append(tempLst)