import PublicFuc
from datetime import datetime,timedelta
import pymssql
import time,csv,re,os
from openpyxl.styles import  Pat<PERSON><PERSON>ill,Alignment
import configparser
config = configparser.RawConfigParser()
def Run(curpath, workBook, alignment):
    ws = workBook['接收测试']
    ws.alignment = alignment
    ProXlcReadDisturb(curpath,ws)
    ProBitCase(curpath, ws)
    ProReadDisturb(curpath, ws)
    ProAllmode(curpath, ws)
    ProWKSPOR(curpath, ws)
    ProTempHighRW(curpath, ws)
    ProTempLowRW(curpath, ws)
    ProDateRemind(curpath, ws)
    PublicFuc.WriteReportTime(ws,'P',2)
    PublicFuc.WriteReportOperator(ws,'E',2)

def GetNewBitsDic(oldDic):
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        dic = oldDic[key]
        newDic[key].append(dic['pc_no'])
        if 'Cap' in dic and dic['Cap'] != '':
            newDic[key].append(int(float(dic['Cap'])))
        else:
            newDic[key].append('')
        if 'BitCycle' in dic:
            newDic[key].append(dic['BitCycle'])
        else:
            newDic[key].append('')
        if 'Duration' in dic:
            newDic[key].append(dic['Duration'])
        else:
            newDic[key].append('')
        if 'waf' in dic:
            newDic[key].append(dic['waf'])
        else:
            newDic[key].append('')
        if 'test_result' in dic:
            newDic[key].append(dic['test_result'])
        else:
            newDic[key].append('')
        rwdata = ''
        if '06' in dic and dic['06'] != '':
            rwdata += str(round(int(dic['06'],16)*512*1000/1024/1024/1024)) + '/'
        else:
            rwdata += '/'
        if '07' in dic and dic['07'] != '':
            rwdata += str(round(int(dic['07'],16)*512*1000/1024/1024/1024))
        newDic[key].append(rwdata)
        #F1、F2 1个单位为32M，需转化为G
        smart = ''
        #统计不为0的smart信息
        for innerKey in dic.keys():
            if innerKey.startswith('id_'):
                if '' == dic[innerKey]:
                    continue
                if 0 != int(dic[innerKey],16):
                    pos = innerKey.find('id_')
                    id = innerKey[pos+len('id_'):].upper()
                    if id in PublicFuc.commonSmartKey:
                        smart += '%s=%s,'%(id, dic[innerKey][2:].upper())
        if '' != smart:
            smart = smart[:-1]
        newDic[key].append(smart)
    
    return newDic
#样片编号、电脑平台、容量、测试结果、读写数据量、测试时间、smart结果

def ProBitCommon(curpath, ws, pattern, bitCol, startLine, diskCnt = 4):
    smartKey = PublicFuc.mars_commonSmartKey
    bitKey = ['pc_no','Cap','qa_err_msg','BitCycle','Duration','waf','06','07']
    colLst = ['C','B','E','H','J','S','T','I','K']
    bitDic = {}
    newKey = bitKey+smartKey
    PublicFuc.ReadMarsIniDataPro(curpath, pattern, bitDic, newKey, '', 1, diskCnt)
    newDic = GetNewBitsDic(bitDic)
    PublicFuc.WriteDataNormal(ws, startLine, newDic, colLst, bitKey)


def ProBitCase(curpath, ws):
    bitCol = ['C','B','E','T','H','J']
    #bit高温老化逻辑盘
    pattern = '.+\\\\Plan43\\\\T-SS_PCIE_M2-C17\\\\BurnIn测试\\\\\d{14}\\\\report.ini$'
    startLine = 112
    ProBitCommon(curpath, ws, pattern, bitCol, startLine,8)
     #bit高温老化物理盘
    pattern = '.+\\\\Plan44\\\\T-SS_PCIE_M2-C18\\\\BurnIn测试\\\\\d{14}\\\\report.ini$'
    startLine = 116
    ProBitCommon(curpath, ws, pattern, bitCol, startLine,8)
    #低温
    pattern = '.+\\\\Plan45\\\\T-SS_PCIE_M2-C19\\\\BurnIn测试\\\\\d{14}\\\\report.ini$'
    startLine = 124
    ProBitCommon(curpath, ws, pattern, bitCol, startLine,8)

    pattern = '.+\\\\Plan46\\\\T-SS_PCIE_M2-C20\\\\BurnIn测试\\\\\d{14}\\\\report.ini$'
    startLine = 128
    ProBitCommon(curpath, ws, pattern, bitCol, startLine,8)

def GetNewMarsDic(oldDic, keyLst):
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        dic = oldDic[key]
        newDic[key].append(dic['pc_name'])
        if dic['cap'] != '':
            newDic[key].append(int(float(dic['cap'])))
        else:
            newDic[key].append('')
        newDic[key].append(dic['test_result'])
        rwdata = ''
        if dic['06'] != '':
            rwdata += str(round(int(dic['06'],16)*512*1000/1024/1024/1024)) + '/'
        else:
            rwdata += '/'
        if dic['07'] != '':
            rwdata += str(round(int(dic['07'],16)*512*1000/1024/1024/1024))
        newDic[key].append(rwdata)
        #F1、F2 1个单位为32M，需转化为G
        if '' == dic['end_time'] or '' == dic['start_time']:
            newDic[key].append('')
        else:
            endtime = datetime.strptime(dic['end_time'], '%Y-%m-%d %H:%M:%S')
            starttime = datetime.strptime(dic['start_time'], '%Y-%m-%d %H:%M:%S')
            hours = timedelta.total_seconds(endtime-starttime)//(60*60)
            newDic[key].append('%dH'%hours)
        smart = ''
        #统计不为0的smart信息
        for innerKey in dic.keys():
            if innerKey.startswith('id_'):
                if '' == dic[innerKey]:
                    continue
                if 0 != int(dic[innerKey],16):
                    pos = innerKey.find('id_')
                    id = innerKey[pos+len('id_'):].upper()
                    if id in PublicFuc.commonSmartKey:
                        smart += '%s=%s,'%(id, dic[innerKey][2:].upper())
        if '' != smart:
            smart = smart[:-1]
        newDic[key].append(smart)
        for item in keyLst:
            newDic[key].append(dic[item])
    return newDic
#样片编号、电脑平台、容量、测试结果、读写数据量、测试时间、smart结果

def GetNewMarsAndSmartDic(oldDic, keyLst):
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        dic = oldDic[key]
        newDic[key].append(dic['pc_name'])
        if dic['cap'] != '':
            newDic[key].append(int(float(dic['cap'])))
        else:
            newDic[key].append('')
        newDic[key].append(dic['test_result'])
        wrdata = ''
        if '07' in dic:
            if dic['07'] != '':
                wrdata += str(round(int(dic['07'],16)*512*1000/1024/1024/1024)) + '/'
            else:
                wrdata += '/'
        if '06' in dic:
            if dic['06'] != '':
                wrdata += str(round(int(dic['06'],16)*512*1000/1024/1024/1024))
        newDic[key].append(wrdata)
        #F1、F2 1个单位为32M，需转化为G
        if '' == dic['end_time'] or '' == dic['start_time']:
            newDic[key].append('')
        else:
            endtime = datetime.strptime(dic['end_time'], '%Y-%m-%d %H:%M:%S')
            starttime = datetime.strptime(dic['start_time'], '%Y-%m-%d %H:%M:%S')
            hours = timedelta.total_seconds(endtime-starttime)//(60*60)
            newDic[key].append('%dH'%hours)
        smart = ''
        #统计不为0的smart信息
        for innerKey in dic.keys():
            if innerKey.startswith('p_'):
                if '' == dic[innerKey]:
                    continue
                if 0 != int(dic[innerKey],16):
                    pos = innerKey.find('p_')
                    id = innerKey[pos+len('p_'):].upper()
                    if id in PublicFuc.commonSmartKey:
                        smart += '%s=%s,'%(id, dic[innerKey][2:].upper())
        if '' != smart:
            smart = smart[:-1]
        newDic[key].append(smart)
        for item in keyLst:
            if item in dic:
                newDic[key].append(dic[item])
            else:
                newDic[key].append('')
    return newDic

def proMarsCommon(curpath, worksheet, pattern, caseName, startLine, keyLst, colLst):
    caseDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, caseDic, caseName,4)
    #GetNewMarsDic把ini读出来的dic按照keyLst转换为模板所需数据列表
    newDic = GetNewMarsDic(caseDic, keyLst)
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst)

def proMarsAndSmart(curpath, worksheet, pattern, caseName, startLine, keyLst, colLst):
    caseDic = {}
    PublicFuc.ReadMarsInfo(curpath, pattern, caseDic, caseName,4)
    #GetNewMarsDic把ini读出来的dic按照keyLst转换为模板所需数据列表
    newDic = GetNewMarsAndSmartDic(caseDic, keyLst)
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst)

def ProReadDisturb(curpath, ws):
    keyLst = ['test_cirlce','avg_write_speed','max_write_speed','min_write_speed','avg_read_speed','max_read_speed','min_read_speed','waf']
    colLst = ['C','B','E','V','J','K','S','I','L','M','N','O','P','Q','U']
 
    pattern = '.+\\\\Plan28\\\\T-SS_PCIE_M2-C68\\\\ReadDisturb\\\\\\d{14}\\\\.+\\\\mms.*\.ini$'
    caseName = 'ReadDisturb'
    startLine = 145 
    proMarsAndSmart(curpath, ws, pattern, caseName, startLine, keyLst, colLst)

def ProXlcReadDisturb(curpath, ws):
    keyLst = ['test_cirlce','avg_write_speed','max_write_speed','min_write_speed','avg_read_speed','max_read_speed','min_read_speed','waf']
    colLst = ['C','B','E','V','J','K','S','I','L','M','N','O','P','Q','U']
 
    pattern = '.+\\\\Plan58\\\\T-SS_PCIE_M2-C107\\\\Mars_OpenXLC\\\\\\d{14}\\\\.+\\\\mms.*\.ini$'
    caseName = 'OpenXLC'
    startLine = 153
    proMarsAndSmart(curpath, ws, pattern, caseName, startLine, keyLst, colLst)

def ProAllmode(curpath, ws):
    keyLst = ['test_cirlce','powercnts','avg_write_speed','max_write_speed','min_write_speed','avg_read_speed','max_read_speed','min_read_speed','waf']
    colLst = ['C','B','E','V','I','J','S','H','K','L','M','N','O','P','Q','U']
 
    pattern = '.+\\\\Plan20\\\\T-SS_PCIE_M2-C24\\\\AllMode\\\\\\d{14}\\\\.+\\\\mms.*\.ini$'
    caseName = 'AllMode'
    startLine = 161
    proMarsCommon(curpath, ws, pattern, caseName, startLine, keyLst, colLst)

def ProWKSPOR(curpath, ws):
    keyLst = ['powercnts','waf']
    colLst = ['C','B','E','O','J','M','K','I','N']
 
    pattern = '.+\\\\Plan64\\\\T-SS_PCIE_M2-C108\\\\WKSPOR\\\\\\d{14}\\\\.+\\\\mms.*\.ini$'
    caseName = 'WKSPOR'
    startLine = 186
    proMarsCommon(curpath, ws, pattern, caseName, startLine, keyLst, colLst)

def ProTempHighRW(curpath, ws):
    TempCol =  ['pc_no','mode_name','Cap','WAF','fir write speed','fir read speed','fir smart','sec read speed','sec smart','test_result']
    colLst = ['B','C','E','S','H','I','J','N','O','T']
    startLine = 170
    dataDic = {}
    pattern = '.+\\\\Plan60\\\\T-SS_PCIE_M2-C75\\\\满盘H2\\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadH2testwData(curpath,pattern,dataDic)

    pattern = '.+\\\\Plan61\\\\T-SS_PCIE_M2-C109\\\\H2_verify\\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadH2testrData(curpath,pattern,dataDic,'sec')
    PublicFuc.WriteData2testw(ws, startLine, dataDic, TempCol, colLst, 'T')

def ProTempLowRW(curpath, ws):
    TempCol =  ['pc_no','mode_name','Cap','WAF','fir write speed','fir read speed','fir smart','sec read speed','sec smart','test_result']
    colLst = ['B','C','E','S','H','I','J','N','O','T']
    startLine = 178
    dataDic = {}
    pattern = '.+\\\\Plan62\\\\T-SS_PCIE_M2-C76\\\\满盘H2\\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadH2testwData(curpath,pattern,dataDic)

    pattern = '.+\\\\Plan63\\\\T-SS_PCIE_M2-C110\\\\H2_verify\\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadH2testrData(curpath,pattern,dataDic,'sec')
    PublicFuc.WriteData2testw(ws, startLine, dataDic, TempCol, colLst, 'T')

def ProDateRemind(curpath, ws):
    TempCol =  ['pc_no','mode_name','Cap','WAF','fir write speed','fir read speed','sec read speed','trd read speed','four read speed','fir smart','sec smart','test_result']
    colLst = ['B','C','E','S','G','H','I','J','k','L','O','U']
    startLine = 135
    dataDic = {}
    pattern = '.+\\\\Plan47\\\\T-SS_PCIE_M2-C21\\\\满盘H2\\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadH2testwData(curpath,pattern,dataDic)

    pattern = '.+\\\\Plan57\\\\T-SS_PCIE_M2-C113\\\\H2_1\\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadH2testrData(curpath,pattern,dataDic,'sec')
    pattern = '.+\\\\Plan57\\\\T-SS_PCIE_M2-C113\\\\H2_2\\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadH2testrData(curpath,pattern,dataDic,'trd')
    pattern = '.+\\\\Plan57\\\\T-SS_PCIE_M2-C113\\\\H2_3\\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadH2testrData(curpath,pattern,dataDic,'four')
    PublicFuc.WriteData2testw(ws, startLine, dataDic, TempCol, colLst, 'U')


   
   