
import configparser
import csv
import os,re,time,logging
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors,Font
from datetime import datetime,timedelta
import openpyxl
import chardet

warnFill = PatternFill('solid', fgColor='FF0000')
red_font = Font(color='FF0000')#红色字体
bold_blue_font = Font(bold=True,color='0000FF')#红色字体
bold_red_font = Font(bold=True,color='FF0000')#红色字体
titlefont=Font('微软雅黑',size=11,color=colors.BLACK,bold=True,italic=False)
alignL = Alignment(horizontal='left',vertical='center',wrap_text=True)

dicPfm = {} #存放性能相关数据

com_dir_col = 1
csv_col = 2
content_col = 3
result_col = 2
test_total_time_col = 3
ocurr_err_time_col = 4
err_msg_col = 5

autoAlignt = Alignment(wrapText=True)

def Run(curpath, workBook, alignment):
    XU4ResultDir=os.path.join(curpath,'XU4')
    wb = openpyxl.Workbook()
    alignment = Alignment(horizontal='center',vertical='center')
    ws=wb.active
    ws.alignment = alignment
    ws.title = 'MTT测试报告'
    ws.column_dimensions['A'].width = 30
    ws.column_dimensions['B'].width = 70
    ws.column_dimensions['C'].width = 18
    ws.column_dimensions['D'].width = 18
    ws.column_dimensions['E'].width = 70
    ws.column_dimensions['F'].width = 10
    ws.row_dimensions[1].height = 30
    print('报告统计中请等待...')
    FillData(XU4ResultDir, ws)
    resultFile = os.path.join(curpath, 'IND_MTT测试报告.xlsx')
    wb.save(resultFile)
    print('报告统计完成')

#判定是否是MTT结果文件夹
def IsMttDir(dirPath):
    listCsv = []
    listLog =[]
    listCsv,listLog = GetSubFile(dirPath)
    if len(listCsv) == 0 or len(listLog) == 0:
        return False
    for csvFileName in listCsv:
        absoluteFilePath = dirPath + '\\' + csvFileName
        with open(absoluteFilePath) as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            birth_header = next(csv_reader) #略过标题
            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中            
                if row[0].upper() == 'FAILED' or row[0].upper() == 'PASSED':
                    return True
    return False

def GetComCaseName(_rawCaseName):
    idx = _rawCaseName.rfind("\\")
    if idx == -1:
        return _rawCaseName
    
    newStr = _rawCaseName[idx+1:]
    return newStr

#写内容
def FillData(curpath,worksheet):
    #寻找所有的文件夹
    planDirPath = curpath
    listDir = []
    listDir = GetXU4Dir(planDirPath)
    dicSampe ={}
    dicSampe = dicSampe.fromkeys(listDir,'')

    listCustomErrKeywords = GetCustomKeyWords(curpath)
            
    dicDataCsv = {}
    for dir in listDir:
        if dir not in dicDataCsv:
            listCsvFile = []
            tmpDirPath = planDirPath + '\\' + dir
            listCsvFile = GetCsvFileWithTimeOrder(tmpDirPath)
            dicDataCsv[dir] = listCsvFile
            
    
    worksheet['%s%d'%(get_column_letter(com_dir_col), 1)] = '测试项'
    worksheet['%s%d'%(get_column_letter(result_col), 1)] = '测试结果'
    worksheet['%s%d'%(get_column_letter(test_total_time_col), 1)] = '测试总体时长'
    worksheet['%s%d'%(get_column_letter(ocurr_err_time_col), 1)] = '测试启动到出错时间'
    worksheet['%s%d'%(get_column_letter(err_msg_col), 1)] = '出错描述'

    for colidx in range(1,6):
        worksheet['%s%d'%(get_column_letter(colidx), 1)].font = titlefont
        worksheet['%s%d'%(get_column_letter(colidx), 1)].alignment = alignL

    curLine = 2 #从第二行开始，第一行是标题
    comDirIdx = 0
    for dirName in dicDataCsv:
        print('当前进度:%.2f%%'%(comDirIdx*100/len(dicDataCsv)))
        comfont=Font('微软雅黑',size=11,color=colors.BLACK,bold=True,italic=False)
        worksheet['%s%d'%(get_column_letter(com_dir_col), curLine)].font = comfont

        comCaseline = curLine #记录当前的case行
        if dirName == '':
            idx = planDirPath.rfind('\\')
            tmpName = planDirPath[idx+1:]
            worksheet['%s%d'%(get_column_letter(com_dir_col), comCaseline)] = tmpName
        else:
            worksheet['%s%d'%(get_column_letter(com_dir_col), comCaseline)] = GetComCaseName(dirName)

        bPass = True
        caseBeginTime = GetCaseBeginTime(dicDataCsv[dirName])
        caseLastTime = GetCaseEndTime(dicDataCsv[dirName])

        for perCsvList in dicDataCsv[dirName]:
            csvFileName = perCsvList[2]
            absoluteFilePath = planDirPath + '\\'+dirName + '\\' + csvFileName
            
            with open(absoluteFilePath) as csvfile:
                csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
                birth_header = next(csv_reader) #略过标题
                for row in csv_reader:  # 将csv 文件中的数据保存到file_data中
                    if len(row) >= 5:
                        if (row[0].upper() == 'FAILED'):
                            bPass = False
                            curLine += 1
                            #失败的文件要记录下来 
                            errFileLogName = 'Failed ' + row[1]
                            worksheet['%s%d'%(get_column_letter(result_col), curLine)] = errFileLogName
                            worksheet['%s%d'%(get_column_letter(result_col), curLine)].font = red_font
                            #计算其距离出错的时间
                            errTime = GetTimeFromString(row[4])
                            strErrTimeGap = GetTimeElapseStr(errTime,caseBeginTime)
                            worksheet['%s%d'%(get_column_letter(ocurr_err_time_col), curLine)] = strErrTimeGap
                            #分析其错误具体信息
                            absoluteLogFilePath = planDirPath + '\\'+dirName + '\\' + errFileLogName + '.log'
                            errinfo = GetErrMsg(absoluteLogFilePath)
                            errinfo += GetAllCustomErrMsg(absoluteLogFilePath,listCustomErrKeywords)
                            if errinfo != '':
                                worksheet['%s%d'%(get_column_letter(err_msg_col), curLine)] = errinfo
                            else:
                                worksheet['%s%d'%(get_column_letter(err_msg_col), curLine)] = ''#排版需要

                            
        if bPass == False:
            worksheet['%s%d'%(get_column_letter(result_col), comCaseline)] = 'FAIL'
            worksheet['%s%d'%(get_column_letter(result_col), comCaseline)].font = bold_red_font
        else:
            worksheet['%s%d'%(get_column_letter(result_col), comCaseline)] = 'PASS'
            worksheet['%s%d'%(get_column_letter(result_col), comCaseline)].font = bold_blue_font

        totalCaseTime= ''
        if caseBeginTime != 0 and caseLastTime != 0:
            totalCaseTime = GetTimeElapseStr(caseLastTime,caseBeginTime) #GetTotalTimeStr(planDirPath + '\\'+dirName + '\\',dicDataCsv[dirName])
        worksheet['%s%d'%(get_column_letter(test_total_time_col), comCaseline)]  = totalCaseTime

        curLine += 1
        comCaseline = curLine
        comDirIdx += 1
       

def GetCaseBeginTime(listPerCase):
    caseBeginTime = 0
    for perCsv in listPerCase:
        caseBeginTime = perCsv[0]
        break
    return caseBeginTime

def GetCaseEndTime(listPerCase):
    caseEndTime = 0
    for perCsv in listPerCase:
        caseEndTime = perCsv[1]

    return caseEndTime


    #worksheet['%s%d'%(get_column_letter(1), 1)] = 'nihao'
#获取默认的错误码
def GetErrMsg(absolutLogFilePath):
    errMsg = ''
    lba = ''
    if os.path.exists(absolutLogFilePath) == False:
        return errMsg

    bFindErrMsg = False
    bFindLBA = False
    strCoding='gb2312'
    nCnt=3
    while nCnt>0:
        try:
            with open(absolutLogFilePath,encoding = strCoding) as logFile:
                for line in logFile:
                    if bFindErrMsg == False:
                        idx = line.upper().find('[ERROR]')
                        if idx != -1:
                            errMsg = line[idx+8:]
                            bFindErrMsg = True
                    
                    if bFindLBA == False:
                        idx = line.find('Dump write blkdata, addr')
                        if idx != -1:
                            pos = line.find('=')
                            if pos != -1:
                                lba =  ' LBA:' + line[pos:]
                                bFindLBA = True
                    
                    if bFindErrMsg == True and bFindLBA == True:
                        break
            errMsg += lba
            break
        except Exception as e:
            strCoding='UTF-8'
            nCnt-=1
            if nCnt == 1:
                with open(absolutLogFilePath, 'rb') as file:
                    raw_data = file.read()
                    result = chardet.detect(raw_data)
                    strCoding = result['encoding']
    if nCnt==0:
        raise -1
    return errMsg

#查找指定的错误类型errKeyWord指定的错误
def GetErrMsgByKeyWord(absolutLogFilePath,errKeyWord):
    keyWord = errKeyWord.upper()
    errMsg = ''
    if os.path.exists(absolutLogFilePath) == False:
        return errMsg

    bFindErrMsg = False
    with open(absolutLogFilePath,encoding = 'gb2312') as logFile:
        for line in logFile:
            if bFindErrMsg == False:
                idx = line.upper().find(keyWord)
                if idx != -1:
                    errMsg = line[idx:]
                    bFindErrMsg = True
                    break
    return errMsg

def GetAllCustomErrMsg(absolutLogFilePath,listErrKeyWord):
    errMsg = ''
    splitChar = ''
    for keyWord in listErrKeyWord:
        errMsg += GetErrMsgByKeyWord(absolutLogFilePath,keyWord)
        errMsg += splitChar

    return errMsg
 
def GetCustomKeyWords(curpath):
    absoluteIniFile = curpath + '\\mtt_keyword.ini'
    config = configparser.RawConfigParser()
 
    errkeywords = ''
    config.clear()
    config.read(absoluteIniFile)
    for sec in config.sections():
        if sec == 'CUSTOM_ERR_CONFIG':
            for key in config[sec]:
                if key == 'err_keyword':
                    errkeywords = config[sec][key]
                    break
            break
    
    listKeyWords = []
    if errkeywords == '':
        return listKeyWords

    listKeyWords = errkeywords.split(';')

    validKeyWords = []
    for keyword in listKeyWords:
        if keyword != '':
            keyword = keyword.strip()
            validKeyWords.append(keyword)
    return validKeyWords


def GetXU4Dir(curPath):
    listDir = []
    if IsMttDir(curPath):
        listDir.append('')
    for dirpath,dirnames,filenames in os.walk(curPath):
        for dir in dirnames:
            dirName = dir.upper()
            absolutePath = dirpath+ '\\' + dir
            if IsMttDir(absolutePath):
                realDir = dir
                idx = absolutePath.find(curPath)
                if idx != -1:
                    realDir = absolutePath[len(curPath)+1:]
                if realDir not in listDir:
                    listDir.append(realDir)
    return listDir

def GetSubFile(curPath):
    AllSubFile = []
    listCsvFile = []
    listLogFile = []
    for dirpath,dirnames,filenames in os.walk(curPath):
        AllSubFile = filenames
        break

    for file in AllSubFile:
        if file.endswith('.csv'):
            listCsvFile.append(file)
        elif file.endswith('.log'):
            listLogFile.append(file)

    return listCsvFile,listLogFile

def GetCsvFileWithTimeOrder(curPath):
    listCsvFile = []
    for dirpath,dirnames,filenames in os.walk(curPath):
        AllSubFile = filenames
        break

    for file in AllSubFile:
        timeList = []
        if file.endswith('.csv'):
            absolutFileName = curPath + '\\'  + file
            if "Result.csv" in file:
                continue
            bTime = GetCsvFileBeginTime(absolutFileName)
            eTime = GetCsvFileEndTime(absolutFileName) #不同csv开始时间可能一样，但是结束时间大概率不一样
            if eTime != 0:
                tempLst = [bTime,eTime, file]
                listCsvFile.append(tempLst)
    
    sortedListCsvFile = sorted(listCsvFile,key = lambda x:(x[0],x[1]))
    return sortedListCsvFile

def GetTimeFromString(str):
    realTime = time.strptime(str,'%Y.%m.%d %H:%M:%S')
    return realTime

def GetCsvFileBeginTime(csvFullFileName):
    beginTime = 0
    with open(csvFullFileName) as csvfile:
        csv_reader = csv.reader(csvfile)
        birth_header = next(csv_reader)

        for row in csv_reader:
            if len(row) >= 5:
                beginTime = GetTimeFromString(row[3]) #获取此csv中最早的文件开始时间
                break

    return beginTime

def GetCsvFileEndTime(csvFullFileName):
    endTime = 0
    with open(csvFullFileName) as csvfile:
        csv_reader = csv.reader(csvfile)
        birth_header = next(csv_reader)

        for row in csv_reader:
            if len(row) >= 5:
                endTime = GetTimeFromString(row[4]) #获取此csv中最晚结束的文件结束时间

    return endTime


def GetFirstLogBeginTime(rootPath,subDirName,listLogFileName):
    minTime = 0
    minTimeFilePath = ''
    if len(listLogFileName) > 0:
        firstFilePath = rootPath + '\\' + subDirName + '\\' + listLogFileName[0]
        minTime = os.path.getmtime(firstFilePath)
        minTimeFilePath = firstFilePath
   
    
    for logFileName in listLogFileName:
        absoluteFilePath = rootPath + '\\' + subDirName + '\\' + logFileName
        curTime = os.path.getmtime(absoluteFilePath)
        if curTime < minTime:
            minTime = curTime
            minTimeFilePath = absoluteFilePath
    
    if minTime != 0:
        #查找最早的时间
        with open(minTimeFilePath,encoding = 'gb2312') as logFile:
            for line in logFile:
                idx = line.find('[')
                dayInfo = line[:idx]
                realMinTime = time.strptime(dayInfo,'%Y%m%d-%H%M%S')
                minTime = time.mktime(realMinTime)               
                break
                
    return minTime

#获取时间字符串
def GetTimeElapseStr(endtime,begintime):
    strTime = ''
    eTime = time.mktime(endtime)
    bTime = time.mktime(begintime)
    timeClapse = eTime - bTime
    hours = int(timeClapse/3600)
    leftSeconds = timeClapse%3600
    minutes = int(leftSeconds/60)
    leftSeconds = int(leftSeconds%60)
    strTime = "%d时%d分%d秒"%(hours,minutes,leftSeconds)
    return strTime

def GetTotalTimeStr(relativePath,dicData):
    lastCsv = ''
    beginTime = 0
    for keyTime in dicData:
        if beginTime ==0:
            beginTime =keyTime
        lastCsv =  dicData[keyTime]

    if  beginTime == 0 or lastCsv == '':
        return ''
    endTime = 0
    lastCsvFullPathName = relativePath + lastCsv
    with open(lastCsvFullPathName) as csvfile:
        csv_reader = csv.reader(csvfile)
        birth_header = next(csv_reader)
        for row in csv_reader:
            if len(row) >= 5:
                endTime = GetTimeFromString(row[4]) #获取此csv中最早的文件开始时间

    if endTime == 0:
        return ''
    
    strTimeGap = GetTimeElapseStr(endTime, beginTime)
    return strTimeGap
