import PublicFuc
import configparser
import csv
import os, re
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import Pattern<PERSON>ill, Alignment, Border, Side, colors, Font
from datetime import datetime, timedelta

MpReportInfo = {}
MpModeInfo = {}

def Run(curpath, workBook, alignment):
    ws = workBook['InjectDebugErr']
    ws.alignment = alignment
    ProIDETable(curpath, ws, 32)
    PublicFuc.WriteReportTime(ws, 'G', 2)
    PublicFuc.WriteReportOperator(ws, 'J', 2)


def ProIDETable(curpath, worksheet, recordCnt):
    pattern = '.+\\\\'+'MANUAL_MP'+'\\\\.*(?i)MP.*\.csv$'
    ReadCsvData(curpath, pattern, MpReportInfo)
    GetMpModeInfo()
    pattern = '.+\\\\Plan49\\\\T_GE_SD_C7\\\\Mars_H2_1st\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2'
    caseDicH2_1st = {}
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseDicH2_1st, caseName, recordCnt)

    pattern = '.+\\\\Plan49\\\\T_GE_SD_C65\\\\InjectDebugErr\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'InjectDebugErr'
    caseDicIDE = {}
    PublicFuc.ReadMarsIDEIniData(curpath, pattern, caseDicIDE, caseName, recordCnt)

    pattern = '.+\\\\Plan49\\\\T_GE_SD_C7\\\\Mars_H2_2nd\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2'
    caseDicH2_2nd = {}
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseDicH2_2nd, caseName, recordCnt)

    keyLst = ['cap', 'MPMode', 'pc_no_H2', 'wavg_H2_1', 'ravg_H2_1', 'test_time_H2_1', 'result_H2_1',
              'test_time_IDE', 'err_1', 'err_2', 'err_3', 'err_4', 'err_5', 'err_6', 'err_7',
              'err_8', 'err_9', 'err_10', 'err_11', 'wavg_H2_2', 'ravg_H2_2', 'test_time_H2_2',
              'result_H2_2', 'RetryCnt', 'SLCBadBlock_New', 'TLCBadBlock_New', 'WL_SLC_MAX', 'WL_SLC_MIN',
              'WL_SLC_AVG', 'WL_TLC_MAX', 'WL_TLC_MIN', 'WL_TLC_AVG', 'SLC_Diff', 'TLC_Diff', 'PowerUpCnt', 'Conclusion']  # 第一次也显示磨损差值

    # GetNewMarsDic把ini读出来的dic按照keyLst转换为模板所需数据列表
    if caseDicH2_1st != {}:
        newDic = GetNewIDEReportDic1(caseDicH2_1st, caseDicH2_2nd, caseDicIDE)
    elif caseDicIDE != {}:
        newDic = GetNewIDEReportDic2(caseDicH2_2nd, caseDicIDE)
    else:
        newDic = GetNewIDEReportDic3(caseDicH2_2nd)
    colLst = ['B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T',
              'U', 'Z', 'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP']

    startLine = 7
    resultColumnList = ['K','L','M','N','O','P','Q','R','S','T','U','W','X','Y','AC','AP']
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst, 1, resultColumnList)


def GetMpModeInfo():
    MdTimeDic = {}
    for idx in MpReportInfo:
        infoList = MpReportInfo[idx]
        for i in range(len(infoList)):
            sampleNo = infoList[i][2]
            mpMode = infoList[i][15]
            if sampleNo == "" or mpMode == '':
                continue
            if sampleNo in MpModeInfo:
                if infoList[i][-1] >  MdTimeDic[sampleNo]:
                    MpModeInfo[sampleNo] = mpMode
                    MdTimeDic[sampleNo] = infoList[i][-1]
            else:
                MpModeInfo[sampleNo] = mpMode
                MdTimeDic[sampleNo] = infoList[i][-1]


# 获取测试时间
def GetTestTime(dic):
    # 测试时间
    endTimeStr = PublicFuc.GetValueFromDic(dic, 'end_time')
    startTimeStr = PublicFuc.GetValueFromDic(dic, 'start_time')
    if '' == endTimeStr or '' == startTimeStr:
        return ''
    else:
        try:
            endtime = datetime.strptime(endTimeStr, '%Y-%m-%d %H:%M:%S')
            starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
            totalSecond = timedelta.total_seconds(endtime - starttime)
            totalSecond = int(totalSecond)
            hour = int(totalSecond / 3600)
            lefSeconds = totalSecond % 3600
            minutes = int(lefSeconds / 60)
            seconds = lefSeconds % 60
            timeStr = '%d:%d:%d' % (hour, minutes, seconds)
            return timeStr
        except:
            return ''

def GetSmartInfo(smartInfo, casedic, key):
    # smart信息
    WL_SLC_MAX = ''
    WL_SLC_MIN = ''
    WL_TLC_MAX = ''
    WL_TLC_MIN = ''
    if key in casedic:  # 如果键值在里面
        dic = casedic[key]
        if 'RetryCnt'.lower() in dic:
            if '' == dic['RetryCnt'.lower()]:
                smartInfo.append('')
            else:
                smartInfo.append(dic['RetryCnt'.lower()])
        else:
            smartInfo.append('')

        if 'SLCBadBlock_New'.lower() in dic:
            if '' == dic['SLCBadBlock_New'.lower()]:
                smartInfo.append('')
            else:
                smartInfo.append(dic['SLCBadBlock_New'.lower()])
        else:
            smartInfo.append('')

        if 'TLCBadBlock_New'.lower() in dic:
            if '' == dic['TLCBadBlock_New'.lower()]:
                smartInfo.append('')
            else:
                smartInfo.append(dic['TLCBadBlock_New'.lower()])
        else:
            smartInfo.append('')

        if 'WL_SLC_MAX'.lower() in dic or 'WL SLC MAX'.lower() in dic:
            if 'WL_SLC_MAX'.lower() in dic:
                WL_SLC_MAX = dic['WL_SLC_MAX'.lower()]
                smartInfo.append(dic['WL_SLC_MAX'.lower()])
            else:
                WL_SLC_MAX = dic['WL SLC MAX'.lower()]
                smartInfo.append(dic['WL SLC MAX'.lower()])
        else:
            smartInfo.append('')

        if 'WL_SLC_MIN'.lower() in dic or 'WL SLC MIN'.lower() in dic:
            if 'WL_SLC_MIN'.lower() in dic:
                WL_SLC_MIN = dic['WL_SLC_MIN'.lower()]
                smartInfo.append(dic['WL_SLC_MIN'.lower()])
            else:
                WL_SLC_MIN = dic['WL SLC MIN'.lower()]
                smartInfo.append(dic['WL SLC MIN'.lower()])
        else:
            smartInfo.append('')

        if 'WL_SLC_AVG'.lower() in dic or 'WL SLC AVG'.lower() in dic:
            if 'WL_SLC_AVG'.lower() in dic:
                smartInfo.append(dic['WL_SLC_AVG'.lower()])
            else:
                smartInfo.append(dic['WL SLC AVG'.lower()])
        else:
            smartInfo.append('')

        if 'WL_TLC_MAX'.lower() in dic or 'WL TLC MAX'.lower() in dic:
            if 'WL_TLC_MAX'.lower() in dic:
                WL_TLC_MAX = dic['WL_TLC_MAX'.lower()]
                smartInfo.append(dic['WL_TLC_MAX'.lower()])
            else:
                WL_TLC_MAX = dic['WL TLC MAX'.lower()]
                smartInfo.append(dic['WL TLC MAX'.lower()])
        else:
            smartInfo.append('')

        if 'WL_TLC_MIN'.lower() in dic or 'WL TLC MIN'.lower() in dic:
            if 'WL_TLC_MIN'.lower() in dic:
                WL_TLC_MIN = dic['WL_TLC_MIN'.lower()]
                smartInfo.append(dic['WL_TLC_MIN'.lower()])
            else:
                WL_TLC_MIN = dic['WL TLC MIN'.lower()]
                smartInfo.append(dic['WL TLC MIN'.lower()])
        else:
            smartInfo.append('')

        if 'WL_TLC_AVG'.lower() in dic or 'WL TLC AVG'.lower() in dic:
            if 'WL_TLC_AVG'.lower() in dic:
                smartInfo.append(dic['WL_TLC_AVG'.lower()])
            else:
                smartInfo.append(dic['WL TLC AVG'.lower()])
        else:
            smartInfo.append('')

        if WL_SLC_MAX == '' or WL_SLC_MIN == '':
            smartInfo.append('')
        else:
            smartInfo.append(str(PublicFuc.convert_to_number(WL_SLC_MAX)-PublicFuc.convert_to_number(WL_SLC_MIN)))

        if WL_TLC_MAX == '' or WL_TLC_MIN == '':
            smartInfo.append('')
        else:
            smartInfo.append(str(PublicFuc.convert_to_number(WL_TLC_MAX)-PublicFuc.convert_to_number(WL_TLC_MIN)))

        if 'PowerUpCnt'.lower() in dic:
            if '' == dic['PowerUpCnt'.lower()]:
                smartInfo.append('')
            else:
                smartInfo.append(dic['PowerUpCnt'.lower()])
        else:
            smartInfo.append('')
    else:
        smartInfo.append('')
        smartInfo.append('')
        smartInfo.append('')
        smartInfo.append('')
        smartInfo.append('')
        smartInfo.append('')
        smartInfo.append('')
        smartInfo.append('')
        smartInfo.append('')
        smartInfo.append('')
        smartInfo.append('')
        smartInfo.append('')

def GetNewIDEReportDic1(caseDicH2_1st, caseDicH2_2nd, caseDicIDE):
    global isErrorH1
    global isErrorIDE
    global isErrorH2
    newDic = {}
    for key in caseDicH2_1st:
        isErrorH1 = False
        isErrorIDE = False
        isErrorH2 = False
        smartInfoH1 = []
        smartInfoIDE = []
        smartInfoH2 = []

        #H2Test1st
        newDic[key] = []
        dic = caseDicH2_1st[key]

        # 容量取整数G
        if '' == dic['capacity']:
            newDic[key].append('')
        else:
            newDic[key].append(int(float(dic['capacity']) * 1024))  # 转化为M为单位

        # 量化模式
        if key in MpModeInfo:
            newDic[key].append(MpModeInfo[key])
        else:
            newDic[key].append('')

        # PC编号
        if '' == dic['MMS_PC']:
            newDic[key].append('')
        else:
            newDic[key].append(dic['MMS_PC'])

        newDic[key].append(PublicFuc.GetValueFromDic(dic,'average_write_vel'))
        newDic[key].append(PublicFuc.GetValueFromDic(dic,'average_read_vel'))

        # 测试时间
        testTime = GetTestTime(dic)
        newDic[key].append(testTime)

        # smartInfo
        GetSmartInfo(smartInfoH1, caseDicH2_1st, key)

        # 测试结果
        h2Result = PublicFuc.GetValueFromDic(dic, 'test_result', 'FAIL')
        newDic[key].append(h2Result)
        if h2Result != 'UNFINISHED' and h2Result.upper() != 'PASS' and h2Result.upper() != 'TRUE':
            isErrorH1 = True
            PublicFuc.AppendErrDiskInfo('InjectDebugError_Err', key, h2Result, dic['MMS_PC'], dic['file_path'])

        #IDE
        if key in caseDicIDE:
            dic = caseDicIDE[key]

            # 测试时间
            testTime = GetTestTime(dic)
            newDic[key].append(testTime)

            # smartInfo
            GetSmartInfo(smartInfoIDE, caseDicIDE, key)

            # 测试结果
            h2Result = PublicFuc.GetValueFromDic(dic, 'test_result', 'FAIL')
            if h2Result != 'UNFINISHED' and h2Result.upper() != 'PASS' and h2Result.upper() != 'TRUE':
                isErrorIDE = True
                PublicFuc.AppendErrDiskInfo('InjectDebugError_Err', key, h2Result, dic['MMS_PC'], dic['file_path'])

            # 高等级错误
            newDic[key].append(PublicFuc.GetValueFromDic(dic, '高等级错误1'))
            newDic[key].append(PublicFuc.GetValueFromDic(dic, '高等级错误2'))
            newDic[key].append(PublicFuc.GetValueFromDic(dic, '高等级错误3'))
            newDic[key].append(PublicFuc.GetValueFromDic(dic, '高等级错误4'))
            newDic[key].append(PublicFuc.GetValueFromDic(dic, '高等级错误5'))
            newDic[key].append(PublicFuc.GetValueFromDic(dic, '高等级错误6'))
            newDic[key].append(PublicFuc.GetValueFromDic(dic, '高等级错误7'))
            newDic[key].append(PublicFuc.GetValueFromDic(dic, '高等级错误8'))
            newDic[key].append(PublicFuc.GetValueFromDic(dic, '高等级错误9'))
            newDic[key].append(PublicFuc.GetValueFromDic(dic, '高等级错误10'))
            newDic[key].append(PublicFuc.GetValueFromDic(dic, '高等级错误11'))
        else:
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')


        #H2Test2nd
        if key in caseDicH2_2nd:
            dic = caseDicH2_2nd[key]
            newDic[key].append(PublicFuc.GetValueFromDic(dic, 'average_write_vel'))
            newDic[key].append(PublicFuc.GetValueFromDic(dic, 'average_read_vel'))

            # 测试时间
            testTime = GetTestTime(dic)
            newDic[key].append(testTime)

            # smartInfo
            GetSmartInfo(smartInfoH2, caseDicH2_2nd, key)

            # 测试结果
            h2Result = PublicFuc.GetValueFromDic(dic, 'test_result', 'FAIL')
            newDic[key].append(h2Result)
            if h2Result != 'UNFINISHED' and h2Result.upper() != 'PASS' and h2Result.upper() != 'TRUE':
                isErrorH2 = True
                PublicFuc.AppendErrDiskInfo('InjectDebugError_Err', key, h2Result, dic['MMS_PC'], dic['file_path'])

        else:
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')

        # 原则上smart信息取最后一个case，如果某个case出错，则记录该case,同时考虑只测某几个case的情况
        if isErrorH1 or len(smartInfoH2) == 0 and len(smartInfoIDE) == 0:
            newDic[key].extend(smartInfoH1)
        elif isErrorIDE or len(smartInfoH2) == 0:
            newDic[key].extend(smartInfoIDE)
        else:
            newDic[key].extend(smartInfoH2)


        # Conclusion
        if isErrorH1 or isErrorIDE or isErrorH2:
            newDic[key].append('FAIL')
        else:
            newDic[key].append('PASS')

    return newDic

def GetNewIDEReportDic2(caseDicH2_2nd, caseDicIDE):
    global isErrorIDE
    global isErrorH2
    newDic = {}
    for key in caseDicIDE:
        isErrorIDE = False
        isErrorH2 = False
        smartInfoIDE = []
        smartInfoH2 = []

        #IDE
        newDic[key] = []
        dic = caseDicIDE[key]

        # 容量取整数G
        if '' == dic['capacity']:
            newDic[key].append('')
        else:
            newDic[key].append(int(float(dic['capacity']) * 1024))  # 转化为M为单位

        # 量化模式
        if key in MpModeInfo:
            newDic[key].append(MpModeInfo[key])
        else:
            newDic[key].append('')

        # PC编号
        if '' == dic['MMS_PC']:
            newDic[key].append('')
        else:
            newDic[key].append(dic['MMS_PC'])

        # 补偿H2_1st的测试结果
        newDic[key].append('')
        newDic[key].append('')
        newDic[key].append('')
        newDic[key].append('')

        # 测试时间
        testTime = GetTestTime(dic)
        newDic[key].append(testTime)

        # smartInfo
        GetSmartInfo(smartInfoIDE, caseDicIDE, key)

        # 测试结果
        h2Result = PublicFuc.GetValueFromDic(dic, 'test_result', 'FAIL')
        if h2Result != 'UNFINISHED' and h2Result.upper() != 'PASS' and h2Result.upper() != 'TRUE':
            isErrorIDE = True
            PublicFuc.AppendErrDiskInfo('InjectDebugError_Err', key, h2Result, dic['MMS_PC'], dic['file_path'])

        # 高等级错误
        newDic[key].append(PublicFuc.GetValueFromDic(dic, '高等级错误1'))
        newDic[key].append(PublicFuc.GetValueFromDic(dic, '高等级错误2'))
        newDic[key].append(PublicFuc.GetValueFromDic(dic, '高等级错误3'))
        newDic[key].append(PublicFuc.GetValueFromDic(dic, '高等级错误4'))
        newDic[key].append(PublicFuc.GetValueFromDic(dic, '高等级错误5'))
        newDic[key].append(PublicFuc.GetValueFromDic(dic, '高等级错误6'))
        newDic[key].append(PublicFuc.GetValueFromDic(dic, '高等级错误7'))
        newDic[key].append(PublicFuc.GetValueFromDic(dic, '高等级错误8'))
        newDic[key].append(PublicFuc.GetValueFromDic(dic, '高等级错误9'))
        newDic[key].append(PublicFuc.GetValueFromDic(dic, '高等级错误10'))
        newDic[key].append(PublicFuc.GetValueFromDic(dic, '高等级错误11'))

        #H2Test2nd
        if key in caseDicH2_2nd:
            dic = caseDicH2_2nd[key]
            newDic[key].append(PublicFuc.GetValueFromDic(dic, 'average_write_vel'))
            newDic[key].append(PublicFuc.GetValueFromDic(dic, 'average_read_vel'))

            # 测试时间
            testTime = GetTestTime(dic)
            newDic[key].append(testTime)

            # smartInfo
            GetSmartInfo(smartInfoH2, caseDicH2_2nd, key)

            # 测试结果
            h2Result = PublicFuc.GetValueFromDic(dic, 'test_result', 'FAIL')
            newDic[key].append(h2Result)
            if h2Result != 'UNFINISHED' and h2Result.upper() != 'PASS' and h2Result.upper() != 'TRUE':
                isErrorH2 = True
                PublicFuc.AppendErrDiskInfo('InjectDebugError_Err', key, h2Result, dic['MMS_PC'], dic['file_path'])

        else:
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')

        # 原则上smart信息取最后一个case，如果某个case出错，则记录该case,同时考虑只测某几个case的情况
        if isErrorIDE or len(smartInfoH2) == 0:
            newDic[key].extend(smartInfoIDE)
        else:
            newDic[key].extend(smartInfoH2)


        # Conclusion
        if isErrorIDE or isErrorH2:
            newDic[key].append('FAIL')
        else:
            newDic[key].append('PASS')

    return newDic


def GetNewIDEReportDic3(caseDicH2_2nd):
    global isErrorH2
    newDic = {}
    for key in caseDicH2_2nd:
        isErrorH2 = False
        smartInfoH2 = []

        # H2Test1st
        newDic[key] = []
        dic = caseDicH2_2nd[key]

        # 容量取整数G
        if '' == dic['capacity']:
            newDic[key].append('')
        else:
            newDic[key].append(int(float(dic['capacity']) * 1024))  # 转化为M为单位

        # 量化模式
        if key in MpModeInfo:
            newDic[key].append(MpModeInfo[key])
        else:
            newDic[key].append('')

        # PC编号
        if '' == dic['MMS_PC']:
            newDic[key].append('')
        else:
            newDic[key].append(dic['MMS_PC'])

        # 补偿H2_1st的测试结果
        newDic[key].append('')
        newDic[key].append('')
        newDic[key].append('')
        newDic[key].append('')

        # 补偿HIDE测试结果
        newDic[key].append('')
        newDic[key].append('')
        newDic[key].append('')
        newDic[key].append('')
        newDic[key].append('')
        newDic[key].append('')
        newDic[key].append('')
        newDic[key].append('')
        newDic[key].append('')
        newDic[key].append('')
        newDic[key].append('')
        newDic[key].append('')

        newDic[key].append(PublicFuc.GetValueFromDic(dic, 'average_write_vel'))
        newDic[key].append(PublicFuc.GetValueFromDic(dic, 'average_read_vel'))

        # 测试时间
        testTime = GetTestTime(dic)
        newDic[key].append(testTime)

        # smartInfo
        GetSmartInfo(smartInfoH2, caseDicH2_2nd, key)

        # 测试结果
        h2Result = PublicFuc.GetValueFromDic(dic, 'test_result', 'FAIL')
        newDic[key].append(h2Result)
        if h2Result != 'UNFINISHED' and h2Result.upper() != 'PASS' and h2Result.upper() != 'TRUE':
            isErrorH2 = True
            PublicFuc.AppendErrDiskInfo('InjectDebugError_Err', key, h2Result, dic['MMS_PC'], dic['file_path'])

        newDic[key].extend(smartInfoH2)

        # Conclusion
        if isErrorH2:
            newDic[key].append('FAIL')
        else:
            newDic[key].append('PASS')

    return newDic

def ReadCsvData(curpath, pattern, dataDic):
    csvHeaderColumn = []
    fileIdx = 1
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue

        file_data = []
        mdTime = os.path.getmtime(file)  # file[pos+1:];#
        with open(file, encoding='gb18030') as csvfile:
            csv_reader = csv.reader(_.replace('\x00', '') for _ in csvfile)  # 使用csv.reader读取csvfile中的文件csv.reader(csvfile)
            try:
                birth_header =  next(csv_reader)
            except:
                continue
            if csvHeaderColumn == []:
                csvHeaderColumn = birth_header
            for row in csv_reader:
                if row == [] or len(row) != len(csvHeaderColumn):
                    continue

                row.append(file)  # 把文件路径信息添加，用于输出错误信息路径
                row.append(mdTime)
                file_data.append(row)
        dataDic[fileIdx] = file_data
        fileIdx += 1
