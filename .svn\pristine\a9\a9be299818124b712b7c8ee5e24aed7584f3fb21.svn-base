#include "NVMeDeviceIo.h"
#include "define.h"
#include <assert.h>
#include <define.h>
#include <ssd\ssd.h>
#include "pcie\pcie.h"

using namespace PCIeIO;



CNVMeDeviceIo::CNVMeDeviceIo(void)
{
}

CNVMeDeviceIo::~CNVMeDeviceIo(void)
{
}

Bool CNVMeDeviceIo::OpenDevice(U8 _phyDiskNo, bool bShare/* = True*/, Bool bOvelapped/* = False*/)
{
	if (INVALID_HANDLE_VALUE != m_hDev)
	{
		CloseDevice();
	}

	char szIdx[4] = {0};
	_itoa_s(_phyDiskNo, szIdx, 10);
	std::string strPhyDrive = "\\\\.\\PhysicalDrive";
	strPhyDrive.append(szIdx);
	
	if (bShare)
		m_hDev = CreateFile(strPhyDrive.c_str(),GENERIC_READ|GENERIC_WRITE,FILE_SHARE_READ|FILE_SHARE_WRITE,NULL,OPEN_EXISTING,0,NULL);
	else
		m_hDev = CreateFile(strPhyDrive.c_str(),GENERIC_READ|GENERIC_WRITE,0,NULL,OPEN_EXISTING,0,NULL);

	if (INVALID_HANDLE_VALUE == m_hDev)
	{
		return False;
	}

	return True;
}

Bool CNVMeDeviceIo::GetIdentify(void *_pCMD, U32 _nCMDSize, U8 *_pBuffer, U32 _bufferSize)
{
	UNREFERENCED_PARAMETER(_pCMD || _nCMDSize);

	YSNVMe::COMMAND_SET cmdSet = {0};
	cmdSet.bOpcode = 0x06; // Identify
	cmdSet.dw10 = 1;	   // NVME_IDENTIFY_CNS_CONTROLLER
	cmdSet.dwNSID = 0;

	BYTE bBuff[4096] = {0};
	assert(_bufferSize <= sizeof(bBuff));
	if (!SendNVMeInbox(m_hDev, &cmdSet, bBuff))
	{
		return False;	
	}

	memcpy_s(_pBuffer, _bufferSize, bBuff, _bufferSize);

	return True;
}

Bool CNVMeDeviceIo::SendNVMeInbox(HANDLE hDeviceIOCTL, void *_pSrtCommandSet, BYTE *bIOBuf) const
{
	YSNVMe::COMMAND_SET srtCommandSet = *((YSNVMe::COMMAND_SET*)_pSrtCommandSet);

	BOOL boRtnStatus = FALSE;
	if (srtCommandSet.bOpcode == 0x06)
	{
		YSNVMe::TStorageQueryWithBuffer pProtocolCmd = {};

		pProtocolCmd.ProtocolSpecific.ProtocolType = YSNVMe::ProtocolTypeNvme;
		pProtocolCmd.ProtocolSpecific.DataType = YSNVMe::NVMeDataTypeIdentify;
		pProtocolCmd.ProtocolSpecific.ProtocolDataOffset = sizeof(YSNVMe::TStorageProtocolSpecificData);
		pProtocolCmd.ProtocolSpecific.ProtocolDataLength = 4096;
		pProtocolCmd.ProtocolSpecific.ProtocolDataRequestValue = srtCommandSet.dw10; /*NVME_IDENTIFY_CNS_CONTROLLER*/
		pProtocolCmd.ProtocolSpecific.ProtocolDataRequestSubValue = srtCommandSet.dwNSID;
		pProtocolCmd.Query.PropertyId = YSNVMe::StorageAdapterProtocolSpecificProperty;
		pProtocolCmd.Query.QueryType = YSNVMe::PropertyStandardQuery;
		DWORD dwReturned = 0;

		boRtnStatus = DeviceIoControl(hDeviceIOCTL, IOCTL_STORAGE_QUERY_PROPERTY,
			&pProtocolCmd, sizeof(pProtocolCmd), &pProtocolCmd, sizeof(pProtocolCmd), &dwReturned, NULL);

		memcpy_s(bIOBuf, sizeof(PCIeIO::NVME_IDENTIFY_DEVICE), pProtocolCmd.Buffer, sizeof(PCIeIO::NVME_IDENTIFY_DEVICE));
	}
	else if(srtCommandSet.bOpcode == 0x02)
	{
		YSNVMe::TStorageQueryWithBuffer pProtocolCmd;
		ZeroMemory(&pProtocolCmd, sizeof(pProtocolCmd));

		pProtocolCmd.ProtocolSpecific.ProtocolType = YSNVMe::ProtocolTypeNvme;
		pProtocolCmd.ProtocolSpecific.DataType = YSNVMe::NVMeDataTypeLogPage;
		pProtocolCmd.ProtocolSpecific.ProtocolDataOffset = sizeof(YSNVMe::TStorageProtocolSpecificData);
		pProtocolCmd.ProtocolSpecific.ProtocolDataLength = 512;
		pProtocolCmd.ProtocolSpecific.ProtocolDataRequestValue = 2;
		if(srtCommandSet.dwNSID == 0xFFFFFFFF)
		{
			srtCommandSet.dwNSID = 0;
		}
		pProtocolCmd.ProtocolSpecific.ProtocolDataRequestSubValue = srtCommandSet.dwNSID;
		pProtocolCmd.Query.PropertyId = YSNVMe::StorageAdapterProtocolSpecificProperty;
		pProtocolCmd.Query.QueryType = YSNVMe::PropertyStandardQuery;
		DWORD dwReturned = 0;

		boRtnStatus = DeviceIoControl(hDeviceIOCTL, IOCTL_STORAGE_QUERY_PROPERTY,
			&pProtocolCmd, sizeof(pProtocolCmd), &pProtocolCmd, sizeof(pProtocolCmd), &dwReturned, NULL);

		memcpy_s(bIOBuf, 512, pProtocolCmd.Buffer, 512);	
	}
	else
	{
		YSNVMe::PSTORAGE_PROTOCOL_COMMAND pProtocolCmd = NULL;
		YSNVMe::PNVME_COMMAND pCmd = NULL;
		BYTE* bTmpBuf = NULL;
		DWORD dwTmpBufSize = 0;
		DWORD dwRtnLength = 0;

		dwTmpBufSize = FIELD_OFFSET(YSNVMe::STORAGE_PROTOCOL_COMMAND, Command) + STORAGE_PROTOCOL_COMMAND_LENGTH_NVME + sizeof(YSNVMe::NVME_ERROR_INFO_LOG) + srtCommandSet.dwDataLength;
		bTmpBuf = (BYTE *)::malloc((size_t)dwTmpBufSize);
		if(bTmpBuf == NULL)
			return False;

		if(srtCommandSet.ulTimeout == 0)
			srtCommandSet.ulTimeout = 10;

		::ZeroMemory(bTmpBuf, dwTmpBufSize);

		pProtocolCmd = (YSNVMe::PSTORAGE_PROTOCOL_COMMAND)bTmpBuf;
		pProtocolCmd->Version = STORAGE_PROTOCOL_STRUCTURE_VERSION;
		pProtocolCmd->Length = sizeof(YSNVMe::STORAGE_PROTOCOL_COMMAND);
		pProtocolCmd->ProtocolType = YSNVMe::ProtocolTypeNvme;
		pProtocolCmd->Flags = STORAGE_PROTOCOL_COMMAND_FLAG_ADAPTER_REQUEST;
		pProtocolCmd->CommandLength = STORAGE_PROTOCOL_COMMAND_LENGTH_NVME;
		pProtocolCmd->ErrorInfoLength = sizeof(YSNVMe::NVME_ERROR_INFO_LOG);
		pProtocolCmd->TimeOutValue = srtCommandSet.ulTimeout;
		pProtocolCmd->ErrorInfoOffset = FIELD_OFFSET(YSNVMe::STORAGE_PROTOCOL_COMMAND, Command) + STORAGE_PROTOCOL_COMMAND_LENGTH_NVME;
		pProtocolCmd->CommandSpecific = STORAGE_PROTOCOL_SPECIFIC_NVME_ADMIN_COMMAND;

		if(srtCommandSet.bProtocolField == 2)
		{
			pProtocolCmd->DataFromDeviceTransferLength = srtCommandSet.dwDataLength;
			pProtocolCmd->DataFromDeviceBufferOffset = pProtocolCmd->ErrorInfoOffset + pProtocolCmd->ErrorInfoLength;
		}
		else if(srtCommandSet.bProtocolField == 3)
		{
			pProtocolCmd->DataToDeviceTransferLength = srtCommandSet.dwDataLength;
			pProtocolCmd->DataToDeviceBufferOffset = pProtocolCmd->ErrorInfoOffset + pProtocolCmd->ErrorInfoLength;

			::CopyMemory(((PCHAR)pProtocolCmd + pProtocolCmd->DataToDeviceBufferOffset), bIOBuf, srtCommandSet.dwDataLength);
		}

		pCmd = (YSNVMe::PNVME_COMMAND)pProtocolCmd->Command;
		pCmd->CDW0.OPC = srtCommandSet.bOpcode;
		pCmd->NSID = srtCommandSet.dwNSID;
		pCmd->u.GENERAL.CDW10 = srtCommandSet.dw10;
		pCmd->u.GENERAL.CDW11 = srtCommandSet.dw11;
		pCmd->u.GENERAL.CDW12 = srtCommandSet.dw12;
		pCmd->u.GENERAL.CDW13 = srtCommandSet.dw13;
		pCmd->u.GENERAL.CDW14 = srtCommandSet.dw14;
		pCmd->u.GENERAL.CDW15 = srtCommandSet.dw15;

		boRtnStatus = ::DeviceIoControl(hDeviceIOCTL, IOCTL_STORAGE_PROTOCOL_COMMAND, bTmpBuf, dwTmpBufSize, bTmpBuf, dwTmpBufSize, &dwRtnLength, NULL);
		if(boRtnStatus && (srtCommandSet.bProtocolField == 2))
			::CopyMemory(bIOBuf, ((PCHAR)pProtocolCmd + pProtocolCmd->DataToDeviceBufferOffset) + 0xD0, srtCommandSet.dwDataLength);

		::free(bTmpBuf);
	}

	return (Bool)boRtnStatus;
}

Bool CNVMeDeviceIo::DeviceIO(void *_pCMD, U32 _nCMDSize, U8 *, U32)
{
	PCIeIO::ADMIN_IO_CMD *pCMD = (PCIeIO::ADMIN_IO_CMD *)_pCMD;
	YSNVMe::COMMAND_SET srtCommandSet = {0};
	srtCommandSet.bOpcode = pCMD->opCode;
	srtCommandSet.dw10 = pCMD->cdw10;
	srtCommandSet.dw11 = pCMD->cdw11;
	srtCommandSet.dw12 = pCMD->cdw12;
	srtCommandSet.dw13 = pCMD->cdw13;
	srtCommandSet.dw14 = pCMD->cdw14;
	srtCommandSet.dw15 = pCMD->cdw15;
	srtCommandSet.dwDataLength = pCMD->len;
	srtCommandSet.bProtocolField = pCMD->flags== 1? 3: pCMD->flags;
	srtCommandSet.ulTimeout = max(pCMD->timeout, 10);
	srtCommandSet.dwNSID =pCMD->nsid;

	return SendNVMeInbox(m_hDev, (void*)&srtCommandSet, (PBYTE)pCMD->addr);
}

void CNVMeDeviceIo::CloseDevice()
{
    if (m_hDev != INVALID_HANDLE_VALUE)
    {
        CloseHandle(m_hDev);
        m_hDev = INVALID_HANDLE_VALUE;
    }
}
