
import PublicFuc
import configparser
import csv
import os,re
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors,Font
from datetime import datetime,timedelta

g_bParseMpColumnPos = True #是否解析列位置信息
g_listMPColumnName = ['Flash编号','LogCap','MP_Result','MP_Time','MPStatus','FlashID']
g_invalid_pos = -1


def Run(curpath, workBook, alignment):
    ws = workBook['详细测试结果']
    ws.alignment = alignment
    ProDetailSheet(curpath, ws)

    ws_conclusion = workBook['测试小结']
    ws_conclusion.alignment = alignment
    ProConclusionSheet(curpath, ws_conclusion)

mp_column_name_map = {} #量产工具低格高格测试结果的列映射关系
h2_column_name_map = {} #DUT的h2测试结果列映射关系
combined_column_name_map = {} #合并后的数据列映射关系
rawLowFormatDataList = [] #原始低格量产数据
rawHighFormatDataList = [] #原始高格量产数据
rawH2FirstDataList = [] #第一次H2原始数据
rawH2SecondDataList = [] #第二次H2原始数据
rawH2ThirdDataList = [] #第三次H2原始数据

LowFormatDataDic = {} #经过处理的低格数据字典，
HighFormatDataDic = {} #经过处理的高格量产数据
H2FirstDataDic = {} #第一次H2经过处理的数据
H2SecondDataDic = {} #第二次H2经过处理的数据
H2ThirdDataDic = {} #第三次H2经过处理的数据

combinedDataDic = {} #整个plan1所需的数据合集
conclusionDicByCap = {} #按容量整合的汇总信息列表
listFailSample = [0]*13 #统计表格中不良的数量
listTotalSample = [0]*13 #统计中总数量


dataDicIndexBySample = {} #按照样本组织起来的字典
DETAIL_DATA_START_ROW_NO = 5
CONCLUSION_DATA_START_ROW_NO = 2
CONCLUSION_DATA_START_COLUMN_NO = 9

titleFill = PatternFill('solid')
contentFill = PatternFill('solid')

def ParserMPColumnName(line):
    for i in g_listMPColumnName:
        pos = g_invalid_pos
        try:
            pos = line.index(i)
        except:
            pos = g_invalid_pos
        if pos == g_invalid_pos:
            return False #只要有一个名称没有找到则直接返回false
        mp_column_name_map[i] = pos
    return True

def ProDetailSheet(curpath, worksheet):
    #pattern = '.+\\\\MpTool\\\\\D+_\d{14}.csv$'

    #初始化量产原始日志csv文件中各种信息的列号对应关系
    InitMPCsvColumnNameMap(mp_column_name_map)
    InitH2CsvColumnNameMap(h2_column_name_map)
    InitCombinedDataColumnNameMap(combined_column_name_map)

    #从原始日志中读取测试结果到数据列表中
    pattern = '.+\\\\Plan1\\\\T_GE_U2_C1\\\\低格\\\\.+.csv$' 
    ReadMPRawCsvData(curpath,pattern,rawLowFormatDataList)
    pattern = '.+\\\\Plan1\\\\T_GE_U2_C8\\\\高格\\\\.+.csv$'
    ReadMPRawCsvData(curpath,pattern,rawHighFormatDataList)
    pattern = '.+\\\\Plan1\\\\T_GE_U2_C22\\\\H2-1\\\\.+.csv$'
    ReadRawCsvData(curpath,pattern,rawH2FirstDataList)
    pattern = '.+\\\\Plan1\\\\T_GE_U2_C22\\\\H2-2\\\\.+.csv$'
    ReadRawCsvData(curpath,pattern,rawH2SecondDataList)
    pattern = '.+\\\\Plan1\\\\T_GE_U2_C22\\\\H2-3\\\\.+.csv$'
    ReadRawCsvData(curpath,pattern,rawH2ThirdDataList)

    #将数据列表转换为数字字典，且只取需要的数据列
    InitMpRawDataMap(LowFormatDataDic,rawLowFormatDataList)
    InitMpRawDataMap(HighFormatDataDic,rawHighFormatDataList)
    InitH2RawDataMap(H2FirstDataDic,rawH2FirstDataList)
    InitH2RawDataMap(H2SecondDataDic,rawH2SecondDataList)
    InitH2RawDataMap(H2ThirdDataDic,rawH2ThirdDataList)

    #合并出新的字典
    #第一步生以sampleno为key的空值字典
    InitEmptyDicIndexBySampleNo(LowFormatDataDic)
    InitEmptyDicIndexBySampleNo(HighFormatDataDic)
    InitEmptyDicIndexBySampleNo(H2FirstDataDic)
    InitEmptyDicIndexBySampleNo(H2SecondDataDic)
    InitEmptyDicIndexBySampleNo(H2ThirdDataDic)
    #第二步去各个原始字典中插入对应的值填充空字典的value内容得到合并的新字典
    InitCombinedDataDic()
    #写数据到详细测试结果sheet
    WriteDetailData2WorkSheet(worksheet) 
    PublicFuc.WriteReportTime(worksheet,'AD',1)
    PublicFuc.WriteReportOperator(worksheet,'AG',1)

def ProConclusionSheet(curpath, worksheet):
    #得到一个按cap统计的空表格数据
    GetEmptyConclusionDicIndexByCap()
    #初始化表格中需要的各种模式的样本统计数据
    InitConclusionDicIndexByCap()
    #计算各种百分比
    CalcRatioOfConclusionDic()

    #写数据
    WriteConclusionData2WorkSheet(worksheet)
    PublicFuc.WriteReportTime(worksheet,'S',1)
    PublicFuc.WriteReportOperator(worksheet,'X',1)
    

#计算表格数据中所有百分比
def CalcRatioOfConclusionDic():
    for cap in conclusionDicByCap:
        for mode in conclusionDicByCap[cap]:
            modeColumData = conclusionDicByCap[cap][mode]
            tmpRate = ''
            totalLowMpCnt = listTotalSample[0]
            if totalLowMpCnt != 0:
                tmpRate = "%.2f%%" % (float(modeColumData[0])*100/totalLowMpCnt)
                modeColumData[1] = tmpRate

            tmpRate = ''
            if modeColumData[2] != 0:
                tmpRate = "%.2f%%" % (float(modeColumData[3])*100/modeColumData[2])
                modeColumData[4] = tmpRate

            tmpRate = ''
            if modeColumData[5] != 0:
                tmpRate = "%.2f%%" % (float(modeColumData[6])*100/modeColumData[5])
                modeColumData[7] = tmpRate      

            tmpRate = ''
            totalHighMpCnt = listTotalSample[8]
            if totalHighMpCnt != 0:
                tmpRate = "%.2f%%" % (float(modeColumData[8])*100/totalHighMpCnt)
                modeColumData[9] = tmpRate

            tmpRate = ''
            if modeColumData[10] != 0:
                tmpRate = "%.2f%%" % (float(modeColumData[11])*100/modeColumData[10])
                modeColumData[12] = tmpRate
 
    tmpRate = ''
    totalLowMpCnt = listTotalSample[0]
    if totalLowMpCnt != 0:
        tmpRate = "%.2f%%" % (float(listFailSample[0])*100/totalLowMpCnt)
        listFailSample[1] = tmpRate

        tmpRate = "%.2f%%" % (float(totalLowMpCnt - listFailSample[0])*100/totalLowMpCnt)
        listTotalSample[1] = tmpRate

    tmpRate = ''
    if listTotalSample[2] != 0:
        tmpRate = "%.2f%%" % (float(listTotalSample[3])*100/listTotalSample[2])
        listTotalSample[4] = tmpRate

        tmpRate = "%.2f%%" % (float(listFailSample[3])*100/listTotalSample[2])
        listFailSample[4] = tmpRate

    tmpRate = ''
    if listTotalSample[5] != 0:
        tmpRate = "%.2f%%" % (float(listTotalSample[6])*100/listTotalSample[5])
        listTotalSample[7] = tmpRate

        tmpRate = "%.2f%%" % (float(listFailSample[6])*100/listTotalSample[5])
        listFailSample[7] = tmpRate

    tmpRate = ''
    totalHighMpCnt = listTotalSample[8]
    if totalHighMpCnt != 0:
        tmpRate = "%.2f%%" % (float(listFailSample[8])*100/totalHighMpCnt)
        listFailSample[9] = tmpRate

        tmpRate = "%.2f%%" % (float(totalLowMpCnt - listFailSample[8])*100/totalHighMpCnt)
        listTotalSample[9] = tmpRate

    tmpRate = ''
    if listTotalSample[10] != 0:
        tmpRate = "%.2f%%" % (float(listTotalSample[11])*100/listTotalSample[10])
        listTotalSample[12] = tmpRate

        tmpRate = "%.2f%%" % (float(listFailSample[11])*100/listTotalSample[10])
        listFailSample[12] = tmpRate

#初始化汇总表格的所有数据 
def InitConclusionDicIndexByCap():
    for key in combinedDataDic:
         row = combinedDataDic[key]
         if row[combined_column_name_map['low_format_result']] != '':
                listTotalSample[0] += 1 #说明量产过，这里总量产数量加1
                if row[combined_column_name_map['low_format_result']].upper() != 'PASS':
                    listFailSample[0] += 1
         if row[combined_column_name_map['h2_1_result']] != '':
                listTotalSample[2] += 1 #说明测试过，这里总测试数量加1
                if row[combined_column_name_map['h2_1_result']].upper() != "PASS":
                    listFailSample[3] += 1
                else:
                    listTotalSample[3] += 1 #说明PASS了

         if row[combined_column_name_map['h2_2_result']] != '':
                listTotalSample[5] += 1 #说明测试过，这里总量产数量加1
                if row[combined_column_name_map['h2_2_result']].upper() != "PASS":
                    listFailSample[6] += 1
                else:
                    listTotalSample[6] += 1 #说明测试过，这里总量产数量加1

         if row[combined_column_name_map['high_format_result']] != '':
                listTotalSample[8] += 1 #说明量产过，这里总量产数量加1
                if row[combined_column_name_map['high_format_result']].upper() != 'PASS':
                    listFailSample[8] += 1

         if row[combined_column_name_map['h2_3_result']] != '':
                listTotalSample[10] += 1 #说明测试过，这里总量产数量加1
                if row[combined_column_name_map['h2_3_result']].upper() != "PASS":
                    listFailSample[11] += 1
                else:
                    listTotalSample[11] += 1 #说明测试过，这里总量产数量加1

    for cap in conclusionDicByCap:
        for sampleKey in combinedDataDic:
            row = combinedDataDic[sampleKey]
            bNsMode = IsNSMode(row[combined_column_name_map['low_format_mode']])         
            if row[combined_column_name_map['low_format_cap']] == cap:
                if row[combined_column_name_map['low_format_result']].upper() == "PASS":
                    if bNsMode:
                        conclusionDicByCap[cap]['NS'][0] += 1
                    else:
                        conclusionDicByCap[cap]['HS'][0] += 1

            if row[combined_column_name_map['low_format_cap']] == cap:
                if row[combined_column_name_map['h2_1_result']] != '':
                    #说明测试过
                    if bNsMode:
                        conclusionDicByCap[cap]['NS'][2] += 1
                    else:
                        conclusionDicByCap[cap]['HS'][2] += 1

                    if row[combined_column_name_map['h2_1_result']].upper() == "PASS":
                        #listTotalSample[3] += 1 #说明测试过，这里总量产数量加1
                        if bNsMode:
                            conclusionDicByCap[cap]['NS'][3] += 1
                        else:
                            conclusionDicByCap[cap]['HS'][3] += 1

            if row[combined_column_name_map['low_format_cap']] == cap:
                if row[combined_column_name_map['h2_2_result']] != '':
                    #说明测试过
                    if bNsMode:
                        conclusionDicByCap[cap]['NS'][5] += 1
                    else:
                        conclusionDicByCap[cap]['HS'][5] += 1

                    if row[combined_column_name_map['h2_2_result']].upper() == "PASS":
                        #listTotalSample[6] += 1 #说明测试过，这里总量产数量加1
                        if bNsMode:
                            conclusionDicByCap[cap]['NS'][6] += 1
                        else:
                            conclusionDicByCap[cap]['HS'][6] += 1
                        
            bNsMode = IsNSMode(row[combined_column_name_map['high_format_mode']]) #高格后面的模式要以高格为准
            if row[combined_column_name_map['high_format_cap']] == cap:
                if row[combined_column_name_map['high_format_result']].upper() == "PASS":
                    if bNsMode:
                        conclusionDicByCap[cap]['NS'][8] += 1
                    else:
                        conclusionDicByCap[cap]['HS'][8] += 1

            if row[combined_column_name_map['high_format_cap']] == cap:
                if row[combined_column_name_map['h2_3_result']] != '':
                    #说明测试过
                    if bNsMode:
                        conclusionDicByCap[cap]['NS'][10] += 1
                    else:
                        conclusionDicByCap[cap]['HS'][10] += 1

                    if row[combined_column_name_map['h2_3_result']].upper() == "PASS":
                        #listTotalSample[11] += 1 #说明测试过，这里总量产数量加1
                        if bNsMode:
                            conclusionDicByCap[cap]['NS'][11] += 1
                        else:
                            conclusionDicByCap[cap]['HS'][11] += 1     


#得到空的以容量为索引的记录
def GetEmptyConclusionDicIndexByCap():
    for key in combinedDataDic:
        tmprow = combinedDataDic.get(key,[])
        if tmprow == []:
            continue

        tmpCap = tmprow[combined_column_name_map['low_format_cap']]
        if tmpCap != '' and tmpCap != '0' and tmpCap != 0:
            conclusionDicByCap[tmpCap] = {'HS':[0]*13,'NS':[0]*13}
        
        tmpCap = tmprow[combined_column_name_map['high_format_cap']]
        if tmpCap != '' and tmpCap != '0' and tmpCap != 0:
            conclusionDicByCap[tmpCap] = {'HS':[0]*13,'NS':[0]*13}


def IsNSMode(strMode):
    strMode.rstrip()
    tmpStr = strMode[len(strMode)-2:]
    tmpStr.upper()
    if tmpStr == "NS":
        return True
    else:
        return False
        

#获取量产数据的字典
def InitMpRawDataMap(mpDic,rawDataList):
    mpDic.clear()
    for row in rawDataList:
        tmpRow = ['']*(len(mp_column_name_map)-1) #减1是不再记录sampleNo，因为key已经记录   
        tmpRow[0] = row[mp_column_name_map['FlashID']]
        tmpRow[1] = row[mp_column_name_map['LogCap']]
        tmpRow[2] = row[mp_column_name_map['MP_Result']]
        tmpRow[3] = row[mp_column_name_map['MP_Time']]
        tmpRow[4] = row[mp_column_name_map['MPStatus']]
        mpDic[row[mp_column_name_map['Flash编号']]] = tmpRow
    
#获取H2数据的字典
def InitH2RawDataMap(h2Dic,rawDataList):
    h2Dic.clear()
    for row in rawDataList:
        tmpRow = ['']*(len(h2_column_name_map)-1) #减1是不再记录sampleNo，因为key已经记录   
        tmpRow[0] = row[h2_column_name_map['(H2)写速度']]
        tmpRow[1] = row[h2_column_name_map['(H2)读速度']]
        tmpRow[2] = row[h2_column_name_map['(H2)错误']]
        tmpRow[3] = row[h2_column_name_map['first_err_offset']]
        h2Dic[row[h2_column_name_map['Flash编号']]] = tmpRow

#初始化空的
def InitEmptyDicIndexBySampleNo(rawDataMap):
    for key in rawDataMap:
        combinedDataDic[key] = [] #一开始是空的，主要是得到所有的键值

def InitMPCsvColumnNameMap(columnNameMap):
    columnNameMap.clear()
    columnNameMap['Flash编号'] = -1 #22
    columnNameMap['LogCap'] = -1 #4  #开卡容量
    columnNameMap['MP_Result'] = -1 #5 #通过Pass 否则错误码
    columnNameMap['MP_Time'] = -1 #6 #量产时间
    columnNameMap['MPStatus'] = -1 #12 #模式P-NS,P-HS等
    columnNameMap['FlashID'] = -1 #15
    
def InitH2CsvColumnNameMap(columnNameMap):
    columnNameMap.clear()
    columnNameMap['Flash编号'] = 3
    columnNameMap['(H2)写速度'] = 14
    columnNameMap['(H2)读速度'] = 15
    columnNameMap['(H2)错误'] = 17 #PASS,or 错误码
    columnNameMap['first_err_offset'] = 18

def InitCombinedDataColumnNameMap(columnNameMap):
    columnNameMap.clear()
    columnNameMap['flashid'] = 0
    columnNameMap['low_format_cap'] = 1
    columnNameMap['low_format_result'] = 2 #PASS,or 错误码
    columnNameMap['low_format_time'] = 3 
    columnNameMap['low_format_mode'] = 4
    columnNameMap['h2_1_write_speed'] = 5
    columnNameMap['h2_1_read_speed'] = 6
    columnNameMap['h2_1_result'] = 7
    columnNameMap['h2_1_first_err_offset'] = 8
    columnNameMap['h2_2_write_speed'] = 9
    columnNameMap['h2_2_read_speed'] = 10
    columnNameMap['h2_2_result'] = 11
    columnNameMap['h2_2_first_err_offset'] = 12
    columnNameMap['high_format_cap'] = 13
    columnNameMap['high_format_result'] = 14 #PASS,or 错误码
    columnNameMap['high_format_time'] = 15 
    columnNameMap['high_format_mode'] = 16
    columnNameMap['h2_3_write_speed'] = 17
    columnNameMap['h2_3_read_speed'] = 18
    columnNameMap['h2_3_result'] = 19
    columnNameMap['h2_3_first_err_offset'] = 20

#初始化合并后的全集
def InitCombinedDataDic():
    for sampleNo in combinedDataDic:
        tmpList = LowFormatDataDic.get(sampleNo,[])
        if tmpList == []:
            combinedDataDic[sampleNo] += ['']*(len(mp_column_name_map)-1)#减2是减去了一个样本编号留下了flashid
        else:
            combinedDataDic[sampleNo] += tmpList
        
        tmpList = H2FirstDataDic.get(sampleNo,[])
        if tmpList == []:
            combinedDataDic[sampleNo] += ['']*(len(h2_column_name_map)-1)
        else:
            combinedDataDic[sampleNo] += tmpList

        tmpList = H2SecondDataDic.get(sampleNo,[])
        if tmpList == []:
            combinedDataDic[sampleNo] += ['']*(len(h2_column_name_map)-1)
        else:
            combinedDataDic[sampleNo] += tmpList

        tmpList = HighFormatDataDic.get(sampleNo,[])
        if tmpList == []:
            combinedDataDic[sampleNo] += ['']*(len(mp_column_name_map)-2) #减2是减去了一个样本编号和一个flashid
        else:
            combinedDataDic[sampleNo] += tmpList[1:] #不使用第一个flashid内容

        tmpList = H2ThirdDataDic.get(sampleNo,[])
        if tmpList == []:
            combinedDataDic[sampleNo] += ['']*(len(h2_column_name_map)-1)
        else:
            combinedDataDic[sampleNo] += tmpList


def WriteDetailData2WorkSheet(worksheet):
    #绘制excel模板
    InitDetailReportTemplateInWorkSheet(worksheet)

    rowIdx = 0
    keyList = sorted(combinedDataDic.keys())
    for sampleNo in keyList:
        tmpRow = combinedDataDic[sampleNo]
        worksheet['%s%d'%(get_column_letter(1), DETAIL_DATA_START_ROW_NO+rowIdx)] = sampleNo
        for col in range(len(tmpRow)):
            worksheet['%s%d'%(get_column_letter(3+col), DETAIL_DATA_START_ROW_NO+rowIdx)] = tmpRow[col]
            if (col == combined_column_name_map['low_format_result'] or col == combined_column_name_map['high_format_result']) and tmpRow[col].upper() != 'PASS' and tmpRow[col] != '':
                worksheet['%s%d'%(get_column_letter(3+col), DETAIL_DATA_START_ROW_NO+rowIdx)].fill = PublicFuc.warnFill
            if (col == combined_column_name_map['h2_1_result'] or col == combined_column_name_map['h2_2_result'] or col == combined_column_name_map['h2_3_result']) and tmpRow[col].upper() != 'PASS' and tmpRow[col] != '':
                worksheet['%s%d'%(get_column_letter(3+col), DETAIL_DATA_START_ROW_NO+rowIdx)].fill = PublicFuc.warnFill
        rowIdx += 1

def InitDetailReportTemplateInWorkSheet(worksheet):
    #titleFont=Font('宋体',size=11,color=colors.BLACK,bold=True,italic=False)
    cellfont=Font('微软雅黑',size=10,color=colors.BLACK,bold=False,italic=False)
    for rowIdx in range(len(combinedDataDic)):
        for col in range(23):
            worksheet['%s%d'%(get_column_letter(col+1), DETAIL_DATA_START_ROW_NO+rowIdx)].alignment = PublicFuc.alignment
            worksheet['%s%d'%(get_column_letter(col+1), DETAIL_DATA_START_ROW_NO+rowIdx)].border = PublicFuc.my_border('thin', 'thin', 'thin', 'thin')
            worksheet['%s%d'%(get_column_letter(col+1), DETAIL_DATA_START_ROW_NO+rowIdx)].font = cellfont

def WriteConclusionData2WorkSheet(worksheet):
    #绘制excel模板
    InitConlusionReportTemplateInWorkSheet(worksheet)

    #capIdx = 0
    keyList = sorted(conclusionDicByCap.keys())
    for capIdx,cap in enumerate(keyList):
        childCapDic = conclusionDicByCap[cap]
        tmpDataList = childCapDic['HS']
        for i,val in enumerate(tmpDataList):
            worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + capIdx*2), CONCLUSION_DATA_START_ROW_NO+2 + i)] = val

        tmpDataList = childCapDic['NS']
        for i,val in enumerate(tmpDataList):
            worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + capIdx*2 + 1), CONCLUSION_DATA_START_ROW_NO+2 + i)] = val

    #写不良率列
    for idx, val in enumerate(listFailSample):
        worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + len(conclusionDicByCap)*2), CONCLUSION_DATA_START_ROW_NO+2 + idx)] = val

    #写总体情况
    for idx, val in enumerate(listTotalSample):
        worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + len(conclusionDicByCap)*2 + 1), CONCLUSION_DATA_START_ROW_NO+2 + idx)] = val


#按照实际模式数量生成汇总表格
def InitConlusionReportTemplateInWorkSheet(worksheet):
    CapColumnCnt = len(conclusionDicByCap)
    for rowIdx in range(CONCLUSION_DATA_START_ROW_NO,CONCLUSION_DATA_START_ROW_NO+15):
        for col in range(CONCLUSION_DATA_START_COLUMN_NO,CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2 + 3):
            worksheet['%s%d'%(get_column_letter(col), rowIdx)].alignment = PublicFuc.alignment
            worksheet['%s%d'%(get_column_letter(col), rowIdx)].border = PublicFuc.my_border('thin', 'thin', 'thin', 'thin')

    #填充表格背景填充
    titleCell=worksheet['%s%d'%(get_column_letter(8), 2)]
    tpfill = titleCell.fill
    titleFill.bgColor = tpfill.bgColor
    titleFill.fgColor = tpfill.fgColor
    titleFont= Font('宋体',size=11,color=colors.BLACK,bold=True,italic=False)

    str = '%s%d'%(get_column_letter(8), 4)
    
    contentFont= Font('宋体',size=11,color=colors.BLACK,bold=False,italic=False)

    #初始化标题的字体和背景
    for columnIdx in range(CONCLUSION_DATA_START_COLUMN_NO,CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2 + 3):
        for rowIdx in range(CONCLUSION_DATA_START_ROW_NO,CONCLUSION_DATA_START_ROW_NO+2):
            worksheet['%s%d'%(get_column_letter(columnIdx), rowIdx)].fill = titleFill
            worksheet['%s%d'%(get_column_letter(columnIdx), rowIdx)].alignment = PublicFuc.alignment
            #worksheet['%s%d'%(get_column_letter(11+columnIdx), ERR_CODE_TITLE_START_ROW_NO+rowIdx)].border = PublicFuc.my_border('thin', 'thin', 'thin', 'thin')
            worksheet['%s%d'%(get_column_letter(columnIdx), rowIdx)].font = titleFont

    #初始化内容字体和背景
    for rowIdx in range(CONCLUSION_DATA_START_ROW_NO+2,CONCLUSION_DATA_START_ROW_NO+15):
        contentCell=worksheet['%s%d'%(get_column_letter(8), rowIdx)]
        tmpfill = contentCell.fill
        contentFill.bgColor = tmpfill.bgColor
        contentFill.fgColor = tmpfill.fgColor
        for columnIdx in range(CONCLUSION_DATA_START_COLUMN_NO,CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2 + 2):                
            worksheet['%s%d'%(get_column_letter(columnIdx), rowIdx)].fill = contentFill          
            worksheet['%s%d'%(get_column_letter(columnIdx), rowIdx)].alignment = PublicFuc.alignment

    #填充标题
    keyList = sorted(conclusionDicByCap.keys())
    i = 0
    for cap in keyList:
        tmpResult = conclusionDicByCap[cap]
        str = '%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+i*2), CONCLUSION_DATA_START_ROW_NO)
        worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+i*2), CONCLUSION_DATA_START_ROW_NO)] = cap
        worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+i*2), CONCLUSION_DATA_START_ROW_NO+1)] = 'HS'
        worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+i*2+1), CONCLUSION_DATA_START_ROW_NO+1)] = 'NS'
        i += 1
     
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+ CapColumnCnt*2), CONCLUSION_DATA_START_ROW_NO)] = '测试不良'
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+ CapColumnCnt*2+1), CONCLUSION_DATA_START_ROW_NO)] = '测试总数/总良率'
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+ CapColumnCnt*2+2), CONCLUSION_DATA_START_ROW_NO)] = '备注'

    #合并单元格
    i = 0
    for cap in keyList:
        worksheet.merge_cells(start_row=CONCLUSION_DATA_START_ROW_NO, start_column=CONCLUSION_DATA_START_COLUMN_NO+i*2, end_row=CONCLUSION_DATA_START_ROW_NO, end_column=CONCLUSION_DATA_START_COLUMN_NO+i*2+1)
        i += 1

    worksheet.merge_cells(start_row=CONCLUSION_DATA_START_ROW_NO, start_column=CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2, end_row=CONCLUSION_DATA_START_ROW_NO+1, end_column=CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2)

    worksheet.merge_cells(start_row=CONCLUSION_DATA_START_ROW_NO, start_column=CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2+1, end_row=CONCLUSION_DATA_START_ROW_NO+1, end_column=CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2+1)

    worksheet.merge_cells(start_row=CONCLUSION_DATA_START_ROW_NO, start_column=CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2+2, end_row=CONCLUSION_DATA_START_ROW_NO+1, end_column=CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2+2)

    worksheet.merge_cells(start_row=CONCLUSION_DATA_START_ROW_NO+2, start_column=CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2+2, end_row=CONCLUSION_DATA_START_ROW_NO+9, end_column=CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2+2)

    worksheet.merge_cells(start_row=CONCLUSION_DATA_START_ROW_NO+10, start_column=CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2+2, end_row=CONCLUSION_DATA_START_ROW_NO+14, end_column=CONCLUSION_DATA_START_COLUMN_NO+CapColumnCnt*2+2)

def ReadRawCsvData(curpath,pattern,dataDic):
    #fileIdx = 1
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除

        #file_data = []
        with open(file,mode='r', buffering=-1, encoding='cp936') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            birth_header = next(csv_reader)  # 读取第一行每一列的标题
            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中
                dataDic.append(row)

def ReadMPRawCsvData(curpath,pattern,dataDic):
    #fileIdx = 1
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除

        #file_data = []
        with open(file,mode='r', buffering=-1, encoding='cp936') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            birth_header = next(csv_reader)  # 读取第一行每一列的标题

            #解析量产工具各列位置
            global g_bParseMpColumnPos
            if g_bParseMpColumnPos:
                if ParserMPColumnName(birth_header):
                    g_bParseMpColumnPos = False

            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中
                dataDic.append(row)

#初始化制定区域边框为所有框线
def format_border(s_column, s_index, e_column , e_index):
    for row in tuple(sheet[s_column + str(s_index):e_column + str(e_index)]):
        for cell in row:
            cell.border = my_border('thin', 'thin', 'thin', 'thin')
