import PublicFuc
from datetime import datetime,timedelta
import pymssql
import time,csv,re,os
from openpyxl.styles import  Pattern<PERSON>ill,Alignment
import configparser
config = configparser.RawConfigParser()

START_LINE = 4
alignment = Alignment(horizontal='center',vertical='center')
def Run(curpath, workBook, alignment):
    ws = workBook['03-常温ReadDisturb']
    ws.alignment = alignment
    ProReadDisturb(curpath, ws)

def ProReadDisturb(curpath, ws):
    keyLst = ['test_cirlce','avg_write_speed','max_write_speed','min_write_speed','avg_read_speed','max_read_speed','min_read_speed']
    colLst = ['C','B','D','R','G','H','P','F','I','J','K','L','M','N','Q','T','U','V']

    pattern = '.+\\\\Plan602\\\\T-SS-PCIE-E02\\\\ReadDisturb_48H\\\\\\d{14}\\\\.+\\\\mms.*\.ini$'
    caseName = 'ReadDisturb_48H'
    startLine = START_LINE
    proMarsAndSmart(curpath, ws, pattern, caseName, startLine, keyLst, colLst)

    pattern = '.+\\\\Plan611\\\\T-SS-PCIE-E11\\\\ReadDisturb\\\\\\d{14}\\\\.+\\\\mms.*\.ini$'
    caseName = 'ReadDisturb'
    startLine = START_LINE
    proMarsAndSmart(curpath, ws, pattern, caseName, startLine, keyLst, colLst)

def proMarsAndSmart(curpath, worksheet, pattern, caseName, startLine, keyLst, colLst):
    caseDic = {}
    ReadMarsInfo(curpath, pattern, caseDic, caseName,0)
    #GetNewMarsDic把ini读出来的dic按照keyLst转换为模板所需数据列表
    newDic = GetNewMarsAndSmartDic(caseDic, keyLst)
    WriteData(worksheet, startLine, newDic, colLst, keyLst, 103)

def ReadMarsInfo(curpath, pattern, dataDic, caseName, recordCnt = 2):
    unitLst = ['M/s']
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        split_path = file.split("\\")
        smartpat = "\\\\".join(split_path[:-4])+'\\\\SMART\\\\\\d{14}\\\\report.ini$'
        #logging.info(file)
        if caseName not in config.sections():
            continue
        keyName = config['HWCONFIG']['MMS_FLASH']
        if keyName not in dataDic:
            #0表示样片数量不确定，需要获取全部数据
            if recordCnt == len(dataDic) and 0 != recordCnt:
                continue
            dataDic[keyName] = {}
            dataDic[keyName]['MarsTime'] = os.path.getmtime(file)
            for key in PublicFuc.muil_marsKey:
                if key not in config[caseName]:
                    dataDic[keyName][key] = ''
                    continue
                value = config[caseName][key]
                #去除单位
                for unit in unitLst:
                    if unit in value:
                        value = value[:value.find(unit)]
                        break
                dataDic[keyName][key] = value
            if 'HWCONFIG' in config.sections() and 'CAPACITY' in config['HWCONFIG']:
                dataDic[keyName]['cap'] = config['HWCONFIG']['CAPACITY']
            if 'PASS' != dataDic[keyName]['test_result']:
                filemt = time.localtime(os.stat(file).st_mtime)
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                PublicFuc.errDiskLst.append([keyName, dataDic[keyName]['flash_name'], dataDic[keyName]['test_result'], strTime])
            SmartKeyEx = PublicFuc.mars_commonSmartKey + ['06', '07']
            for key in SmartKeyEx:
                if key.lower() in config[caseName]:
                    value = config[caseName][key]
                    if key == '06' or key == '07':
                        key = key + '_1'
                    dataDic[keyName][key] = value
            if 'waf' in config[caseName]:
                dataDic[keyName]['waf_1'] = config[caseName]['waf']
            for smartfile in PublicFuc.fileLst:
                if not re.match(smartpat, smartfile):
                    continue
                config.clear()
                config.read(smartfile,encoding = 'gbk')
                SmartKey = PublicFuc.temp_commonSmartKey + ['06', '07']
                for sec in config.sections():
                    if sec not in dataDic:
                        continue
                #统计不为0的smart信息
                    for key in SmartKey:
                        if key.lower() in config[sec]:
                            value = config[sec][key]
                            if key == '06' or key == '07':
                                key = key + '_2'
                            dataDic[sec][key] = value
                    if 'waf' in config[sec]:
                        dataDic[sec]['waf_2'] = config[sec]['waf']
                break
        else:
            fileMdTime = os.path.getmtime(file)
            if dataDic[keyName]['MarsTime'] > fileMdTime:
                continue
            dataDic[keyName] = {}
            dataDic[keyName]['MarsTime'] = fileMdTime
            for key in PublicFuc.muil_marsKey:
                if key not in config[caseName]:
                    dataDic[keyName][key] = ''
                    continue
                value = config[caseName][key]
                #去除单位
                for unit in unitLst:
                    if unit in value:
                        value = value[:value.find(unit)]
                        break
                dataDic[keyName][key] = value
            if 'HWCONFIG' in config.sections() and 'CAPACITY' in config['HWCONFIG']:
                dataDic[keyName]['cap'] = config['HWCONFIG']['CAPACITY']
            if 'PASS'!= dataDic[keyName]['test_result']:
                filemt = time.localtime(os.stat(file).st_mtime)
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                PublicFuc.errDiskLst.append([keyName, dataDic[keyName]['flash_name'], dataDic[keyName]['test_result'], strTime])
            SmartKeyEx = PublicFuc.mars_commonSmartKey + ['06', '07']
            for key in SmartKeyEx:
                if key.lower() in config[caseName]:
                    value = config[caseName][key]
                    if key == '06' or key == '07':
                        key = key + '_1'
                    dataDic[keyName][key] = value
            for smartfile in PublicFuc.fileLst:
                if not re.match(smartpat, smartfile):
                    continue
                config.clear()
                config.read(smartfile,encoding = 'gbk')
                SmartKey = PublicFuc.temp_commonSmartKey + ['06', '07']
                for sec in config.sections():
                    if sec not in dataDic:
                        continue
                #统计不为0的smart信息
                    for key in SmartKey:
                        if key.lower() in config[sec]:
                            value = config[sec][key]
                            if key == '06' or key == '07':
                                key = key + '_2'
                            dataDic[sec][key] = value
                    if 'waf' in config[sec]:
                        dataDic[sec]['waf_2'] = config[sec]['waf']
                break

def GetNewMarsAndSmartDic(oldDic, keyLst):
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        dic = oldDic[key]
        newDic[key].append(dic['pc_name'])
        if dic['cap'] != '':
            newDic[key].append(int(float(dic['cap'])))
        else:
            newDic[key].append('')
        newDic[key].append(dic['test_result'])
        # 统计测试中的写读数据量
        wrdata = ''
        if '07_1' in dic:
            if dic['07_1'] != '':
                wrdata += str(round(int(dic['07_1'],16)*512*1000/1024/1024/1024)) + '/'
            else:
                wrdata += '/'
        if '06_1' in dic:
            if dic['06_1'] != '':
                wrdata += str(round(int(dic['06_1'],16)*512*1000/1024/1024/1024))
        newDic[key].append(wrdata)
        #F1、F2 1个单位为32M，需转化为G
        if '' == dic['end_time'] or '' == dic['start_time']:
            newDic[key].append('')
        else:
            endtime = datetime.strptime(dic['end_time'], '%Y-%m-%d %H:%M:%S')
            starttime = datetime.strptime(dic['start_time'], '%Y-%m-%d %H:%M:%S')
            hours = timedelta.total_seconds(endtime-starttime)//(60*60)
            newDic[key].append('%dH'%hours)
        smart = ''
        #统计测试中不为0的smart信息
        for innerKey in dic.keys():
            if innerKey.startswith('id_'):
                if '' == dic[innerKey]:
                    continue
                if 0 != int(dic[innerKey],16):
                    pos = innerKey.find('id_')
                    id = innerKey[pos+len('id_'):].upper()
                    if id in PublicFuc.commonSmartKey:
                        smart += '%s=%s,'%(id, dic[innerKey][2:].upper())
        if '' != smart:
            smart = smart[:-1]
        newDic[key].append(smart)
        for item in keyLst:
            if item in dic:
                newDic[key].append(dic[item])
            else:
                newDic[key].append('')
        # 统计测试中写放大
        if 'waf_1' in dic and dic['waf_1'] != '':
            newDic[key].append(dic['waf_1'])
        else:
            newDic[key].append('')

        # 统计测试后的写读数据量
        wrdata = ''
        if '07_2' in dic:
            if dic['07_2'] != '':
                wrdata += str(round(int(dic['07_2'],16)*512*1000/1024/1024/1024)) + '/'
            else:
                wrdata += '/'
        if '06_2' in dic:
            if dic['06_2'] != '':
                wrdata += str(round(int(dic['06_2'],16)*512*1000/1024/1024/1024))
        newDic[key].append(wrdata)
        # 统计测试后写放大
        if 'waf_2' in dic and dic['waf_2'] != '':
            newDic[key].append(dic['waf_2'])
        else:
            newDic[key].append('')
        #统计测试后不为0的smart信息
        smart = ''
        for innerKey in dic.keys():
            if innerKey.startswith('p_'):
                if '' == dic[innerKey]:
                    continue
                if 0 != int(dic[innerKey],16):
                    pos = innerKey.find('p_')
                    id = innerKey[pos+len('p_'):].upper()
                    if id in PublicFuc.commonSmartKey:
                        smart += '%s=%s,'%(id, dic[innerKey][2:].upper())
        if '' != smart:
            smart = smart[:-1]
        newDic[key].append(smart)
    return newDic

def WriteData(worksheet, startLine, dataDic, colLst, keyLst, maxLine = 1):
    curLine = startLine
    while worksheet['%s%d' % ('C', curLine)].value != '' and worksheet['%s%d' % ('C', curLine)].value != None:
        curLine += 1
        if curLine > maxLine:
            return
    for key in dataDic:
        line = dataDic[key]
        for index,col in enumerate(colLst):
            try:
                if 0 == index:
                    #第一列是编号，直接填key
                    worksheet['%s%d'%(col, curLine)] = key
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
                worksheet['%s%d'%(col, curLine)].alignment = alignment
            #合并的单元格只能写一次，需要捕获异常
            except(AttributeError):
                continue
        curLine += 1