﻿<!DOCTYPE html>
<html>
<head>
   <title>The User Interface</title>
   <meta name="generator" content="Help &amp; Manual" />
   <meta name="keywords" content="User Interface" />
   <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
   <meta http-equiv="Content-Style-Type" content="text/css" />
   <link type="text/css" href="default.css" rel="stylesheet" />
   <script type="text/javascript" src="jquery.js"></script>
   <script type="text/javascript" src="helpman_settings.js"></script>
   <script type="text/javascript" src="helpman_topicinit.js"></script>

   <script type="text/javascript">
     HMSyncTOC("index.html", "hid_ui.htm");
   </script>
   <script type="text/javascript" src="highlight.js"></script>
   <script type="text/javascript">
     $(document).ready(function(){highlight();});
   </script>
</head>
<body style="margin: 0px 0px 0px 0px; background: #FFFFFF;">


<table width="100%" border="0" cellspacing="0" cellpadding="5" bgcolor="#649CCC">
  <tr valign="middle">
    <td align="left">
      <p style="page-break-after: avoid; margin: 7px 0px 7px 0px;"><span style="font-size: 16pt; font-weight: bold; color: #ffffff;">The User Interface</span></p>

    </td>
    <td align="right">
     <span style="font-size: 9pt">
     <a href="hid_overview.htm"><img src="nav_up_blue.gif" border=0 alt="Top"></a>&nbsp;
     <a href="hid_sleepstates.htm"><img src="nav_left_blue.gif" border=0 alt="Previous"></a>&nbsp;
     <a href="hid_config.htm"><img src="nav_right_blue.gif" border=0 alt="Next"></a>
     </span>
    </td>
  </tr>
</table>


<!-- Placeholder for topic body. -->
<table width="100%" border="0" cellspacing="0" cellpadding="5"><tr valign="top"><td align="left">
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">Suspend Type</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Specifies the type of sleep action, which will be carried out when the user hits the ‘Sleep Now’ button.</span></p>
<p style="margin: 16px 0px 4px 0px;"><span style="color: #010100;">S1, S2, S3, S4</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Put the system to sleep in the specified state for the length of time in the Sleep Duration box. Note that for the state S4, it is necessary to ensure that the ‘Enable hibernate support’ checkbox is checked in Control Panel &gt; Power Options &gt; Hibernate.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Test all supported states</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Runs a series of sleep actions; one for each state supported on the system. (These can be seen in the ‘Misc Info’ section). Each Sleeper will put the system into each sleep state for the length of time specified in the ‘Test Duration’ field and will wait for a length of time as specified in ‘Cycle Interval Duration’ between each subsequent sleep state. </span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Enable Hybrid Sleep</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Check this options to turn on hybrid sleep (when supported).</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Example:</span><br />
<span style="color: #010100;">If this option is selected on a system, which supports states S1, S3 and S4, with a ‘Test Duration’ of 60 seconds and a ‘Cycle Interval Duration’ of 30 seconds, then you would expect to see the following behaviour.</span><br />
<span style="color: #010100;">S1 for 60 seconds,</span><br />
<span style="color: #010100;">Wait for a 30 second interval</span><br />
<span style="color: #010100;">S3 for 60 seconds,</span><br />
<span style="color: #010100;">Wait for a 30 second interval</span><br />
<span style="color: #010100;">S4 for 60 seconds</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">Sleep System Information</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Contains some miscellaneous information about the systems supported sleep states and whether ‘Full Wake’ is supported or not. Systems without Full Wake support may experience some problems when waking automatically from some sleep states.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">Configuration</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Opens the configuration dialog for Sleeper.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">Pause</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Pause the countdown to the next sleep. Clicking this button again will un-pause the countdown.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">Sleep Now</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Stars the currently selected sleep action.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">About</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Displays the About window containing some version and contact details.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">Help</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Launches this help.</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="font-style: italic; font-weight: bold; color: #010100;">Exit</span></p>
<p style="margin: 7px 0px 7px 0px;"><span style="color: #010100;">Quits Sleeper.</span></p>

</td></tr></table>

</body>
</html>
