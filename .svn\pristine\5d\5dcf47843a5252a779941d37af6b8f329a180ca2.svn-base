

import PublicFuc
import configparser
import csv
import os,re
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors,Font
from datetime import datetime,timedelta

def Run(curpath, workBook, alignment):
    ws = workBook['MP_H2']
    ws.alignment = alignment
    ProMPTool(curpath, ws)
    PublicFuc.WriteReportTime(ws,'P',1)
    PublicFuc.WriteReportOperator(ws,'T',1)
    
dataDicIndexByFile = {} #按照文件组织起来的字典
listAllSampleResultData = [] #所有样本量产原始数据
mp_column_name_map = {} #量产结果列映射
h2_column_name_map = {} #H2结果列映射
dataDicIndexBySample = {} #按照样本组织起来的字典
g_usb_version = 'USB3' 
data_dir = 'MP_H2_SD3.0'
START_ROW = 4

titleFill = PatternFill('solid')

def ProMPTool(curpath, worksheet):
    global g_usb_version
    global data_dir
    g_usb_version = GetUsbVersion()
    if g_usb_version == 'USB2':
        data_dir = 'MP_H2_SD2.0'
    #MP数据收集
    pattern = '.+\\\\'+data_dir+'\\\\\D+_\d{14}.csv$'
    #初始化原始日志csv文件中各种信息的列号对应关系
    InitMPCsvColumnNameMap(mp_column_name_map)
    #从原始日志中读取测试结果到字典中，字典的key是简单的递增顺序数，也可代表文件的序号，内容为量产工具每次启动测试的一次结果。
    ReadCsvData(curpath,pattern,dataDicIndexByFile)
    #合并所有的测试数据到一个list中
    MergeAllSampleData(dataDicIndexByFile,listAllSampleResultData)
    InitFillMpDataToDic(listAllSampleResultData)

    #H2数据收集
    pattern = '.+\\\\'+data_dir+'\\\\.+\\\\Plan22\\\\T_GE_SD_C7\\\\H2_1\\\\\D+_\d{14}.csv$'
    #初始化原始日志csv文件中各种信息的列号对应关系
    InitH2CsvColumnNameMap(h2_column_name_map)
    #从原始日志中读取测试结果到字典中，字典的key是简单的递增顺序数，也可代表文件的序号，内容为量产工具每次启动测试的一次结果。
    dataDicIndexByFile.clear()
    ReadCsvData(curpath,pattern,dataDicIndexByFile)
    #合并所有的测试数据到一个list中
    listAllSampleResultData.clear()
    MergeAllSampleData(dataDicIndexByFile,listAllSampleResultData)
    InitFillH2DataToDic(listAllSampleResultData)

    #直接生成详细数据，填充详细数据
    startLine = START_ROW #详细数据起始行

    InitReportTemplateInWorkSheet(worksheet)
    
    WriteDetailResult2WorkSheet(worksheet,startLine,dataDicIndexBySample)


 #按照实际数据条数生成表格样式
def InitReportTemplateInWorkSheet(worksheet):
    nRowCnt = len(dataDicIndexBySample)
 
    #cellfont=Font('Times New Roman',size=10,color=colors.BLACK,bold=False,italic=False)
    for rowIdx in range(nRowCnt):
       for col in range(1,13+1):
           worksheet['%s%d'%(get_column_letter(col), START_ROW+rowIdx)].alignment = PublicFuc.alignment
           worksheet['%s%d'%(get_column_letter(col), START_ROW+rowIdx)].border = PublicFuc.my_border('thin', 'thin', 'thin', 'thin')
           #worksheet['%s%d'%(get_column_letter(col), rowIdx)].font = cellfont
       worksheet['%s%d'%(get_column_letter(1), START_ROW+rowIdx)] = rowIdx+1

       

 #初始化MP量产数据
def InitFillMpDataToDic(listSampleResultData):
    for line in listSampleResultData:
        sampleNo = line[mp_column_name_map['物料编号']]
        if sampleNo not in dataDicIndexBySample:
            dataDicIndexBySample[sampleNo] = {}
        if 'MP' in dataDicIndexBySample[sampleNo]:
            if dataDicIndexBySample[sampleNo]['MP'] != None and len(dataDicIndexBySample[sampleNo]['MP']) > 0:
                if dataDicIndexBySample[sampleNo]['MP'][-1] < line[-1]:
                    dataDicIndexBySample[sampleNo]['MP'] = line #更新时间的信息来了，才需要覆盖
        else:
            dataDicIndexBySample[sampleNo]['MP'] = line

 #初始化H2量产数据
def InitFillH2DataToDic(listSampleResultData):
    for line in listSampleResultData:
        sampleNo = line[h2_column_name_map['物料编号']]
        if sampleNo not in dataDicIndexBySample:
            dataDicIndexBySample[sampleNo] = {}
        if 'H2' in dataDicIndexBySample[sampleNo]:
            if dataDicIndexBySample[sampleNo]['H2'] != None and len(dataDicIndexBySample[sampleNo]['H2']) > 0:
                if dataDicIndexBySample[sampleNo]['H2'][10].strip() == "":
                    #如果原来取的无效数据，则需要覆盖，不比较任何时间
                    dataDicIndexBySample[sampleNo]['H2'] = line
                else:
                    if dataDicIndexBySample[sampleNo]['H2'][-1] < line[-1]:#时间比较
                        if line[10].strip() != "":
                            dataDicIndexBySample[sampleNo]['H2'] = line #更新时间的信息来了，才需要覆盖
        else:
            dataDicIndexBySample[sampleNo]['H2'] = line


def InitMPCsvColumnNameMap(columnNameMap):
    columnNameMap.clear()
    #columnNameMap['端口'] = 0
    columnNameMap['物料编号'] = 2
    columnNameMap['ErrCode'] = 10
    columnNameMap['容量'] = 11
    columnNameMap['坏块数'] = 12
    columnNameMap['Bin级'] = 13
    columnNameMap['量产模式'] = 14
    columnNameMap['量产耗时'] = 16


def InitH2CsvColumnNameMap(columnNameMap):
    columnNameMap.clear()
    #columnNameMap['端口'] = 0
    columnNameMap['物料编号'] = 3
    columnNameMap['写速度'] = 14
    columnNameMap['读速度'] = 15
    columnNameMap['测试时间'] = 29
    columnNameMap['结果'] = 17

#将所有的文件数据合并到一个列表中
def MergeAllSampleData(dataDicIndexByFile,mergedList):
    mergedList.clear()
    for idx in dataDicIndexByFile:
        for item in dataDicIndexByFile[idx]:
            mergedList.append(item)

detailDataFont=Font('宋体',size=11,color=colors.BLACK,bold=False,italic=False)

def GetUsbVersion():
    strUsbVersion = 'USB3' 
    pattern = '.+\\\\MP_H2_SD2.0\\\\\D+_\d{14}.csv$'
    for file in PublicFuc.fileLst:
        if re.match(pattern, file):
            strUsbVersion = 'USB2'
            break
    return strUsbVersion

def SpeedErrDetect(_wspeed,_rspeed):
    wspeed = float(_wspeed)
    rspeed = float(_rspeed)
    
    str = ''
    #进一步判定是否有速度超标,标准来源于3月1号邮件Admiral<EMAIL>
    if g_usb_version == 'USB2':
        if wspeed < 10 and rspeed < 20:
            str = "speed err"
        elif wspeed < 10 and rspeed >= 20:
            str = "write speed err"
        elif wspeed >= 10 and rspeed < 20:
            str = "read speed err"
        else:
            str = ''
    else:
        if wspeed < 10 and rspeed < 50:
            str = "speed err"
        elif wspeed < 10 and rspeed >= 50:
            str = "write speed err"
        elif wspeed >= 10 and rspeed < 50:
            str = "read speed err"
        else:
            str = ''

    return str



#写详细数据
def WriteDetailResult2WorkSheet(worksheet, startLine, dataDic):
    curLine = startLine
    rowIdx = 1

    #排序输出
    keySortLst = sorted(dataDic.keys(), reverse=False)
    for sampleKey in keySortLst:
        sampleDic = dataDic[sampleKey]

        try:
            #第一列是编号，直接填行号
            if 'MP' in sampleDic:
                lineMp = sampleDic['MP']
                worksheet['%s%d'%(get_column_letter(2), curLine)] = sampleKey
                worksheet['%s%d'%(get_column_letter(3), curLine)] = lineMp[mp_column_name_map['ErrCode']]
                worksheet['%s%d'%(get_column_letter(4), curLine)] = lineMp[mp_column_name_map['容量']]
                worksheet['%s%d'%(get_column_letter(5), curLine)] = lineMp[mp_column_name_map['坏块数']]
                worksheet['%s%d'%(get_column_letter(6), curLine)] = lineMp[mp_column_name_map['Bin级']]
                worksheet['%s%d'%(get_column_letter(7), curLine)] = lineMp[mp_column_name_map['量产模式']]
                worksheet['%s%d'%(get_column_letter(8), curLine)] = lineMp[mp_column_name_map['量产耗时']] 
            
            #填充H2数据
            if 'H2' in sampleDic:
                lineH2 = sampleDic['H2']
                worksheet['%s%d'%(get_column_letter(9), curLine)] = lineH2[h2_column_name_map['写速度']]
                worksheet['%s%d'%(get_column_letter(10), curLine)] = lineH2[h2_column_name_map['读速度']]
                worksheet['%s%d'%(get_column_letter(11), curLine)] = lineH2[h2_column_name_map['测试时间']]
                result = lineH2[h2_column_name_map['结果']].upper()

                if result.strip() != 'PASS':
                    worksheet['%s%d'%(get_column_letter(12), curLine)].fill = PublicFuc.warnFill
                    worksheet['%s%d'%(get_column_letter(12), curLine)] = lineH2[h2_column_name_map['结果']]
                else:
                    worksheet['%s%d'%(get_column_letter(12), curLine)] = 'PASS'
                    rawWspeed = lineH2[h2_column_name_map['写速度']]
                    rawRspeed = lineH2[h2_column_name_map['读速度']]
                    strSpeedErrInfo = SpeedErrDetect(rawWspeed,rawRspeed)
                    if strSpeedErrInfo != '':
                        worksheet['%s%d'%(get_column_letter(12), curLine)].fill = PublicFuc.warnFill
                        worksheet['%s%d'%(get_column_letter(12), curLine)] = strSpeedErrInfo

            
        except(AttributeError):
            continue
        rowIdx += 1 
        curLine += 1



def GetBinShowTxt(binValue):
    if binValue == 0:
        return ''
    else:
        return binValue
    


def ReadCsvData(curpath,pattern,dataDic):
    fileIdx = 1
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除

        file_data = []
        mdTime = file[pos+1:];#os.path.getmtime(file) 用文件名代替时间可以取得真正准确的时间
        with open(file, encoding='gb18030') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            birth_header = next(csv_reader)  # 读取第一行每一列的标题
            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中   
                row.append(mdTime)
                file_data.append(row)

        dataDic[fileIdx]=file_data
        fileIdx += 1

#初始化制定区域边框为所有框线
def format_border(s_column, s_index, e_column , e_index):
    for row in tuple(sheet[s_column + str(s_index):e_column + str(e_index)]):
        for cell in row:
            cell.border = my_border('thin', 'thin', 'thin', 'thin')