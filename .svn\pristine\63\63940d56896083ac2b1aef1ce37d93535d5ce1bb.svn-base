import os,sys,logging,traceback,time,json,MTT,MTT_PV_Report,Mars_hr,BLX_hr,xu4_tbw_um,xu4_sw_speed,xu4_longtime_rw_sw,terminalreport

curpath = os.path.split(sys.argv[0])[0]
reportPath = curpath
bRmsRun = False
if len(sys.argv) > 1:
    #Rms调用报告统计，需要有个独占文件，防止多人同时合并报告
    reportPath = sys.argv[1]

curtime = time.strftime("%Y%m%d%H%M%S", time.localtime())
logname = os.path.join(reportPath, 'EMMC_Report_' + curtime + '.log')
#日志配置
logging.basicConfig(filename = logname,
                    filemode='w',
                    format = '%(asctime)s-%(name)s-%(levelname)s-%(module)s: %(message)s',
                    datefmt = '%Y-%m-%d %H:%M:%S %p',
                    level = logging.INFO)


try:
    import shutil,tempfile
    import requests
    import openpyxl
    from openpyxl.styles import Alignment
    import Performance,Wl,PublicFuc,Wl_TOOLS,Platform_pf,PowerOffTest,Coroutines,MarsEnvironment,Endurance,DataRetention,VTEPerf,XU4Merger,summary
    import urllib.parse

    if len(sys.argv) > 1:
        testNo = os.path.split(reportPath)[1]
        enTestNo = urllib.parse.quote(testNo)
        onlyPath = os.path.join(reportPath, 'public.txt')
        jsonData = {}
        jsonData['token'] = 'c05d5f8fe466c0f90a3bd19a45664509dc3d4a34c8570ccc9868df6fac217930'
        jsonData['secret'] = 'SECec46d8320d6919606777e4d8fd7e9bf9a8aa450742dc5b03c0792239b9b57877'
        jsonData['atLst'] = '17711811339'
        rmsUrl = 'http://ereport.yeestor.com/sendDingTalkMsg' 
        bRmsRun = True
    templateFile = os.path.join(curpath, 'emmc_report_template.xlsx')
    errTempfile = os.path.join(curpath, 'ErrorList_template.xlsx') 
    resultFileName = 'IND_Emmc汇总报告.xlsx'
    resultFile = os.path.join(reportPath, resultFileName) 
    errDiskFile = os.path.join(reportPath, 'ErrorList.xlsx')

    logging.info('程序开始运行！')
    PublicFuc.GetAllFile(reportPath)
    wb = openpyxl.load_workbook(filename = templateFile)
    alignment = Alignment(horizontal='center',vertical='center')
    PowerOffTest.Run(reportPath, wb, alignment)
    Coroutines.Run(reportPath, wb, alignment)
    MarsEnvironment.Run(reportPath, wb, alignment)
    Endurance.Run(reportPath, wb, alignment)
    DataRetention.Run(reportPath, wb, alignment)
    Performance.Run(reportPath, wb, alignment)
    VTEPerf.Run(reportPath, wb, alignment)
    #Wl.Run(reportPath, wb, alignment)
    Wl_TOOLS.Run(reportPath, wb, alignment)
    #Platform_pf.Run(reportPath, wb, alignment)
    MTT.Run(reportPath, wb, alignment)
    summary.Run(reportPath, wb, alignment)
    PublicFuc.WriteErrDiskFile(openpyxl.load_workbook(filename = errTempfile),errDiskFile)
    wb.save(resultFile)
    Mars_hr.Run(reportPath)
    BLX_hr.Run(reportPath)
    if bRmsRun:
        XU4rtemplateFile = os.path.join(curpath, 'XU4_template.xlsx')
        wb = openpyxl.load_workbook(filename = XU4rtemplateFile)
        alignment = Alignment(horizontal='center',vertical='center') 
        XU4Merger.Run(wb, alignment, testNo)
        try:
            MTT_PV_Report.Run(curpath,reportPath, wb, alignment,bRmsRun)
            xu4_tbw_um.Run(reportPath, wb)
            xu4_sw_speed.Run(reportPath, wb)
            xu4_longtime_rw_sw.Run(reportPath, wb)
        except:
            print(traceback.format_exc())
            logging.error(traceback.format_exc())
        XU4resultFileName = 'IND_XU4汇总报告.xlsx'
        XU4resultFile = os.path.join(reportPath, XU4resultFileName)
        wb.save(XU4resultFile)
        terminalreport.Run(curpath,reportPath,testNo)
    if bRmsRun:
        jsonData['type'] = 'link'
        jsonData['title'] = 'eReport报告合并完成通知'
        jsonData['text'] = '测试单号：%s \r\n报告合并成功，请前往查看！'%testNo
        jsonData['url'] = 'http://ereport.yeestor.com/report/download/?nid=IND_EMMC&key=%s'%enTestNo
        response = requests.request('POST', rmsUrl, data=jsonData)
        if os.path.exists(onlyPath):
            os.remove(onlyPath)
        #工单系统回调
        strUrl = 'http://ereport.yeestor.com/report/file_download/?product=%s&testNo=%s&file='%('IND_EMMC',enTestNo)
        woDic = {}
        woDic['orderNo'] = testNo
        woDic['reportInfoList'] = []
        if os.path.exists(resultFile):
            tempDic = {}
            tempDic['name'] = resultFileName
            tempDic['type'] = 'report'
            tempDic['url'] = strUrl+resultFileName
            woDic['reportInfoList'].append(tempDic)
        woUrl = "http://gateway.yeestor.com:8789/wo/report/status"
        headers = {'Content-Type': 'application/json','ORDER-NO':enTestNo}
        requests.request("POST", woUrl, headers=headers, data=json.dumps(woDic))
    logging.info('结束！')

except:
    print(traceback.format_exc())
    logging.error(traceback.format_exc())
    if bRmsRun:
        jsonData['type'] = 'text'
        jsonData['text'] = 'eReport报告合并异常！@13686893642\r\n测试单号：%s'%testNo
        response = requests.request('POST', rmsUrl, data=jsonData)
        if os.path.exists(onlyPath):
            os.remove(onlyPath)



