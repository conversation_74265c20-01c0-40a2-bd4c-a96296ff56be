from openpyxl import  utils,Workbook
import os,sys,logging,traceback,time
import re
import test_result,MTT_PV_Report_UFS
curpath = os.path.split(sys.argv[0])[0]
reportPath = curpath


bRmsRun = False
argInfo = ''
productType = '' #分EMMC和IND_EMMC
productRev = '' #分0 - alpha 和 1 - release
if len(sys.argv) > 3:
    #Rms调用报告统计，需要有个独占文件，防止多人同时合并报告
    reportPath = sys.argv[1]
    productType = sys.argv[2]
    productRev = sys.argv[3]
    for i in range(len(sys.argv)):
        argInfo += sys.argv[i]
        argInfo += "\n"

curtime = time.strftime("%Y%m%d%H%M%S", time.localtime())
logname = os.path.join(reportPath, 'BLX_HR_template_' + curtime + '.log')

#日志配置
logging.basicConfig(filename = logname,
                    filemode='w',
                    format = '%(asctime)s-%(name)s-%(levelname)s-%(module)s: %(message)s',
                    datefmt = '%Y-%m-%d %H:%M:%S %p',
                    level = logging.INFO)

try:
    import openpyxl
    from openpyxl.styles import Alignment
    import urllib.parse
    import shutil,tempfile
    import requests


    if len(sys.argv) > 1:
        testNo = os.path.split(reportPath)[1]
        enTestNo = urllib.parse.quote(testNo)
        onlyPath = os.path.join(reportPath, 'public.txt')
        jsonData = {}
        jsonData['token'] = 'd7faec554b2d83ff9592d5bb507e12b1d9915f21a29490638f3a735117ba6076'
        jsonData['secret'] = 'SEC4afd2f150ce4a5808c2a6337d59de4bc098c0a37f2140d4ea741519ef75e09da'
        jsonData['atLst'] = '18312006726'
        rmsUrl = 'http://ereport.yeestor.com/sendDingTalkMsg' 
        bRmsRun = True

    resultFileName = 'UFS_DV测试汇总报告.xlsx'
    resultFile = os.path.join(reportPath, resultFileName)
    logging.info('程序开始运行！')
    logging.info(argInfo)
    print('开始汇总报告，请等待。。。')
    UFSrtemplateFile = os.path.join(curpath, 'UFS_template.xlsx')
    wb = openpyxl.load_workbook(filename = UFSrtemplateFile)
    alignment = Alignment(horizontal='center',vertical='center') 
    test_result.Run(wb,alignment,testNo)
    MTT_PV_Report_UFS.Run(curpath,reportPath,wb,'',bRmsRun)
    wb.save(resultFile)

    if bRmsRun:
        jsonData['type'] = 'link'
        jsonData['title'] = '[MTT-UFS]eReport报告合并完成通知'
        jsonData['text'] = '测试单号：%s \r\n报告合并成功，请前往查看！'%testNo
        jsonData['url'] = 'http://ereport.yeestor.com/eb/report/download?nid=%s&key=%s&type=%s'%(productType,enTestNo,productRev)
        response = requests.request('POST', rmsUrl, data=jsonData)
        if os.path.exists(onlyPath):
            os.remove(onlyPath)
        #工单系统回调
        strUrl = 'http://ereport.yeestor.com/report/file_download/?product=%s&testNo=%s&type=%s&file='%(productType,enTestNo,productRev)
        woDic = {}
        woDic['orderNo'] = testNo
        woDic['reportInfoList'] = []
        if os.path.exists(resultFile):
            tempDic = {}
            tempDic['name'] = resultFileName
            tempDic['type'] = 'report'
            tempDic['url'] = strUrl+resultFileName
            woDic['reportInfoList'].append(tempDic)

        woUrl = "http://ebuildin.yeestor.com:8789/qa/reportReply" #http://gateway.yeestor.com:8789/wo/report/status
        headers = {'Content-Type': 'application/json'}
        querystring = {"testNo":woDic['orderNo']}
        requests.request("POST", woUrl, headers=headers, json=woDic['reportInfoList'],params=querystring)
  
    logging.info('结束！')
except:
    print(traceback.format_exc())
    logging.error(traceback.format_exc())
    if bRmsRun:
        try:
            jsonData['type'] = 'text'
            jsonData['text'] = '[MTT-UFS]eReport报告合并异常！@18312006726\r\n测试单号：%s'%testNo
            response = requests.request('POST', rmsUrl, data=jsonData)
            if os.path.exists(onlyPath):
                os.remove(onlyPath)
        except:
            if os.path.exists(onlyPath):
                os.remove(onlyPath)       
    
