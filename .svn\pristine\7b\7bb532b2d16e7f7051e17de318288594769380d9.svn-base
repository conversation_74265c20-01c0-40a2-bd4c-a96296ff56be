#pragma once

#define MAXPAGE 12
// CTabSheet

class CTabSheet : public CTabCtrl
{
	DECLARE_DYNAMIC(CTabSheet)

public:
	CTabSheet();
	virtual ~CTabSheet();

public:
	BOOL AddPage(LPCTSTR title, CDialog *pDialog, UINT ID);
	void SetRect();
	void Show();
	int  SetCurSel(int nItem);
	int  GetCurSel();

protected:
	BYTE m_nNumOfPages;
	BYTE m_nCurrentPage;
	CDialog *m_pPages[MAXPAGE];
	UINT m_IDD[MAXPAGE];
	CString m_Title[MAXPAGE];

	DECLARE_MESSAGE_MAP()
};


