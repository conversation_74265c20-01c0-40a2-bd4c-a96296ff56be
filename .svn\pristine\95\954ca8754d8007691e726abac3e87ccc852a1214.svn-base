import os
import re
import openpyxl
from openpyxl.styles import Alignment
strList = ['Failed ','Passed ','.log']
colname = ['TLCWLMinCnt','TLCWLMaxCnt','TLCWLAvgCnt','TLC ec gap','SLCWLMinCnt','SLCWLMaxCnt','SLCWLAvgCnt','SLC ec gap','ErrCode','CRCErrCnt','VDTIntCnt','PowerOnCnt','RetryCnt','WatchDogCnt','FFUCnt','PhyTotalWriteCnt','HostTotalWriteCnt','BadBlkOriginalCnt','NewSLCBadBlockCnt','NewTLCBadBlockCnt']

class filedata:
    def __init__(self, path, folder, time, case):
        self.path = path
        self.folder = folder
        self.time = time
        self.case = case
Capacity = 0
def Run(curpath):
    orgcurpath = curpath
    curpath = os.path.join(curpath, "XU4")
    MarsresultFileName = 'SMART_INFO_汇总.xlsx'
    MarsresultFile = os.path.join(orgcurpath, MarsresultFileName)
    wb = openpyxl.Workbook()
    global category
    category = []
    data = []
    if os.path.isdir(curpath):
        for item in os.listdir(curpath):
        # 拼接子文件夹路径
            sub_folder_path = os.path.join(curpath, item)
            
            # 判断是否为文件夹
            if os.path.isdir(sub_folder_path):
                file_start = item.split('-')[0]
                hr_merger_path = os.path.join(sub_folder_path, file_start)
                if os.path.isdir(hr_merger_path):
                    for subitem in os.listdir(sub_folder_path):
                        if subitem.endswith('.log') and ('Passed' in subitem or 'Failed' in subitem) and ' env ' not in subitem:
                            inipath = os.path.join(sub_folder_path,subitem)
                            time = os.path.getmtime(inipath)
                            text = subitem
                            for seg in strList:
                                text = text.replace(seg,'')
                            data.append(filedata(inipath,item,time,text))
        data.sort(key=lambda x: (x.folder, x.time))
        load_data(data,wb)
        if len(wb.sheetnames) != 1:
            sheet = wb["Sheet"]
            wb.remove(sheet)
        wb.save(MarsresultFile)

def insertdata(remindata,sample,lineimt,title,realdata):
    title[sample] = colname
    if sample in realdata:
        realdata[sample].append(getData(remindata,lineimt))
    else:
        realdata[sample] = [getData(remindata,lineimt)]

def insertNull(realdata,sample,item,title):
    Nullimt = {}
    Nullimt['folder'] = item.folder
    Nullimt['case'] = item.case
    for col_item in colname:
        Nullimt[col_item] = 'NA'
    title[sample] = colname
    if sample in realdata:
        realdata[sample].append(Nullimt)
    else:
        realdata[sample] = [Nullimt]


def load_data(data,wb):
    title = {}
    realdata = {}
    key = 0
    remindata = []
    nulllist = []
    for item in data:
        pattern = r'-(.*?)-(.*?)_'
        match = re.search(pattern, item.folder)
        if re.search(pattern, item.folder):
            sample = match.group(2)
        with open(item.path, "r",errors='ignore') as file:
            lines = file.readlines()
            remindata = []
            key = 0
            for line in lines:
                if '[tools info get_smart_info]' in line:
                    key = 2
                if key > 1:
                    remindata.append(line)
                if "NewTLCBadBlockCnt" in line:
                    lineimt = {}
                    lineimt['folder'] = item.folder
                    lineimt['case'] = item.case
                    if remindata != []:
                        insertdata(remindata,sample,lineimt,title,realdata)
                    remindata = []
                    key = 1
            if key == 0:
                nulllist.append([sample,item])
    for item in nulllist:
        insertNull(realdata,item[0],item[1],title)
    
    rowline = 1
    for sample in realdata:
        sheet = wb.create_sheet(title=sample)
        sheet.column_dimensions['A'].width = 35
        sheet.column_dimensions['B'].width = 15

        # 为20列数据设置列宽为原来的2倍宽（假设原来默认宽度为10，设置为20）
        data_columns = ['C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V']  # 对应20列数据
        for col in data_columns:
            sheet.column_dimensions[col].width = 20

        colline = 1
        rowline = 1
        for val in ['测试模块','case']:
            sheet.cell(row=rowline, column=colline, value=val)
            colline += 1
        for val in title[sample]:
            header_cell = sheet.cell(row=rowline, column=colline, value=val)
            # 为表头的4列数据设置居中对齐
            header_cell.alignment = Alignment(horizontal='center', vertical='center')
            colline += 1

        # 创建居中对齐样式
        center_alignment = Alignment(horizontal='center', vertical='center')

        for item in realdata[sample]:
            colline = 1
            rowline += 1
            for val in ['folder','case']:
                sheet.cell(row=rowline, column=colline, value=item[val])
                colline += 1
            for val in title[sample]:
                cell = sheet.cell(row=rowline, column=colline)
                if val in item:
                    cell.value = item[val]
                else:
                    cell.value = 'NA'
                # 为4列数据设置居中对齐
                cell.alignment = center_alignment
                colline += 1
        consheet(sheet)


def getData(remindata,lineimt):
    # 提取TLCWLMinCnt, TLCWLMaxCnt, TLCWLAvgCnt的值
    tlc_min = 0
    tlc_max = 0
    tlc_avg = 0

    # 提取SLCWLMinCnt, SLCWLMaxCnt, SLCWLAvgCnt的值
    slc_min = 0
    slc_max = 0
    slc_avg = 0

    # 初始化其他字段
    other_fields = {
        'ErrCode': 0,
        'CRCErrCnt': 0,
        'VDTIntCnt': 0,
        'PowerOnCnt': 0,
        'RetryCnt': 0,
        'WatchDogCnt': 0,
        'FFUCnt': 0,
        'PhyTotalWriteCnt': 0,
        'HostTotalWriteCnt': 0,
        'BadBlkOriginalCnt': 0,
        'NewSLCBadBlockCnt': 0,
        'NewTLCBadBlockCnt': 0
    }

    for line in remindata:
        if 'TLCWLMinCnt:' in line:
            try:
                # 提取冒号后面的内容，去掉括号内容
                value_part = line.split(':')[-1].strip()
                if '(' in value_part:
                    value_part = value_part.split('(')[0].strip()
                tlc_min = int(value_part, 16) if value_part.startswith('0x') else int(value_part)
            except:
                tlc_min = 0
        elif 'TLCWLMaxCnt:' in line:
            try:
                value_part = line.split(':')[-1].strip()
                if '(' in value_part:
                    value_part = value_part.split('(')[0].strip()
                tlc_max = int(value_part, 16) if value_part.startswith('0x') else int(value_part)
            except:
                tlc_max = 0
        elif 'TLCWLAvgCnt:' in line:
            try:
                value_part = line.split(':')[-1].strip()
                if '(' in value_part:
                    value_part = value_part.split('(')[0].strip()
                tlc_avg = int(value_part, 16) if value_part.startswith('0x') else int(value_part)
            except:
                tlc_avg = 0
        elif 'SLCWLMinCnt:' in line:
            try:
                value_part = line.split(':')[-1].strip()
                if '(' in value_part:
                    value_part = value_part.split('(')[0].strip()
                slc_min = int(value_part, 16) if value_part.startswith('0x') else int(value_part)
            except:
                slc_min = 0
        elif 'SLCWLMaxCnt:' in line:
            try:
                value_part = line.split(':')[-1].strip()
                if '(' in value_part:
                    value_part = value_part.split('(')[0].strip()
                slc_max = int(value_part, 16) if value_part.startswith('0x') else int(value_part)
            except:
                slc_max = 0
        elif 'SLCWLAvgCnt:' in line:
            try:
                value_part = line.split(':')[-1].strip()
                if '(' in value_part:
                    value_part = value_part.split('(')[0].strip()
                slc_avg = int(value_part, 16) if value_part.startswith('0x') else int(value_part)
            except:
                slc_avg = 0
        else:
            # 处理其他字段
            for field_name in other_fields.keys():
                if f'{field_name}:' in line:
                    try:
                        value_part = line.split(':')[-1].strip()
                        if '(' in value_part:
                            value_part = value_part.split('(')[0].strip()
                        other_fields[field_name] = int(value_part, 16) if value_part.startswith('0x') else int(value_part)
                    except:
                        other_fields[field_name] = 0
                    break

    # 计算ec gap
    tlc_ec_gap = tlc_max - tlc_min
    slc_ec_gap = slc_max - slc_min

    # 填充数据
    lineimt['TLCWLMinCnt'] = tlc_min
    lineimt['TLCWLMaxCnt'] = tlc_max
    lineimt['TLCWLAvgCnt'] = tlc_avg
    lineimt['TLC ec gap'] = tlc_ec_gap
    lineimt['SLCWLMinCnt'] = slc_min
    lineimt['SLCWLMaxCnt'] = slc_max
    lineimt['SLCWLAvgCnt'] = slc_avg
    lineimt['SLC ec gap'] = slc_ec_gap

    # 添加其他字段
    for field_name, value in other_fields.items():
        lineimt[field_name] = value

    return lineimt

def consheet(sheet):
    sheet.column_dimensions['A'].width = 30
    sheet.column_dimensions['B'].width = 50
    # 冻结窗格
    sheet.freeze_panes='C2'
    name_range_dict = {}
    for row in sheet.iter_rows(min_row=2, min_col=1, max_col=1):
        name = row[0].value
    # 如果名字已存在于字典中，则更新合并范围的结束行号
        if name in name_range_dict:
            name_range_dict[name]['end_row'] = row[0].row
        else:
            # 否则，在字典中创建新的合并范围条目
            name_range_dict[name] = {'start_row': row[0].row, 'end_row': row[0].row}

# 合并相同名字的单元格
    for name, range_dict in name_range_dict.items():
        align = Alignment(horizontal='center', vertical='center')
        sheet['A%d'%range_dict['start_row']].alignment = align
        start_row = range_dict['start_row']
        end_row = range_dict['end_row']
        sheet.merge_cells(f"A{start_row}:A{end_row}")
    


