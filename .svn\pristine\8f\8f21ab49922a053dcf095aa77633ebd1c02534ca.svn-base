**** NTLOG INITIATED  +DATE+05/13/2022  +TIME+15:48:14
****   NtLog date: Fri May 12 08:07:25 2000
****   Exe   date: Mon Aug 20 05:45:39 2001
****   Processor : Intel CPU, Unknown P6 family CPU, Model 14, stepping 11, 0K on-chip cache detected
****   CPUS      : 4
****   System    : Windows NT 6.2[9200], Retail, Mouse, 
****   Vid Driver: Unknown
****    Chip Type: Intel(R) UHD Graphics Family
****    DAC Type : Internal
****    Adapter  : Intel(R) UHD Graphics 630
****    MiniPort : {20CA20E8-CC75-11EC-8530-8E12EE1CDC16}
****    VRAM     : 1048576
****    Hertz    : 59
****    X Res    : 1920
****    Y Res    : 1080
****    BPP      : 32
****    Planes   : 1
****    RGB Masks: (888)(bgr)
****   Machine   : DESKTOP-48OLA2D
****   User Name : admin
****   Language  : Chinese (Simpli
****   KM boundary: 2
****   ProductOptions: Terminal Server

****
****
2248.224C :  VAR[INFO ]    0 : SdStress - Storage Device Stress utility, Version 1.91
2248.224C :  VAR[INFO ]    0 : OS: Windows NT 
2248.224C :  VAR[INFO ]    0 : /R switch specified - Randomizing block sizes 
2248.224C :  VAR[INFO ]    0 : /D switch specified - 23 simultaneous disk thread(s) selected 
2248.224C :  VAR[INFO ]    0 : /P switch specified - Number of test passes is 60000 
2248.224C :  VAR[INFO ]    0 : /S switch specified, the following drives are selected for stress: 
2248.224C :  VAR[INFO ]    0 : --> Drive C: is selected. 
2248.224C :  VAR[INFO ]    0 : /ZA switch specified, all test scenarios are selected 
2248.224C :  VAR[INFO ]    0 : Drive C: is a Fixed Disk with 983934541824 bytes free space remaining. 
2248.224C :  VAR[INFO ]    0 : Drive C: - Required space is 25165824 
2248.224C :  VAR[INFO ]    0 :  Creating files used for test: 
2248.224C :  VAR[INFO ]    0 : Drive C: - Drive is not CDROM.  Creating C:\SDDATA\TESTFILE.DAT. 
2248.224C :  VAR[INFO ]    0 : ** Test Scenario 1 - CopyTestFile **  
2248.224C :  VAR[INFO ]    0 : Thread init'd - Src:C, Dst:C, Instance:1 
2248.224C :  VAR[INFO ]    0 : Thread init'd - Src:C, Dst:C, Instance:2 
2248.224C :  VAR[INFO ]    0 : Thread init'd - Src:C, Dst:C, Instance:3 
2248.224C :  VAR[INFO ]    0 : Thread init'd - Src:C, Dst:C, Instance:4 
2248.224C :  VAR[INFO ]    0 : Thread init'd - Src:C, Dst:C, Instance:5 
2248.224C :  VAR[INFO ]    0 : Thread init'd - Src:C, Dst:C, Instance:6 
2248.224C :  VAR[INFO ]    0 : Thread init'd - Src:C, Dst:C, Instance:7 
2248.224C :  VAR[INFO ]    0 : Thread init'd - Src:C, Dst:C, Instance:8 
2248.224C :  VAR[INFO ]    0 : Thread init'd - Src:C, Dst:C, Instance:9 
2248.224C :  VAR[INFO ]    0 : Thread init'd - Src:C, Dst:C, Instance:10 
2248.224C :  VAR[INFO ]    0 : Thread init'd - Src:C, Dst:C, Instance:11 
2248.224C :  VAR[INFO ]    0 : Thread init'd - Src:C, Dst:C, Instance:12 
2248.224C :  VAR[INFO ]    0 : Thread init'd - Src:C, Dst:C, Instance:13 
2248.224C :  VAR[INFO ]    0 : Thread init'd - Src:C, Dst:C, Instance:14 
2248.224C :  VAR[INFO ]    0 : Thread init'd - Src:C, Dst:C, Instance:15 
2248.224C :  VAR[INFO ]    0 : Thread init'd - Src:C, Dst:C, Instance:16 
2248.224C :  VAR[INFO ]    0 : Thread init'd - Src:C, Dst:C, Instance:17 
2248.224C :  VAR[INFO ]    0 : Thread init'd - Src:C, Dst:C, Instance:18 
2248.224C :  VAR[INFO ]    0 : Thread init'd - Src:C, Dst:C, Instance:19 
2248.224C :  VAR[INFO ]    0 : Thread init'd - Src:C, Dst:C, Instance:20 
2248.224C :  VAR[INFO ]    0 : Thread init'd - Src:C, Dst:C, Instance:21 
2248.224C :  VAR[INFO ]    0 : Thread init'd - Src:C, Dst:C, Instance:22 
2248.224C :  VAR[INFO ]    0 : Thread init'd - Src:C, Dst:C, Instance:23 
2248.22F8 :  +VAR+PASS     0 : PASS (1/60000) on thread: Src:C, Dst:C, Instance:5 
2248.22EC :  +VAR+PASS     1 : PASS (1/60000) on thread: Src:C, Dst:C, Instance:4 
2248.22E8 :  +VAR+PASS     2 : PASS (1/60000) on thread: Src:C, Dst:C, Instance:3 
2248.22D8 :  +VAR+PASS     3 : PASS (1/60000) on thread: Src:C, Dst:C, Instance:1 
2248.22E0 :  +VAR+PASS     4 : PASS (1/60000) on thread: Src:C, Dst:C, Instance:2 
2248.233C :  +VAR+PASS     5 : PASS (1/60000) on thread: Src:C, Dst:C, Instance:14 
2248.232C :  +VAR+PASS     6 : PASS (1/60000) on thread: Src:C, Dst:C, Instance:12 
2248.2324 :  +VAR+PASS     7 : PASS (1/60000) on thread: Src:C, Dst:C, Instance:11 
2248.231C :  +VAR+PASS     8 : PASS (1/60000) on thread: Src:C, Dst:C, Instance:10 
2248.230C :  +VAR+PASS     9 : PASS (1/60000) on thread: Src:C, Dst:C, Instance:8 
2248.2318 :  +VAR+PASS    10 : PASS (1/60000) on thread: Src:C, Dst:C, Instance:9 
2248.2304 :  +VAR+PASS    11 : PASS (1/60000) on thread: Src:C, Dst:C, Instance:7 
2248.22FC :  +VAR+PASS    12 : PASS (1/60000) on thread: Src:C, Dst:C, Instance:6 
2248.2364 :  +VAR+PASS    13 : PASS (1/60000) on thread: Src:C, Dst:C, Instance:19 
2248.235C :  +VAR+PASS    14 : PASS (1/60000) on thread: Src:C, Dst:C, Instance:18 
2248.2358 :  +VAR+PASS    15 : PASS (1/60000) on thread: Src:C, Dst:C, Instance:17 
2248.2350 :  +VAR+PASS    16 : PASS (1/60000) on thread: Src:C, Dst:C, Instance:16 
2248.2384 :  +VAR+PASS    17 : PASS (1/60000) on thread: Src:C, Dst:C, Instance:23 
2248.237C :  +VAR+PASS    18 : PASS (1/60000) on thread: Src:C, Dst:C, Instance:22 
2248.2374 :  +VAR+PASS    19 : PASS (1/60000) on thread: Src:C, Dst:C, Instance:21 
2248.236C :  +VAR+PASS    20 : PASS (1/60000) on thread: Src:C, Dst:C, Instance:20 
2248.2334 :  +VAR+PASS    21 : PASS (1/60000) on thread: Src:C, Dst:C, Instance:13 
2248.2344 :  +VAR+PASS    22 : PASS (1/60000) on thread: Src:C, Dst:C, Instance:15 
2248.22E8 :  +VAR+PASS    23 : PASS (2/60000) on thread: Src:C, Dst:C, Instance:3 
2248.22E0 :  +VAR+PASS    24 : PASS (2/60000) on thread: Src:C, Dst:C, Instance:2 
2248.22D8 :  +VAR+PASS    25 : PASS (2/60000) on thread: Src:C, Dst:C, Instance:1 
2248.22F8 :  +VAR+PASS    26 : PASS (2/60000) on thread: Src:C, Dst:C, Instance:5 
2248.22EC :  +VAR+PASS    27 : PASS (2/60000) on thread: Src:C, Dst:C, Instance:4 
2248.2304 :  +VAR+PASS    28 : PASS (2/60000) on thread: Src:C, Dst:C, Instance:7 
2248.22FC :  +VAR+PASS    29 : PASS (2/60000) on thread: Src:C, Dst:C, Instance:6 
2248.2364 :  +VAR+PASS    30 : PASS (2/60000) on thread: Src:C, Dst:C, Instance:19 
2248.2318 :  +VAR+PASS    31 : PASS (2/60000) on thread: Src:C, Dst:C, Instance:9 
2248.230C :  +VAR+PASS    32 : PASS (2/60000) on thread: Src:C, Dst:C, Instance:8 
2248.231C :  +VAR+PASS    33 : PASS (2/60000) on thread: Src:C, Dst:C, Instance:10 
2248.2324 :  +VAR+PASS    34 : PASS (2/60000) on thread: Src:C, Dst:C, Instance:11 
2248.232C :  +VAR+PASS    35 : PASS (2/60000) on thread: Src:C, Dst:C, Instance:12 
2248.2344 :  +VAR+PASS    36 : PASS (2/60000) on thread: Src:C, Dst:C, Instance:15 
2248.233C :  +VAR+PASS    37 : PASS (2/60000) on thread: Src:C, Dst:C, Instance:14 
2248.2334 :  +VAR+PASS    38 : PASS (2/60000) on thread: Src:C, Dst:C, Instance:13 
2248.236C :  +VAR+PASS    39 : PASS (2/60000) on thread: Src:C, Dst:C, Instance:20 
2248.2374 :  +VAR+PASS    40 : PASS (2/60000) on thread: Src:C, Dst:C, Instance:21 
2248.237C :  +VAR+PASS    41 : PASS (2/60000) on thread: Src:C, Dst:C, Instance:22 
2248.2384 :  +VAR+PASS    42 : PASS (2/60000) on thread: Src:C, Dst:C, Instance:23 
2248.2350 :  +VAR+PASS    43 : PASS (2/60000) on thread: Src:C, Dst:C, Instance:16 
2248.2358 :  +VAR+PASS    44 : PASS (2/60000) on thread: Src:C, Dst:C, Instance:17 
2248.235C :  +VAR+PASS    45 : PASS (2/60000) on thread: Src:C, Dst:C, Instance:18 
2248.22D8 :  +VAR+PASS    46 : PASS (3/60000) on thread: Src:C, Dst:C, Instance:1 
2248.22EC :  +VAR+PASS    47 : PASS (3/60000) on thread: Src:C, Dst:C, Instance:4 
2248.22E0 :  +VAR+PASS    48 : PASS (3/60000) on thread: Src:C, Dst:C, Instance:2 
2248.22F8 :  +VAR+PASS    49 : PASS (3/60000) on thread: Src:C, Dst:C, Instance:5 
2248.22E8 :  +VAR+PASS    50 : PASS (3/60000) on thread: Src:C, Dst:C, Instance:3 
2248.2358 :  +VAR+PASS    51 : PASS (3/60000) on thread: Src:C, Dst:C, Instance:17 
2248.235C :  +VAR+PASS    52 : PASS (3/60000) on thread: Src:C, Dst:C, Instance:18 
2248.2350 :  +VAR+PASS    53 : PASS (3/60000) on thread: Src:C, Dst:C, Instance:16 
2248.2384 :  +VAR+PASS    54 : PASS (3/60000) on thread: Src:C, Dst:C, Instance:23 
2248.237C :  +VAR+PASS    55 : PASS (3/60000) on thread: Src:C, Dst:C, Instance:22 
2248.2374 :  +VAR+PASS    56 : PASS (3/60000) on thread: Src:C, Dst:C, Instance:21 
2248.236C :  +VAR+PASS    57 : PASS (3/60000) on thread: Src:C, Dst:C, Instance:20 
2248.2334 :  +VAR+PASS    58 : PASS (3/60000) on thread: Src:C, Dst:C, Instance:13 
2248.233C :  +VAR+PASS    59 : PASS (3/60000) on thread: Src:C, Dst:C, Instance:14 
2248.232C :  +VAR+PASS    60 : PASS (3/60000) on thread: Src:C, Dst:C, Instance:12 
2248.2344 :  +VAR+PASS    61 : PASS (3/60000) on thread: Src:C, Dst:C, Instance:15 
2248.2324 :  +VAR+PASS    62 : PASS (3/60000) on thread: Src:C, Dst:C, Instance:11 
2248.231C :  +VAR+PASS    63 : PASS (3/60000) on thread: Src:C, Dst:C, Instance:10 
2248.230C :  +VAR+PASS    64 : PASS (3/60000) on thread: Src:C, Dst:C, Instance:8 
2248.2318 :  +VAR+PASS    65 : PASS (3/60000) on thread: Src:C, Dst:C, Instance:9 
2248.2364 :  +VAR+PASS    66 : PASS (3/60000) on thread: Src:C, Dst:C, Instance:19 
2248.22FC :  +VAR+PASS    67 : PASS (3/60000) on thread: Src:C, Dst:C, Instance:6 
2248.2304 :  +VAR+PASS    68 : PASS (3/60000) on thread: Src:C, Dst:C, Instance:7 
2248.22E8 :  +VAR+PASS    69 : PASS (4/60000) on thread: Src:C, Dst:C, Instance:3 
2248.22F8 :  +VAR+PASS    70 : PASS (4/60000) on thread: Src:C, Dst:C, Instance:5 
2248.22E0 :  +VAR+PASS    71 : PASS (4/60000) on thread: Src:C, Dst:C, Instance:2 
2248.22EC :  +VAR+PASS    72 : PASS (4/60000) on thread: Src:C, Dst:C, Instance:4 
2248.22D8 :  +VAR+PASS    73 : PASS (4/60000) on thread: Src:C, Dst:C, Instance:1 
2248.2364 :  +VAR+PASS    74 : PASS (4/60000) on thread: Src:C, Dst:C, Instance:19 
2248.2318 :  +VAR+PASS    75 : PASS (4/60000) on thread: Src:C, Dst:C, Instance:9 
2248.22FC :  +VAR+PASS    76 : PASS (4/60000) on thread: Src:C, Dst:C, Instance:6 
2248.2304 :  +VAR+PASS    77 : PASS (4/60000) on thread: Src:C, Dst:C, Instance:7 
2248.230C :  +VAR+PASS    78 : PASS (4/60000) on thread: Src:C, Dst:C, Instance:8 
2248.231C :  +VAR+PASS    79 : PASS (4/60000) on thread: Src:C, Dst:C, Instance:10 
2248.2324 :  +VAR+PASS    80 : PASS (4/60000) on thread: Src:C, Dst:C, Instance:11 
2248.2344 :  +VAR+PASS    81 : PASS (4/60000) on thread: Src:C, Dst:C, Instance:15 
2248.232C :  +VAR+PASS    82 : PASS (4/60000) on thread: Src:C, Dst:C, Instance:12 
2248.233C :  +VAR+PASS    83 : PASS (4/60000) on thread: Src:C, Dst:C, Instance:14 
2248.2334 :  +VAR+PASS    84 : PASS (4/60000) on thread: Src:C, Dst:C, Instance:13 
2248.236C :  +VAR+PASS    85 : PASS (4/60000) on thread: Src:C, Dst:C, Instance:20 
2248.2374 :  +VAR+PASS    86 : PASS (4/60000) on thread: Src:C, Dst:C, Instance:21 
2248.237C :  +VAR+PASS    87 : PASS (4/60000) on thread: Src:C, Dst:C, Instance:22 
2248.2384 :  +VAR+PASS    88 : PASS (4/60000) on thread: Src:C, Dst:C, Instance:23 
2248.2350 :  +VAR+PASS    89 : PASS (4/60000) on thread: Src:C, Dst:C, Instance:16 
2248.235C :  +VAR+PASS    90 : PASS (4/60000) on thread: Src:C, Dst:C, Instance:18 
2248.2358 :  +VAR+PASS    91 : PASS (4/60000) on thread: Src:C, Dst:C, Instance:17 
2248.22E0 :  +VAR+PASS    92 : PASS (5/60000) on thread: Src:C, Dst:C, Instance:2 
2248.22D8 :  +VAR+PASS    93 : PASS (5/60000) on thread: Src:C, Dst:C, Instance:1 
2248.22E8 :  +VAR+PASS    94 : PASS (5/60000) on thread: Src:C, Dst:C, Instance:3 
2248.22F8 :  +VAR+PASS    95 : PASS (5/60000) on thread: Src:C, Dst:C, Instance:5 
2248.22EC :  +VAR+PASS    96 : PASS (5/60000) on thread: Src:C, Dst:C, Instance:4 
2248.237C :  +VAR+PASS    97 : PASS (5/60000) on thread: Src:C, Dst:C, Instance:22 
2248.2350 :  +VAR+PASS    98 : PASS (5/60000) on thread: Src:C, Dst:C, Instance:16 
2248.2384 :  +VAR+PASS    99 : PASS (5/60000) on thread: Src:C, Dst:C, Instance:23 
2248.2374 :  +VAR+PASS   100 : PASS (5/60000) on thread: Src:C, Dst:C, Instance:21 
2248.236C :  +VAR+PASS   101 : PASS (5/60000) on thread: Src:C, Dst:C, Instance:20 
2248.2334 :  +VAR+PASS   102 : PASS (5/60000) on thread: Src:C, Dst:C, Instance:13 
2248.233C :  +VAR+PASS   103 : PASS (5/60000) on thread: Src:C, Dst:C, Instance:14 
2248.2344 :  +VAR+PASS   104 : PASS (5/60000) on thread: Src:C, Dst:C, Instance:15 
2248.232C :  +VAR+PASS   105 : PASS (5/60000) on thread: Src:C, Dst:C, Instance:12 
2248.2324 :  +VAR+PASS   106 : PASS (5/60000) on thread: Src:C, Dst:C, Instance:11 
2248.231C :  +VAR+PASS   107 : PASS (5/60000) on thread: Src:C, Dst:C, Instance:10 
2248.230C :  +VAR+PASS   108 : PASS (5/60000) on thread: Src:C, Dst:C, Instance:8 
2248.2304 :  +VAR+PASS   109 : PASS (5/60000) on thread: Src:C, Dst:C, Instance:7 
2248.22FC :  +VAR+PASS   110 : PASS (5/60000) on thread: Src:C, Dst:C, Instance:6 
2248.2318 :  +VAR+PASS   111 : PASS (5/60000) on thread: Src:C, Dst:C, Instance:9 
2248.2364 :  +VAR+PASS   112 : PASS (5/60000) on thread: Src:C, Dst:C, Instance:19 
2248.2358 :  +VAR+PASS   113 : PASS (5/60000) on thread: Src:C, Dst:C, Instance:17 
2248.235C :  +VAR+PASS   114 : PASS (5/60000) on thread: Src:C, Dst:C, Instance:18 
2248.22EC :  +VAR+PASS   115 : PASS (6/60000) on thread: Src:C, Dst:C, Instance:4 
2248.22E0 :  +VAR+PASS   116 : PASS (6/60000) on thread: Src:C, Dst:C, Instance:2 
2248.22E8 :  +VAR+PASS   117 : PASS (6/60000) on thread: Src:C, Dst:C, Instance:3 
2248.22F8 :  +VAR+PASS   118 : PASS (6/60000) on thread: Src:C, Dst:C, Instance:5 
2248.22D8 :  +VAR+PASS   119 : PASS (6/60000) on thread: Src:C, Dst:C, Instance:1 
2248.237C :  +VAR+PASS   120 : PASS (6/60000) on thread: Src:C, Dst:C, Instance:22 
2248.232C :  +VAR+PASS   121 : PASS (6/60000) on thread: Src:C, Dst:C, Instance:12 
2248.231C :  +VAR+PASS   122 : PASS (6/60000) on thread: Src:C, Dst:C, Instance:10 
2248.2324 :  +VAR+PASS   123 : PASS (6/60000) on thread: Src:C, Dst:C, Instance:11 
2248.2344 :  +VAR+PASS   124 : PASS (6/60000) on thread: Src:C, Dst:C, Instance:15 
2248.233C :  +VAR+PASS   125 : PASS (6/60000) on thread: Src:C, Dst:C, Instance:14 
2248.2334 :  +VAR+PASS   126 : PASS (6/60000) on thread: Src:C, Dst:C, Instance:13 
2248.236C :  +VAR+PASS   127 : PASS (6/60000) on thread: Src:C, Dst:C, Instance:20 
2248.2374 :  +VAR+PASS   128 : PASS (6/60000) on thread: Src:C, Dst:C, Instance:21 
2248.2384 :  +VAR+PASS   129 : PASS (6/60000) on thread: Src:C, Dst:C, Instance:23 
2248.2350 :  +VAR+PASS   130 : PASS (6/60000) on thread: Src:C, Dst:C, Instance:16 
2248.235C :  +VAR+PASS   131 : PASS (6/60000) on thread: Src:C, Dst:C, Instance:18 
2248.2358 :  +VAR+PASS   132 : PASS (6/60000) on thread: Src:C, Dst:C, Instance:17 
2248.2318 :  +VAR+PASS   133 : PASS (6/60000) on thread: Src:C, Dst:C, Instance:9 
2248.2364 :  +VAR+PASS   134 : PASS (6/60000) on thread: Src:C, Dst:C, Instance:19 
2248.2304 :  +VAR+PASS   135 : PASS (6/60000) on thread: Src:C, Dst:C, Instance:7 
2248.230C :  +VAR+PASS   136 : PASS (6/60000) on thread: Src:C, Dst:C, Instance:8 
2248.22FC :  +VAR+PASS   137 : PASS (6/60000) on thread: Src:C, Dst:C, Instance:6 
2248.22E0 :  +VAR+PASS   138 : PASS (7/60000) on thread: Src:C, Dst:C, Instance:2 
2248.22EC :  +VAR+PASS   139 : PASS (7/60000) on thread: Src:C, Dst:C, Instance:4 
2248.22F8 :  +VAR+PASS   140 : PASS (7/60000) on thread: Src:C, Dst:C, Instance:5 
2248.22E8 :  +VAR+PASS   141 : PASS (7/60000) on thread: Src:C, Dst:C, Instance:3 
2248.22D8 :  +VAR+PASS   142 : PASS (7/60000) on thread: Src:C, Dst:C, Instance:1 
2248.232C :  +VAR+PASS   143 : PASS (7/60000) on thread: Src:C, Dst:C, Instance:12 
2248.237C :  +VAR+PASS   144 : PASS (7/60000) on thread: Src:C, Dst:C, Instance:22 
2248.2304 :  +VAR+PASS   145 : PASS (7/60000) on thread: Src:C, Dst:C, Instance:7 
2248.230C :  +VAR+PASS   146 : PASS (7/60000) on thread: Src:C, Dst:C, Instance:8 
2248.2364 :  +VAR+PASS   147 : PASS (7/60000) on thread: Src:C, Dst:C, Instance:19 
2248.2318 :  +VAR+PASS   148 : PASS (7/60000) on thread: Src:C, Dst:C, Instance:9 
2248.2358 :  +VAR+PASS   149 : PASS (7/60000) on thread: Src:C, Dst:C, Instance:17 
2248.235C :  +VAR+PASS   150 : PASS (7/60000) on thread: Src:C, Dst:C, Instance:18 
2248.2350 :  +VAR+PASS   151 : PASS (7/60000) on thread: Src:C, Dst:C, Instance:16 
2248.2384 :  +VAR+PASS   152 : PASS (7/60000) on thread: Src:C, Dst:C, Instance:23 
2248.2374 :  +VAR+PASS   153 : PASS (7/60000) on thread: Src:C, Dst:C, Instance:21 
2248.236C :  +VAR+PASS   154 : PASS (7/60000) on thread: Src:C, Dst:C, Instance:20 
2248.2334 :  +VAR+PASS   155 : PASS (7/60000) on thread: Src:C, Dst:C, Instance:13 
2248.233C :  +VAR+PASS   156 : PASS (7/60000) on thread: Src:C, Dst:C, Instance:14 
2248.2324 :  +VAR+PASS   157 : PASS (7/60000) on thread: Src:C, Dst:C, Instance:11 
2248.231C :  +VAR+PASS   158 : PASS (7/60000) on thread: Src:C, Dst:C, Instance:10 
2248.2344 :  +VAR+PASS   159 : PASS (7/60000) on thread: Src:C, Dst:C, Instance:15 
2248.22E0 :  +VAR+PASS   160 : PASS (8/60000) on thread: Src:C, Dst:C, Instance:2 
2248.22EC :  +VAR+PASS   161 : PASS (8/60000) on thread: Src:C, Dst:C, Instance:4 
2248.22D8 :  +VAR+PASS   162 : PASS (8/60000) on thread: Src:C, Dst:C, Instance:1 
2248.22F8 :  +VAR+PASS   163 : PASS (8/60000) on thread: Src:C, Dst:C, Instance:5 
2248.22E8 :  +VAR+PASS   164 : PASS (8/60000) on thread: Src:C, Dst:C, Instance:3 
2248.235C :  +VAR+PASS   165 : PASS (8/60000) on thread: Src:C, Dst:C, Instance:18 
2248.2358 :  +VAR+PASS   166 : PASS (8/60000) on thread: Src:C, Dst:C, Instance:17 
2248.2350 :  +VAR+PASS   167 : PASS (8/60000) on thread: Src:C, Dst:C, Instance:16 
2248.2318 :  +VAR+PASS   168 : PASS (8/60000) on thread: Src:C, Dst:C, Instance:9 
2248.2364 :  +VAR+PASS   169 : PASS (8/60000) on thread: Src:C, Dst:C, Instance:19 
2248.230C :  +VAR+PASS   170 : PASS (8/60000) on thread: Src:C, Dst:C, Instance:8 
2248.2304 :  +VAR+PASS   171 : PASS (8/60000) on thread: Src:C, Dst:C, Instance:7 
2248.237C :  +VAR+PASS   172 : PASS (8/60000) on thread: Src:C, Dst:C, Instance:22 
2248.232C :  +VAR+PASS   173 : PASS (8/60000) on thread: Src:C, Dst:C, Instance:12 
2248.2324 :  +VAR+PASS   174 : PASS (8/60000) on thread: Src:C, Dst:C, Instance:11 
2248.231C :  +VAR+PASS   175 : PASS (8/60000) on thread: Src:C, Dst:C, Instance:10 
2248.2384 :  +VAR+PASS   176 : PASS (8/60000) on thread: Src:C, Dst:C, Instance:23 
2248.233C :  +VAR+PASS   177 : PASS (8/60000) on thread: Src:C, Dst:C, Instance:14 
2248.2334 :  +VAR+PASS   178 : PASS (8/60000) on thread: Src:C, Dst:C, Instance:13 
2248.236C :  +VAR+PASS   179 : PASS (8/60000) on thread: Src:C, Dst:C, Instance:20 
2248.2374 :  +VAR+PASS   180 : PASS (8/60000) on thread: Src:C, Dst:C, Instance:21 
2248.2344 :  +VAR+PASS   181 : PASS (8/60000) on thread: Src:C, Dst:C, Instance:15 
2248.1E10 :  +VAR+INFO   182 :  CTRL-C pressed, aborting test. 
2248.1E10 :  +VAR+WARN   182 :   Scenario aborted! 
2248.1E10 :  NTLOG REPORT -------------------------------------------------------
2248.1E10 :    Tests Total           0      | Variations Total         183
2248.1E10 :    ------------------------------------------------------------------
2248.1E10 :    Tests Passed          0   0% | Variations Passed        182  99%
2248.1E10 :    Tests Warned          0   0% | Variations Warned          1   0%
2248.1E10 :    Tests Failed sev3     0   0% | Variations Failed sev3     0   0%
2248.1E10 :    Tests Failed sev2     0   0% | Variations Failed sev2     0   0%
2248.1E10 :    Tests Failed sev1     0   0% | Variations Failed sev1     0   0%
2248.1E10 :    Tests Blocked         0   0% | Variations Blocked         0   0%
2248.1E10 :    Tests Aborted         0   0% | Variations Aborted         0   0%
2248.1E10 :  --------------------------------------------------------------------
****

**** NTLOG TERMINATED +DATE+05/13/2022  +TIME+15:48:23
