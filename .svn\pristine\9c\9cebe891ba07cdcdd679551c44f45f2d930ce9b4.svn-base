import PublicFuc
from openpyxl.styles import Font, Alignment
font = Font(name=u'宋体', bold = True)
align = Alignment(horizontal='left', vertical='center')

def Run(curpath, workBook, alignment):
    ws = workBook['常温老化']
    ws.alignment = alignment
    proBit(curpath, ws)
    PublicFuc.WriteReportTime(ws,'K',2)
    PublicFuc.WriteReportOperator(ws,'Q',2)

def SetTilteLine(ws, line, strTitle):
    #需要先把备注列给去除合并
    ws.unmerge_cells('W%d:X%d'%(line,line))
    ws.merge_cells('A%d:X%d'%(line,line))
    ws['A%d'%line].alignment = align
    ws['A%d'%line].font = font
    ws['A%d'%line] = strTitle

def proMarsCommon(curpath, worksheet, pattern, startLine, strTilte):
    caseDic = {}
    caseName = 'BurnInTest'
    PublicFuc.ReadMarsIniData(curpath, pattern, caseDic, caseName, 0)
    if 0 == len(caseDic):
        return startLine
    SetTilteLine(worksheet, startLine, strTilte)
    startLine += 1
    keyLst = ['pc_no','cap','result','cycle','wrdata','runtime','SmartInfo','wavg','wmax','wmin','ravg','rmax','rmin','wovertime','A5-A6']
    #GetNewMarsDic把ini读出来的dic按照keyLst转换为模板所需数据列表
    newDic = PublicFuc.GetNewMarsDic(caseDic, keyLst)
    colLst = ['C','B','E','U','H','I','J','K','L','M','N','O','P','Q','R','T']
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst)
    return startLine+len(newDic)

def ProBitCommon(curpath, worksheet, pattern, startLine, strTilte):
    smartKey = PublicFuc.commonSmartKey
    smartKeyNew = ['F1F2', 'SmartInfo', 'A5-A6']
    bitKey = ['pc_no', 'Cap', 'qa_err_msg','BitCycle','Duration']
    bitCol = ['C','B','E','U','H','J','I','K','T']
    bitDic = {}
    newKey = bitKey+smartKey
    PublicFuc.ReadQaIniData(curpath, pattern, bitDic, newKey, '', 1, 0)
    if 0 == len(bitDic):
        return startLine
    SetTilteLine(worksheet, startLine, strTilte)
    startLine += 1
    newDic = PublicFuc.GetNewBitDic(bitDic, len(bitKey), smartKey)
    newKey = bitKey+smartKeyNew
    startLine = PublicFuc.WriteData(worksheet, startLine, newDic, bitCol, newKey)
    return startLine

def proBit(curpath, ws):
    startLine = 6
    #Mars常温老化逻辑盘
    pattern = '.+\\\\Plan13\\\\T-SS-SS-A10\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    startLine = proMarsCommon(curpath, ws, pattern, startLine, 'Mars逻辑盘')
    #Mars常温老化物理盘
    pattern = '.+\\\\Plan14\\\\T-SS-SS-A12\\\\Mars\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    startLine = proMarsCommon(curpath, ws, pattern, startLine, 'Mars物理盘')

    #BIT常温老化逻辑盘
    pattern = '.+\\\\Plan63\\\\T-SS-SS-B12\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    startLine = ProBitCommon(curpath, ws, pattern, startLine, 'BurnInTest逻辑盘')
    #BIT常温老化物理盘
    pattern = '.+\\\\Plan64\\\\T-SS-SS-B13\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    startLine = ProBitCommon(curpath, ws, pattern, startLine, 'BurnInTest物理盘')