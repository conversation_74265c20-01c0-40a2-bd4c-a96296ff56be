import PublicFuc
from openpyxl.styles import  PatternFill,Alignment

alignment = Alignment(horizontal='center',vertical='center')
warnFill = PatternFill('solid', fgColor='FF0000')

empty_disk_cnt = 0
empty_max_time = 0
empty_min_time = 10000

full_disk_cnt = 0
full_max_time = 0
full_min_time = 10000

FULL_DISK_THRESHOLD = 1 #百分之1作为满盘的标志，百分比超过1%则认为是满盘。

def Run(curpath, workBook, alignment):
    ws = workBook['格式化']
    ws.alignment = alignment
    ProFormat(curpath, ws)
    PublicFuc.WriteReportTime(ws,'I',2)
    PublicFuc.WriteReportOperator(ws,'N',2)

def ProFormat(curpath, ws):
    #格式化
    #获取数据
    pattern = '.+\\\\format_report.ini$'
    dataKey = ['Cap_MB', 'Cap_MB_USED', 'SerialNo','FirmVersion','Fmt_Time','result']
    formatDataDic = {}
    PublicFuc.ReadQaIniData(curpath, pattern, formatDataDic, dataKey, '', 1, 0)

    #写详细数据
    newFormatDataDic = GetNewFormatDic(formatDataDic)
    dataWriteCol = ['C','D','E','F','G','H','I']
    startLine = 6
    WriteDataLocal(ws, startLine, newFormatDataDic, dataWriteCol)
    
    #写统计数据  
    global empty_disk_cnt
    global empty_max_time
    global empty_min_time

    global full_disk_cnt
    global full_max_time
    global full_min_time
    
    ws['B2'] = empty_disk_cnt
    if empty_min_time == 10000:
        empty_min_time = 0
    ws['C2'] = empty_min_time
    ws['D2'] = empty_max_time

    ws['B3'] = full_disk_cnt
    if full_min_time == 10000:
        full_min_time = 0
    ws['C3'] = full_min_time
    ws['D3'] = full_max_time

#1个样片只有一条记录  lineCnt兼容excel多行合并成一行的情况
def WriteDataLocal(worksheet, startLine, dataDic, colLst):
    curLine = startLine
    for key in dataDic:
        line = dataDic[key]
        for index,col in enumerate(colLst):
            try:
                if 0 == index:
                    #第一列是编号，直接填key
                    worksheet['%s%d'%(col, curLine)] = key
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
                    if index == len(colLst)-1 and line[index-1] != 'PASS':
                        worksheet['%s%d'%(col, curLine)].fill = warnFill
                worksheet['%s%d'%(col, curLine)].alignment = alignment
            #合并的单元格只能写一次，需要捕获异常
            except(AttributeError):
                continue
        curLine += 1
   

def IsEmptyDisk(_totalCap,_usedCap):
    cap_percent = int(_usedCap)/int(_totalCap)
    if cap_percent*100 > FULL_DISK_THRESHOLD:
        return False    
    return True

def GetNewFormatDic(_rawDic):
    global empty_disk_cnt
    global empty_max_time
    global empty_min_time

    global full_disk_cnt
    global full_max_time
    global full_min_time
    newDic = {}
    for sampleNo in _rawDic:
        curLine = _rawDic[sampleNo][0]       
        if(len(curLine) >= 6):
            if IsEmptyDisk(curLine[0],curLine[1]):
                empty_disk_cnt += 1              
                if int(curLine[4]) > empty_max_time:
                    empty_max_time = int(curLine[4])               
                if int(curLine[4]) < empty_min_time:
                    empty_min_time = int(curLine[4])
            else:
                full_disk_cnt += 1
                if int(curLine[4]) > full_max_time:
                    full_max_time = int(curLine[4])                
                if int(curLine[4]) < full_min_time:
                    full_min_time = int(curLine[4])

        capUsedPercent = int(int(curLine[1])*100/int(curLine[0]))
        capUsedStr = str(capUsedPercent) + '%'
        capTotalInGB = int(int(curLine[0])/1024)
        capTotalStr = str(capTotalInGB) + 'GB'

        newDic[sampleNo] = []
        lineNew = []
        lineNew.append(capTotalStr)
        lineNew.append(capUsedStr)
        lineNew.append(curLine[2])
        lineNew.append(curLine[3])
        lineNew.append(int(curLine[4]))
        strResult = curLine[-1]
        if strResult.upper() == 'PASS':
            strResult = 'PASS'
        else:
            strResult = 'FAIL'
        lineNew.append(strResult)
        newDic[sampleNo] = lineNew

    return newDic;

def ProBitCase(curpath, ws):
    bitCol = ['C','B','E','R','H','J','I','K','Q']
    #BurnInTest_01
    pattern = '.+\\\\Plan70\\\\T-SS-SS-C39\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    startLine = 43
    ProBitCommon(curpath, ws, pattern, bitCol, startLine)
    #BurnInTest_02
    pattern = '.+\\\\Plan71\\\\T-SS-SS-C40\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    startLine = 49
    ProBitCommon(curpath, ws, pattern, bitCol, startLine)
    #BurnInTest_03
    pattern = '.+\\\\Plan72\\\\T-SS-SS-C41\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    startLine = 55
    ProBitCommon(curpath, ws, pattern, bitCol, startLine)
    #BurnInTest_04
    pattern = '.+\\\\Plan73\\\\T-SS-SS-C42\\\\BurnInTest\\\\\d{14}\\\\report.ini$'
    startLine = 61
    ProBitCommon(curpath, ws, pattern, bitCol, startLine)