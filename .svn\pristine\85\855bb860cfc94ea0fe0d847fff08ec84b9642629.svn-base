#pragma once
#include <vector>
using namespace std;


typedef struct _DEVICE_INFO
{
	char letter;
	BYTE bPortNo;
	BYTE bArrID[2][6];
	char szDeviceID[128];
	char szLocation[128];
}DEVICE_INFO;

class CDeviceMgr
{
public:
	CDeviceMgr(void);
	~CDeviceMgr(void);

public:
	void Init(CWnd* _pMainWnd);
	void RefreshAllDevice(BYTE _bType = 0);

private:
	vector<DEVICE_INFO> m_vValidDevice;
	CWnd* m_pMainWnd;
	BYTE m_bPreType;
};

