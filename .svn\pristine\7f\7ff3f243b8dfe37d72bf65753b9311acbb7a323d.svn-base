from openpyxl.utils import get_column_letter
import configparser
import csv,time
import os,re
import openpyxl
import Public
from openpyxl.utils import get_column_letter,column_index_from_string
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors
from datetime import datetime,timedelta
from xml.dom.minidom import parse,parseString

from lxml import etree

def Run(exportFile, xmlPath):
    if not os.path.exists(exportFile):
        return
    wb = openpyxl.load_workbook(filename = exportFile,data_only = True) #源excel

    for productName in Public.g_productList:       
        xmlFileName = xmlPath + "\\plan_" + productName+".xml"
        if not os.path.exists(xmlFileName):
            continue

        ws = wb[productName]
        xml_file = etree.parse(xmlFileName)
        root_node = xml_file.getroot()
        planInfoNode = root_node

        for planNode in planInfoNode:
            for caseNode in planNode:
                #获取case的名称
                caseID = caseNode.attrib['id']
                caseUrl = findUrlFromExportExcel(caseID,ws)
                caseNode.set('url',caseUrl)

        #注，此处用了method="html"会保留空白的节点，但是得不到xml的声明行。二者只能取其一。
        xml_file.write(xmlFileName, encoding="gb2312", xml_declaration=True, pretty_print=True,standalone = True)#,method="html"

#依据caseID去Export文件查找URL链接
def findUrlFromExportExcel(caseid,ws):
    url = ''
    rowCnt = ws.max_row
    for rowNo in range(1,rowCnt+1):
        if caseid == ws['%s%d'%('A', rowNo)].value:
            if ws['%s%d'%('B', rowNo)].value != None:
                url = ws['%s%d'%('B', rowNo)].value
            break
    return url