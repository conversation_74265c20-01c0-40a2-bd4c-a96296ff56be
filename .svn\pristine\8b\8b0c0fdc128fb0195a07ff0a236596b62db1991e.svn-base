#ifndef _QCA_BASE64_H
#define _QCA_BASE64_H

/**
* @fn int base64_encode(unsigned char *in, int length, char *out)
* @brief base64 encode.
* @param in string that want to encode.
* @param length in string length
* @param out string that after encode
* @return 0 success, -1 failed
*/
int base64_encode(unsigned char *in, int length, char *out);

/**
* @fn int base64_decode(char* in, unsigned char *out)
* @brief base64 decode.
* @param in string that want to decode.
* @param out string that after decode
* @return 0 success, -1 failed
*/
int base64_decode(char* in, unsigned char *out);


#endif
