import PublicFuc
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  Pat<PERSON><PERSON>ill,Alignment,Border,Side,colors,Font

dicPfm = {}

def Run(curpath, workBook, alignment):
    ws = workBook['竞品SD性能']
    ws.alignment = alignment
    ProData(curpath, ws)
    DrawSumTable(ws)
    ProSumData(curpath, ws)
    ProPicture(workBook)
    PublicFuc.WriteReportTime(ws,'D',2)
    PublicFuc.WriteReportOperator(ws,'H',2)

def ProPicture(workBook):
    ws = workBook['HDBench截图']
    DrawPicTable(ws,1,3,7)
    #['HDB_', 'CDM_', 'H2_', 'ATTO_','HDR', 'HDW','IMT_','IMT_FULL_','MH2_']
    WriteOneTestCasePicture(ws,'HDB_',2,3)
    ws = workBook['CDM截图']
    DrawPicTable(ws,1,3,7)
    WriteOneTestCasePicture(ws,'CDM_',2,3)
    ws = workBook['H2截图']
    DrawPicTable(ws,1,3,7)
    WriteOneTestCasePicture(ws,'H2_',2,3)
    ws = workBook['ATTO截图']
    DrawPicTable(ws,1,3,7)
    WriteOneTestCasePicture(ws,'ATTO_',2,3)
    ws = workBook['HDTune截图']
    DrawPicTable(ws,1,4,13)
    WriteOneTestCasePicture(ws,'HDR',2,4)
    WriteOneTestCasePicture(ws,'HDW',8,4)
    ws = workBook['Iometer截图']
    DrawPicTable(ws,1,4,13)
    WriteOneTestCasePicture(ws,'IMT_',2,4)
    WriteOneTestCasePicture(ws,'IMT_FULL_',8,4)
    ws = workBook['Mars截图']
    DrawPicTable(ws,1,3,7)
    WriteOneTestCasePicture(ws,'MH2_',2,3)
    
def ProData(curpath, worksheet):
    #hdbench
    hdbKey = ['Cap','Read','Write','RRead','RWrite']
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\HDBench_1\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDB_1', hdbKey, 'HDBench.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\HDBench_2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDB_2', hdbKey, 'HDBench.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\HDBench_3\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDB_3', hdbKey, 'HDBench.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\HDBench_4\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDB_4', hdbKey, 'HDBench.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\HDBench_5\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDB_5', hdbKey, 'HDBench.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\HDBench_6\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDB_6', hdbKey, 'HDBench.bmp',0)
    #ATTO
    attoKey = ['Cap','64 MB_Write','64 MB_Read']
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\ATTO_1\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'ATTO_1', attoKey, 'ATTO4_0_MBps.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\ATTO_2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'ATTO_2', attoKey, 'ATTO4_0_MBps.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\ATTO_3\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'ATTO_3', attoKey, 'ATTO4_0_MBps.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\ATTO_4\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'ATTO_4', attoKey, 'ATTO4_0_MBps.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\ATTO_5\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'ATTO_5', attoKey, 'ATTO4_0_MBps.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\ATTO_6\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'ATTO_6', attoKey, 'ATTO4_0_MBps.bmp',0)
    #cdm
    cdmKey = ['Cap','SeqQ32T1_Read','SeqQ32T1_Write','4KQ32T1_Read','4KQ32T1_Write','Seq_Read','Seq_Write','4K_Read','4K_Write']
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\CDM_1\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'CDM_1', cdmKey, 'CDM.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\CDM_2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'CDM_2', cdmKey, 'CDM.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\CDM_3\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'CDM_3', cdmKey, 'CDM.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\CDM_4\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'CDM_4', cdmKey, 'CDM.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\CDM_5\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'CDM_5', cdmKey, 'CDM.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\CDM_6\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'CDM_6', cdmKey, 'CDM.bmp',0)
    #h2testw
    h2Key = ['Cap','write speed','read speed']
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\H2testw_1\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'H2_1', h2Key, 'H2.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\H2testw_2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'H2_2', h2Key, 'H2.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\H2testw_3\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'H2_3', h2Key, 'H2.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\H2testw_4\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'H2_4', h2Key, 'H2.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\H2testw_5\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'H2_5', h2Key, 'H2.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\H2testw_6\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'H2_6', h2Key, 'H2.bmp',0)
    #hdtune
    hdtuneKey =  ['Cap','min spped','max spped','avg spped','acess time','sundden trans rate','cpu usage']
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\HDTune_Read_1\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDR1', hdtuneKey, 'HDTune.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\HDTune_Write_1\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDW1', hdtuneKey, 'HDTune.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\HDTune_Read_2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDR2', hdtuneKey, 'HDTune.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\HDTune_Write_2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDW2', hdtuneKey, 'HDTune.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\HDTune_Read_3\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDR3', hdtuneKey, 'HDTune.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\HDTune_Write_3\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDW3', hdtuneKey, 'HDTune.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\HDTune_Read_4\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDR4', hdtuneKey, 'HDTune.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\HDTune_Write_4\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDW4', hdtuneKey, 'HDTune.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\HDTune_Read_5\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDR5', hdtuneKey, 'HDTune.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\HDTune_Write_5\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDW5', hdtuneKey, 'HDTune.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\HDTune_Read_6\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDR6', hdtuneKey, 'HDTune.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\HDTune_Write_6\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'HDW6', hdtuneKey, 'HDTune.bmp',0)
    #iometer
    imtKey = ['Cap','512K_SEQ_100R_MiBps','512B_SEQ_100W_MiBps','4KALG_Random_100R_MiBps','4KALG_Random_100W_MiBps','512K_SEQ_100R_Iops','512B_SEQ_100W_Iops','4KALG_Random_100R_Iops','4KALG_Random_100W_Iops']
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\IOmeter_1G_1\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'IMT_1', imtKey, 'IOmeter.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\IOmeter_1G_2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'IMT_2', imtKey, 'IOmeter.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\IOmeter_1G_3\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'IMT_3', imtKey, 'IOmeter.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\IOmeter_1G_4\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'IMT_4', imtKey, 'IOmeter.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\IOmeter_1G_5\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'IMT_5', imtKey, 'IOmeter.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\IOmeter_1G_6\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'IMT_6', imtKey, 'IOmeter.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\IOmeter_FullCard_1\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'IMT_FULL_1', imtKey, 'IOmeter.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\IOmeter_FullCard_2\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'IMT_FULL_2', imtKey, 'IOmeter.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\IOmeter_FullCard_3\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'IMT_FULL_3', imtKey, 'IOmeter.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\IOmeter_FullCard_4\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'IMT_FULL_4', imtKey, 'IOmeter.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\IOmeter_FullCard_5\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'IMT_FULL_5', imtKey, 'IOmeter.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\IOmeter_FullCard_6\\\\\d{14}\\\\report.ini$'
    PublicFuc.ReadQaIniData(curpath, pattern, dicPfm, 'IMT_FULL_6', imtKey, 'IOmeter.bmp',0)
    #mars_h2
    marsH2Key = ['Cap','MIN_WRITE_VEL','MIN_READ_VEL']
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\Mars_H2_1\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    PublicFuc.ReadMarsIniData(curpath, pattern, dicPfm, 'AT_H2', 'MH2_1', marsH2Key, 'Mars.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\Mars_H2_2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    PublicFuc.ReadMarsIniData(curpath, pattern, dicPfm, 'AT_H2', 'MH2_2', marsH2Key, 'Mars.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\Mars_H2_3\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    PublicFuc.ReadMarsIniData(curpath, pattern, dicPfm, 'AT_H2', 'MH2_3', marsH2Key, 'Mars.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\Mars_H2_4\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    PublicFuc.ReadMarsIniData(curpath, pattern, dicPfm, 'AT_H2', 'MH2_4', marsH2Key, 'Mars.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\Mars_H2_5\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    PublicFuc.ReadMarsIniData(curpath, pattern, dicPfm, 'AT_H2', 'MH2_5', marsH2Key, 'Mars.bmp',0)
    pattern = '.+\\\\Plan21\\\\T_GE_SD_C52\\\\Mars_H2_6\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    PublicFuc.ReadMarsIniData(curpath, pattern, dicPfm, 'AT_H2', 'MH2_6', marsH2Key, 'Mars.bmp',0)
    

def WriteData(worksheet, startLine, dataDic, caseKey, colNum, lineCnt, disk=''):
    if caseKey not in dataDic:
        return
    col = get_column_letter(colNum)
    curLine = startLine
    line = dataDic[caseKey]
    if '' != disk:
        worksheet['%s4'%col] = '%s'%(disk)
    for data in line[1:1+lineCnt]:
        worksheet['%s%d'%(col, curLine)] = data
        curLine += 1

#绘制汇总报告
def DrawSumTable(ws):
    STARTLINE = 3
    STARTCOL = 13
    keyLst = sorted(dicPfm.keys(), reverse=False)
    sampleCnt = len(keyLst)
    if sampleCnt <= 1:
        return True  #第一条数据不需要绘制，模板中有
    
    #填写表头内容
    for idx in range(sampleCnt-1):
        col = STARTCOL + idx*6
        ws['%s%d'%(get_column_letter(col), 5)] = 'Clean1'
        ws['%s%d'%(get_column_letter(col+1), 5)] = 'Clean2'
        ws['%s%d'%(get_column_letter(col+2), 5)] = 'Clean3'
        ws['%s%d'%(get_column_letter(col+3), 5)] = 'Dirty1'
        ws['%s%d'%(get_column_letter(col+4), 5)] = 'Dirty2'
        ws['%s%d'%(get_column_letter(col+5), 5)] = 'Dirty3'
        for i in range(6):
            ws['%s%d'%(get_column_letter(col+i), STARTLINE)] = 'SanDisk'
        

    #填充表格标题格式
    ce=ws['%s%d'%(get_column_letter(7), STARTLINE)]
    tpfill = ce.fill
    titleFill = PatternFill('solid')
    titleFill.bgColor = tpfill.bgColor
    titleFill.fgColor = tpfill.fgColor

    titleFont=Font('Arial',size=11,color=colors.BLACK,bold=True,italic=False)
    tempFont = ce.font
    titleFont.size = tempFont.size
    titleFont.color = tempFont.color
    titleFont.charset = tempFont.charset
    titleFont.family = tempFont.family

    sampleFont = Font('Arial',size=9,color=colors.BLACK,bold=True,italic=False)
    align = Alignment(horizontal='center',vertical='center',wrap_text=True)
    contentFont = Font('Arial',size=9,color=colors.BLACK,bold=False,italic=False)
    for rowNo in range(STARTLINE,38):
        for idx in range(sampleCnt-1): 
            col = STARTCOL + idx*6
            for colIdx in range(6):
                ws['%s%d'%(get_column_letter(col+colIdx), rowNo)].alignment = PublicFuc.alignment
                ws['%s%d'%(get_column_letter(col+colIdx), rowNo)].border = PublicFuc.my_border('thin', 'thin', 'thin', 'thin')
                #填充表头颜色
                if rowNo >= STARTLINE and rowNo <= STARTLINE+2:
                    if rowNo == STARTLINE + 1:
                        ws['%s%d'%(get_column_letter(col+colIdx), rowNo)].fill = titleFill
                        ws['%s%d'%(get_column_letter(col+colIdx), rowNo)].font = sampleFont
                        ws['%s%d'%(get_column_letter(col+colIdx), rowNo)].alignment = align
                    else:
                        ws['%s%d'%(get_column_letter(col+colIdx), rowNo)].fill = titleFill
                        ws['%s%d'%(get_column_letter(col+colIdx), rowNo)].font = titleFont
                else:
                    #ws['%s%d'%(get_column_letter(col+colIdx), rowNo)].fill = titleFill
                    ws['%s%d'%(get_column_letter(col+colIdx), rowNo)].font = contentFont

#绘制汇总报告
def DrawPicTable(ws,_startRow,_startCol,_rowcnt):
    STARTLINE = _startRow
    STARTCOL = _startCol
    keyLst = sorted(dicPfm.keys(), reverse=False)
    sampleCnt = len(keyLst)
    if sampleCnt <= 1:
        return True  #第一条数据不需要绘制，模板中有
    
    #填写表头内容
    for idx,disk in enumerate(keyLst):
        ws['%s%d'%(get_column_letter(idx+STARTCOL), STARTLINE)] = disk       

    #填充表格标题格式
    sampleFont = Font('Arial',size=12,color=colors.BLACK,bold=True,italic=False)
    align = Alignment(horizontal='center',vertical='center',wrap_text=True)
    #contentFont = Font('Arial',size=9,color=colors.BLACK,bold=False,italic=False)
    for colNo in range(STARTCOL,STARTCOL + sampleCnt): 
        ws.column_dimensions['%s'%(get_column_letter(colNo))].width = 36.38
        for rowNo in range(STARTLINE,STARTLINE+_rowcnt):      
            ws['%s%d'%(get_column_letter(colNo), rowNo)].alignment = PublicFuc.alignment
            ws['%s%d'%(get_column_letter(colNo), rowNo)].border = PublicFuc.my_border('thin', 'thin', 'thin', 'thin')           
            #填充表头颜色 
            if rowNo == STARTLINE:
                    #ws['%s%d'%(get_column_letter(col+colIdx), rowNo)].fill = titleFill
                    ws['%s%d'%(get_column_letter(colNo), rowNo)].font = sampleFont
                    ws['%s%d'%(get_column_letter(colNo), rowNo)].alignment = align               

def ProSumData(curpath, ws):
    #cleanLst = ['HDB_1','HDB_2','HDB_3', 'ATTO_1', 'ATTO_2', 'ATTO_3','CDM_1','CDM_2','CDM_3', 'H2_1', 'H2_2', 'H2_3', 'HDR1', 'HDR2', 'HDR3', 'HDW1', 'HDW2', 'HDW3', 'IMT_1', 'IMT_2', 'IMT_3','IMT_FULL_1','IMT_FULL_1','IMT_FULL_1','MH2_1','MH2_2','MH2_3']
    #dirtyLst = ['HDB_4','HDB_5','HDB_6', 'ATTO_4', 'ATTO_5', 'ATTO_6','CDM_4','CDM_5','CDM_6', 'H2_4', 'H2_5', 'H2_6', 'HDR4', 'HDR5', 'HDR6', 'HDW4', 'HDW5', 'HDW6', 'IMT_4', 'IMT_5', 'IMT_6','IMT_FULL_4','IMT_FULL_5','IMT_FULL_6','MH2_4','MH2_5','MH2_6']
    toolLst = ['HDB_','CDM_', 'H2_','ATTO_','HDR', 'HDW','IMT_','IMT_FULL_','MH2_']
    lineLst = [4, 8, 2, 2, 3, 3, 4, 4, 2]
    STARTCOL = 7
    keyLst = sorted(dicPfm.keys(), reverse=False)
    diskIdx = 0
    for disk in keyLst:
        col = diskIdx*6 + STARTCOL
        for i in range(1,7):
            startLine = 6
            for idx,caseKey in enumerate(toolLst):
                toolKey = '%s%d'%(caseKey,i)
                WriteData(ws, startLine, dicPfm[disk], toolKey, col, lineLst[idx], disk)
                startLine += lineLst[idx]
            col += 1
        diskIdx += 1

#写一种测试Case的图片内容
def WriteOneTestCasePicture(ws,caseKeyNamePrefix,startRow,startCol):
    #cleanLst = ['HDB_1','HDB_2','HDB_3', 'ATTO_1', 'ATTO_2', 'ATTO_3','CDM_1','CDM_2','CDM_3', 'H2_1', 'H2_2', 'H2_3', 'HDR1', 'HDR2', 'HDR3', 'HDW1', 'HDW2', 'HDW3', 'IMT_1', 'IMT_2', 'IMT_3','IMT_FULL_1','IMT_FULL_1','IMT_FULL_1','MH2_1','MH2_2','MH2_3']
    #dirtyLst = ['HDB_4','HDB_5','HDB_6', 'ATTO_4', 'ATTO_5', 'ATTO_6','CDM_4','CDM_5','CDM_6', 'H2_4', 'H2_5', 'H2_6', 'HDR4', 'HDR5', 'HDR6', 'HDW4', 'HDW5', 'HDW6', 'IMT_4', 'IMT_5', 'IMT_6','IMT_FULL_4','IMT_FULL_5','IMT_FULL_6','MH2_4','MH2_5','MH2_6']
    #toolLst = ['HDB_', 'CDM_', 'H2_', 'ATTO_','HDR', 'HDW','IMT_','IMT_FULL_','MH2_']
    imageCol = startCol
    imageLine = startRow
    keyLst = sorted(dicPfm.keys(), reverse=False)
    for disk in keyLst:
        #写图片数据
        imageLine = startRow
        for i in range(6):
            toolKey = '%s%d'%(caseKeyNamePrefix,i+1)
            if toolKey not in dicPfm[disk]:
                imageLine += 1
                continue
            line = dicPfm[disk][toolKey]
      
            # 列表最后一项是图片路径
            if '' != line[-1]:
                img = Image(line[-1])
                img.width = 260
                img.height = 240
                ws.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))     
            imageLine += 1
        imageCol += 1
       