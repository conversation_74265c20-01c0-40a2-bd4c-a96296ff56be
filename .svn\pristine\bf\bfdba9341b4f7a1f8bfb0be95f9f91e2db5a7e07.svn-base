#pragma once
#include <Windows.h>



template<typename T>
class CSingletonT
{
private:
	class CGarbo   
	{
	public:
		CGarbo()
		{
			::InitializeCriticalSection(&CSingletonT::m_cs);
		}
		~CGarbo()
		{
			::DeleteCriticalSection(&CSingletonT::m_cs);
			if(CSingletonT::m_pInstance)
				delete CSingletonT::m_pInstance;
		}
	};

public:
	// thread protection
	class CMyLock
	{
	public:
		CMyLock()
		{
			EnterCriticalSection(&CSingletonT::m_cs);
		}
		~CMyLock()
		{
			::LeaveCriticalSection(&CSingletonT::m_cs);
		}
	};

public:
	static T* GetInstance()
	{
		if (NULL == m_pInstance) 
		{
			// Force instantiate m_garbo
			m_garbo;

			// Preventing instance from creating success
			CMyLock lock;

			if (NULL == m_pInstance)
			{
				m_pInstance = new T();
			}
		}

		return m_pInstance;
	}

private:
	static T* m_pInstance;
	static CGarbo m_garbo;
	static CRITICAL_SECTION m_cs;
};

template<typename T>
T* CSingletonT<T>::m_pInstance = NULL;

template<typename T>
typename CSingletonT<T>::CGarbo CSingletonT<T>::m_garbo; 

template<typename T>
CRITICAL_SECTION CSingletonT<T>::m_cs;

