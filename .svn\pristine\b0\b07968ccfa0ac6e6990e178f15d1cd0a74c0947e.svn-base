import re,os
from collections import defaultdict
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors,Font
from openpyxl.utils import get_column_letter

def parse_perf_idle_dirty(log_path):
    dicIdleDirty = {}
    try:
        with open(log_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if 'G left' in line and not line.startswith('Item'):
                    try:
                        # 使用正则表达式精确匹配数字+G left模式
                        import re
                        match = re.search(r'(\d+G) left', line)
                        if not match:
                            print(f"第 {line_num} 行格式不匹配，跳过处理")
                            continue
                            
                        key = match.group(1) + ' left'  # 重构标准key格式
                        
                        # 分割右侧数值部分
                        value_part = line.split(match.group(0))[-1]
                        parts = value_part.strip().split()
                        
                        if len(parts) < 6:
                            print(f"第 {line_num} 行数值不足，跳过处理")
                            continue
                            
                        # 数值转换
                        values = []
                        for x in parts[:6]:
                            try:
                                values.append(float(x) if '.' in x else int(x))
                            except ValueError:
                                values.append(None)
                                print(f"异常值在第 {line_num} 行: {x}")
                                
                        dicIdleDirty[key] = values
                        print(f"成功解析 {key}: {values}")
                        
                    except Exception as e:
                        print(f"行处理错误（第 {line_num} 行）: {str(e)}")
                        continue
                        
        return dicIdleDirty
    
    except FileNotFoundError:
        print(f"文件未找到: {log_path}")
        return {}
    except Exception as e:
        print(f"未知错误: {str(e)}")
        return {}

def GetIdleDirtyData(path:str):
    dataDic = {}
    lognamePass = 'Passed verify performance proxima check_PV_when_idle_in_dirty.log'
    lognameFail = 'Failed verify performance proxima check_PV_when_idle_in_dirty.log'
    lognameNormal ='verify performance proxima check_PV_when_idle_in_dirty.log'
    logPath = os.path.join(path,lognamePass)
    if not os.path.exists(logPath):
        logPath = os.path.join(path,lognameFail)
    if not os.path.exists(logPath):  # Fixed indentation
        logPath = os.path.join(path,lognameNormal)
    if not os.path.exists(logPath):
        print("log not exist")
        return {}
    dataDic = parse_perf_idle_dirty(logPath)
        
    return dataDic

#写数据
def WriteIdleDirtyData(wb,strType:str,lData:dict,rgb="00000000",alignment=Alignment(horizontal='center',vertical='center')):
    dataDicRows = {'x1':3,'x2':17,'x4':31}
    strWsName= 'Idle_in_dirty'
    ws=wb[strWsName]
    logdataStartRow = dataDicRows[strType]
    for Index,Item in enumerate(lData):        
        ws['%s%d'%(get_column_letter(1),logdataStartRow)] = Item
        for dataIdx,data in enumerate(lData[Item]):
            nStartCol = 2
            
            ws['%s%d'%(get_column_letter(nStartCol+dataIdx),logdataStartRow)] = data
            ws['%s%d'%(get_column_letter(nStartCol+dataIdx), logdataStartRow)].alignment=alignment
            ws['%s%d'%(get_column_letter(nStartCol+dataIdx), logdataStartRow)].font=rgb
        logdataStartRow+=1


if __name__ == "__main__":
    # 使用原始路径处理Windows反斜杠
    log_file = r"DV8803_E09T_MP020001_Release_x2_128GB_20250430\UFS\PV-COM45-SS2-A32_1746509521\Passed verify PV Performance ufs_performance_dirty_slc_idle_gc_perf.log"
    
    print(">>> 开始解析 <<<")
    result = parse_perf_idle_dirty(log_file)
    
    print("\n解析结果示例：")
    for key in sorted(result.keys(), key=lambda x: int(x.rstrip('G')), reverse=True):
        print(f"{key}: {result[key]}")