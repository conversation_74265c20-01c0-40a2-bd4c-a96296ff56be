﻿#pragma once

#include <TypeDef.h>

namespace NVME_PUBLIC_DEF
{
	/* NVME definition */
	static const U16 NVME_STORPORT_DRIVER = 0xE000;

	/* the following are the NVME driver private IOCTL definitions */
	static const S32 NVME_PASS_THROUGH_SRB_IO_CODE = CTL_CODE(NVME_STORPORT_DRIVER, 0x800, METHOD_BUFFERED, FILE_ANY_ACCESS);

	static const S32 NVME_RESET_DEVICE = CTL_CODE(NVME_STORPORT_DRIVER, 0x801, METHOD_BUFFERED, FILE_ANY_ACCESS);

	static const S32 NVME_HOT_ADD_NAMESPACE = CTL_CODE(NVME_STORPORT_DRIVER, 0x802, METHOD_BUFFERED, FILE_ANY_ACCESS);

	static const S32 NVME_HOT_REMOVE_NAMESPACE = CTL_CODE(NVME_STORPORT_DRIVER, 0x803, METH<PERSON>_BUFFERED, FILE_ANY_ACCESS);

	static const S32 DFP_RECEIVE_DRIVE_DATA = CTL_CODE(IOCTL_DISK_BASE, 0x0022, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS); 

	static const U32 IDE_ATA_IDENTIFY = 0xEC;

	static const U32 IOCTL_SCSI_MINIPORT_IDENTIFY = ((FILE_DEVICE_SCSI << 16) + 0x0501);


	static const char NVME_SIG_STR[]   =       "NvmeMini";
	static const U32  NVME_SIG_STR_LEN = 8;
	static const char SCSI_SIG_STR[]   =          "SCSIDISK";
	static const U32 SCSI_SIG_STR_LEN = 8;
	static const U32 NVME_NO_DATA_TX       = 0; /* No data transfer involved */
	static const U32 NVME_FROM_HOST_TO_DEV = 1; /* Transfer data from host to device */
	static const U32 NVME_FROM_DEV_TO_HOST = 2; /* Transfer data from device to host */
	static const U32 NVME_BI_DIRECTION     = 3; /* Tx data from host to device and back */

	static const U32 NVME_IOCTL_VENDOR_SPECIFIC_DW_SIZE = 6;  /* Vendor sp qualifier (DWORDs) */
	static const U32 NVME_IOCTL_CMD_DW_SIZE             = 16; /* NVMe cmd entry size (DWORDs) */
	static const U32 NVME_IOCTL_COMPLETE_DW_SIZE        = 4;  /* NVMe cpl entry size (DWORDs) */


	/*******************************************************************************
	 * NVMe Pass Through IOCTL return codes from Miniport driver.
	 *
	 * These numbers are returned in ReturnCode of SRB_IO_CONTROL structure.
	 * When driver receives an IOCTL request, before issuing the associated NVMe
	 * command, it examines all fields of NVME_PASS_THROUGH_IOCTL structure.
	 *
	 * Once certain error found, the error is interpreted and noted in ReturnCode
	 * of SRB_IO_CONTROL structure. User applications can find out the specific
	 * status after driver processes the request.
	 ******************************************************************************/
	enum _IOCTL_STATUS
	{
		 NVME_IOCTL_SUCCESS,
		 NVME_IOCTL_INTERNAL_ERROR,
		 NVME_IOCTL_INVALID_IOCTL_CODE,
		 NVME_IOCTL_INVALID_SIGNATURE,
		 NVME_IOCTL_INSUFFICIENT_IN_BUFFER,
		 NVME_IOCTL_INSUFFICIENT_OUT_BUFFER,
		 NVME_IOCTL_UNSUPPORTED_ADMIN_CMD,
		 NVME_IOCTL_UNSUPPORTED_NVM_CMD,
		 NVME_IOCTL_UNSUPPORTED_OPERATION,
		 NVME_IOCTL_INVALID_ADMIN_VENDOR_SPECIFIC_OPCODE,
		 NVME_IOCTL_INVALID_NVM_VENDOR_SPECIFIC_OPCODE,
		 NVME_IOCTL_ADMIN_VENDOR_SPECIFIC_NOT_SUPPORTED,  // i.e., AVSCC = 0
		 NVME_IOCTL_NVM_VENDOR_SPECIFIC_NOT_SUPPORTED,    // i.e., NVSCC = 0
		 NVME_IOCTL_INVALID_DIRECTION_SPECIFIED,          // Direction > 3
		 NVME_IOCTL_INVALID_META_BUFFER_LENGTH,
		 NVME_IOCTL_PRP_TRANSLATION_ERROR,
		 NVME_IOCTL_INVALID_PATH_TARGET_ID,
		 NVME_IOCTL_FORMAT_NVM_PENDING,      // Only one Format NVM at a time
		 NVME_IOCTL_FORMAT_NVM_FAILED,
		 NVME_IOCTL_INVALID_NAMESPACE_ID,
		 NVME_IOCTL_MAX_SSD_NAMESPACES_REACHED,
		 NVME_IOCTL_ZERO_DATA_TX_LENGTH_ERROR,
		 NVME_IOCTL_MAX_AER_REACHED
	};

#pragma pack(1)
	/******************************************************************************
	 * NVMe Pass Through IOCTL data structure.
	 *
	 * This structure contains WDK defined SRB_IO_CONTROL structure, 64-byte
	 * NVMe command entry and 16-byte completion entry, and other important fields
	 * that driver needs to reference when processing the requests.
	 *
	 * User applications need to allocate proper size of buffer(s) and populate the
	 * fields to ensure the requests are being processed correctly after issuing.
	 ******************************************************************************/
	typedef struct _NVME_PASS_THROUGH_IOCTL
	{
		/* WDK defined SRB_IO_CONTROL structure */
		SRB_IO_CONTROL SrbIoCtrl;

		/* Vendor unique qualifiers for vendor unique commands */
		ULONG          VendorSpecific[NVME_IOCTL_VENDOR_SPECIFIC_DW_SIZE];

		/* 64-byte submission entry defined in NVMe Specification */
		ULONG          NVMeCmd[NVME_IOCTL_CMD_DW_SIZE];

		/* DW[0..3] of completion entry */
		ULONG          CplEntry[NVME_IOCTL_COMPLETE_DW_SIZE];

		/* Data transfer direction, from host to device or vice versa */
		ULONG          Direction;

		/* 0 means using Admin queue, otherwise, IO queue is used */
		ULONG          QueueId;

		/* Transfer byte length, including Metadata, starting at DataBuffer */
		ULONG          DataBufferLen;

		/* Set to 0 if not supported or interleaved with data  */
		ULONG          MetaDataLen;

		/*
		 * Returned byte length from device to host, at least the length of this
		 * structure. When data transfer required, add the length of the data.
		 */
		ULONG          ReturnBufferLen;

		/* Start with Metadata if present, and then regular data */
		UCHAR          DataBuffer[1];
	} NVME_PASS_THROUGH_IOCTL, *PNVME_PASS_THROUGH_IOCTL;
#pragma pack()

	static const U32 DRIVE_TEMPERATURE_CODE             = 0xE7;
	static const U32 REALLOCATED_SECTORS_COUNT_CODE     = 0x05;
	static const U32 ENDURANCE_REMAINING_CODE           = 0xE8;
	static const U32 LBAS_READ_CODE                     = 0xF2;
	static const U32 LBAS_WRITTEN_CODE                  = 0xF1;
	static const U32 LOADED_HOURS_CODE                  = 0xDE;
	static const U32 POWER_CYCLE_COUNT_CODE             = 0x0C;
	static const U32 POWER_ON_HOURS_CODE                = 0x09;
	static const U32 REPORTED_UNCORRECTABLE_ERRORS_CODE = 0xBB;

#pragma pack(1)
	/******************************************************************************
	 * SMART Attribute structure.
	 *
	 * This structure contains the code and value pair for a SMART attribute
	 ******************************************************************************/
	typedef struct _NVME_SMART_ATTRIBUTES
	{
		UCHAR Code;
		UCHAR Value;

	} NVME_SMART_ATTRIBUTES, *PNVME_SMART_ATTRIBUTES;
#pragma pack()

#pragma pack(1)
	/******************************************************************************
	 * NVMe SMART READ ATTRIBUTES DATA structure.
	 *
	 * This structure contains the information about SMART passed back when a
	 * IOCTL_SCSI_MINIPORT_SMART_READ_ATTRIBS is requested.
	 *
	 * User applications need to allocate proper size of buffer(s) and populate the
	 * fields to ensure the requests are being processed correctly after issuing.
	 ******************************************************************************/
	typedef struct _NVME_SMART_READ_ATTRIBUTES_DATA
	{
		SRB_IO_CONTROL SrbIoCtrl;
		NVME_SMART_ATTRIBUTES DriveTemperature;
		NVME_SMART_ATTRIBUTES ReallocatedSectorsCount;
		NVME_SMART_ATTRIBUTES EnduranceRemaining;
		NVME_SMART_ATTRIBUTES LBAsRead;
		NVME_SMART_ATTRIBUTES LBAsWritten;
		NVME_SMART_ATTRIBUTES LoadedHours;
		NVME_SMART_ATTRIBUTES PowerCycleCount;
		NVME_SMART_ATTRIBUTES PowerOnHours;
		NVME_SMART_ATTRIBUTES ReportedUncorrectableErrors;
	} NVME_SMART_READ_ATTRIBUTES_DATA, *PNVME_SMART_READ_ATTRIBUTES_DATA;
#pragma pack()

#pragma pack(1)
	/******************************************************************************
	 * NVMe SMART READ THRESHOLDS DATA structure.
	 *
	 * This structure contains the information about SMART passed back when a
	 * IOCTL_SCSI_MINIPORT_SMART_READ_THRESHOLDS is requested.
	 *
	 * User applications need to allocate proper size of buffer(s) and populate the
	 * fields to ensure the requests are being processed correctly after issuing.
	 ******************************************************************************/
	typedef struct _NVME_SMART_READ_THRESHOLDS_DATA
	{
		SRB_IO_CONTROL SrbIoCtrl;
		NVME_SMART_ATTRIBUTES DriveTemperature;
		NVME_SMART_ATTRIBUTES ReallocatedSectorsCount;
	} NVME_SMART_READ_THRESHOLDS_DATA, *PNVME_SMART_READ_THRESHOLDS_DATA;
#pragma pack()

#pragma pack(1)
	//
	//FIRMWARE Update
	//
	static const U32 IOCTL_SCSI_MINIPORT_FIRMWARE = ((FILE_DEVICE_SCSI << 16) + 0x0780);
	static const U32 FIRMWARE_REQUEST_BLOCK_STRUCTURE_VERSION = 0x1;
	static const U32 STORAGE_FIRMWARE_DOWNLOAD_STRUCTURE_VERSION = 0x1;
	static const U32 FIRMWARE_FUNCTION_GET_INFO = 0x01;
	static const U32 FIRMWARE_FUNCTION_DOWNLOAD = 0x02;
	static const U32 FIRMWARE_FUNCTION_ACTIVATE = 0x03;
	static const U32 FIRMWARE_REQUEST_FLAG_CONTROLLER = 0x00000001;

	//FW UPDATE
	typedef struct _FIRMWARE_REQUEST_BLOCK {
		ULONG   Version;            // FIRMWARE_REQUEST_BLOCK_STRUCTURE_VERSION
		ULONG   Size;               // Size of the data structure.
		ULONG   Function;           // Function code
		ULONG   Flags;

		ULONG   DataBufferOffset;   // the offset is from the beginning of buffer. e.g. from beginning of SRB_IO_CONTROL. The value should be multiple of sizeof(PVOID); Value 0 means that there is no data buffer.
		ULONG   DataBufferLength;   // length of the buffer
	} FIRMWARE_REQUEST_BLOCK, *PFIRMWARE_REQUEST_BLOCK;

	typedef struct _STORAGE_FIRMWARE_SLOT_INFO {

		UCHAR   SlotNumber;
		BOOLEAN ReadOnly;
		UCHAR   Reserved[6];

		union {
			UCHAR     Info[8];
			ULONGLONG AsUlonglong;
		} Revision;

	} STORAGE_FIRMWARE_SLOT_INFO, *PSTORAGE_FIRMWARE_SLOT_INFO;

	typedef struct _STORAGE_FIRMWARE_INFO {

		ULONG   Version;        // STORAGE_FIRMWARE_INFO_STRUCTURE_VERSION
		ULONG   Size;           // sizeof(STORAGE_FIRMWARE_INFO)

		BOOLEAN UpgradeSupport;
		UCHAR   SlotCount;
		UCHAR   ActiveSlot;
		UCHAR   PendingActivateSlot;

		ULONG   Reserved;

		STORAGE_FIRMWARE_SLOT_INFO Slot[0];

	} STORAGE_FIRMWARE_INFO, *PSTORAGE_FIRMWARE_INFO;

	typedef struct _STORAGE_FIRMWARE_DOWNLOAD {

		ULONG       Version;            // STORAGE_FIRMWARE_DOWNLOAD_STRUCTURE_VERSION
		ULONG       Size;               // sizeof(STORAGE_FIRMWARE_DOWNLOAD)

		ULONGLONG   Offset;             // image file offset, should be aligned to value of "ImagePayloadAlignment" from STORAGE_FIRMWARE_INFO.
		ULONGLONG   BufferSize;         // should be multiple of value of "ImagePayloadAlignment" from STORAGE_FIRMWARE_INFO

		UCHAR       ImageBuffer[0];     // firmware image file. 

	} STORAGE_FIRMWARE_DOWNLOAD, *PSTORAGE_FIRMWARE_DOWNLOAD; 

	typedef struct _STORAGE_FIRMWARE_ACTIVATE {

		ULONG   Version;
		ULONG   Size;

		UCHAR   SlotToActivate;
		UCHAR   Reserved0[3];

	} STORAGE_FIRMWARE_ACTIVATE, *PSTORAGE_FIRMWARE_ACTIVATE;
#pragma pack()

	typedef struct _NVMe_COMMAND_DWORD_0
	{
		/* [Opcode] This field indicates the opcode of the command to be executed */
		UCHAR    OPC;

		/*
		 * [Fused Operation] In a fused operation, a complex command is created by
		 * "fusing together two simpler commands. Refer to section 6.1. This field
		 * indicates whether this command is part of a fused operation and if so,
		 * which command it is in the sequence. Value 00b Normal Operation, Value
		 * 01b == Fused operation, first command, Value 10b == Fused operation,
		 * second command, Value 11b == Reserved.
		 */
		UCHAR    FUSE           :2;
		UCHAR    Reserved       :6;

		/*
		 * [Command Identifier] This field indicates a unique identifier for the
		 * command when combined with the Submission Queue identifier.
		 */
		USHORT   CID;
	} NVMe_COMMAND_DWORD_0, *PNVMe_COMMAND_DWORD_0;

	typedef struct _NVMe_COMMAND
	{
		/*
		 * [Command Dword 0] This field is common to all commands and is defined
		 * in Figure 6.
		 */
		NVMe_COMMAND_DWORD_0    CDW0;

		 /*
		  * [Namespace Identifier] This field indicates the namespace that this
		  * command applies to. If the namespace is not used for the command, then
		  * this field shall be cleared to 0h. If a command shall be applied to all
		  * namespaces on the device, then this value shall be set to FFFFFFFFh.
		  */
		ULONG                   NSID;

		/* DWORD 2, 3 */
		ULONGLONG               Reserved;

		/*
		 * [Metadata Pointer] This field contains the address of a contiguous
		 * physical buffer of metadata. This field is only used if metadata is not
		 * interleaved with the LBA data, as specified in the Format NVM command.
		 * This field shall be Dword aligned.
		 */
		ULONGLONG               MPTR;

		/* [PRP Entry 1] This field contains the first PRP entry for the command. */
		ULONGLONG               PRP1;

		/*
		 * [PRP Entry 2] This field contains the second PRP entry for the command.
		 * If the data transfer spans more than two memory pages, then this field is
		 * a PRP List pointer.
		 */
		ULONGLONG               PRP2;

		/* [Command Dword 10] This field is command specific Dword 10. */
		union {
			ULONG               CDW10;
			/*
			 * Defined in Admin and NVM Vendor Specific Command format.
			 * Number of DWORDs in PRP, data transfer (in Figure 8).
			 */
			ULONG               NDP;
		};

		/* [Command Dword 11] This field is command specific Dword 11. */
		union {
			ULONG               CDW11;
			/*
			 * Defined in Admin and NVM Vendor Specific Command format.
			 * Number of DWORDs in MPTR, Metadata transfer (in Figure 8).
			 */
			ULONG               NDM;
		};

		/* [Command Dword 12] This field is command specific Dword 12. */
		ULONG                   CDW12;

		/* [Command Dword 13] This field is command specific Dword 13. */
		ULONG                   CDW13;

		/* [Command Dword 14] This field is command specific Dword 14. */
		ULONG                   CDW14;

		/* [Command Dword 15] This field is command specific Dword 15. */
		ULONG                   CDW15;
	} NVMe_COMMAND, *PNVMe_COMMAND;

	//-------------------------------------------------
	// from Toolbox
	//-------------------------------------------------
	static const U32 SIZE_MAX_NVME = 4096;
	static const U32 SIZE_MAX_EC_TABLE = (SIZE_MAX_NVME * 2);
	
	static const U32 IOCTL_STORAGE_PROTOCOL_COMMAND = CTL_CODE(IOCTL_STORAGE_BASE, 0x04F0, METHOD_BUFFERED, FILE_READ_ACCESS | FILE_WRITE_ACCESS);

	//
	// Command Specific Information for Storage Protocols - "CommandSpecific" field.
	//
	static const U32 STORAGE_PROTOCOL_SPECIFIC_NVME_ADMIN_COMMAND = 0x01;
	static const U32 STORAGE_PROTOCOL_SPECIFIC_NVME_NVM_COMMAND      = 0x02;
	//
	// Command Length for Storage Protocols.
	//
	static const U32 STORAGE_PROTOCOL_COMMAND_LENGTH_NVME            = 0x40;            // NVMe commands are always 64 bytes.
	static const U32 STORAGE_PROTOCOL_COMMAND_FLAG_ADAPTER_REQUEST   = 0x80000000;     // Flag indicates the request targeting to adapter instead of device.
	//
	// Command completion status
	// The "Phase Tag" field and "Status Field" are separated in spec. We define them in the same data structure to ease the memory access from software.
	//
	typedef union
	{
		struct
		{
			USHORT  P           : 1;        // Phase Tag (P)

			USHORT  SC          : 8;        // Status Code (SC)
			USHORT  SCT         : 3;        // Status Code Type (SCT)
			USHORT  Reserved    : 2;
			USHORT  M           : 1;        // More (M)
			USHORT  DNR         : 1;        // Do Not Retry (DNR)
		} DUMMYSTRUCTNAME;

		USHORT AsUshort;

	} NVME_COMMAND_STATUS, *PNVME_COMMAND_STATUS;
	//
	// Information of log: NVME_LOG_PAGE_ERROR_INFO. Size: 64 bytes
	//
	typedef struct
	{

		ULONGLONG           ErrorCount;
		USHORT              SQID;           // Submission Queue ID
		USHORT              CMDID;          // Command ID
		NVME_COMMAND_STATUS Status;         // Status Field: This field indicates the Status Field for the command  that completed.  The Status Field is located in bits 15:01, bit 00 corresponds to the Phase Tag posted for the command.

		struct
		{
			USHORT  Byte        : 8;        // Byte in command that contained the error.
			USHORT  Bit         : 3;        // Bit in command that contained the error.
			USHORT  Reserved    : 5;
		} ParameterErrorLocation;

		ULONGLONG           Lba;            // LBA: This field indicates the first LBA that experienced the error condition, if applicable.
		ULONG               NameSpace;      // Namespace: This field indicates the namespace that the error is associated with, if applicable.

		UCHAR               VendorInfoAvailable;    // Vendor Specific Information Available

		UCHAR               Reserved0[3];

		ULONGLONG           CommandSpecificInfo;    // This field contains command specific information. If used, the command definition specifies the information returned.

		UCHAR               Reserved1[24];

	} NVME_ERROR_INFO_LOG, *PNVME_ERROR_INFO_LOG;

	typedef enum _STORAGE_PROTOCOL_TYPE
	{
		ProtocolTypeUnknown = 0x00,
		ProtocolTypeScsi,
		ProtocolTypeAta,
		ProtocolTypeNvme,
		ProtocolTypeSd,
		ProtocolTypeProprietary = 0x7E,
		ProtocolTypeMaxReserved = 0x7F
	} STORAGE_PROTOCOL_TYPE, *PSTORAGE_PROTOCOL_TYPE;
	//
	// Parameter for IOCTL_STORAGE_PROTOCOL_COMMAND
	// Buffer layout: <STORAGE_PROTOCOL_COMMAND> <Command> [Error Info Buffer] [Data-to-Device Buffer] [Data-from-Device Buffer]
	//
	static const U32 STORAGE_PROTOCOL_STRUCTURE_VERSION = 0x1;

	typedef struct _STORAGE_PROTOCOL_COMMAND
	{

		DWORD   Version;                        // STORAGE_PROTOCOL_STRUCTURE_VERSION
		DWORD   Length;                         // sizeof(STORAGE_PROTOCOL_COMMAND)

		STORAGE_PROTOCOL_TYPE  ProtocolType;
		DWORD   Flags;                          // Flags for the request

		DWORD   ReturnStatus;                   // return value
		DWORD   ErrorCode;                      // return value, optional

		DWORD   CommandLength;                  // non-zero value should be set by caller
		DWORD   ErrorInfoLength;                // optional, can be zero
		DWORD   DataToDeviceTransferLength;     // optional, can be zero. Used by WRITE type of request.
		DWORD   DataFromDeviceTransferLength;   // optional, can be zero. Used by READ type of request.

		DWORD   TimeOutValue;                   // in unit of seconds

		DWORD   ErrorInfoOffset;                // offsets need to be pointer aligned
		DWORD   DataToDeviceBufferOffset;       // offsets need to be pointer aligned
		DWORD   DataFromDeviceBufferOffset;     // offsets need to be pointer aligned

		DWORD   CommandSpecific;                // optional information passed along with Command.
		DWORD   Reserved0;

		DWORD   FixedProtocolReturnData;        // return data, optional. Some protocol, such as NVMe, may return a small amount data (DWORD0 from completion queue entry) without the need of separate device data transfer.
		DWORD   Reserved1[3];

		BYTE    Command[ANYSIZE_ARRAY];

	} STORAGE_PROTOCOL_COMMAND, *PSTORAGE_PROTOCOL_COMMAND;

	typedef union
	{
		struct
		{
			ULONG   CNS      : 2;        // Controller or Namespace Structure (CNS)
			ULONG   Reserved : 30;
		} DUMMYSTRUCTNAME;

		ULONG AsUlong;

	} NVME_CDW10_IDENTIFY, *PNVME_CDW10_IDENTIFY;
	//
	// Command Dword 0
	//
	typedef union
	{

		struct
		{
			//LSB
			ULONG OPC       : 8;        // Opcode (OPC)
			ULONG FUSE      : 2;        // Fused Operation (FUSE)
			ULONG Reserved0 : 5;
			ULONG PSDT      : 1;        // PRP or SGL for Data Transfer (PSDT)
			ULONG CID       : 16;       // Command Identifier (CID)
			//MSB
		} DUMMYSTRUCTNAME;

		ULONG AsUlong;

	} NVME_COMMAND_DWORD0, *PNVME_COMMAND_DWORD0;
	//
	// NVMe command data structure
	//
	typedef struct
	{
		//
		// Common fields for all commands
		//
		NVME_COMMAND_DWORD0 CDW0;
		ULONG               NSID;
		ULONG               Reserved0[2];
		ULONGLONG           MPTR;
		ULONGLONG           PRP1;
		ULONGLONG           PRP2;

		//
		// Command independent fields from CDW10 to CDW15
		//
		union
		{

			//
			// General Command data fields
			//
			struct
			{
				ULONG   CDW10;
				ULONG   CDW11;
				ULONG   CDW12;
				ULONG   CDW13;
				ULONG   CDW14;
				ULONG   CDW15;
			} GENERAL;

			//
			// Admin Command: Identify
			//
			struct
			{
				NVME_CDW10_IDENTIFY CDW10;
				ULONG   CDW11;
				ULONG   CDW12;
				ULONG   CDW13;
				ULONG   CDW14;
				ULONG   CDW15;
			} IDENTIFY;
		} u;

	} NVME_COMMAND, *PNVME_COMMAND;

	typedef struct _IDINFO 
	{
		USHORT wGenConfig; // WORD 0: 基本信息字 
		USHORT wNumCyls; // WORD 1: 柱面数 
		USHORT wReserved2; // WORD 2: 保留 
		USHORT wNumHeads; // WORD 3: 磁头数 
		USHORT wReserved4; // WORD 4: 保留 
		USHORT wReserved5; // WORD 5: 保留 
		USHORT wNumSectorsPerTrack; // WORD 6: 每磁道扇区数 
		USHORT wVendorUnique[3]; // WORD 7-9: 厂家设定值 
		CHAR sSerialNumber[20]; // WORD 10-19:序列号 
		USHORT wBufferType; // WORD 20: 缓冲类型 
		USHORT wBufferSize; // WORD 21: 缓冲大小 
		USHORT wECCSize; // WORD 22: ECC校验大小 
		CHAR sFirmwareRev[8]; // WORD 23-26: 固件版本 
		CHAR sModelNumber[40]; // WORD 27-46: 内部型号 
		USHORT wMoreVendorUnique; // WORD 47: 厂家设定值 
		USHORT wReserved48; // WORD 48: 保留 
		struct { USHORT reserved1:8; USHORT DMA:1; // 1=支持DMA 
		USHORT LBA:1; // 1=支持LBA 
		USHORT DisIORDY:1; // 1=可不使用IORDY 
		USHORT IORDY:1; // 1=支持IORDY 
		USHORT SoftReset:1; // 1=需要ATA软启动 
		USHORT Overlap:1; // 1=支持重叠操作 
		USHORT Queue:1; // 1=支持命令队列 
		USHORT InlDMA:1; // 1=支持交叉存取DMA 
		} wCapabilities; 
		// WORD 49: 一般能力 
		USHORT wReserved1; // WORD 50: 保留 
		USHORT wPIOTiming; // WORD 51: PIO时序
		USHORT wDMATiming; // WORD 52: DMA时序 
		struct { 
			USHORT CHSNumber:1; // 1=WORD 54-58有效 
			USHORT CycleNumber:1; // 1=WORD 64-70有效 
			USHORT UnltraDMA:1; // 1=WORD 88有效 
			USHORT reserved:13; 
		} wFieldValidity; // WORD 53: 后续字段有效性标志 
		USHORT wNumCurCyls; // WORD 54: CHS可寻址的柱面数 
		USHORT wNumCurHeads; // WORD 55: CHS可寻址的磁头数 
		USHORT wNumCurSectorsPerTrack; // WORD 56: CHS可寻址每磁道扇区数 
		USHORT wCurSectorsLow; // WORD 57: CHS可寻址的扇区数低位字 
		USHORT wCurSectorsHigh; // WORD 58: CHS可寻址的扇区数高位字 
		struct { 
			USHORT CurNumber:8; // 当前一次性可读写扇区数 
			USHORT Multi:1; // 1=已选择多扇区读写 
			USHORT reserved1:7; 
		} wMultSectorStuff; 

		// WORD 59: 多扇区读写设定 
		ULONG dwTotalSectors; // WORD 60-61: LBA可寻址的扇区数 
		USHORT wSingleWordDMA; // WORD 62: 单字节DMA支持能力 

		struct { 
			USHORT Mode0:1; // 1=支持模式0 (4.17Mb/s) 
			USHORT Mode1:1; // 1=支持模式1 (13.3Mb/s) 
			USHORT Mode2:1; // 1=支持模式2 (16.7Mb/s) 
			USHORT Reserved1:5; USHORT Mode0Sel:1; // 1=已选择模式0 
			USHORT Mode1Sel:1; // 1=已选择模式1 
			USHORT Mode2Sel:1; // 1=已选择模式2 
			USHORT Reserved2:5; 
		} wMultiWordDMA; // WORD 63: 多字节DMA支持能力 

		struct { 
			USHORT AdvPOIModes:8; // 支持高级POI模式数 
			USHORT reserved:8; 
		} wPIOCapacity; // WORD 64: 高级PIO支持能力 

		USHORT wMinMultiWordDMACycle; // WORD 65: 多字节DMA传输周期的最小值 

		USHORT wRecMultiWordDMACycle; // WORD 66: 多字节DMA传输周期的建议值 
		USHORT wMinPIONoFlowCycle; // WORD 67: 无流控制时PIO传输周期的最小值 
		USHORT wMinPOIFlowCycle; // WORD 68: 有流控制时PIO传输周期的最小值 
		USHORT wReserved69[11]; // WORD 69-79: 保留 

		struct 
		{ 
			USHORT Reserved1:1; 
			USHORT ATA1:1; // 1=支持ATA-1 
			USHORT ATA2:1; // 1=支持ATA-2 
			USHORT ATA3:1; // 1=支持ATA-3 
			USHORT ATA4:1; // 1=支持ATA/ATAPI-4 
			USHORT ATA5:1; // 1=支持ATA/ATAPI-5 
			USHORT ATA6:1; // 1=支持ATA/ATAPI-6 
			USHORT ATA7:1; // 1=支持ATA/ATAPI-7 
			USHORT ATA8:1; // 1=支持ATA/ATAPI-8 
			USHORT ATA9:1; // 1=支持ATA/ATAPI-9 
			USHORT ATA10:1; // 1=支持ATA/ATAPI-10 
			USHORT ATA11:1; // 1=支持ATA/ATAPI-11 
			USHORT ATA12:1; // 1=支持ATA/ATAPI-12 
			USHORT ATA13:1; // 1=支持ATA/ATAPI-13 
			USHORT ATA14:1; // 1=支持ATA/ATAPI-14 
			USHORT Reserved2:1; 
		} wMajorVersion; // WORD 80: 主版本 

		USHORT wMinorVersion; // WORD 81: 副版本 
		USHORT wReserved82[6]; // WORD 82-87: 保留 

		struct
		{ 
			USHORT Mode0:1; // 1=支持模式0 (16.7Mb/s) 
			USHORT Mode1:1; // 1=支持模式1 (25Mb/s) 
			USHORT Mode2:1; // 1=支持模式2 (33Mb/s) 
			USHORT Mode3:1; // 1=支持模式3 (44Mb/s) 
			USHORT Mode4:1; // 1=支持模式4 (66Mb/s) 
			USHORT Mode5:1; // 1=支持模式5 (100Mb/s) 
			USHORT Mode6:1; // 1=支持模式6 (133Mb/s) 
			USHORT Mode7:1; // 1=支持模式7 (166Mb/s)
			USHORT Mode0Sel:1; // 1=已选择模式0 
			USHORT Mode1Sel:1; // 1=已选择模式1 
			USHORT Mode2Sel:1; // 1=已选择模式2 
			USHORT Mode3Sel:1; // 1=已选择模式3 
			USHORT Mode4Sel:1; // 1=已选择模式4 
			USHORT Mode5Sel:1; // 1=已选择模式5 
			USHORT Mode6Sel:1; // 1=已选择模式6 
			USHORT Mode7Sel:1; // 1=已选择模式7 
		} wUltraDMA; 

		// WORD 88: Ultra DMA支持能力 
		USHORT wReserved89[167]; // WORD 89-255
	} IDINFO, *PIDINFO;

	static const U32 CMD_TIMEOUT = 10;

///////////////////////////////////////////////////////////
	typedef enum {
		StorageDeviceProperty = 0,
		StorageAdapterProperty,
		StorageDeviceIdProperty,
		StorageDeviceUniqueIdProperty,
		StorageDeviceWriteCacheProperty,
		StorageMiniportProperty,
		StorageAccessAlignmentProperty,
		StorageDeviceSeekPenaltyProperty,
		StorageDeviceTrimProperty,
		StorageDeviceWriteAggregationProperty,
		StorageDeviceDeviceTelemetryProperty,
		StorageDeviceLBProvisioningProperty,
		StorageDevicePowerProperty,
		StorageDeviceCopyOffloadProperty,
		StorageDeviceResiliencyProperty,
		StorageDeviceMediumProductType,
		StorageDeviceRpmbProperty,
		StorageDeviceIoCapabilityProperty = 48,
		StorageAdapterProtocolSpecificProperty,
		StorageDeviceProtocolSpecificProperty,
		StorageAdapterTemperatureProperty,
		StorageDeviceTemperatureProperty,
		StorageAdapterPhysicalTopologyProperty,
		StorageDevicePhysicalTopologyProperty,
		StorageDeviceAttributesProperty,
	} TStoragePropertyId;

	typedef enum {
		PropertyStandardQuery = 0,
		PropertyExistsQuery,
		PropertyMaskQuery,
		PropertyQueryMaxDefined
	} TStorageQueryType;

	typedef struct {
		TStoragePropertyId PropertyId;
		TStorageQueryType QueryType;
	} TStoragePropertyQuery;

	typedef struct {
		STORAGE_PROTOCOL_TYPE ProtocolType;
		DWORD   DataType;
		DWORD   ProtocolDataRequestValue;
		DWORD   ProtocolDataRequestSubValue;
		DWORD   ProtocolDataOffset;
		DWORD   ProtocolDataLength;
		DWORD   FixedProtocolReturnData;
		DWORD   Reserved[3];
	} TStorageProtocolSpecificData;

	typedef enum {
		NVMeDataTypeUnknown = 0,
		NVMeDataTypeIdentify,
		NVMeDataTypeLogPage,
		NVMeDataTypeFeature,
	} TStorageProtocolNVMeDataType;

	typedef struct {
		TStoragePropertyQuery Query;
		TStorageProtocolSpecificData ProtocolSpecific;
		BYTE Buffer[4096];
	} TStorageQueryWithBuffer;
}