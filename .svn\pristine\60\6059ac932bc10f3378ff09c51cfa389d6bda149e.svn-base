import PublicFuc
import configparser
import csv,time
import os,re
from openpyxl.utils import get_column_letter,column_index_from_string
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Font,Alignment,Border,Side,colors
from datetime import datetime,timedelta
font = Font(name = 'Arial',size = 10)
align = Alignment(horizontal='center', vertical='center')
itemFont = Font(name = 'Arial',bold = True, size = 11)
itemAlignment = Alignment(horizontal='center',vertical='center',wrap_text=True)

minKey = ['Read Acc Time']
alignment = Alignment(horizontal='center',vertical='center')
warnFill = PatternFill('solid', fgColor='FF0000')
restoreFill = PatternFill('solid',fgColor='FFFFFF')
maxFill = PatternFill('solid', fgColor='64C8FF')
config = configparser.RawConfigParser()

resultTrueCellFont = Font(name = 'Arial',size = 10,color = '0000FF')
resultFalseCellFont = Font(name = 'Arial',size = 10,color = 'FF0000')

conclusionPassCellFont = Font(name = 'Arial',bold = True,size = 10,color = '0000FF')
conclusionFailCellFont = Font(name = 'Arial',bold = True,size = 10,color = 'FF0000')

startLine = 6
#wlKeyLst=['valume label','errcode','datacrc','cmdcrc','vdt','wdtr','slcbadblock_new','tlcbadblock_new','maxdoneblocklen','doneblockerror','slc_min_block_length','tlc_min_block_length','retrycnt','waf','tbw','wl_slc_max','wl_slc_min','wl_slc_avg','wl_tlc_max','wl_tlc_min','wl_tlc_avg','wl slc inherit','wl tlc inherit']
wliniSmartKeyLst=['valume label','debugerrcode','datacrcerrcnt','cmdcrcerrcnt','vdtcnt','watchdogcnt','slcbadblock_new','tlcbadblock_new','max_doneblocklen','doneblockerror','slc_fminblklen','tlc_fminblklen','retrycnt','waf','waf_onlydata','write data','slc_wl','tlc_wl','slc inherit wl','tlc inherit wl','slcoverflow','tlcoverflow','pt_lifetimea','pt_lifetimeb'] #原始的ini文件中能找到的数据项名称。
wlSmartKeyLst=['valume label','debugerrcode','datacrcerrcnt','cmdcrcerrcnt','vdtcnt','watchdogcnt','slcbadblock_new','tlcbadblock_new','max_doneblocklen','doneblockerror','slc_fminblklen','tlc_fminblklen','retrycnt','waf','waf_onlydata','write data','nand_write_data','wl_slc_max','wl_slc_min','wl_slc_avg','wl_tlc_max','wl_tlc_min','wl_tlc_avg','slc inherit wl','tlc inherit wl','slcoverflow','tlcoverflow','pt_lifetimea','pt_lifetimeb']
colSmartKeyLst = wlSmartKeyLst+ ['slc_diff','tlc_diff','wear_avg']
colOrgSmartLst = ['B','C','D','E','F','G','H','I','J','K','L','M','N','O','P','Q','R','V','W','X','Y','Z','AA','AB','AC','AD','AE','AF','AG']+['S','T','U']
smartBeginCol = 'Y'
smartColLst = []

wlMaxDic = {'slc_diff':750,'tlc_diff':75, 'wear_avg':10}
def Run(curpath, workBook, alignment):
    ws = workBook['Mars_Environment Test']
    ws.alignment = alignment
    GetSmartColLst()
    ProMarsEnvironment(curpath, ws)
    #ProWl(curpath, ws)
    PublicFuc.WriteReportTime(ws,'E',2)
    PublicFuc.WriteReportOperator(ws,'J',2)

#将列名称正确调整
def GetSmartColLst():
    global smartColLst
    smartColLst = [0 for x in range(0,len(colOrgSmartLst))]
    nBeginColIdx = column_index_from_string(smartBeginCol)
    nOldBeginColIdx = column_index_from_string(colOrgSmartLst[0])
    colIdxOffset = nBeginColIdx - nOldBeginColIdx
    for colName in colOrgSmartLst:
        colIdx = column_index_from_string(colName)
        colIdx = colIdx + colIdxOffset
        listIdx = colOrgSmartLst.index(colName)
        smartColLst[listIdx] = get_column_letter(colIdx)

def LocalReadMarsIniData(curpath, pattern, resultDic, caseName):
    unitLst = ['M/s','MB']
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        if 'HWCONFIG' not in config.sections() or caseName not in config.sections():
            continue
        keyName = config['HWCONFIG']['mms_flash']
        if keyName not in resultDic:
            resultDic[keyName] = {}
        if caseName in resultDic[keyName]:
            continue #此样片已经有此数据
        tempDic = {}
        tempDic['capacity'] = config['HWCONFIG']['capacity']
        tempDic['mms_pc'] = config['HWCONFIG']['MMS_PC']
        tempDic['keytime'] = file.split('\\')[-4]
        tempDic['case_data'] = {}
        for key in config[caseName]:
            value = config[caseName][key]
            #去除单位
            for unit in unitLst:
                if unit in value:
                    value = value[:value.find(unit)]
                    break
            tempDic['case_data'][key] = value
        if caseName in resultDic[keyName]:
            if resultDic[keyName][caseName]['keytime'] > tempDic['keytime']: 
                continue #此样片已经有此数据
        resultDic[keyName][caseName] = tempDic
        if 'test_result' not in tempDic['case_data'] or 'TRUE'== tempDic['case_data']['test_result']:
            tempDic['test_result'] = 'PASS'
        else:
            tempDic['test_result'] = tempDic['case_data']['test_result']
            filesplit = file.split('\\')
            length = len(filesplit)
            Test_item = ''
            if filesplit[length-7] == 'Plan26':
                Test_item = 'RT BIT1（25℃/24H）'
            elif filesplit[length-7] == 'Plan27':
                Test_item = 'RT BIT2（DDR52/25℃/168H）'
            elif filesplit[length-7] == 'Plan28':
                Test_item = 'RT BIT3（HS200/25℃/168H）'
            elif filesplit[length-7] == 'Plan29':
                Test_item = 'RT BIT4 (H-Voltage/25℃/168H）'
            elif filesplit[length-7] == 'Plan30':
                Test_item = 'RT BIT5（L-Voltage/25℃/168H）'
            elif filesplit[length-7] == 'Plan31':
                Test_item = 'HT BIT（85℃/168H)'
            elif filesplit[length-7] == 'Plan32':
                Test_item = 'LT BIT (-25℃/168H)'
            elif filesplit[length-7] == 'Plan33':
                Test_item = 'TC  BIT (85℃~ -25℃) 168H'
            elif filesplit[length-7] == 'Plan61':
                Test_item = 'HT BIT（MaxTemp/168H，3.3V)'
            PublicFuc.errDiskLst.append([Test_item,caseName,keyName,tempDic['mms_pc'],'',tempDic['case_data']['teset_circle'],tempDic['case_data']['end_time'],PublicFuc.GetTestTime(tempDic['case_data']),tempDic['test_result'],
            int(tempDic['case_data']['debugerrcode'],16),int(tempDic['case_data']['watchdogcnt'],16),tempDic['case_data']['slcbadblock_new'],tempDic['case_data']['tlcbadblock_new'],file])

def ProMarsEnvironment(curpath, ws):
    ProPlan26(curpath, ws)
    # ProPlan27(curpath, ws)
    # ProPlan28(curpath, ws)
    ProPlan29(curpath, ws)
    ProPlan30(curpath, ws)
    ProPlan31(curpath, ws)
    ProPlan32(curpath, ws)
    ProPlan33(curpath, ws)
    ProPlan61(curpath, ws)
    

def ProPlan26(curpath, ws):
    resultDic = {}
    #统计Plan26的结果
    pattern1 = '.+\\\\Plan26\\\\T_EM_NA_C9\\\\Mars_CopyFile\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_CopyFile'
    LocalReadMarsIniData(curpath, pattern1, resultDic, caseName)

    pattern2 = '.+\\\\Plan26\\\\T_EM_NA_C37\\\\BIT_24H\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_BurnIn_24H'
    LocalReadMarsIniData(curpath, pattern2, resultDic, caseName)

    pattern3 = '.+\\\\Plan26\\\\T_EM_NA_C9\\\\Mars_CopyFile_verify\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_Verify_CF'
    LocalReadMarsIniData(curpath, pattern3, resultDic, caseName)

    pattern4 = '.+\\\\Plan26\\\\T_EM_NA_C8\\\\Mars_H2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'H2'
    LocalReadMarsIniData(curpath, pattern4, resultDic, caseName)

    caseName = 'H2_2'
    LocalReadMarsIniData(curpath, pattern4, resultDic, caseName)

    caseName = 'H2_3'
    LocalReadMarsIniData(curpath, pattern4, resultDic, caseName)

    #FillBasicInfo(resultDic,ws)
    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)

    LocalWriteMarsData(ws, startLine, newDic,'AT_BurnIn_24H')
    SetFirstCol(ws, newDic, 'RT BIT1（25℃/24H）')

    # 获取case的开始结束时间
    patterns = []
    patterns.append(pattern1)
    patterns.append(pattern2)
    patterns.append(pattern3)
    patterns.append(pattern4)
    PublicFuc.GetTimeRange(curpath, patterns, 'Plan26_AT_BurnIn_24H')

def ProPlan27(curpath, ws):
    resultDic = {}
    
    pattern = '.+\\\\Plan27\\\\T_EM_NA_C9\\\\Mars_CopyFile\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_CopyFile'
    LocalReadMarsIniData(curpath, pattern, resultDic, caseName)

    pattern = '.+\\\\Plan27\\\\T_EM_NA_C38\\\\BIT_168H\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_BurnIn_168H'
    LocalReadMarsIniData(curpath, pattern, resultDic, caseName)

    pattern = '.+\\\\Plan27\\\\T_EM_NA_C9\\\\Mars_CopyFile_verify\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_Verify_CF'
    LocalReadMarsIniData(curpath, pattern, resultDic, caseName)

    pattern = '.+\\\\Plan27\\\\T_EM_NA_C8\\\\Mars_H2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'H2'
    LocalReadMarsIniData(curpath, pattern, resultDic, caseName)

    caseName = 'H2_2'
    LocalReadMarsIniData(curpath, pattern, resultDic, caseName)

    caseName = 'H2_3'
    LocalReadMarsIniData(curpath, pattern, resultDic, caseName)

    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)

    LocalWriteMarsData(ws, startLine, newDic,'AT_BurnIn_168H')
    SetFirstCol(ws, newDic, 'RT BIT2（DDR52/25℃/168H）')

def ProPlan28(curpath, ws):
    resultDic = {}
    
    pattern = '.+\\\\Plan28\\\\T_EM_NA_C9\\\\Mars_CopyFile\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_CopyFile'
    LocalReadMarsIniData(curpath, pattern, resultDic, caseName)

    pattern = '.+\\\\Plan28\\\\T_EM_NA_C39\\\\BIT_168H\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_BurnIn_168H'
    LocalReadMarsIniData(curpath, pattern, resultDic, caseName)

    pattern = '.+\\\\Plan28\\\\T_EM_NA_C9\\\\Mars_CopyFile_verify\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_Verify_CF'
    LocalReadMarsIniData(curpath, pattern, resultDic, caseName)

    pattern = '.+\\\\Plan28\\\\T_EM_NA_C8\\\\Mars_H2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'H2'
    LocalReadMarsIniData(curpath, pattern, resultDic, caseName)

    caseName = 'H2_2'
    LocalReadMarsIniData(curpath, pattern, resultDic, caseName)

    caseName = 'H2_3'
    LocalReadMarsIniData(curpath, pattern, resultDic, caseName)

    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)

    LocalWriteMarsData(ws, startLine, newDic,'AT_BurnIn_168H')
    SetFirstCol(ws, newDic, 'RT BIT3（HS200/25℃/168H）')

def ProPlan29(curpath, ws):
    resultDic = {}
    
    pattern1 = '.+\\\\Plan29\\\\T_EM_NA_C9\\\\Mars_CopyFile\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_CopyFile'
    LocalReadMarsIniData(curpath, pattern1, resultDic, caseName)

    pattern2 = '.+\\\\Plan29\\\\T_EM_NA_C40\\\\BIT_168H\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_BurnIn_168H'
    LocalReadMarsIniData(curpath, pattern2, resultDic, caseName)

    pattern3 = '.+\\\\Plan29\\\\T_EM_NA_C9\\\\Mars_CopyFile_verify\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_Verify_CF'
    LocalReadMarsIniData(curpath, pattern3, resultDic, caseName)

    pattern4 = '.+\\\\Plan29\\\\T_EM_NA_C8\\\\Mars_H2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'H2'
    LocalReadMarsIniData(curpath, pattern4, resultDic, caseName)

    caseName = 'H2_2'
    LocalReadMarsIniData(curpath, pattern4, resultDic, caseName)

    caseName = 'H2_3'
    LocalReadMarsIniData(curpath, pattern4, resultDic, caseName)

    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)

    LocalWriteMarsData(ws, startLine, newDic,'AT_BurnIn_168H')
    SetFirstCol(ws, newDic, 'RT BIT4 (H-Voltage/25℃/168H）')

    # 获取case的开始结束时间
    patterns = []
    patterns.append(pattern1)
    patterns.append(pattern2)
    patterns.append(pattern3)
    patterns.append(pattern4)
    PublicFuc.GetTimeRange(curpath, patterns, 'Plan29_AT_BurnIn_168H')

def ProPlan30(curpath, ws):
    resultDic = {}
    
    pattern1 = '.+\\\\Plan30\\\\T_EM_NA_C9\\\\Mars_CopyFile\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_CopyFile'
    LocalReadMarsIniData(curpath, pattern1, resultDic, caseName)

    pattern2 = '.+\\\\Plan30\\\\T_EM_NA_C41\\\\BIT_168H\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_BurnIn_168H'
    LocalReadMarsIniData(curpath, pattern2, resultDic, caseName)

    pattern3 = '.+\\\\Plan30\\\\T_EM_NA_C9\\\\Mars_CopyFile_verify\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_Verify_CF'
    LocalReadMarsIniData(curpath, pattern3, resultDic, caseName)

    pattern4 = '.+\\\\Plan30\\\\T_EM_NA_C8\\\\Mars_H2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'H2'
    LocalReadMarsIniData(curpath, pattern4, resultDic, caseName)

    caseName = 'H2_2'
    LocalReadMarsIniData(curpath, pattern4, resultDic, caseName)

    caseName = 'H2_3'
    LocalReadMarsIniData(curpath, pattern4, resultDic, caseName)

    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)

    LocalWriteMarsData(ws, startLine, newDic,'AT_BurnIn_168H')
    SetFirstCol(ws, newDic, 'RT BIT5（L-Voltage/25℃/168H）')

    # 获取case的开始结束时间
    patterns = []
    patterns.append(pattern1)
    patterns.append(pattern2)
    patterns.append(pattern3)
    patterns.append(pattern4)
    PublicFuc.GetTimeRange(curpath, patterns, 'Plan30_AT_BurnIn_168H')

def ProPlan31(curpath, ws):
    resultDic = {}
    
    pattern1 = '.+\\\\Plan31\\\\T_EM_NA_C9\\\\Mars_CopyFile\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_CopyFile'
    LocalReadMarsIniData(curpath, pattern1, resultDic, caseName)

    pattern2 = '.+\\\\Plan31\\\\T_EM_NA_C42\\\\BIT_168H\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_BurnIn_168H'
    LocalReadMarsIniData(curpath, pattern2, resultDic, caseName)

    pattern3 = '.+\\\\Plan31\\\\T_EM_NA_C9\\\\Mars_CopyFile_verify\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_Verify_CF'
    LocalReadMarsIniData(curpath, pattern3, resultDic, caseName)

    pattern4 = '.+\\\\Plan31\\\\T_EM_NA_C8\\\\Mars_H2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'H2'
    LocalReadMarsIniData(curpath, pattern4, resultDic, caseName)

    caseName = 'H2_2'
    LocalReadMarsIniData(curpath, pattern4, resultDic, caseName)

    caseName = 'H2_3'
    LocalReadMarsIniData(curpath, pattern4, resultDic, caseName)

    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)

    LocalWriteMarsData(ws, startLine, newDic,'AT_BurnIn_168H')
    SetFirstCol(ws, newDic, 'HT BIT（85℃/168H)')

    # 获取case的开始结束时间
    patterns = []
    patterns.append(pattern1)
    patterns.append(pattern2)
    patterns.append(pattern3)
    patterns.append(pattern4)
    PublicFuc.GetTimeRange(curpath, patterns, 'Plan31_AT_BurnIn_168H')

def ProPlan32(curpath, ws):
    resultDic = {}
    
    pattern1 = '.+\\\\Plan32\\\\T_EM_NA_C9\\\\Mars_CopyFile\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_CopyFile'
    LocalReadMarsIniData(curpath, pattern1, resultDic, caseName)

    pattern2 = '.+\\\\Plan32\\\\T_EM_NA_C43\\\\BIT_168H\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_BurnIn_168H'
    LocalReadMarsIniData(curpath, pattern2, resultDic, caseName)

    pattern3 = '.+\\\\Plan32\\\\T_EM_NA_C9\\\\Mars_CopyFile_verify\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_Verify_CF'
    LocalReadMarsIniData(curpath, pattern3, resultDic, caseName)

    pattern4 = '.+\\\\Plan32\\\\T_EM_NA_C8\\\\Mars_H2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'H2'
    LocalReadMarsIniData(curpath, pattern4, resultDic, caseName)

    caseName = 'H2_2'
    LocalReadMarsIniData(curpath, pattern4, resultDic, caseName)

    caseName = 'H2_3'
    LocalReadMarsIniData(curpath, pattern4, resultDic, caseName)

    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)

    LocalWriteMarsData(ws, startLine, newDic,'AT_BurnIn_168H')
    SetFirstCol(ws, newDic, 'LT BIT (-25℃/168H)')

    # 获取case的开始结束时间
    patterns = []
    patterns.append(pattern1)
    patterns.append(pattern2)
    patterns.append(pattern3)
    patterns.append(pattern4)
    PublicFuc.GetTimeRange(curpath, patterns, 'Plan32_AT_BurnIn_168H')

def ProPlan33(curpath, ws):
    resultDic = {}
    
    pattern1 = '.+\\\\Plan33\\\\T_EM_NA_C9\\\\Mars_CopyFile\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_CopyFile'
    LocalReadMarsIniData(curpath, pattern1, resultDic, caseName)

    pattern2 = '.+\\\\Plan33\\\\T_EM_NA_C44\\\\BIT_168H\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_BurnIn_168H'
    LocalReadMarsIniData(curpath, pattern2, resultDic, caseName)

    pattern3 = '.+\\\\Plan33\\\\T_EM_NA_C9\\\\Mars_CopyFile_verify\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_Verify_CF'
    LocalReadMarsIniData(curpath, pattern3, resultDic, caseName)

    pattern4 = '.+\\\\Plan33\\\\T_EM_NA_C8\\\\Mars_H2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'H2'
    LocalReadMarsIniData(curpath, pattern4, resultDic, caseName)

    caseName = 'H2_2'
    LocalReadMarsIniData(curpath, pattern4, resultDic, caseName)

    caseName = 'H2_3'
    LocalReadMarsIniData(curpath, pattern4, resultDic, caseName)

    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)

    LocalWriteMarsData(ws, startLine, newDic,'AT_BurnIn_168H')
    SetFirstCol(ws, newDic, 'TC  BIT (85℃~ -25℃) 168H')

    # 获取case的开始结束时间
    patterns = []
    patterns.append(pattern1)
    patterns.append(pattern2)
    patterns.append(pattern3)
    patterns.append(pattern4)
    PublicFuc.GetTimeRange(curpath, patterns, 'Plan33_AT_BurnIn_168H')

def ProPlan61(curpath, ws):
    resultDic = {}

    pattern1 = '.+\\\\Plan61\\\\T_EM_NA_C9\\\\Mars_CopyFile\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_CopyFile'
    LocalReadMarsIniData(curpath, pattern1, resultDic, caseName)

    pattern2 = '.+\\\\Plan61\\\\T_EM_NA_C42\\\\BIT_168H\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_BurnIn_168H'
    LocalReadMarsIniData(curpath, pattern2, resultDic, caseName)

    pattern3 = '.+\\\\Plan61\\\\T_EM_NA_C9\\\\Mars_CopyFile_verify\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_Verify_CF'
    LocalReadMarsIniData(curpath, pattern3, resultDic, caseName)

    pattern4 = '.+\\\\Plan61\\\\T_EM_NA_C8\\\\Mars_H2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'H2'
    LocalReadMarsIniData(curpath, pattern4, resultDic, caseName)

    caseName = 'H2_2'
    LocalReadMarsIniData(curpath, pattern4, resultDic, caseName)

    caseName = 'H2_3'
    LocalReadMarsIniData(curpath, pattern4, resultDic, caseName)

    # 所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)

    LocalWriteMarsData(ws, startLine, newDic, 'AT_BurnIn_168H')
    SetFirstCol(ws, newDic, 'HT BIT（MaxTemp/168H，3.3V)')

    # 获取case的开始结束时间
    patterns = []
    patterns.append(pattern1)
    patterns.append(pattern2)
    patterns.append(pattern3)
    patterns.append(pattern4)
    PublicFuc.GetTimeRange(curpath, patterns, 'Plan61_AT_BurnIn_168H')

def LocalGetConclusion(diskDataDic):
    for case in diskDataDic:
        strResult = diskDataDic[case]['test_result']
        if strResult != 'PASS' and strResult != '' and strResult != None:
            return 'FAIL'
    return 'PASS'


def LocalWriteMarsData(worksheet, startLine, dataDic,caseName):
    curLine = startLine
    for disk in dataDic:
        worksheet['%s%d'%('C', curLine)] = disk #首先写样本编号
        
        if 'AT_CopyFile' in dataDic[disk]:
            caseData = dataDic[disk]['AT_CopyFile']
            caseColLst = ['E','F']
            caseColKeyLst = ['test_time','test_result']       
            LocalWriteSingleLineCaseData(worksheet,curLine,caseData, caseColLst, caseColKeyLst)

        if caseName in dataDic[disk]:
            caseData = dataDic[disk][caseName]
            caseColLst = ['G','H','I']
            caseColKeyLst = ['teset_circle','test_time','test_result']
            LocalWriteSingleLineCaseData(worksheet,curLine,caseData, caseColLst, caseColKeyLst)

        if 'AT_Verify_CF' in dataDic[disk]:
            caseData = dataDic[disk]['AT_Verify_CF']
            caseColLst = ['J','K']      
            caseColKeyLst = ['test_time','test_result']
            LocalWriteSingleLineCaseData(worksheet,curLine,caseData, caseColLst, caseColKeyLst)

        if 'H2' in dataDic[disk]:
            caseData = dataDic[disk]['H2']
            caseColLst = ['L','M','N','O']
            caseColKeyLst = ['v_write_avg','v_read_avg','test_time','test_result']       
            LocalWriteSingleLineCaseData(worksheet,curLine,caseData, caseColLst, caseColKeyLst)

        if 'H2_2' in dataDic[disk]:
            caseData = dataDic[disk]['H2_2']
            caseColLst = ['P','Q','R','S']
            caseColKeyLst = ['v_write_avg','v_read_avg','test_time','test_result']       
            LocalWriteSingleLineCaseData(worksheet,curLine,caseData, caseColLst, caseColKeyLst)

        if 'H2_3' in dataDic[disk]:
            caseData = dataDic[disk]['H2_3']
            caseColLst = ['T','U','V','W']
            caseColKeyLst = ['v_write_avg','v_read_avg','test_time','test_result']       
            LocalWriteSingleLineCaseData(worksheet,curLine,caseData, caseColLst, caseColKeyLst)
        
        #写结论。
        strConclusion = LocalGetConclusion(dataDic[disk])
        worksheet['%s%d'%('X', curLine)] = strConclusion
        if strConclusion == 'PASS':
            worksheet['%s%d'%('X', curLine)].font = conclusionPassCellFont
        else:
            worksheet['%s%d'%('X', curLine)].font = conclusionFailCellFont

        curLine += 1

#写一种Case的一行数据
def LocalWriteSingleLineCaseData(worksheet,lineIdx,caseData, caseColLst, caseColKeyLst):
    if caseData == {}:
        return
    
    #写前缀
    pc_no = caseData['mms_pc']
    worksheet['%s%d'%('D', lineIdx)] = pc_no #写此case的电脑编号

    #写剩余数据
    colLst = caseColLst + smartColLst
    colKeyLst = caseColKeyLst + colSmartKeyLst

    purCaseData = caseData['case_data']
    test_time = PublicFuc.GetTestTime(purCaseData)
    purCaseData['test_time'] = test_time
    
    LocalWriteSingleLineData(worksheet, lineIdx, purCaseData, colLst, colKeyLst, wlMaxDic)

#写入mars磨损数据
def LocalWriteSingleLineData(worksheet, lineIdx, dataDic, colLst, colKeyLst, wlMaxDic):
    if dataDic == {}:
        return

    innerDic = dataDic
    if innerDic == {}:
        return
    for index,col in enumerate(colLst):
        colName = colKeyLst[index]
        value = ''
        if colName in innerDic:
            value = innerDic[colName]
        if colName == 'test_result':
            if value == 'TRUE':
                worksheet['%s%d'%(col, lineIdx)].font = resultTrueCellFont
            else:
                worksheet['%s%d'%(col, lineIdx)].font = resultFalseCellFont
        if colName == 'valume label' and  value != '':
            value = '#'+value
        worksheet['%s%d'%(col, lineIdx)] = value
        worksheet['%s%d' % (col, lineIdx)].fill = restoreFill
        if '' != value:
            if colName in wlMaxDic:
                if value >= wlMaxDic[colName]:
                    worksheet['%s%d' % (col, lineIdx)] = value
                    worksheet['%s%d'%(col, lineIdx)].fill = warnFill

def SetFirstCol(ws, dataDic, item):
    global startLine
    lineCnt = 0
    lineCnt = len(dataDic)

    colCnt = column_index_from_string('BD')

    if lineCnt > 0:
        #绘制线条
        for rowIdx in range(startLine,startLine+lineCnt):
            for col in range(1,1+colCnt):
                ws['%s%d'%(get_column_letter(col), rowIdx)].alignment = PublicFuc.alignment
                ws['%s%d'%(get_column_letter(col), rowIdx)].border = PublicFuc.my_border('thin', 'thin', 'thin', 'thin')
                ws['A%d'%rowIdx] = rowIdx - 5 #填编号

        ws.merge_cells('B%d:B%d'%(startLine, startLine+lineCnt-1))
        ws['B%d'%startLine].alignment = itemAlignment
        ws['B%d'%startLine].font = itemFont
        ws['B%d'%startLine] = item
        startLine += lineCnt

def GetNewMarsDic(caseDic):
    newDic = {}
    for disk in caseDic:
        startime = ''
        for caseName in list(caseDic[disk].keys()):
            if startime == '':
                startime = caseDic[disk][caseName]['keytime']
            if startime > caseDic[disk][caseName]['keytime']:
                caseDic[disk].pop(caseName)
    for disk in caseDic:
        newDic[disk] = {} #代表CaseData
        for caseName in caseDic[disk]:
            tempDic = {}
            dicData = caseDic[disk][caseName]['case_data']
            newCase = caseDic[disk][caseName]
            newDic[disk][caseName] = newCase
            for key in wliniSmartKeyLst:
                if key not in dicData:
                    if key == 'slc_wl':
                        tempDic['wl_slc_max'] = ''
                        tempDic['wl_slc_min'] = ''
                        tempDic['wl_slc_avg'] = ''
                    elif key == 'tlc_wl':
                        tempDic['wl_tlc_max'] = ''
                        tempDic['wl_tlc_min'] = ''  
                        tempDic['wl_tlc_avg'] = ''
                    else:
                        tempDic[key] = ''
                else:
                    value = dicData[key]
                    if value.startswith('0x'):
                       if key == 'slc inherit wl' or key == 'tlc inherit wl':
                           maxminavg = value.split('|')
                           value = maxminavg[0]
                           value = int(value,16)
                           tempDic[key] = value
                       elif key == 'slc_wl':
                           maxminavg = value.split('|')
                           value = maxminavg[0]
                           value = int(value,16)
                           tempDic['wl_slc_max'] = value
                           value = maxminavg[1]
                           value = int(value,16)
                           tempDic['wl_slc_min'] = value
                           value = maxminavg[2]
                           value = int(value,16)
                           tempDic['wl_slc_avg'] = value
                       elif key == 'tlc_wl':
                           maxminavg = value.split('|')
                           value = maxminavg[0]
                           value = int(value,16)
                           tempDic['wl_tlc_max'] = value
                           value = maxminavg[1]
                           value = int(value,16)
                           tempDic['wl_tlc_min'] = value
                           value = maxminavg[2]
                           value = int(value,16)
                           tempDic['wl_tlc_avg'] = value
                       else:
                           value = int(value,16)
                           tempDic[key] = value
                    else:
                        tempDic[key] = value
                        
            if  'wl_slc_max' not in tempDic:
                tempDic['wl_slc_max'] = ''
            if 'wl_slc_min' not in tempDic:
                tempDic['wl_slc_min'] = ''
            if 'wl_tlc_max' not in tempDic:
                tempDic['wl_tlc_max'] = ''
            if 'wl_tlc_min' not in tempDic:
                tempDic['wl_tlc_min'] = ''
            if 'wl_slc_avg' not in tempDic:
                tempDic['wl_slc_avg'] = ''
            if 'wl_tlc_avg' not in tempDic:
                tempDic['wl_tlc_avg'] = ''

            if '' != tempDic['wl_slc_max'] and '' != tempDic['wl_slc_min']:
                tempDic['slc_diff'] = tempDic['wl_slc_max']-tempDic['wl_slc_min']
            else:
                tempDic['slc_diff'] = ''
            if '' != tempDic['wl_tlc_max'] and '' != tempDic['wl_tlc_min']:
                tempDic['tlc_diff'] = tempDic['wl_tlc_max']-tempDic['wl_tlc_min']
            else:
                tempDic['tlc_diff'] = ''
            if '' != tempDic['wl_slc_avg'] and '' != tempDic['wl_tlc_avg'] and 0 != tempDic['wl_tlc_avg']:
                tempDic['wear_avg'] = round(tempDic['wl_slc_avg']/tempDic['wl_tlc_avg'], 2)
            else:
                tempDic['wear_avg'] = ''

            #需要计算出nand_write_data
            if ('waf_onlydata' in tempDic) and ('write data' in tempDic):
                sum = 0
                try:
                    a = float(tempDic['waf_onlydata'])
                    b = float(tempDic['write data'])
                    tempDic['nand_write_data']=a*b
                    tempDic['nand_write_data']=round(tempDic['nand_write_data'],2)
                except:
                     tempDic['nand_write_data']=''

            #以上是得到了smart相关的信息，现在还需要将非smart信息加入
            for key in dicData:
                if key not in tempDic:
                    value = dicData[key]
                    if value.startswith('0x') and ('|' not in value):
                        value = int(value,16)
                    tempDic[key] = value
            newDic[disk][caseName]['case_data'] = tempDic

    return newDic

def FillBasicInfo(resultDic,ws):
    for sampleId in resultDic:
        pc_no = resultDic[sampleId][0]['MMS_PC']
        ws['%s%d'%('B', 2)] = pc_no
        break

def ProH2(curpath, ws):
    pattern = '.+\\\\Plan6\\\\H2testw.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)
    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'H2testw')

def ProCopyFile(curpath, ws):
    pattern = '.+\\\\Plan6\\\\CopyFile.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)
    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'Copy File/Compare')

def ProImtSmart(curpath, ws):
    pattern = '.+\\\\Plan6\\\\Iometer_4K_WR_12H.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)
    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'Iometer 4K  W/R 12H')

def ProPOR(curpath, ws):
    pattern = '.+\\\\Plan6\\\\POR_20000Cycles.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)
   
    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'POR (20000 cycles)')

def ProSPOR(curpath, ws):
    pattern = '.+\\\\Plan6\\\\SPOR_15000Cycles.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)
    
    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'SPOR (15000 cycles)')

def ProSPORPart(curpath, ws):
    pattern = '.+\\\\Plan6\\\\SPOR_Part_5000Cycles.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)
   
    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'SPOR Part  (5000 cycles)')


def ProFix_Interval_SPOR_24H(curpath, ws):
    pattern = '.+\\\\Plan6\\\\Fix_Interval_SPOR_24H.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)
   
    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'Fix Interval SPOR（24H）')

def ProRT_BIT_1(curpath, ws):
    pattern = '.+\\\\Plan6\\\\RT_BIT_1.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)
   
    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'RT BIT 1')

def ProHCTest(curpath, ws):
    pattern = '.+\\\\Plan6\\\\HCTest.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)
   
    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'HCTest')

def ProReadDisturb_85T(curpath, ws):
    pattern = '.+\\\\Plan6\\\\ReadDisturb_85T.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)
   
    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'Read Disturb 85°C')

def ProHT_BIT_85T_168H(curpath, ws):
    pattern = '.+\\\\Plan6\\\\HT_BIT_85T_168H.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)
   
    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'HT BIT（85℃/168H)')

def ProLT_BIT_N25T_168H(curpath, ws):
    pattern = '.+\\\\Plan6\\\\LT_BIT_-25T_168H.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)
   
    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'LT BIT (-25℃/168H)')

def ProTC_BIT_85T_N25T_168H(curpath, ws):
    pattern = '.+\\\\Plan6\\\\TC_BIT_85T_-25T_168H.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)
   
    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'TC  BIT (85℃~ -25℃) 168H')

def ProPowerRecover6000Cycles(curpath, ws):
    pattern = '.+\\\\Plan6\\\\Power_Recover_6000Cycles.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)

    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'Power Recover (6000 cycles)')

def ProStaticSPOR(curpath, ws):
    pattern = '.+\\\\Plan6\\\\StaticSPOR.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)

    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'StaticSPOR')

def ProFullCardIometer1M4KWrite48H(curpath, ws):
    pattern = '.+\\\\Plan6\\\\Full_Card_Iometer_1M_4K_W_48H.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)

    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'Full card Iometer (1M/4K  W-48H)')

def ProRT_BT_2_25T_168H(curpath, ws):
    pattern = '.+\\\\Plan6\\\\RT_BIT_2_25T_168H.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)

    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'RT BIT 2(25℃/168H)')

def ProFixSectionSPOR1(curpath, ws):
    pattern = '.+\\\\Plan6\\\\FixSectionSPOR1.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)

    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'FixSectionSPOR1')

def ProFixSectionSPOR2(curpath, ws):
    pattern = '.+\\\\Plan6\\\\FixSectionSPOR2.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)

    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'FixSectionSPOR2')

def ProAllMode(curpath, ws):
    pattern = '.+\\\\Plan6\\\\AllMode.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)

    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'AllMode')

def ProIOzone(curpath, ws):
    pattern = '.+\\\\Plan6\\\\IOzone.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)

    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'IOzone')

def ProRemainAreaTest(curpath, ws):
    pattern = '.+\\\\Plan6\\\\RemainAreaTest.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)

    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'RemainAreaTest')

def ProXU4_EnhancedRegions(curpath, ws):
    pattern = '.+\\\\Plan6\\\\XU4_EnhancedRegions.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)

    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'XU4_EnhancedRegions')

def ProXU4_ResponseSleep(curpath, ws):
    pattern = '.+\\\\Plan6\\\\XU4_ResponseSleep.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)

    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'XU4_ResponseSleep')

def ProXU4_RPMBNormal(curpath, ws):
    pattern = '.+\\\\Plan6\\\\XU4_RPMBNormal.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)

    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'XU4_RPMBNormal')

def ProXU4_SPOR1(curpath, ws):
    pattern = '.+\\\\Plan6\\\\XU4_SPOR1.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)

    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'XU4_SPOR1')

def ProXU4_SPOR2(curpath, ws):
    pattern = '.+\\\\Plan6\\\\XU4_SPOR2.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)

    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'XU4_SPOR2')

def ProXU4_SPOR3(curpath, ws):
    pattern = '.+\\\\Plan6\\\\XU4_SPOR3.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)

    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'XU4_SPOR3')

def ProXU4_VCCJitter(curpath, ws):
    pattern = '.+\\\\Plan6\\\\XU4_VCCJitter.*\\\\mms.+.ini$'
    caseName = 'Smart'
    resultDic = {}
    PublicFuc.ReadMarsIniData(curpath, pattern, resultDic, caseName)
    FillBasicInfo(resultDic,ws)

    #所有读取完毕后处理数据
    newDic = GetNewMarsDic(resultDic)
    PublicFuc.WriteMarsWlData(ws, startLine, newDic, colLst, colKeyLst, wlMaxDic)
    SetFirstCol(ws, newDic, 'XU4_VCCJitter')

def ProWl(curpath, ws):
    ProH2(curpath, ws)
    ProCopyFile(curpath, ws)
    #ProImtSmart(curpath, ws)
    ProFullCardIometer1M4KWrite48H(curpath, ws)
    ProPOR(curpath, ws)
    ProSPOR(curpath, ws)
    ProSPORPart(curpath, ws)
    ProStaticSPOR(curpath, ws)
    ProFixSectionSPOR1(curpath, ws)
    ProFixSectionSPOR2(curpath, ws)
    ProPowerRecover6000Cycles(curpath, ws)  
    #ProFix_Interval_SPOR_24H(curpath, ws)
    ProRT_BIT_1(curpath, ws)
    ProRT_BT_2_25T_168H(curpath, ws)
    ProHCTest(curpath, ws)
    ProReadDisturb_85T(curpath, ws)
    ProHT_BIT_85T_168H(curpath, ws)
    ProLT_BIT_N25T_168H(curpath, ws)
    ProTC_BIT_85T_N25T_168H(curpath, ws)
    ProAllMode(curpath, ws)
    ProIOzone(curpath, ws)
    ProRemainAreaTest(curpath, ws)
    ProXU4_EnhancedRegions(curpath, ws)
    ProXU4_ResponseSleep(curpath, ws)
    ProXU4_RPMBNormal(curpath, ws)
    ProXU4_SPOR1(curpath, ws)
    ProXU4_SPOR2(curpath, ws)
    ProXU4_SPOR3(curpath, ws)
    ProXU4_VCCJitter(curpath, ws)


