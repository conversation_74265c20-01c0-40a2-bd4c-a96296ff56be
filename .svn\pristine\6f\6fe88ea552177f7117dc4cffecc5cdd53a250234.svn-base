(function($){function Plot(target_,data_,options_){var series=[],options={colors:["#edc240","#afd8f8","#cb4b4b","#4da74d","#9440ed"],legend:{show:true,noColumns:1,labelFormatter:null,labelBoxBorderColor:"#ccc",container:null,position:"ne",margin:5,backgroundColor:null,backgroundOpacity:0.85},xaxis:{mode:null,min:null,max:null,autoscaleMargin:null,ticks:null,tickFormatter:null,labelWidth:null,labelHeight:null,tickDecimals:null,tickSize:null,minTickSize:null,monthNames:null,timeformat:null},yaxis:{autoscaleMargin:0.02},x2axis:{autoscaleMargin:null},y2axis:{autoscaleMargin:0.02},points:{show:false,radius:3,lineWidth:2,fill:true,fillColor:"#ffffff"},lines:{lineWidth:2,fill:false,fillColor:null,steps:false},bars:{show:false,lineWidth:2,barWidth:1,fill:true,fillColor:null,align:"left"},grid:{color:"#545454",backgroundColor:null,tickColor:"#dddddd",labelMargin:5,borderWidth:2,borderColor:null,markings:null,markingsColor:"#f4f4f4",markingsLineWidth:2,clickable:false,hoverable:false,autoHighlight:true,mouseActiveRadius:10},selection:{mode:null,color:"#e8cfac"},crosshair:{mode:null,color:"#aa0000"},shadowSize:4},canvas=null,overlay=null,eventHolder=null,ctx=null,octx=null,target=$(target_),axes={xaxis:{},yaxis:{},x2axis:{},y2axis:{}},plotOffset={left:0,right:0,top:0,bottom:0},canvasWidth=0,canvasHeight=0,plotWidth=0,plotHeight=0,workarounds={};this.setData=setData;this.setupGrid=setupGrid;this.draw=draw;this.clearSelection=clearSelection;this.setSelection=setSelection;this.getCanvas=function(){return canvas;};this.getPlotOffset=function(){return plotOffset;};this.getData=function(){return series;};this.getAxes=function(){return axes;};this.setCrosshair=setCrosshair;this.clearCrosshair=function(){setCrosshair(null);};this.highlight=highlight;this.unhighlight=unhighlight;parseOptions(options_);setData(data_);constructCanvas();setupGrid();draw();function setData(d){series=parseData(d);fillInSeriesOptions();processData();}
function parseData(d){var res=[];for(var i=0;i<d.length;++i){var s;if(d[i].data){s={};for(var v in d[i])
s[v]=d[i][v];}
else{s={data:d[i]};}
res.push(s);}
return res;}
function parseOptions(o){$.extend(true,options,o);if(options.grid.borderColor==null)
options.grid.borderColor=options.grid.color
if(options.xaxis.noTicks&&options.xaxis.ticks==null)
options.xaxis.ticks=options.xaxis.noTicks;if(options.yaxis.noTicks&&options.yaxis.ticks==null)
options.yaxis.ticks=options.yaxis.noTicks;if(options.grid.coloredAreas)
options.grid.markings=options.grid.coloredAreas;if(options.grid.coloredAreasColor)
options.grid.markingsColor=options.grid.coloredAreasColor;}
function fillInSeriesOptions(){var i;var neededColors=series.length,usedColors=[],assignedColors=[];for(i=0;i<series.length;++i){var sc=series[i].color;if(sc!=null){--neededColors;if(typeof sc=="number")
assignedColors.push(sc);else
usedColors.push(parseColor(series[i].color));}}
for(i=0;i<assignedColors.length;++i){neededColors=Math.max(neededColors,assignedColors[i]+1);}
var colors=[],variation=0;i=0;while(colors.length<neededColors){var c;if(options.colors.length==i)
c=new Color(100,100,100);else
c=parseColor(options.colors[i]);var sign=variation%2==1?-1:1;var factor=1+sign*Math.ceil(variation/2)*0.2;c.scale(factor,factor,factor);colors.push(c);++i;if(i>=options.colors.length){i=0;++variation;}}
var colori=0,s;for(i=0;i<series.length;++i){s=series[i];if(s.color==null){s.color=colors[colori].toString();++colori;}
else if(typeof s.color=="number")
s.color=colors[s.color].toString();s.lines=$.extend(true,{},options.lines,s.lines);s.points=$.extend(true,{},options.points,s.points);s.bars=$.extend(true,{},options.bars,s.bars);if(s.lines.show==null&&!s.bars.show&&!s.points.show)
s.lines.show=true;if(s.shadowSize==null)
s.shadowSize=options.shadowSize;if(!s.xaxis)
s.xaxis=axes.xaxis;if(s.xaxis==1)
s.xaxis=axes.xaxis;else if(s.xaxis==2)
s.xaxis=axes.x2axis;if(!s.yaxis)
s.yaxis=axes.yaxis;if(s.yaxis==1)
s.yaxis=axes.yaxis;else if(s.yaxis==2)
s.yaxis=axes.y2axis;}}
function processData(){var topSentry=Number.POSITIVE_INFINITY,bottomSentry=Number.NEGATIVE_INFINITY,axis,i,j,k;for(axis in axes){axes[axis].datamin=topSentry;axes[axis].datamax=bottomSentry;axes[axis].min=options[axis].min;axes[axis].max=options[axis].max;axes[axis].used=false;}
for(i=0;i<series.length;++i){var s=series[i];s.datapoints={points:[],incr:2};var data=s.data,points=s.datapoints.points,incr=s.datapoints.incr,axisx=s.xaxis,axisy=s.yaxis,xmin=topSentry,xmax=bottomSentry,ymin=topSentry,ymax=bottomSentry;axisx.used=axisy.used=true;for(j=k=0;j<data.length;++j,k+=incr){var x=null,y=null;if(data[j]!=null){x=data[j][0];y=data[j][1];}
if(x!=null&&!isNaN(x=+x)){if(x<xmin)
xmin=x;if(x>xmax)
xmax=x}
else
x=null;if(y!=null&&!isNaN(y=+y)){if(y<ymin)
ymin=y;if(y>ymax)
ymax=y;}
else
y=null;if(x==null||y==null)
x=y=null;points[k+1]=y;points[k]=x;}
if(s.bars.show){var delta=s.bars.align=="left"?0:-s.bars.barWidth/2;xmin+=delta;xmax+=delta+s.bars.barWidth;}
axisx.datamin=Math.min(axisx.datamin,xmin);axisx.datamax=Math.max(axisx.datamax,xmax);axisy.datamin=Math.min(axisy.datamin,ymin);axisy.datamax=Math.max(axisy.datamax,ymax);}}
function constructCanvas(){function makeCanvas(width,height){var c=document.createElement('canvas');c.width=width;c.height=height;
try{c=window.G_vmlCanvasManager.initElement(c);}catch(e){}return c;}
canvasWidth=target.width();canvasHeight=target.height();target.html("");if(target.css("position")=='static')
target.css("position","relative");if(canvasWidth<=0||canvasHeight<=0)
throw"Invalid dimensions for plot, width = "+canvasWidth+", height = "+canvasHeight;canvas=$(makeCanvas(canvasWidth,canvasHeight)).appendTo(target).get(0);ctx=canvas.getContext("2d");overlay=$(makeCanvas(canvasWidth,canvasHeight)).css({position:'absolute',left:0,top:0}).appendTo(target).get(0);octx=overlay.getContext("2d");eventHolder=$([overlay,canvas]);if(options.selection.mode!=null||options.crosshair.mode!=null||options.grid.hoverable){eventHolder.each(function(){this.onmousemove=onMouseMove;});if(options.selection.mode!=null)
eventHolder.mousedown(onMouseDown);}
if(options.crosshair.mode!=null)
eventHolder.mouseout(onMouseOut);if(options.grid.clickable)
eventHolder.click(onClick);}
function setupGrid(){function setupAxis(axis,options){setRange(axis,options);prepareTickGeneration(axis,options);setTicks(axis,options);if(axis==axes.xaxis||axis==axes.x2axis){axis.p2c=function(p){return(p-axis.min)*axis.scale;};axis.c2p=function(c){return axis.min+c/axis.scale;};}
else{axis.p2c=function(p){return(axis.max-p)*axis.scale;};axis.c2p=function(p){return axis.max-p/axis.scale;};}}
for(var axis in axes)
setupAxis(axes[axis],options[axis]);setSpacing();insertLabels();insertLegend();}
function setRange(axis,axisOptions){var min=axisOptions.min!=null?+axisOptions.min:axis.datamin,max=axisOptions.max!=null?+axisOptions.max:axis.datamax;if(min==Number.POSITIVE_INFINITY)
min=0;if(max==Number.NEGATIVE_INFINITY)
max=1;if(max-min==0.0){var widen=max==0?1:0.01;if(axisOptions.min==null)
min-=widen;if(axisOptions.max==null||axisOptions.min!=null)
max+=widen;}
else{var margin=axisOptions.autoscaleMargin;if(margin!=null){if(axisOptions.min==null){min-=(max-min)*margin;if(min<0&&axis.datamin>=0)
min=0;}
if(axisOptions.max==null){max+=(max-min)*margin;if(max>0&&axis.datamax<=0)
max=0;}}}
axis.min=min;axis.max=max;}
function prepareTickGeneration(axis,axisOptions){var noTicks;if(typeof axisOptions.ticks=="number"&&axisOptions.ticks>0)
noTicks=axisOptions.ticks;else if(axis==axes.xaxis||axis==axes.x2axis)
noTicks=canvasWidth/100;else
noTicks=canvasHeight/60;var delta=(axis.max-axis.min)/noTicks;var size,generator,unit,formatter,i,magn,norm;if(axisOptions.mode=="time"){var timeUnitSize={"second":1000,"minute":60*1000,"hour":60*60*1000,"day":24*60*60*1000,"month":30*24*60*60*1000,"year":365.2425*24*60*60*1000};var spec=[[1,"second"],[2,"second"],[5,"second"],[10,"second"],[30,"second"],[1,"minute"],[2,"minute"],[5,"minute"],[10,"minute"],[30,"minute"],[1,"hour"],[2,"hour"],[4,"hour"],[8,"hour"],[12,"hour"],[1,"day"],[2,"day"],[3,"day"],[0.25,"month"],[0.5,"month"],[1,"month"],[2,"month"],[3,"month"],[6,"month"],[1,"year"]];var minSize=0;if(axisOptions.minTickSize!=null){if(typeof axisOptions.tickSize=="number")
minSize=axisOptions.tickSize;else
minSize=axisOptions.minTickSize[0]*timeUnitSize[axisOptions.minTickSize[1]];}
for(i=0;i<spec.length-1;++i)
if(delta<(spec[i][0]*timeUnitSize[spec[i][1]]
+spec[i+1][0]*timeUnitSize[spec[i+1][1]])/2&&spec[i][0]*timeUnitSize[spec[i][1]]>=minSize)
break;size=spec[i][0];unit=spec[i][1];if(unit=="year"){magn=Math.pow(10,Math.floor(Math.log(delta/timeUnitSize.year)/Math.LN10));norm=(delta/timeUnitSize.year)/magn;if(norm<1.5)
size=1;else if(norm<3)
size=2;else if(norm<7.5)
size=5;else
size=10;size*=magn;}
if(axisOptions.tickSize){size=axisOptions.tickSize[0];unit=axisOptions.tickSize[1];}
generator=function(axis){var ticks=[],tickSize=axis.tickSize[0],unit=axis.tickSize[1],d=new Date(axis.min);var step=tickSize*timeUnitSize[unit];if(unit=="second")
d.setUTCSeconds(floorInBase(d.getUTCSeconds(),tickSize));if(unit=="minute")
d.setUTCMinutes(floorInBase(d.getUTCMinutes(),tickSize));if(unit=="hour")
d.setUTCHours(floorInBase(d.getUTCHours(),tickSize));if(unit=="month")
d.setUTCMonth(floorInBase(d.getUTCMonth(),tickSize));if(unit=="year")
d.setUTCFullYear(floorInBase(d.getUTCFullYear(),tickSize));d.setUTCMilliseconds(0);if(step>=timeUnitSize.minute)
d.setUTCSeconds(0);if(step>=timeUnitSize.hour)
d.setUTCMinutes(0);if(step>=timeUnitSize.day)
d.setUTCHours(0);if(step>=timeUnitSize.day*4)
d.setUTCDate(1);if(step>=timeUnitSize.year)
d.setUTCMonth(0);var carry=0,v=Number.NaN,prev;do{prev=v;v=d.getTime();ticks.push({v:v,label:axis.tickFormatter(v,axis)});if(unit=="month"){if(tickSize<1){d.setUTCDate(1);var start=d.getTime();d.setUTCMonth(d.getUTCMonth()+1);var end=d.getTime();d.setTime(v+carry*timeUnitSize.hour+(end-start)*tickSize);carry=d.getUTCHours();d.setUTCHours(0);}
else
d.setUTCMonth(d.getUTCMonth()+tickSize);}
else if(unit=="year"){d.setUTCFullYear(d.getUTCFullYear()+tickSize);}
else
d.setTime(v+step);}while(v<axis.max&&v!=prev);return ticks;};formatter=function(v,axis){var d=new Date(v);if(axisOptions.timeformat!=null)
return $.plot.formatDate(d,axisOptions.timeformat,axisOptions.monthNames);var t=axis.tickSize[0]*timeUnitSize[axis.tickSize[1]];var span=axis.max-axis.min;if(t<timeUnitSize.minute)
fmt="%h:%M:%S";else if(t<timeUnitSize.day){if(span<2*timeUnitSize.day)
fmt="%h:%M";else
fmt="%b %d %h:%M";}
else if(t<timeUnitSize.month)
fmt="%b %d";else if(t<timeUnitSize.year){if(span<timeUnitSize.year)
fmt="%b";else
fmt="%b %y";}
else
fmt="%y";return $.plot.formatDate(d,fmt,axisOptions.monthNames);};}
else{var maxDec=axisOptions.tickDecimals;var dec=-Math.floor(Math.log(delta)/Math.LN10);if(maxDec!=null&&dec>maxDec)
dec=maxDec;magn=Math.pow(10,-dec);norm=delta/magn;if(norm<1.5)
size=1;else if(norm<3){size=2;if(norm>2.25&&(maxDec==null||dec+1<=maxDec)){size=2.5;++dec;}}
else if(norm<7.5)
size=5;else
size=10;size*=magn;if(axisOptions.minTickSize!=null&&size<axisOptions.minTickSize)
size=axisOptions.minTickSize;if(axisOptions.tickSize!=null)
size=axisOptions.tickSize;axis.tickDecimals=Math.max(0,(maxDec!=null)?maxDec:dec);generator=function(axis){var ticks=[];var start=floorInBase(axis.min,axis.tickSize),i=0,v=Number.NaN,prev;do{prev=v;v=start+i*axis.tickSize;ticks.push({v:v,label:axis.tickFormatter(v,axis)});++i;}while(v<axis.max&&v!=prev);return ticks;};formatter=function(v,axis){return v.toFixed(axis.tickDecimals);};}
axis.tickSize=unit?[size,unit]:size;axis.tickGenerator=generator;if($.isFunction(axisOptions.tickFormatter))
axis.tickFormatter=function(v,axis){return""+axisOptions.tickFormatter(v,axis);};else
axis.tickFormatter=formatter;if(axisOptions.labelWidth!=null)
axis.labelWidth=axisOptions.labelWidth;if(axisOptions.labelHeight!=null)
axis.labelHeight=axisOptions.labelHeight;}
function setTicks(axis,axisOptions){axis.ticks=[];if(!axis.used)
return;if(axisOptions.ticks==null)
axis.ticks=axis.tickGenerator(axis);else if(typeof axisOptions.ticks=="number"){if(axisOptions.ticks>0)
axis.ticks=axis.tickGenerator(axis);}
else if(axisOptions.ticks){var ticks=axisOptions.ticks;if($.isFunction(ticks))
ticks=ticks({min:axis.min,max:axis.max});var i,v;for(i=0;i<ticks.length;++i){var label=null;var t=ticks[i];if(typeof t=="object"){v=t[0];if(t.length>1)
label=t[1];}
else
v=t;if(label==null)
label=axis.tickFormatter(v,axis);axis.ticks[i]={v:v,label:label};}}
if(axisOptions.autoscaleMargin!=null&&axis.ticks.length>0){if(axisOptions.min==null)
axis.min=Math.min(axis.min,axis.ticks[0].v);if(axisOptions.max==null&&axis.ticks.length>1)
axis.max=Math.min(axis.max,axis.ticks[axis.ticks.length-1].v);}}
function setSpacing(){function measureXLabels(axis){if(axis.labelWidth==null)
axis.labelWidth=canvasWidth/6;if(axis.labelHeight==null){labels=[];for(i=0;i<axis.ticks.length;++i){l=axis.ticks[i].label;if(l)
labels.push('<div class="tickLabel" style="float:left;width:'+axis.labelWidth+'px">'+l+'</div>');}
axis.labelHeight=0;if(labels.length>0){var dummyDiv=$('<div style="position:absolute;top:-10000px;width:10000px;font-size:smaller">'
+labels.join("")+'<div style="clear:left"></div></div>').appendTo(target);axis.labelHeight=dummyDiv.height();dummyDiv.remove();}}}
function measureYLabels(axis){if(axis.labelWidth==null||axis.labelHeight==null){var i,labels=[],l;for(i=0;i<axis.ticks.length;++i){l=axis.ticks[i].label;if(l)
labels.push('<div class="tickLabel">'+l+'</div>');}
if(labels.length>0){var dummyDiv=$('<div style="position:absolute;top:-10000px;font-size:smaller">'
+labels.join("")+'</div>').appendTo(target);if(axis.labelWidth==null)
axis.labelWidth=dummyDiv.width();if(axis.labelHeight==null)
axis.labelHeight=dummyDiv.find("div").height();dummyDiv.remove();}
if(axis.labelWidth==null)
axis.labelWidth=0;if(axis.labelHeight==null)
axis.labelHeight=0;}}
measureXLabels(axes.xaxis);measureYLabels(axes.yaxis);measureXLabels(axes.x2axis);measureYLabels(axes.y2axis);var maxOutset=options.grid.borderWidth;for(i=0;i<series.length;++i)
maxOutset=Math.max(maxOutset,2*(series[i].points.radius+series[i].points.lineWidth/2));plotOffset.left=plotOffset.right=plotOffset.top=plotOffset.bottom=maxOutset;var margin=options.grid.labelMargin+options.grid.borderWidth;if(axes.xaxis.labelHeight>0)
plotOffset.bottom=Math.max(maxOutset,axes.xaxis.labelHeight+margin);if(axes.yaxis.labelWidth>0)
plotOffset.left=Math.max(maxOutset,axes.yaxis.labelWidth+margin);if(axes.x2axis.labelHeight>0)
plotOffset.top=Math.max(maxOutset,axes.x2axis.labelHeight+margin);if(axes.y2axis.labelWidth>0)
plotOffset.right=Math.max(maxOutset,axes.y2axis.labelWidth+margin);plotWidth=canvasWidth-plotOffset.left-plotOffset.right;plotHeight=canvasHeight-plotOffset.bottom-plotOffset.top;axes.xaxis.scale=plotWidth/(axes.xaxis.max-axes.xaxis.min);axes.yaxis.scale=plotHeight/(axes.yaxis.max-axes.yaxis.min);axes.x2axis.scale=plotWidth/(axes.x2axis.max-axes.x2axis.min);axes.y2axis.scale=plotHeight/(axes.y2axis.max-axes.y2axis.min);}
function draw(){drawGrid();for(var i=0;i<series.length;i++){drawSeries(series[i]);}}
function extractRange(ranges,coord){var firstAxis=coord+"axis",secondaryAxis=coord+"2axis",axis,from,to,reverse;if(ranges[firstAxis]){axis=axes[firstAxis];from=ranges[firstAxis].from;to=ranges[firstAxis].to;}
else if(ranges[secondaryAxis]){axis=axes[secondaryAxis];from=ranges[secondaryAxis].from;to=ranges[secondaryAxis].to;}
else{axis=axes[firstAxis];from=ranges[coord+"1"];to=ranges[coord+"2"];}
if(from!=null&&to!=null&&from>to)
return{from:to,to:from,axis:axis};return{from:from,to:to,axis:axis};}
function drawGrid(){var i;ctx.save();ctx.clearRect(0,0,canvasWidth,canvasHeight);ctx.translate(plotOffset.left,plotOffset.top);if(options.grid.backgroundColor){ctx.fillStyle=getColorOrGradient(options.grid.backgroundColor,plotHeight,0,"rgba(255, 255, 255, 0)");ctx.fillRect(0,0,plotWidth,plotHeight);}
var markings=options.grid.markings;if(markings){if($.isFunction(markings))
markings=markings({xmin:axes.xaxis.min,xmax:axes.xaxis.max,ymin:axes.yaxis.min,ymax:axes.yaxis.max,xaxis:axes.xaxis,yaxis:axes.yaxis,x2axis:axes.x2axis,y2axis:axes.y2axis});for(i=0;i<markings.length;++i){var m=markings[i],xrange=extractRange(m,"x"),yrange=extractRange(m,"y");if(xrange.from==null)
xrange.from=xrange.axis.min;if(xrange.to==null)
xrange.to=xrange.axis.max;if(yrange.from==null)
yrange.from=yrange.axis.min;if(yrange.to==null)
yrange.to=yrange.axis.max;if(xrange.to<xrange.axis.min||xrange.from>xrange.axis.max||yrange.to<yrange.axis.min||yrange.from>yrange.axis.max)
continue;xrange.from=Math.max(xrange.from,xrange.axis.min);xrange.to=Math.min(xrange.to,xrange.axis.max);yrange.from=Math.max(yrange.from,yrange.axis.min);yrange.to=Math.min(yrange.to,yrange.axis.max);if(xrange.from==xrange.to&&yrange.from==yrange.to)
continue;xrange.from=xrange.axis.p2c(xrange.from);xrange.to=xrange.axis.p2c(xrange.to);yrange.from=yrange.axis.p2c(yrange.from);yrange.to=yrange.axis.p2c(yrange.to);if(xrange.from==xrange.to||yrange.from==yrange.to){ctx.strokeStyle=m.color||options.grid.markingsColor;ctx.beginPath();ctx.lineWidth=m.lineWidth||options.grid.markingsLineWidth;ctx.moveTo(xrange.from,yrange.from);ctx.lineTo(xrange.to,yrange.to);ctx.stroke();}
else{ctx.fillStyle=m.color||options.grid.markingsColor;ctx.fillRect(xrange.from,yrange.to,xrange.to-xrange.from,yrange.from-yrange.to);}}}
ctx.lineWidth=1;ctx.strokeStyle=options.grid.tickColor;ctx.beginPath();var v,axis=axes.xaxis;for(i=0;i<axis.ticks.length;++i){v=axis.ticks[i].v;if(v<=axis.min||v>=axes.xaxis.max)
continue;ctx.moveTo(Math.floor(axis.p2c(v))+ctx.lineWidth/2,0);ctx.lineTo(Math.floor(axis.p2c(v))+ctx.lineWidth/2,plotHeight);}
axis=axes.yaxis;for(i=0;i<axis.ticks.length;++i){v=axis.ticks[i].v;if(v<=axis.min||v>=axis.max)
continue;ctx.moveTo(0,Math.floor(axis.p2c(v))+ctx.lineWidth/2);ctx.lineTo(plotWidth,Math.floor(axis.p2c(v))+ctx.lineWidth/2);}
axis=axes.x2axis;for(i=0;i<axis.ticks.length;++i){v=axis.ticks[i].v;if(v<=axis.min||v>=axis.max)
continue;ctx.moveTo(Math.floor(axis.p2c(v))+ctx.lineWidth/2,-5);ctx.lineTo(Math.floor(axis.p2c(v))+ctx.lineWidth/2,5);}
axis=axes.y2axis;for(i=0;i<axis.ticks.length;++i){v=axis.ticks[i].v;if(v<=axis.min||v>=axis.max)
continue;ctx.moveTo(plotWidth-5,Math.floor(axis.p2c(v))+ctx.lineWidth/2);ctx.lineTo(plotWidth+5,Math.floor(axis.p2c(v))+ctx.lineWidth/2);}
ctx.stroke();if(options.grid.borderWidth){var bw=options.grid.borderWidth;ctx.lineWidth=bw;ctx.strokeStyle=options.grid.borderColor;ctx.strokeRect(-bw/2,-bw/2,plotWidth+bw,plotHeight+bw);}
ctx.restore();}
function insertLabels(){target.find(".tickLabels").remove();var html='<div class="tickLabels" style="font-size:smaller;color:'+options.grid.color+'">';function addLabels(axis,labelGenerator){for(var i=0;i<axis.ticks.length;++i){var tick=axis.ticks[i];if(!tick.label||tick.v<axis.min||tick.v>axis.max)
continue;html+=labelGenerator(tick,axis);}}
var margin=options.grid.labelMargin+options.grid.borderWidth;addLabels(axes.xaxis,function(tick,axis){return'<div style="position:absolute;top:'+(plotOffset.top+plotHeight+margin)+'px;left:'+(plotOffset.left+axis.p2c(tick.v)-axis.labelWidth/2)+'px;width:'+axis.labelWidth+'px;text-align:center" class="tickLabel">'+tick.label+"</div>";});addLabels(axes.yaxis,function(tick,axis){return'<div style="position:absolute;top:'+(plotOffset.top+axis.p2c(tick.v)-axis.labelHeight/2)+'px;right:'+(plotOffset.right+plotWidth+margin)+'px;width:'+axis.labelWidth+'px;text-align:right" class="tickLabel">'+parseInt(tick.label)+"</div>";});addLabels(axes.x2axis,function(tick,axis){return'<div style="position:absolute;bottom:'+(plotOffset.bottom+plotHeight+margin)+'px;left:'+(plotOffset.left+axis.p2c(tick.v)-axis.labelWidth/2)+'px;width:'+axis.labelWidth+'px;text-align:center" class="tickLabel">'+tick.label+"</div>";});addLabels(axes.y2axis,function(tick,axis){return'<div style="position:absolute;top:'+(plotOffset.top+axis.p2c(tick.v)-axis.labelHeight/2)+'px;left:'+(plotOffset.left+plotWidth+margin)+'px;width:'+axis.labelWidth+'px;text-align:left" class="tickLabel">'+tick.label+"</div>";});html+='</div>';target.append(html);}
function drawSeries(series){if(series.lines.show)
drawSeriesLines(series);if(series.bars.show)
drawSeriesBars(series);if(series.points.show)
drawSeriesPoints(series);}
function drawSeriesLines(series){function plotLine(datapoints,offset,axisx,axisy){var points=datapoints.points,incr=datapoints.incr,drawx=null,drawy=null;ctx.beginPath();for(var i=incr;i<points.length;i+=incr){var x1=points[i-incr],y1=points[i-incr+1],x2=points[i],y2=points[i+1];if(x1==null||x2==null)
continue;if(y1<=y2&&y1<axisy.min){if(y2<axisy.min)
continue;x1=(axisy.min-y1)/(y2-y1)*(x2-x1)+x1;y1=axisy.min;}
else if(y2<=y1&&y2<axisy.min){if(y1<axisy.min)
continue;x2=(axisy.min-y1)/(y2-y1)*(x2-x1)+x1;y2=axisy.min;}
if(y1>=y2&&y1>axisy.max){if(y2>axisy.max)
continue;x1=(axisy.max-y1)/(y2-y1)*(x2-x1)+x1;y1=axisy.max;}
else if(y2>=y1&&y2>axisy.max){if(y1>axisy.max)
continue;x2=(axisy.max-y1)/(y2-y1)*(x2-x1)+x1;y2=axisy.max;}
if(x1<=x2&&x1<axisx.min){if(x2<axisx.min)
continue;y1=(axisx.min-x1)/(x2-x1)*(y2-y1)+y1;x1=axisx.min;}
else if(x2<=x1&&x2<axisx.min){if(x1<axisx.min)
continue;y2=(axisx.min-x1)/(x2-x1)*(y2-y1)+y1;x2=axisx.min;}
if(x1>=x2&&x1>axisx.max){if(x2>axisx.max)
continue;y1=(axisx.max-x1)/(x2-x1)*(y2-y1)+y1;x1=axisx.max;}
else if(x2>=x1&&x2>axisx.max){if(x1>axisx.max)
continue;y2=(axisx.max-x1)/(x2-x1)*(y2-y1)+y1;x2=axisx.max;}
if(drawx!=axisx.p2c(x1)||drawy!=axisy.p2c(y1)+offset)
ctx.moveTo(axisx.p2c(x1),axisy.p2c(y1)+offset);drawx=axisx.p2c(x2);drawy=axisy.p2c(y2)+offset;ctx.lineTo(drawx,drawy);}
ctx.stroke();}
function plotLineArea(datapoints,axisx,axisy){var points=datapoints.points,incr=datapoints.incr,bottom=Math.min(Math.max(0,axisy.min),axisy.max),top,lastX=0,areaOpen=false;for(var i=incr;i<points.length;i+=incr){var x1=points[i-incr],y1=points[i-incr+1],x2=points[i],y2=points[i+1];if(areaOpen&&x1!=null&&x2==null){ctx.lineTo(axisx.p2c(lastX),axisy.p2c(bottom));ctx.fill();areaOpen=false;continue;}
if(x1==null||x2==null)
continue;if(x1<=x2&&x1<axisx.min){if(x2<axisx.min)
continue;y1=(axisx.min-x1)/(x2-x1)*(y2-y1)+y1;x1=axisx.min;}
else if(x2<=x1&&x2<axisx.min){if(x1<axisx.min)
continue;y2=(axisx.min-x1)/(x2-x1)*(y2-y1)+y1;x2=axisx.min;}
if(x1>=x2&&x1>axisx.max){if(x2>axisx.max)
continue;y1=(axisx.max-x1)/(x2-x1)*(y2-y1)+y1;x1=axisx.max;}
else if(x2>=x1&&x2>axisx.max){if(x1>axisx.max)
continue;y2=(axisx.max-x1)/(x2-x1)*(y2-y1)+y1;x2=axisx.max;}
if(!areaOpen){ctx.beginPath();ctx.moveTo(axisx.p2c(x1),axisy.p2c(bottom));areaOpen=true;}
if(y1>=axisy.max&&y2>=axisy.max){ctx.lineTo(axisx.p2c(x1),axisy.p2c(axisy.max));ctx.lineTo(axisx.p2c(x2),axisy.p2c(axisy.max));lastX=x2;continue;}
else if(y1<=axisy.min&&y2<=axisy.min){ctx.lineTo(axisx.p2c(x1),axisy.p2c(axisy.min));ctx.lineTo(axisx.p2c(x2),axisy.p2c(axisy.min));lastX=x2;continue;}
var x1old=x1,x2old=x2;if(y1<=y2&&y1<axisy.min&&y2>=axisy.min){x1=(axisy.min-y1)/(y2-y1)*(x2-x1)+x1;y1=axisy.min;}
else if(y2<=y1&&y2<axisy.min&&y1>=axisy.min){x2=(axisy.min-y1)/(y2-y1)*(x2-x1)+x1;y2=axisy.min;}
if(y1>=y2&&y1>axisy.max&&y2<=axisy.max){x1=(axisy.max-y1)/(y2-y1)*(x2-x1)+x1;y1=axisy.max;}
else if(y2>=y1&&y2>axisy.max&&y1<=axisy.max){x2=(axisy.max-y1)/(y2-y1)*(x2-x1)+x1;y2=axisy.max;}
if(x1!=x1old){if(y1<=axisy.min)
top=axisy.min;else
top=axisy.max;ctx.lineTo(axisx.p2c(x1old),axisy.p2c(top));ctx.lineTo(axisx.p2c(x1),axisy.p2c(top));}
ctx.lineTo(axisx.p2c(x1),axisy.p2c(y1));ctx.lineTo(axisx.p2c(x2),axisy.p2c(y2));if(x2!=x2old){if(y2<=axisy.min)
top=axisy.min;else
top=axisy.max;ctx.lineTo(axisx.p2c(x2),axisy.p2c(top));ctx.lineTo(axisx.p2c(x2old),axisy.p2c(top));}
lastX=Math.max(x2,x2old);}
if(areaOpen){ctx.lineTo(axisx.p2c(lastX),axisy.p2c(bottom));ctx.fill();}}
ctx.save();ctx.translate(plotOffset.left,plotOffset.top);ctx.lineJoin="round";var lw=series.lines.lineWidth,sw=series.shadowSize;if(lw>0&&sw>0){var w=sw/2;ctx.lineWidth=w;ctx.strokeStyle="rgba(0,0,0,0.1)";plotLine(series.datapoints,lw/2+w+w/2,series.xaxis,series.yaxis);ctx.strokeStyle="rgba(0,0,0,0.2)";plotLine(series.datapoints,lw/2+w/2,series.xaxis,series.yaxis);}
ctx.lineWidth=lw;ctx.strokeStyle=series.color;var fillStyle=getFillStyle(series.lines,series.color,0,plotHeight);if(fillStyle){ctx.fillStyle=fillStyle;plotLineArea(series.datapoints,series.xaxis,series.yaxis);}
if(lw>0)
plotLine(series.datapoints,0,series.xaxis,series.yaxis);ctx.restore();}
function drawSeriesPoints(series){function plotPoints(datapoints,radius,fillStyle,offset,circumference,axisx,axisy){var points=datapoints.points,incr=datapoints.incr;for(var i=0;i<points.length;i+=incr){var x=points[i],y=points[i+1];if(x==null||x<axisx.min||x>axisx.max||y<axisy.min||y>axisy.max)
continue;ctx.beginPath();ctx.arc(axisx.p2c(x),axisy.p2c(y)+offset,radius,0,circumference,true);if(fillStyle){ctx.fillStyle=fillStyle;ctx.fill();}
ctx.stroke();}}
ctx.save();ctx.translate(plotOffset.left,plotOffset.top);var lw=series.lines.lineWidth,sw=series.shadowSize,radius=series.points.radius;if(lw>0&&sw>0){var w=sw/2;ctx.lineWidth=w;ctx.strokeStyle="rgba(0,0,0,0.1)";plotPoints(series.datapoints,radius,null,w+w/2,2*Math.PI,series.xaxis,series.yaxis);ctx.strokeStyle="rgba(0,0,0,0.2)";plotPoints(series.datapoints,radius,null,w/2,2*Math.PI,series.xaxis,series.yaxis);}
ctx.lineWidth=lw;ctx.strokeStyle=series.color;plotPoints(series.datapoints,radius,getFillStyle(series.points,series.color),0,2*Math.PI,series.xaxis,series.yaxis);ctx.restore();}
function drawBar(x,y,barLeft,barRight,offset,fillStyleCallback,axisx,axisy,c){var drawLeft=true,drawRight=true,drawTop=true,drawBottom=false,left=x+barLeft,right=x+barRight,bottom=0,top=y;if(top<bottom){top=0;bottom=y;drawBottom=true;drawTop=false;}
if(right<axisx.min||left>axisx.max||top<axisy.min||bottom>axisy.max)
return;if(left<axisx.min){left=axisx.min;drawLeft=false;}
if(right>axisx.max){right=axisx.max;drawRight=false;}
if(bottom<axisy.min){bottom=axisy.min;drawBottom=false;}
if(top>axisy.max){top=axisy.max;drawTop=false;}
left=axisx.p2c(left);bottom=axisy.p2c(bottom);right=axisx.p2c(right);top=axisy.p2c(top);if(fillStyleCallback){c.beginPath();c.moveTo(left,bottom);c.lineTo(left,top);c.lineTo(right,top);c.lineTo(right,bottom);c.fillStyle=fillStyleCallback(bottom,top);c.fill();}
if(drawLeft||drawRight||drawTop||drawBottom){c.beginPath();c.moveTo(left,bottom+offset);if(drawLeft)
c.lineTo(left,top+offset);else
c.moveTo(left,top+offset);if(drawTop)
c.lineTo(right,top+offset);else
c.moveTo(right,top+offset);if(drawRight)
c.lineTo(right,bottom+offset);else
c.moveTo(right,bottom+offset);if(drawBottom)
c.lineTo(left,bottom+offset);else
c.moveTo(left,bottom+offset);c.stroke();}}
function drawSeriesBars(series){function plotBars(datapoints,barLeft,barRight,offset,fillStyleCallback,axisx,axisy){var points=datapoints.points,incr=datapoints.incr;for(var i=0;i<points.length;i+=incr){if(points[i]==null)
continue;drawBar(points[i],points[i+1],barLeft,barRight,offset,fillStyleCallback,axisx,axisy,ctx);}}
ctx.save();ctx.translate(plotOffset.left,plotOffset.top);ctx.lineJoin="round";ctx.lineWidth=series.bars.lineWidth;ctx.strokeStyle=series.color;var barLeft=series.bars.align=="left"?0:-series.bars.barWidth/2;var fillStyleCallback=series.bars.fill?function(bottom,top){return getFillStyle(series.bars,series.color,bottom,top);}:null;plotBars(series.datapoints,barLeft,barLeft+series.bars.barWidth,0,fillStyleCallback,series.xaxis,series.yaxis);ctx.restore();}
function getFillStyle(filloptions,seriesColor,bottom,top){var fill=filloptions.fill;if(!fill)
return null;if(filloptions.fillColor)
return getColorOrGradient(filloptions.fillColor,bottom,top,seriesColor);var c=parseColor(seriesColor);c.a=typeof fill=="number"?fill:0.4;c.normalize();return c.toString();}
function insertLegend(){target.find(".legend").remove();if(!options.legend.show)
return;var fragments=[];var rowStarted=false;for(i=0;i<series.length;++i){if(!series[i].label)
continue;if(i%options.legend.noColumns==0){if(rowStarted)
fragments.push('</tr>');fragments.push('<tr>');rowStarted=true;}
var label=series[i].label;if(options.legend.labelFormatter!=null)
label=options.legend.labelFormatter(label);fragments.push('<td class="legendColorBox"><div style="border:1px solid '+options.legend.labelBoxBorderColor+';padding:1px"><div style="width:4px;height:0;border:5px solid '+series[i].color+';overflow:hidden"></div></div></td>'+'<td class="legendLabel">'+label+'</td>');}
if(rowStarted)
fragments.push('</tr>');if(fragments.length==0)
return;var table='<table style="font-size:smaller;color:'+options.grid.color+'">'+fragments.join("")+'</table>';if(options.legend.container!=null)
$(options.legend.container).html(table);else{var pos="",p=options.legend.position,m=options.legend.margin;if(m[0]==null)
m=[m,m];if(p.charAt(0)=="n")
pos+='top:'+(m[1]+plotOffset.top)+'px;';else if(p.charAt(0)=="s")
pos+='bottom:'+(m[1]+plotOffset.bottom)+'px;';if(p.charAt(1)=="e")
pos+='right:'+(m[0]+plotOffset.right)+'px;';else if(p.charAt(1)=="w")
pos+='left:'+(m[0]+plotOffset.left)+'px;';var legend=$('<div class="legend">'+table.replace('style="','style="position:absolute;'+pos+';')+'</div>').appendTo(target);if(options.legend.backgroundOpacity!=0.0){var c=options.legend.backgroundColor;if(c==null){var tmp;if(options.grid.backgroundColor&&typeof options.grid.backgroundColor=="string")
tmp=options.grid.backgroundColor;else
tmp=extractColor(legend);c=parseColor(tmp).adjust(null,null,null,1).toString();}
var div=legend.children();$('<div style="position:absolute;width:'+div.width()+'px;height:'+div.height()+'px;'+pos+'background-color:'+c+';"> </div>').prependTo(legend).css('opacity',options.legend.backgroundOpacity);}}}
var lastMousePos={pageX:null,pageY:null},selection={first:{x:-1,y:-1},second:{x:-1,y:-1},show:false,active:false},crosshair={pos:{x:-1,y:-1}},highlights=[],clickIsMouseUp=false,redrawTimeout=null,hoverTimeout=null;function findNearbyItem(mouseX,mouseY,seriesFilter){var maxDistance=options.grid.mouseActiveRadius,lowestDistance=maxDistance*maxDistance+1,item=null,foundPoint=false,i,j;for(var i=0;i<series.length;++i){if(!seriesFilter(series[i]))
continue;var s=series[i],points=s.datapoints.points,incr=s.datapoints.incr,axisx=s.xaxis,axisy=s.yaxis,mx=axisx.c2p(mouseX),my=axisy.c2p(mouseY),maxx=maxDistance/axisx.scale,maxy=maxDistance/axisy.scale,checkbar=s.bars.show,checkpoint=!s.bars.show||s.lines.show||s.points.show,barLeft=s.bars.align=="left"?0:-s.bars.barWidth/2,barRight=barLeft+s.bars.barWidth;for(j=0;j<points.length;j+=incr){var x=points[j],y=points[j+1];if(x==null)
continue;if(checkbar){if(!foundPoint&&mx>=x+barLeft&&mx<=x+barRight&&my>=Math.min(0,y)&&my<=Math.max(0,y))
item=[i,j/incr];}
if(checkpoint){if((x-mx>maxx||x-mx<-maxx)||(y-my>maxy||y-my<-maxy))
continue;var dx=Math.abs(axisx.p2c(x)-mouseX),dy=Math.abs(axisy.p2c(y)-mouseY),dist=dx*dx+dy*dy;if(dist<lowestDistance){lowestDistance=dist;foundPoint=true;item=[i,j/incr];}}}}
if(item){i=item[0];j=item[1];return{datapoint:series[i].data[j],dataIndex:j,series:series[i],seriesIndex:i}}
return null;}
function onMouseMove(ev){var e=ev||window.event;if(e.pageX==null&&e.clientX!=null){var de=document.documentElement,b=document.body;lastMousePos.pageX=e.clientX+(de&&de.scrollLeft||b.scrollLeft||0)-(de.clientLeft||0);lastMousePos.pageY=e.clientY+(de&&de.scrollTop||b.scrollTop||0)-(de.clientTop||0);}
else{lastMousePos.pageX=e.pageX;lastMousePos.pageY=e.pageY;}
if(options.grid.hoverable)
triggerClickHoverEvent("plothover",lastMousePos,function(s){return s["hoverable"]!=false;});if(options.crosshair.mode!=null){if(!selection.active){setPositionFromEvent(crosshair.pos,lastMousePos);triggerRedrawOverlay();}
else
crosshair.pos.x=-1;}
if(selection.active){target.trigger("plotselecting",[selectionIsSane()?getSelectionForEvent():null]);updateSelection(lastMousePos);}}
function onMouseDown(e){if(e.which!=1)
return;document.body.focus();if(document.onselectstart!==undefined&&workarounds.onselectstart==null){workarounds.onselectstart=document.onselectstart;document.onselectstart=function(){return false;};}
if(document.ondrag!==undefined&&workarounds.ondrag==null){workarounds.ondrag=document.ondrag;document.ondrag=function(){return false;};}
setSelectionPos(selection.first,e);lastMousePos.pageX=null;selection.active=true;$(document).one("mouseup",onSelectionMouseUp);}
function onMouseOut(ev){if(options.crosshair.mode!=null&&crosshair.pos.x!=-1){crosshair.pos.x=-1;triggerRedrawOverlay();}}
function onClick(e){if(clickIsMouseUp){clickIsMouseUp=false;return;}
triggerClickHoverEvent("plotclick",e,function(s){return s["clickable"]!=false;});}
function triggerClickHoverEvent(eventname,event,seriesFilter){var offset=eventHolder.offset(),pos={pageX:event.pageX,pageY:event.pageY},canvasX=event.pageX-offset.left-plotOffset.left,canvasY=event.pageY-offset.top-plotOffset.top;if(axes.xaxis.used)
pos.x=axes.xaxis.c2p(canvasX);if(axes.yaxis.used)
pos.y=axes.yaxis.c2p(canvasY);if(axes.x2axis.used)
pos.x2=axes.x2axis.c2p(canvasX);if(axes.y2axis.used)
pos.y2=axes.y2axis.c2p(canvasY);var item=findNearbyItem(canvasX,canvasY,seriesFilter);if(item){item.pageX=parseInt(item.series.xaxis.p2c(item.datapoint[0])+offset.left+plotOffset.left);item.pageY=parseInt(item.series.yaxis.p2c(item.datapoint[1])+offset.top+plotOffset.top);}
if(options.grid.autoHighlight){for(var i=0;i<highlights.length;++i){var h=highlights[i];if(h.auto==eventname&&!(item&&h.series==item.series&&h.point==item.datapoint))
unhighlight(h.series,h.point);}
if(item)
highlight(item.series,item.datapoint,eventname);}
target.trigger(eventname,[pos,item]);}
function triggerRedrawOverlay(){if(!redrawTimeout)
redrawTimeout=setTimeout(redrawOverlay,30);}
function redrawOverlay(){redrawTimeout=null;octx.save();octx.clearRect(0,0,canvasWidth,canvasHeight);octx.translate(plotOffset.left,plotOffset.top);var i,hi;for(i=0;i<highlights.length;++i){hi=highlights[i];if(hi.series.bars.show)
drawBarHighlight(hi.series,hi.point);else
drawPointHighlight(hi.series,hi.point);}
if(selection.show&&selectionIsSane()){octx.strokeStyle=parseColor(options.selection.color).scale(null,null,null,0.8).toString();octx.lineWidth=1;ctx.lineJoin="round";octx.fillStyle=parseColor(options.selection.color).scale(null,null,null,0.4).toString();var x=Math.min(selection.first.x,selection.second.x),y=Math.min(selection.first.y,selection.second.y),w=Math.abs(selection.second.x-selection.first.x),h=Math.abs(selection.second.y-selection.first.y);octx.fillRect(x,y,w,h);octx.strokeRect(x,y,w,h);}
if(options.crosshair.mode!=null&&crosshair.pos.x!=-1){octx.strokeStyle=parseColor(options.crosshair.color).scale(null,null,null,0.8).toString();octx.lineWidth=1;ctx.lineJoin="round";var pos=crosshair.pos;octx.beginPath();if(options.crosshair.mode.indexOf("x")!=-1){octx.moveTo(pos.x,0);octx.lineTo(pos.x,plotHeight);}
if(options.crosshair.mode.indexOf("y")!=-1){octx.moveTo(0,pos.y);octx.lineTo(plotWidth,pos.y);}
octx.stroke();}
octx.restore();}
function highlight(s,point,auto){if(typeof s=="number")
s=series[s];if(typeof point=="number")
point=s.data[point];var i=indexOfHighlight(s,point);if(i==-1){highlights.push({series:s,point:point,auto:auto});triggerRedrawOverlay();}
else if(!auto)
highlights[i].auto=false;}
function unhighlight(s,point){if(typeof s=="number")
s=series[s];if(typeof point=="number")
point=s.data[point];var i=indexOfHighlight(s,point);if(i!=-1){highlights.splice(i,1);triggerRedrawOverlay();}}
function indexOfHighlight(s,p){for(var i=0;i<highlights.length;++i){var h=highlights[i];if(h.series==s&&h.point[0]==p[0]&&h.point[1]==p[1])
return i;}
return-1;}
function drawPointHighlight(series,point){var x=point[0],y=point[1],axisx=series.xaxis,axisy=series.yaxis;if(x<axisx.min||x>axisx.max||y<axisy.min||y>axisy.max)
return;var pointRadius=series.points.radius+series.points.lineWidth/2;octx.lineWidth=pointRadius;octx.strokeStyle=parseColor(series.color).scale(1,1,1,0.5).toString();var radius=1.5*pointRadius;octx.beginPath();octx.arc(axisx.p2c(x),axisy.p2c(y),radius,0,2*Math.PI,true);octx.stroke();}
function drawBarHighlight(series,point){octx.lineJoin="round";octx.lineWidth=series.bars.lineWidth;octx.strokeStyle=parseColor(series.color).scale(1,1,1,0.5).toString();var fillStyle=parseColor(series.color).scale(1,1,1,0.5).toString();var barLeft=series.bars.align=="left"?0:-series.bars.barWidth/2;drawBar(point[0],point[1],barLeft,barLeft+series.bars.barWidth,0,function(){return fillStyle;},series.xaxis,series.yaxis,octx);}
function setPositionFromEvent(pos,e){var offset=eventHolder.offset();pos.x=clamp(0,e.pageX-offset.left-plotOffset.left,plotWidth);pos.y=clamp(0,e.pageY-offset.top-plotOffset.top,plotHeight);}
function setCrosshair(pos){if(pos==null)
crosshair.pos.x=-1;else{crosshair.pos.x=clamp(0,pos.x!=null?axes.xaxis.p2c(pos.x):axes.x2axis.p2c(pos.x2),plotWidth);crosshair.pos.y=clamp(0,pos.y!=null?axes.yaxis.p2c(pos.y):axes.y2axis.p2c(pos.y2),plotHeight);}
triggerRedrawOverlay();}
function getSelectionForEvent(){var x1=Math.min(selection.first.x,selection.second.x),x2=Math.max(selection.first.x,selection.second.x),y1=Math.max(selection.first.y,selection.second.y),y2=Math.min(selection.first.y,selection.second.y);var r={};if(axes.xaxis.used)
r.xaxis={from:axes.xaxis.c2p(x1),to:axes.xaxis.c2p(x2)};if(axes.x2axis.used)
r.x2axis={from:axes.x2axis.c2p(x1),to:axes.x2axis.c2p(x2)};if(axes.yaxis.used)
r.yaxis={from:axes.yaxis.c2p(y1),to:axes.yaxis.c2p(y2)};if(axes.y2axis.used)
r.y2axis={from:axes.y2axis.c2p(y1),to:axes.y2axis.c2p(y2)};return r;}
function triggerSelectedEvent(){var r=getSelectionForEvent();target.trigger("plotselected",[r]);if(axes.xaxis.used&&axes.yaxis.used)
target.trigger("selected",[{x1:r.xaxis.from,y1:r.yaxis.from,x2:r.xaxis.to,y2:r.yaxis.to}]);}
function onSelectionMouseUp(e){if(document.onselectstart!==undefined)
document.onselectstart=workarounds.onselectstart;if(document.ondrag!==undefined)
document.ondrag=workarounds.ondrag;selection.active=false;updateSelection(e);if(selectionIsSane()){triggerSelectedEvent();clickIsMouseUp=true;}
else{target.trigger("plotunselected",[]);target.trigger("plotselecting",[null]);}
return false;}
function setSelectionPos(pos,e){setPositionFromEvent(pos,e);if(options.selection.mode=="y"){if(pos==selection.first)
pos.x=0;else
pos.x=plotWidth;}
if(options.selection.mode=="x"){if(pos==selection.first)
pos.y=0;else
pos.y=plotHeight;}}
function updateSelection(pos){if(pos.pageX==null)
return;setSelectionPos(selection.second,pos);if(selectionIsSane()){selection.show=true;triggerRedrawOverlay();}
else
clearSelection(true);}
function clearSelection(preventEvent){if(selection.show){selection.show=false;triggerRedrawOverlay();if(!preventEvent)
target.trigger("plotunselected",[]);}}
function setSelection(ranges,preventEvent){var range;if(options.selection.mode=="y"){selection.first.x=0;selection.second.x=plotWidth;}
else{range=extractRange(ranges,"x");selection.first.x=range.axis.p2c(range.from);selection.second.x=range.axis.p2c(range.to);}
if(options.selection.mode=="x"){selection.first.y=0;selection.second.y=plotHeight;}
else{range=extractRange(ranges,"y");selection.first.y=range.axis.p2c(range.from);selection.second.y=range.axis.p2c(range.to);}
selection.show=true;triggerRedrawOverlay();if(!preventEvent)
triggerSelectedEvent();}
function selectionIsSane(){var minSize=5;return Math.abs(selection.second.x-selection.first.x)>=minSize&&Math.abs(selection.second.y-selection.first.y)>=minSize;}
function getColorOrGradient(spec,bottom,top,defaultColor){if(typeof spec=="string")
return spec;else{var gradient=ctx.createLinearGradient(0,top,0,bottom);for(var i=0,l=spec.colors.length;i<l;++i){var c=spec.colors[i];gradient.addColorStop(i/(l-1),typeof c=="string"?c:parseColor(defaultColor).scale(c.brightness,c.brightness,c.brightness,c.opacity));}
return gradient;}}}
$.plot=function(target,data,options){var plot=new Plot(target,data,options);return plot;};$.plot.formatDate=function(d,fmt,monthNames){var leftPad=function(n){n=""+n;return n.length==1?"0"+n:n;};var r=[];var escape=false;if(monthNames==null)
monthNames=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];for(var i=0;i<fmt.length;++i){var c=fmt.charAt(i);if(escape){switch(c){case'h':c=""+d.getUTCHours();break;case'H':c=leftPad(d.getUTCHours());break;case'M':c=leftPad(d.getUTCMinutes());break;case'S':c=leftPad(d.getUTCSeconds());break;case'd':c=""+d.getUTCDate();break;case'm':c=""+(d.getUTCMonth()+1);break;case'y':c=""+d.getUTCFullYear();break;case'b':c=""+monthNames[d.getUTCMonth()];break;}
r.push(c);escape=false;}
else{if(c=="%")
escape=true;else
r.push(c);}}
return r.join("");};function floorInBase(n,base){return base*Math.floor(n/base);}
function clamp(min,value,max){if(value<min)
return min;else if(value>max)
return max;else
return value;}
function Color(r,g,b,a){var rgba=['r','g','b','a'];var x=4;while(-1<--x){this[rgba[x]]=arguments[x]||((x==3)?1.0:0);}
this.toString=function(){if(this.a>=1.0){return"rgb("+[this.r,this.g,this.b].join(",")+")";}else{return"rgba("+[this.r,this.g,this.b,this.a].join(",")+")";}};this.scale=function(rf,gf,bf,af){x=4;while(-1<--x){if(arguments[x]!=null)
this[rgba[x]]*=arguments[x];}
return this.normalize();};this.adjust=function(rd,gd,bd,ad){x=4;while(-1<--x){if(arguments[x]!=null)
this[rgba[x]]+=arguments[x];}
return this.normalize();};this.clone=function(){return new Color(this.r,this.b,this.g,this.a);};var limit=function(val,minVal,maxVal){return Math.max(Math.min(val,maxVal),minVal);};this.normalize=function(){this.r=clamp(0,parseInt(this.r),255);this.g=clamp(0,parseInt(this.g),255);this.b=clamp(0,parseInt(this.b),255);this.a=clamp(0,this.a,1);return this;};this.normalize();}
var lookupColors={aqua:[0,255,255],azure:[240,255,255],beige:[245,245,220],black:[0,0,0],blue:[0,0,255],brown:[165,42,42],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgrey:[169,169,169],darkgreen:[0,100,0],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkviolet:[148,0,211],fuchsia:[255,0,255],gold:[255,215,0],green:[0,128,0],indigo:[75,0,130],khaki:[240,230,140],lightblue:[173,216,230],lightcyan:[224,255,255],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightyellow:[255,255,224],lime:[0,255,0],magenta:[255,0,255],maroon:[128,0,0],navy:[0,0,128],olive:[128,128,0],orange:[255,165,0],pink:[255,192,203],purple:[128,0,128],violet:[128,0,128],red:[255,0,0],silver:[192,192,192],white:[255,255,255],yellow:[255,255,0]};function extractColor(element){var color,elem=element;do{color=elem.css("background-color").toLowerCase();if(color!=''&&color!='transparent')
break;elem=elem.parent();}while(!$.nodeName(elem.get(0),"body"));if(color=="rgba(0, 0, 0, 0)")
return"transparent";return color;}
function parseColor(str){var result;if(result=/rgb\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*\)/.exec(str))
return new Color(parseInt(result[1],10),parseInt(result[2],10),parseInt(result[3],10));if(result=/rgba\(\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]{1,3})\s*,\s*([0-9]+(?:\.[0-9]+)?)\s*\)/.exec(str))
return new Color(parseInt(result[1],10),parseInt(result[2],10),parseInt(result[3],10),parseFloat(result[4]));if(result=/rgb\(\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*\)/.exec(str))
return new Color(parseFloat(result[1])*2.55,parseFloat(result[2])*2.55,parseFloat(result[3])*2.55);if(result=/rgba\(\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\%\s*,\s*([0-9]+(?:\.[0-9]+)?)\s*\)/.exec(str))
return new Color(parseFloat(result[1])*2.55,parseFloat(result[2])*2.55,parseFloat(result[3])*2.55,parseFloat(result[4]));if(result=/#([a-fA-F0-9]{2})([a-fA-F0-9]{2})([a-fA-F0-9]{2})/.exec(str))
return new Color(parseInt(result[1],16),parseInt(result[2],16),parseInt(result[3],16));if(result=/#([a-fA-F0-9])([a-fA-F0-9])([a-fA-F0-9])/.exec(str))
return new Color(parseInt(result[1]+result[1],16),parseInt(result[2]+result[2],16),parseInt(result[3]+result[3],16));var name=$.trim(str).toLowerCase();if(name=="transparent")
return new Color(255,255,255,0);else{result=lookupColors[name];return new Color(result[0],result[1],result[2]);}}})(jQuery);