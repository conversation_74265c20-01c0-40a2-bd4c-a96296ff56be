from openpyxl.utils import get_column_letter
import configparser
import csv,time
import os,re
import openpyxl
import Public
from openpyxl.utils import get_column_letter,column_index_from_string
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors
from datetime import datetime,timedelta
from xml.dom.minidom import parse,parseString

from lxml import etree

def Run(exportFile, xmlPath):
    if not os.path.exists(exportFile):
        return
    wb = openpyxl.load_workbook(filename = exportFile,data_only = True) #源excel

    for productName in Public.g_productList:       
        xmlFileName = xmlPath + "\\plan_" + productName+".xml"
        if not os.path.exists(xmlFileName):
            continue

        ws = wb[productName]
        xml_file = etree.parse(xmlFileName)
        root_node = xml_file.getroot()
        planInfoNode = root_node

        for planNode in planInfoNode:
            #获取plan的名称
            planID = planNode.attrib['id'].upper() #源文件大小写不规范，因此必须全部转换为大写比对
            planDesc = findPlanDescFromExportExcel(planID,ws)
            planNode.set('desc',planDesc)

        #注，此处用了method="html"会保留空白的节点，但是得不到xml的声明行。二者只能取其一。
        xml_file.write(xmlFileName, encoding="gb2312", xml_declaration=True, pretty_print=True,standalone = True)#,method="html"

#依据caseID去Export文件查找URL链接
def findPlanDescFromExportExcel(planID,ws):
    planDesc = ''
    rowCnt = ws.max_row
    for rowNo in range(2,rowCnt+1):#第一行是合并单元格，也不是我们要的数据，所以从第二行开始
        curPlanID = ws['%s%d'%('A', rowNo)].value
        if curPlanID == None:
            continue
        curPlanID = curPlanID.strip()
        curPlanID = curPlanID.upper()
        if curPlanID == planID.upper():
            if ws['%s%d'%('B', rowNo)].value != None:
                planDesc = ws['%s%d'%('B', rowNo)].value
                planDesc = planDesc.replace("\xa0", " ")#替换空格，其它字符串需要规范化
                #planDesc = "".join(planDesc.split())
            break
    return planDesc
