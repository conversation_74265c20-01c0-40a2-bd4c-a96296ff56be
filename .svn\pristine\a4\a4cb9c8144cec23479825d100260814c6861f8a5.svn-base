
import PublicFuc
import configparser
import csv
import os,re
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors,Font
from datetime import datetime,timedelta

#以下变量全部用于汇总
por_sample_cnt = 0
por_sample_maxtime = ''
spor_sample_cnt = 0
spor_sample_maxtime = ''

def Run(curpath, workBook, alignment):
    ws = workBook['Power Off Test']
    ws.alignment = alignment
    ProPOR(curpath, ws,32)
    ProSPOR(curpath, ws,32)
    ProSPOR2EmptyChunck(curpath, ws,32)
    PublicFuc.WriteReportTime(ws,'E',2)
    PublicFuc.WriteReportOperator(ws,'I',2)
    #ProEntry(workBook)


#填写封面
def ProEntry(workBook):
    ws = workBook['Full Test Reports']
   
    ws['J27'] = spor_sample_cnt
    ws['K27'] = spor_sample_maxtime 
    ws['J29'] = por_sample_cnt
    ws['K29'] = por_sample_maxtime
   

#获取规范化字典中的SPOR或POR中的最长测试时间
def GetMaxTime(dic):
    maxTimeInSeconds = 0
    for sampleNo in dic:
        row = dic[sampleNo]
        timestr = row[12] #SPOR和POR时间所在列
        if timestr != '':
            timedata = timestr.split(':')
            totalSecond = int(timedata[0])*3600 + int(timedata[1])*60 + int(timedata[2])
            if totalSecond > maxTimeInSeconds:
                maxTimeInSeconds = totalSecond

    if maxTimeInSeconds == 0:
        return ''

    hour = int(maxTimeInSeconds/3600)
    lefSeconds = maxTimeInSeconds%3600
    minutes = int(lefSeconds/60)
    seconds = lefSeconds%60
    strTime = '%d:%d:%d'%(hour,minutes,seconds)
    return strTime


def ProPOR(curpath,worksheet,recordCnt):
    pattern = '.+\\\\Plan4\\\\T_GE_SD_C7\\\\Mars_H2_1_1st\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2'
    caseDicH2_1_1st = {} 
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseDicH2_1_1st, caseName, recordCnt)

    pattern = '.+\\\\Plan4\\\\T_GE_SD_C7\\\\Mars_H2_1_2nd\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2' 
    caseDicH2_1_2nd = {}
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseDicH2_1_2nd, caseName, recordCnt)

    pattern = '.+\\\\Plan4\\\\T_GE_SD_C8\\\\AT_CopyFile\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_CopyFile' 
    caseDicCopyFile = {}
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseDicCopyFile, caseName, recordCnt)

    pattern = '.+\\\\Plan4\\\\T_GE_SD_C12\\\\POR\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'POR' 
    caseDicPOR = {}
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseDicPOR, caseName, recordCnt)

    pattern = '.+\\\\Plan4\\\\T_GE_SD_C7\\\\Mars_H2_2_1st\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2'
    caseDicH2_2_1st = {}
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseDicH2_2_1st, caseName, recordCnt)

    pattern = '.+\\\\Plan4\\\\T_GE_SD_C7\\\\Mars_H2_2_2nd\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2'
    caseDicH2_2_2nd = {}
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseDicH2_2_2nd, caseName, recordCnt)

    keyLst = ['cap','pc_no',
             'wavg_H2_1_1','ravg_H2_1_1','test_time_H2_1_1','result_H2_1_1',
             'wavg_H2_1_2','ravg_H2_1_2','test_time_H2_1_2','result_H2_1_2',
             'test_time_CopyFile','result_CopyFile',
             'test_time_POR','circle','result_POR',
             'wavg_H2_2_1','ravg_H2_2_1','test_time_H2_2_1','result_H2_2_1',
             'wavg_H2_2_2','ravg_H2_2_2','test_time_H2_2_2','result_H2_2_2',
             'RetryCnt','SLCBadBlock_New','TLCBadBlock_New','WL_SLC_MAX','WL_SLC_MIN','WL_SLC_AVG','WL_TLC_MAX',
              'WL_TLC_MIN','WL_TLC_AVG','SLC_Diff','TLC_Diff','PowerUpCnt','conclusion']  #第一次也显示磨损差值
    #GetNewMarsDic把ini读出来的dic按照keyLst转换为模板所需数据列表
    newDic = GetNewPorReportDic(caseDicH2_1_1st,caseDicH2_1_2nd,caseDicCopyFile,caseDicPOR,caseDicH2_2_1st,caseDicH2_2_2nd)

    GetConclusion(newDic,keyLst)

    global por_sample_maxtime
    por_sample_maxtime = GetMaxTime(newDic)
    global por_sample_cnt
    por_sample_cnt = len(newDic)

    colLst = ['B','C','D',
              'E','F','G','H',
              'I','J','K','L',
              'M','N',
              'O','P','Q',
              'R','S','T','U',
              'V','W','X','Y',
              'Z','AA','AB','AC','AD','AE','AF','AG','AH','AI','AJ','AK',
              'AL']

    startLine = 7
    #写标号
    FillNo(worksheet, startLine, newDic)
    #写内容
    resultColumnList = ['H','L','N','Q','U','Y','AL']
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst,1,resultColumnList)

def ProSPOR(curpath,worksheet,recordCnt):
    pattern = '.+\\\\Plan5\\\\T_GE_SD_C7\\\\Mars_H2_1_1st\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2'
    caseDicH2_1_1st = {} 
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseDicH2_1_1st, caseName, recordCnt)

    pattern = '.+\\\\Plan5\\\\T_GE_SD_C7\\\\Mars_H2_1_2nd\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2' 
    caseDicH2_1_2nd = {}
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseDicH2_1_2nd, caseName, recordCnt)

    pattern = '.+\\\\Plan5\\\\T_GE_SD_C8\\\\AT_CopyFile\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_CopyFile' 
    caseDicCopyFile = {}
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseDicCopyFile, caseName, recordCnt)

    pattern = '.+\\\\Plan5\\\\T_GE_SD_C11\\\\SPOR\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'SPOR' 
    caseDicPOR = {}
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseDicPOR, caseName, recordCnt)

    pattern = '.+\\\\Plan5\\\\T_GE_SD_C7\\\\Mars_H2_2_1st\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2'
    caseDicH2_2_1st = {}
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseDicH2_2_1st, caseName, recordCnt)

    pattern = '.+\\\\Plan5\\\\T_GE_SD_C7\\\\Mars_H2_2_2nd\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2'
    caseDicH2_2_2nd = {}
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseDicH2_2_2nd, caseName, recordCnt)

    keyLst = ['cap','pc_no',
             'wavg_H2_1_1','ravg_H2_1_1','test_time_H2_1_1','result_H2_1_1',
             'wavg_H2_1_2','ravg_H2_1_2','test_time_H2_1_2','result_H2_1_2',
             'test_time_CopyFile','result_CopyFile',
             'test_time_POR','circle','result_POR',
             'wavg_H2_2_1','ravg_H2_2_1','test_time_H2_2_1','result_H2_2_1',
             'wavg_H2_2_2','ravg_H2_2_2','test_time_H2_2_2','result_H2_2_2',
             'RetryCnt','SLCBadBlock_New','TLCBadBlock_New','WL_SLC_MAX','WL_SLC_MIN','WL_SLC_AVG','WL_TLC_MAX',
              'WL_TLC_MIN','WL_TLC_AVG','SLC_Diff','TLC_Diff','PowerUpCnt','conclusion']  #第一次也显示磨损差值
    #GetNewMarsDic把ini读出来的dic按照keyLst转换为模板所需数据列表
    newDic = GetNewSPorReportDic(caseDicH2_1_1st,caseDicH2_1_2nd,caseDicCopyFile,caseDicPOR,caseDicH2_2_1st,caseDicH2_2_2nd)

    global spor_sample_maxtime
    spor_sample_maxtime = GetMaxTime(newDic)
    global spor_sample_cnt
    spor_sample_cnt = len(newDic)

    GetConclusion(newDic,keyLst)

    colLst = ['B','C','D',
              'E','F','G','H',
              'I','J','K','L',
              'M','N',
              'O','P','Q',
              'R','S','T','U',
              'V','W','X','Y',
              'Z','AA','AB','AC','AD','AE','AF','AG','AH','AI','AJ','AK',
              'AL']

    startLine = 43
    #写标号
    FillNo(worksheet, startLine, newDic)
    #写内容
    resultColumnList = ['H','L','N','Q','U','Y','AL']
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst,1,resultColumnList)

#写标号
def FillNo(worksheet,startLine,newDic):
    for i in range(len(newDic)):
        worksheet['%s%d'%(get_column_letter(1), startLine+i)] = i+1

def IsFloat(str):
    try:
        val = float(str)
        return True
    except:
        return False

def GetConclusion(newDic,keyLst):
    result_H2_2_1 = 'FALSE'
    result_H2_2_2 = 'FALSE'
    result_CopyFile = 'FALSE'
    result_POR = 'FALSE'
    wavg_H2_1_2 = 0
    wavg_H2_2_2 = 0
    conclusion = False
    for key in newDic:
        line = newDic[key]
        for index,col in enumerate(keyLst):
            if col == 'result_H2_2_1':
                result_H2_2_1 = line[index]
            if col == 'result_H2_2_2':
                result_H2_2_2 = line[index]
            if col == 'result_CopyFile':
                result_CopyFile = line[index]
            if col == 'result_POR':
                result_POR = line[index]
            if col == 'wavg_H2_1_2':
                wavg_H2_1_2 = line[index]
            if col == 'wavg_H2_2_2':
                wavg_H2_2_2 = line[index]

        if result_H2_2_1 == 'UNFINISHED' or result_H2_2_2 == 'UNFINISHED' or result_POR == 'UNFINISHED' or result_CopyFile == 'UNFINISHED':
            conclusion = 'UNFINISHED'
        else:
            if result_H2_2_1 != 'TRUE' or result_H2_2_2 != 'TRUE' or result_POR != 'TRUE' or result_CopyFile != 'TRUE':
                conclusion = False

            if IsFloat(wavg_H2_2_2) and IsFloat(wavg_H2_1_2):
                ratio = float(wavg_H2_2_2)/float(wavg_H2_1_2)
                if ratio < 0.95:
                    conclusion = False
                else:
                    conclusion = True
            else:
                conclusion = False

        if conclusion == True:
            newDic[key].append('PASS')
        elif conclusion == 'UNFINISHED':
            newDic[key].append('UNFINISHED')
        else:
            newDic[key].append('FAIL')

        

def AppendH2Result2NewDic(newDic,key,dic):
    newDic[key].append(PublicFuc.GetValueFromDic(dic,'average_write_vel'))
    newDic[key].append(PublicFuc.GetValueFromDic(dic,'average_read_vel'))

    #测试时间
    endTimeStr = PublicFuc.GetValueFromDic(dic,'end_time')
    startTimeStr = PublicFuc.GetValueFromDic(dic,'start_time')
    if '' == endTimeStr or '' == startTimeStr:
        newDic[key].append('')
    else:
        try:
            endtime = datetime.strptime(endTimeStr, '%Y-%m-%d %H:%M:%S')
            starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
            totalSecond = timedelta.total_seconds(endtime-starttime)
            totalSecond = int(totalSecond)
            hour = int(totalSecond/3600)
            lefSeconds = totalSecond%3600
            minutes = int(lefSeconds/60)
            seconds = lefSeconds%60
            timeStr = '%d:%d:%d'%(hour,minutes,seconds)

            newDic[key].append(timeStr)
        except:
            newDic[key].append('')

    #测试结果
    testResult =  PublicFuc.GetValueFromDic(dic,'test_result','FAIL')
    if testResult.upper() != 'PASS' and testResult.upper() != 'TRUE' and testResult.upper() != 'UNFINISHED':
        PublicFuc.AppendErrDiskInfo('H2_Err',key,testResult,dic['MMS_PC'],dic['file_path'])
    newDic[key].append(testResult)

def AppendEmptyH2Result2NewDic(newDic,key,dic):
    newDic[key].append('')
    newDic[key].append('')
    newDic[key].append('')
    newDic[key].append('')

def GenSmart(key,newDic,dicData):
    #smart信息
    WL_SLC_MAX = ''
    WL_SLC_MIN = ''
    WL_TLC_MAX = ''
    WL_TLC_MIN = ''
    if key in dicData:#如果键值在里面
        dic = dicData[key]
        if 'RetryCnt'.lower() in dic:
            if ''== dic['RetryCnt'.lower()]:
                newDic[key].append('')
            else:
                newDic[key].append(dic['RetryCnt'.lower()])
        else:
            newDic[key].append('')

        if 'SLCBadBlock_New'.lower() in dic:
            if ''== dic['SLCBadBlock_New'.lower()]:
                newDic[key].append('')
            else:
                newDic[key].append(dic['SLCBadBlock_New'.lower()])
        else:
            newDic[key].append('')

        if 'TLCBadBlock_New'.lower() in dic:
            if ''== dic['TLCBadBlock_New'.lower()]:
                newDic[key].append('')
            else:
                newDic[key].append(dic['TLCBadBlock_New'.lower()])
        else:
            newDic[key].append('')

        if 'WL_SLC_MAX'.lower() in dic or 'WL SLC MAX'.lower() in dic:
            if 'WL_SLC_MAX'.lower() in dic:
                WL_SLC_MAX = dic['WL_SLC_MAX'.lower()]
                newDic[key].append(dic['WL_SLC_MAX'.lower()])
            else:
                WL_SLC_MAX = dic['WL SLC MAX'.lower()]
                newDic[key].append(dic['WL SLC MAX'.lower()])
        else:
            newDic[key].append('')

        if 'WL_SLC_MIN'.lower() in dic or 'WL SLC MIN'.lower() in dic:
            if 'WL_SLC_MIN'.lower() in dic:
                WL_SLC_MIN = dic['WL_SLC_MIN'.lower()]
                newDic[key].append(dic['WL_SLC_MIN'.lower()])
            else:
                WL_SLC_MIN = dic['WL SLC MIN'.lower()]
                newDic[key].append(dic['WL SLC MIN'.lower()])
        else:
            newDic[key].append('')

        if 'WL_SLC_AVG'.lower() in dic or 'WL SLC AVG'.lower() in dic:
            if 'WL_SLC_AVG'.lower() in dic:
                newDic[key].append(dic['WL_SLC_AVG'.lower()])
            else:
                newDic[key].append(dic['WL SLC AVG'.lower()])
        else:
            newDic[key].append('')

        if 'WL_TLC_MAX'.lower() in dic or 'WL TLC MAX'.lower() in dic:
            if 'WL_TLC_MAX'.lower() in dic:
                WL_TLC_MAX = dic['WL_TLC_MAX'.lower()]
                newDic[key].append(dic['WL_TLC_MAX'.lower()])
            else:
                WL_TLC_MAX = dic['WL TLC MAX'.lower()]
                newDic[key].append(dic['WL TLC MAX'.lower()])
        else:
            newDic[key].append('')

        if 'WL_TLC_MIN'.lower() in dic or 'WL TLC MIN'.lower() in dic:
            if 'WL_TLC_MIN'.lower() in dic:
                WL_TLC_MIN = dic['WL_TLC_MIN'.lower()]
                newDic[key].append(dic['WL_TLC_MIN'.lower()])
            else:
                WL_TLC_MIN = dic['WL TLC MIN'.lower()]
                newDic[key].append(dic['WL TLC MIN'.lower()])
        else:
            newDic[key].append('')

        if 'WL_TLC_AVG'.lower() in dic or 'WL TLC AVG'.lower() in dic:
            if 'WL_TLC_AVG'.lower() in dic:
                newDic[key].append(dic['WL_TLC_AVG'.lower()])
            else:
                newDic[key].append(dic['WL TLC AVG'.lower()])
        else:
            newDic[key].append('')

        if WL_SLC_MAX == '' or WL_SLC_MIN == '':
            newDic[key].append('')
        else:
            newDic[key].append(str(PublicFuc.convert_to_number(WL_SLC_MAX)-PublicFuc.convert_to_number(WL_SLC_MIN)))

        if WL_TLC_MAX == '' or WL_TLC_MIN == '':
            newDic[key].append('')
        else:
            newDic[key].append(str(PublicFuc.convert_to_number(WL_TLC_MAX)-PublicFuc.convert_to_number(WL_TLC_MIN)))

        if 'PowerUpCnt'.lower() in dic:
            if ''== dic['PowerUpCnt'.lower()]:
                newDic[key].append('')
            else:
                newDic[key].append(dic['PowerUpCnt'.lower()])
        else:
            newDic[key].append('')

def GetNewPorReportDic(caseDicH2_1_1st, caseDicH2_1_2nd, caseDicCopyFile,caseDicPOR, caseDicH2_2_1st,caseDicH2_2_2nd):
    newDic = {}
    for key in caseDicH2_1_1st:
        newDic[key] = []
        dic = caseDicH2_1_1st[key]
        #容量取整数G
        if '' == dic['capacity']:
            newDic[key].append('')
        else:
            newDic[key].append(int(float(dic['capacity'])*1024)) #转化为M为单位
        
        #PC编号
        if '' == dic['MMS_PC']:
            newDic[key].append('')
        else:
            newDic[key].append(dic['MMS_PC'])
        
        #H2_1_1st
        AppendH2Result2NewDic(newDic,key,dic)

        #H2_1_2nd
        if key in caseDicH2_1_2nd:#如果键值在里面
            dic = caseDicH2_1_2nd[key]
            AppendH2Result2NewDic(newDic,key,dic)
        else:
            AppendEmptyH2Result2NewDic(newDic,key,dic)

        #CopyFile
        if key in caseDicCopyFile:
            dic = caseDicCopyFile[key]
            endTimeStr = PublicFuc.GetValueFromDic(dic,'end_time')
            startTimeStr = PublicFuc.GetValueFromDic(dic,'start_time')
            if '' == endTimeStr or '' == startTimeStr:
                newDic[key].append('')
            else:
                try:
                    endtime = datetime.strptime(endTimeStr, '%Y-%m-%d %H:%M:%S')
                    starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
                    totalSecond = timedelta.total_seconds(endtime-starttime)
                    totalSecond = int(totalSecond)
                    hour = int(totalSecond/3600)
                    lefSeconds = totalSecond%3600
                    minutes = int(lefSeconds/60)
                    seconds = lefSeconds%60
                    timeStr = '%d:%d:%d'%(hour,minutes,seconds)
                    newDic[key].append(timeStr)
                except:
                    newDic[key].append('')

            testResult =  PublicFuc.GetValueFromDic(dic,'test_result','FAIL')
            if testResult.upper() != 'PASS' and testResult.upper() != 'TRUE' and testResult.upper() != 'UNFINISHED':
                PublicFuc.AppendErrDiskInfo('Copy File_Err',key,testResult,dic['MMS_PC'],dic['file_path'])
            newDic[key].append(testResult)
        else:
            newDic[key].append('')
            newDic[key].append('')

        #POR
        if key in caseDicPOR:
            dic = caseDicPOR[key]

            endTimeStr = PublicFuc.GetValueFromDic(dic,'end_time')
            startTimeStr = PublicFuc.GetValueFromDic(dic,'start_time')

            if '' == endTimeStr or '' == startTimeStr:
                newDic[key].append('')
            else:
                try:
                    endtime = datetime.strptime(endTimeStr, '%Y-%m-%d %H:%M:%S')
                    starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
                    totalSecond = timedelta.total_seconds(endtime-starttime)
                    totalSecond = int(totalSecond)
                    hour = int(totalSecond/3600)
                    lefSeconds = totalSecond%3600
                    minutes = int(lefSeconds/60)
                    seconds = lefSeconds%60
                    timeStr = '%d:%d:%d'%(hour,minutes,seconds)
                    newDic[key].append(timeStr)
                except:
                    newDic[key].append('')
                
            testCycle = PublicFuc.GetValueFromDic(dic,'powerdown_cnt')
            if '' == testCycle:
                newDic[key].append('')
            else:
                newDic[key].append(int(testCycle))

            testResult =  PublicFuc.GetValueFromDic(dic,'test_result','FAIL')
            if testResult.upper() != 'PASS' and testResult.upper() != 'TRUE' and testResult.upper() != 'UNFINISHED':
                PublicFuc.AppendErrDiskInfo('POR_Err',key,testResult,dic['MMS_PC'],dic['file_path'])
            newDic[key].append(testResult)
        else:
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
    
        #H2_2_1st
        if key in caseDicH2_2_1st:#如果键值在里面
            dic = caseDicH2_2_1st[key]
            AppendH2Result2NewDic(newDic,key,dic)
        else:
            AppendEmptyH2Result2NewDic(newDic,key,dic)

        #H2_2_2nd
        if key in caseDicH2_2_2nd:#如果键值在里面
            dic = caseDicH2_2_2nd[key]
            AppendH2Result2NewDic(newDic,key,dic)
        else:
            AppendEmptyH2Result2NewDic(newDic,key,dic)

        #smart信息
        if key in caseDicH2_2_2nd:#如果键值在里面
            GenSmart(key,newDic,caseDicH2_2_2nd)
        elif key in caseDicH2_2_1st:#如果键值在里面
            GenSmart(key,newDic,caseDicH2_2_1st)
        elif key in caseDicPOR:#如果键值在里面
            GenSmart(key,newDic,caseDicPOR)
        elif key in caseDicCopyFile:
            GenSmart(key,newDic,caseDicCopyFile)
        elif key in caseDicH2_1_2nd:
            GenSmart(key,newDic,caseDicH2_1_2nd)
        elif key in caseDicH2_1_1st:
            GenSmart(key,newDic,caseDicH2_1_1st)
        else:
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')

    return newDic 


def GetNewSPorReportDic(caseDicH2_1_1st, caseDicH2_1_2nd, caseDicCopyFile,caseDicPOR, caseDicH2_2_1st,caseDicH2_2_2nd):
    newDic = {}
    for key in caseDicH2_1_1st:
        newDic[key] = []
        dic = caseDicH2_1_1st[key]
        #容量取整数G
        if '' == dic['capacity']:
            newDic[key].append('')
        else:
            newDic[key].append(int(float(dic['capacity'])*1024)) #转化为M为单位
        
        #PC编号
        if '' == dic['MMS_PC']:
            newDic[key].append('')
        else:
            newDic[key].append(dic['MMS_PC'])
        
        #H2_1_1st
        AppendH2Result2NewDic(newDic,key,dic)

        #H2_1_2nd
        if key in caseDicH2_1_2nd:#如果键值在里面
            dic = caseDicH2_1_2nd[key]
            AppendH2Result2NewDic(newDic,key,dic)
        else:
            AppendEmptyH2Result2NewDic(newDic,key,dic)

        #CopyFile
        if key in caseDicCopyFile:
            dic = caseDicCopyFile[key]
            endTimeStr = PublicFuc.GetValueFromDic(dic,'end_time')
            startTimeStr = PublicFuc.GetValueFromDic(dic,'start_time')
            if '' == endTimeStr or '' == startTimeStr:
                newDic[key].append('')
            else:
                try:
                    endtime = datetime.strptime(endTimeStr, '%Y-%m-%d %H:%M:%S')
                    starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
                    totalSecond = timedelta.total_seconds(endtime-starttime)
                    totalSecond = int(totalSecond)
                    hour = int(totalSecond/3600)
                    lefSeconds = totalSecond%3600
                    minutes = int(lefSeconds/60)
                    seconds = lefSeconds%60
                    timeStr = '%d:%d:%d'%(hour,minutes,seconds)
                    newDic[key].append(timeStr)
                except:
                    newDic[key].append('')

            testResult =  PublicFuc.GetValueFromDic(dic,'test_result','FAIL')
            if testResult.upper() != 'PASS' and testResult.upper() != 'TRUE' and testResult.upper() != 'UNFINISHED':
                PublicFuc.AppendErrDiskInfo('Copy File_Err',key,testResult,dic['MMS_PC'],dic['file_path'])
            newDic[key].append(testResult)
        else:
            newDic[key].append('')
            newDic[key].append('')

        #SPOR
        if key in caseDicPOR:
            dic = caseDicPOR[key]

            endTimeStr = PublicFuc.GetValueFromDic(dic,'end_time')
            startTimeStr = PublicFuc.GetValueFromDic(dic,'start_time')

            if '' == endTimeStr or '' == startTimeStr:
                newDic[key].append('')
            else:
                try:
                    endtime = datetime.strptime(endTimeStr, '%Y-%m-%d %H:%M:%S')
                    starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
                    totalSecond = timedelta.total_seconds(endtime-starttime)
                    totalSecond = int(totalSecond)
                    hour = int(totalSecond/3600)
                    lefSeconds = totalSecond%3600
                    minutes = int(lefSeconds/60)
                    seconds = lefSeconds%60
                    timeStr = '%d:%d:%d'%(hour,minutes,seconds)
                    newDic[key].append(timeStr)
                except:
                    newDic[key].append('')
                
            testCycle = PublicFuc.GetValueFromDic(dic,'powerdown_cnt')
            if '' == testCycle:
                newDic[key].append('')
            else:
                newDic[key].append(int(testCycle))

            testResult =  PublicFuc.GetValueFromDic(dic,'test_result','FAIL')
            if testResult.upper() != 'PASS' and testResult.upper() != 'TRUE' and testResult.upper() != 'UNFINISHED':
                PublicFuc.AppendErrDiskInfo('SPOR_Err',key,testResult,dic['MMS_PC'],dic['file_path'])
            newDic[key].append(testResult)
        else:
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
    
        #H2_2_1st
        if key in caseDicH2_2_1st:#如果键值在里面
            dic = caseDicH2_2_1st[key]
            AppendH2Result2NewDic(newDic,key,dic)
        else:
            AppendEmptyH2Result2NewDic(newDic,key,dic)

        #H2_2_2nd
        if key in caseDicH2_2_2nd:#如果键值在里面
            dic = caseDicH2_2_2nd[key]
            AppendH2Result2NewDic(newDic,key,dic)
        else:
            AppendEmptyH2Result2NewDic(newDic,key,dic)

        # smart信息
        if key in caseDicH2_2_2nd:  # 如果键值在里面
            GenSmart(key, newDic, caseDicH2_2_2nd)
        elif key in caseDicH2_2_1st:  # 如果键值在里面
            GenSmart(key, newDic, caseDicH2_2_1st)
        elif key in caseDicPOR:  # 如果键值在里面
            GenSmart(key, newDic, caseDicPOR)
        elif key in caseDicCopyFile:
            GenSmart(key, newDic, caseDicCopyFile)
        elif key in caseDicH2_1_2nd:
            GenSmart(key, newDic, caseDicH2_1_2nd)
        elif key in caseDicH2_1_1st:
            GenSmart(key, newDic, caseDicH2_1_1st)
        else:
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')

    return newDic

def GetNewSPor2ReportDic(caseDicH2_1_1st, caseDicH2_1_2nd, caseDicCopyFile,caseDicPOR, caseDicH2_2_1st,caseDicH2_2_2nd):
    newDic = {}
    for key in caseDicH2_1_1st:
        newDic[key] = []
        dic = caseDicH2_1_1st[key]
        #容量取整数G
        if '' == dic['capacity']:
            newDic[key].append('')
        else:
            newDic[key].append(int(float(dic['capacity'])*1024)) #转化为M为单位
        
        #PC编号
        if '' == dic['MMS_PC']:
            newDic[key].append('')
        else:
            newDic[key].append(dic['MMS_PC'])
        
        #H2_1_1st
        AppendH2Result2NewDic(newDic,key,dic)

        #H2_1_2nd
        if key in caseDicH2_1_2nd:#如果键值在里面
            dic = caseDicH2_1_2nd[key]
            AppendH2Result2NewDic(newDic,key,dic)
        else:
            AppendEmptyH2Result2NewDic(newDic,key,dic)

        #CopyFile
        if key in caseDicCopyFile:
            dic = caseDicCopyFile[key]
            endTimeStr = PublicFuc.GetValueFromDic(dic,'end_time')
            startTimeStr = PublicFuc.GetValueFromDic(dic,'start_time')
            if '' == endTimeStr or '' == startTimeStr:
                newDic[key].append('')
            else:
                try:
                    endtime = datetime.strptime(endTimeStr, '%Y-%m-%d %H:%M:%S')
                    starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
                    totalSecond = timedelta.total_seconds(endtime-starttime)
                    totalSecond = int(totalSecond)
                    hour = int(totalSecond/3600)
                    lefSeconds = totalSecond%3600
                    minutes = int(lefSeconds/60)
                    seconds = lefSeconds%60
                    timeStr = '%d:%d:%d'%(hour,minutes,seconds)
                    newDic[key].append(timeStr)
                except:
                    newDic[key].append('')

            testResult =  PublicFuc.GetValueFromDic(dic,'test_result','FAIL')
            if testResult.upper() != 'PASS' and testResult.upper() != 'TRUE' and testResult.upper() != 'UNFINISHED':
                PublicFuc.AppendErrDiskInfo('Copy File_Err',key,testResult,dic['MMS_PC'],dic['file_path'])
            newDic[key].append(testResult)
        else:
            newDic[key].append('')
            newDic[key].append('')

        #SPOR
        if key in caseDicPOR:
            dic = caseDicPOR[key]

            endTimeStr = PublicFuc.GetValueFromDic(dic,'end_time')
            startTimeStr = PublicFuc.GetValueFromDic(dic,'start_time')

            if '' == endTimeStr or '' == startTimeStr:
                newDic[key].append('')
            else:
                try:
                    endtime = datetime.strptime(endTimeStr, '%Y-%m-%d %H:%M:%S')
                    starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
                    totalSecond = timedelta.total_seconds(endtime-starttime)
                    totalSecond = int(totalSecond)
                    hour = int(totalSecond/3600)
                    lefSeconds = totalSecond%3600
                    minutes = int(lefSeconds/60)
                    seconds = lefSeconds%60
                    timeStr = '%d:%d:%d'%(hour,minutes,seconds)
                    newDic[key].append(timeStr)
                except:
                    newDic[key].append('')
                
            #写读速度
            newDic[key].append(PublicFuc.GetValueFromDic(dic,'average_write_vel'))
            newDic[key].append(PublicFuc.GetValueFromDic(dic,'average_read_vel'))

            #掉电圈数
            testCycle = PublicFuc.GetValueFromDic(dic,'powerdown_cnt')
            if '' == testCycle:
                newDic[key].append('')
            else:
                newDic[key].append(int(testCycle))

            #测试结果
            testResult =  PublicFuc.GetValueFromDic(dic,'test_result','FAIL')
            newDic[key].append(testResult)
            if testResult != '' and testResult.upper() != 'PASS' and testResult.upper() != 'TRUE' and testResult.upper() != 'UNFINISHED':
                PublicFuc.AppendErrDiskInfo('空快耗尽_Err',key,testResult,dic['MMS_PC'],dic['file_path'])

        else:
            newDic[key].append('') #测试时间
            newDic[key].append('') #写平均速度
            newDic[key].append('') #读平均速读
            newDic[key].append('') #掉电圈数
            newDic[key].append('') #测试结果
    
        #H2_2_1st
        if key in caseDicH2_2_1st:#如果键值在里面
            dic = caseDicH2_2_1st[key]
            AppendH2Result2NewDic(newDic,key,dic)
        else:
            AppendEmptyH2Result2NewDic(newDic,key,dic)

        #H2_2_2nd
        if key in caseDicH2_2_2nd:#如果键值在里面
            dic = caseDicH2_2_2nd[key]
            AppendH2Result2NewDic(newDic,key,dic)
        else:
            AppendEmptyH2Result2NewDic(newDic,key,dic)

        #smart信息
        if key in caseDicH2_2_2nd:  # 如果键值在里面
            GenSmart(key, newDic, caseDicH2_2_2nd)
        elif key in caseDicH2_2_1st:  # 如果键值在里面
            GenSmart(key, newDic, caseDicH2_2_1st)
        elif key in caseDicPOR:  # 如果键值在里面
            GenSmart(key, newDic, caseDicPOR)
        elif key in caseDicCopyFile:
            GenSmart(key, newDic, caseDicCopyFile)
        elif key in caseDicH2_1_2nd:
            GenSmart(key, newDic, caseDicH2_1_2nd)
        elif key in caseDicH2_1_1st:
            GenSmart(key, newDic, caseDicH2_1_1st)
        else:
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')
            newDic[key].append('')

    return newDic 

def ProSPOR2EmptyChunck(curpath,worksheet,recordCnt):
    pattern = '.+\\\\Plan41\\\\T_GE_SD_C7\\\\Mars_H2_1_1st\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2'
    caseDicH2_1_1st = {} 
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseDicH2_1_1st, caseName, recordCnt)

    pattern = '.+\\\\Plan41\\\\T_GE_SD_C7\\\\Mars_H2_1_2nd\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2' 
    caseDicH2_1_2nd = {}
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseDicH2_1_2nd, caseName, recordCnt)

    pattern = '.+\\\\Plan41\\\\T_GE_SD_C8\\\\AT_CopyFile\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_CopyFile' 
    caseDicCopyFile = {}
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseDicCopyFile, caseName, recordCnt)

    pattern = '.+\\\\Plan41\\\\T_GE_SD_C61\\\\SPOR-2\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'SPOR-2' 
    caseDicSPOR = {}
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseDicSPOR, caseName, recordCnt)

    pattern = '.+\\\\Plan41\\\\T_GE_SD_C7\\\\Mars_H2_2_1st\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2'
    caseDicH2_2_1st = {}
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseDicH2_2_1st, caseName, recordCnt)

    pattern = '.+\\\\Plan41\\\\T_GE_SD_C7\\\\Mars_H2_2_2nd\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    caseName = 'AT_H2'
    caseDicH2_2_2nd = {}
    PublicFuc.ReadMarsIniDataEx(curpath, pattern, caseDicH2_2_2nd, caseName, recordCnt)

    keyLst = ['cap','pc_no',
             'wavg_H2_1_1','ravg_H2_1_1','test_time_H2_1_1','result_H2_1_1',
             'wavg_H2_1_2','ravg_H2_1_2','test_time_H2_1_2','result_H2_1_2',
             'test_time_CopyFile','result_CopyFile',
             'test_time_POR','wavg_SOPR2','ravg_SOPR2','circle','result_POR',#名字暂时保留POR的名字便于后续调用GetConclusion分析结果
             'wavg_H2_2_1','ravg_H2_2_1','test_time_H2_2_1','result_H2_2_1',
             'wavg_H2_2_2','ravg_H2_2_2','test_time_H2_2_2','result_H2_2_2',
             'RetryCnt','SLCBadBlock_New','TLCBadBlock_New','WL_SLC_MAX','WL_SLC_MIN','WL_SLC_AVG','WL_TLC_MAX',
              'WL_TLC_MIN','WL_TLC_AVG','SLC_Diff','TLC_Diff','PowerUpCnt','conclusion']  #第一次也显示磨损差值
    #GetNewMarsDic把ini读出来的dic按照keyLst转换为模板所需数据列表
    newDic = GetNewSPor2ReportDic(caseDicH2_1_1st,caseDicH2_1_2nd,caseDicCopyFile,caseDicSPOR,caseDicH2_2_1st,caseDicH2_2_2nd)

    #global spor_sample_maxtime
    #spor_sample_maxtime = GetMaxTime(newDic)
    #global spor_sample_cnt
    #spor_sample_cnt = len(newDic)

    GetConclusion(newDic,keyLst)

    colLst = ['B','C','D',
              'E','F','G','H',
              'I','J','K','L',
              'M','N',
              'O','P','Q','R','S',
              'T','U','V','W',
              'X','Y','Z','AA',
              'AB','AC','AD','AE','AF','AG','AH','AI','AJ','AK','AL','AM',
              'AN']

    startLine = 79
    #写标号
    FillNo(worksheet, startLine, newDic)
    #写内容
    resultColumnList = ['H','L','N','S','W','AA','AN']
    PublicFuc.WriteDataNormal(worksheet, startLine, newDic, colLst, keyLst,1,resultColumnList)