#pragma once


// CEMMCConfigDlg dialog

class CEMMCConfigDlg : public CDialogEx
{
	DECLARE_DYNAMIC(CEMMCConfigDlg)

public:
	CEMMCConfigDlg(CWnd* pParent = NULL);   // standard constructor
	virtual ~CEMMCConfigDlg();

// Dialog Data
	enum { IDD = IDD_DIALOG_EMMC };


	virtual BOOL OnInitDialog();
	afx_msg void OnBnClickedOk();

protected:
	virtual void DoDataExchange(CDataExchange* pDX);    // DDX/DDV support

	DECLARE_MESSAGE_MAP()
public:
	afx_msg void OnBnClickedButtonSelectFile();
private:
	CString m_strVtePath;
	CString m_strPhaseFile;
	CString m_strPhaseNode;
	CString m_strPhase1;
	CString m_strPhase2;
	int m_nMinPhase1;
	int m_nMaxPhase1;
	int m_nMinPhase2;
	int m_nMaxPhase2;
	int m_nPhaseStep1;
	int m_nPhaseStep2;
	int m_nCycle;
	CString m_strNo;
	int m_nWatiSec;
};
