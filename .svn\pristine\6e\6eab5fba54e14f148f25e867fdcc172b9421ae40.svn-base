import os
import MTT_PV_Report
import numpy as np
from openpyxl.styles import  Alignment
from openpyxl.chart import Line<PERSON>hart,Reference
flag = True
alignment = Alignment(horizontal='center',vertical='center')
alldata = {
    'x1':{'sw':{},'rw':{}},
    'x2':{'sw':{},'rw':{}},
    'x4':{'sw':{},'rw':{}}
}
def GetPVDir():
    lPath = MTT_PV_Report.lPath
    listDir=[]
    key = 1
    for obj in lPath:
        strXu4Dir=os.path.join(obj,'XU4')
        for dirpath,dirnames,filenames in os.walk(strXu4Dir):
            for dir in dirnames:
                if dir.startswith('PV-'):
                    listDir.append([os.path.join(dirpath,dir),key])
        key += 1
    return listDir

def getsw(speed):
    speed_list = speed.split('\t')
    val_lst = float(speed_list[1].replace('\n',''))
    return val_lst

def getrw(speed):
    speed_list = speed.split('\t')
    val_lst = [float(speed_list[1]),float(speed_list[2]),float(speed_list[3]),float(speed_list[4]),float(speed_list[5]),float(speed_list[7]),float(speed_list[9].replace('\n',''))]
    return val_lst
def Run(filepath,wb):
    filelist = GetPVDir()
    for file_path in filelist:
        for dirpath,dirnames,filenames in os.walk(file_path[0]):
            for item in filenames:
                longtime_sw = []
                longtime_rw = []
                Type = 'x1'
                if '128G' in file_path[0]:
                    Type = 'x2'
                if '256G' in file_path[0]:
                    Type = 'x4'
                if 'verify performance longTime_RW_perf' in item and ('Failed' in item or 'Passed' in item):
                    key = 0
                    with open(os.path.join(file_path[0],item), "r",errors='ignore') as file:
                        lines = file.readlines()
                        for line in lines:
                            if 'cnt	IOPS' in line:
                                key = 1
                                continue
                            if 'Error' in line or 'start USB' in line or 'ERROR' in line:
                                key = 0
                            if key == 1:
                                longtime_rw.append(getrw(line))
                    makeImage_1(longtime_rw,Type,'rw')
                if 'verify performance longTime_SW_perf' in item and ('Failed' in item or 'Passed' in item):
                    key = 0
                    with open(os.path.join(file_path[0],item), "r",errors='ignore') as file:
                        lines = file.readlines()
                        for line in lines:
                            if 'cnt	SW(MB/s)' in line:
                                key = 1
                                continue
                            if 'Error' in line or 'start USB' in line or 'ERROR' in line:
                                key = 0
                            if key == 1:
                                longtime_sw.append(getsw(line))
                    makeImage_2(longtime_sw,Type,'sw')
    insertdata(alldata,wb)
def makeImage_1(data,Type,key):
    xlabel = [x for x in range(len(data))]
    if len(data) > 500:
        # 生成均匀的索引
        indices = np.linspace(0, len(data) - 1, num=500).astype(int)
        xlabel = [idx for idx in indices]
        # 使用生成的索引提取对应的元素
        data = [data[idx] for idx in indices]
    yLst_iops = []
    yLst_max = []
    yLst_0 = []
    yLst_1 = []
    yLst_50 = []
    yLst_100 = []
    yLst_500 = []
    for item in data:
        yLst_iops.append(item[0])
        yLst_max.append(item[1])
        yLst_0.append(item[2])
        yLst_1.append(item[3])
        yLst_50.append(item[4])
        yLst_100.append(item[5])
        yLst_500.append(item[6])
    alldata[Type][key]['yLst_iops'] = yLst_iops
    alldata[Type][key]['yLst_max'] = yLst_max
    alldata[Type][key]['yLst_0'] = yLst_0
    alldata[Type][key]['yLst_1'] = yLst_1
    alldata[Type][key]['yLst_50'] = yLst_50
    alldata[Type][key]['yLst_100'] = yLst_100
    alldata[Type][key]['yLst_500'] = yLst_500
    alldata[Type][key]['xLst'] = xlabel

def makeImage_2(data,Type,key):
    xlabel = [x for x in range(len(data))]
    if len(data) > 500:
        # 生成均匀的索引
        indices = np.linspace(0, len(data) - 1, num=500).astype(int)

        # 使用生成的索引提取对应的元素
        xlabel = [idx for idx in indices]
        data = [data[idx] for idx in indices]
    
    yLst = []
    for item in data:
       yLst.append(item)
    alldata[Type][key]['yLst'] = yLst
    alldata[Type][key]['xLst'] = xlabel

def insertdata(data,wb):
    ws_name =  'longTime_RW_SW'
    ws = wb[ws_name]
    col_line = 1
    for types in data:
        xLst = []
        yLst = []
        if 'xLst' in data[types]['sw']:
            xLst = data[types]['sw']['xLst']
            yLst = data[types]['sw']['yLst']
        line = 2
        for item in xLst:
            ws.cell(row=line, column=col_line, value=item)
            line += 1
        col_line += 1
        line = 2
        for item in yLst:
            ws.cell(row=line, column=col_line, value=item)
            line += 1
        col_line += 1

        xLst = []
        ytitle = ['yLst_iops','yLst_max','yLst_0','yLst_1','yLst_50','yLst_100','yLst_500']
        if 'xLst' in data[types]['rw']:
            xLst = data[types]['rw']['xLst']
        line = 2
        for item in xLst:
            ws.cell(row=line, column=col_line, value=item)
            line += 1
        col_line += 1
        for item in ytitle:
            line = 2
            yLst = []
            if item in data[types]['rw']:
                yLst = data[types]['rw'][item]
            for item in yLst:
                ws.cell(row=line, column=col_line, value=item)
                line += 1
            col_line += 1
        col_line += 1
    col_line = 1
    
    for types in data:
        pic_col = 'AH'
        if types == 'x2':
            pic_col = 'AQ'
        if types == 'x4':
            pic_col = 'AZ'
        lines = 1       
        chart = LineChart()
        chart.title = types + '_sw_speed'
        chart.x_axis.title = "cnt"
        chart.y_axis.title = types + '_sw_speed'
        if 'xLst' in data[types]['sw'] and len(data[types]['sw']['xLst']) > 0:
            categories_reference = Reference(ws, min_col=col_line,max_col=col_line, min_row=2, max_row=len(data[types]['sw']['xLst']))
        # 设置数据范围
        col_line += 1
        if 'xLst' in data[types]['sw'] and len(data[types]['rw']['xLst']) > 0:
            data_reference = Reference(ws, min_col=col_line,  max_col=col_line,min_row=2, max_row=len(data[types]['sw']['yLst']))
            # 将数据和类别添加到图表
            chart.add_data(data_reference, titles_from_data=True)
            chart.set_categories(categories_reference)
            chart.legend = None
            # 将图表添加到工作表
            ws.add_chart(chart,  "%s%d"%(pic_col,lines))  # 将图表插入在 E5 单元格位置
        chart = LineChart()
        col_line += 1
        lines += 16
        if 'xLst' in data[types]['rw'] and len(data[types]['rw']['xLst']) > 0:
            categories_reference = Reference(ws, min_col=col_line,max_col=col_line, min_row=2, max_row=len(data[types]['rw']['xLst']))
        col_line += 1
        ytitle = ['yLst_iops','yLst_max']
        for item in ytitle:
            chart.title = types  + item.replace('yLst','') 
            chart.x_axis.title = "cnt"
            chart.y_axis.title = types + item.replace('yLst','') 
            if 'xLst' in data[types]['rw'] and len(data[types]['rw']['xLst']) > 0:
                data_reference = Reference(ws, min_col=col_line,  max_col=col_line,min_row=2, max_row=len(data[types]['rw'][item]))
                # 将数据和类别添加到图表
                chart.add_data(data_reference, titles_from_data=True)
                chart.set_categories(categories_reference)
                chart.legend = None
                # 将图表添加到工作表
                ws.add_chart(chart, "%s%d"%(pic_col,lines))  # 将图表插入在 E5 单元格位置
                chart = LineChart()
            col_line += 1
            lines += 16
        col_line += 6
