[TOOL]
; 工具类型：1 AS SSD, 2 CDM, 3 h2test, 4 hd tune, 5 ATTO DISK, 6 BIT
TYPE = 24
PATH = TOOLS\CrystalDiskMark8_0_1\DiskMark64.exe

[PARAM]
; 0 全部测试模式 1-4 单项测试模式，缺省值：0
TEST_MODE = 0
; 测试圈数，1-9，缺省值：1
TEST_CIRCLE = 1
; 测试容量 缺省值：1GiB
TEST_CAP = 1GiB
; 测试配置模式 0 Default配置 1  NVMe SSD, 2 custom定制模式 -目前等同于0
TEST_CFG_MODE = 1
; 显示单位，包含MB/s、GB/s、IOPS、us 默认MB/s
TEST_DISPLAY_UNIT = MB/s
; 显示模式,0 默认，1 峰值性能，2 实际表现 3 demo 4 默认[+mix] 5 峰值性能[+mix] 6 实际表现[+mix]，默认为0
Profile=4
; 显示状态中下半部分模式, 1 Read[+mix] 2 Write[+mix] 3 Read&Write[+mix]，默认为3
Benchmark=3
; 混和比例只有nProfile为mix模式时，此值才有意义，[0-8]对应[R10%/W90% - R90%/W10%]变化梯度为10%，默认为6，即R70%/W30%
TestMix=6