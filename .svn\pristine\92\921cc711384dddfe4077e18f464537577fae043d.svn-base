// stdafx.cpp : source file that includes just the standard includes
// PortMapLocate.pch will be the pre-compiled header
// stdafx.obj will contain the pre-compiled type information

#include "stdafx.h"

std::string GetSystemRootDir()
{
	char sysDir[MAX_PATH] = {0};
	GetSystemDirectory(sysDir, MAX_PATH);
	std::string strSystemDir(sysDir);
	int pos = strSystemDir.find(":");
	if (-1 != pos)
	{
		strSystemDir = strSystemDir.substr(pos-1, 2);
	}
	return strSystemDir;
}

std::string GetAterExMapLocFilePath()
{
	std::string strLocalConfigFilePath = GetSystemRootDir() + "\\Yeestor\\";
	strLocalConfigFilePath += ATEREX_MAP_LOC_FILE;
	return strLocalConfigFilePath;
}