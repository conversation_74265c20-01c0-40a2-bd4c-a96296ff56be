import configparser
import csv
import os,re,time,logging
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment
from datetime import datetime,timedelta

testno = '' #测试单号，用于从数据库中统计睡眠和休眠信息
minKey = ['Read Acc Time']
alignment = Alignment(horizontal='center',vertical='center')
alignmentNewLine = Alignment(horizontal='center',vertical='center',wrap_text=True)
warnFill = PatternFill('solid', fgColor='FF0000')
commonSmartKey = ['F9','A3','A4','A5','A6','A7','A8','A9','AA','AB','AF','B2','B5','B6','B7','B8','B9','BA','05','0C','C0','C3','C4','C5','C6','C7']
errDiskLst = []
marsKey = ['06','07','test_result','start_time','end_time','pc_name','flash_name','cap','fw_version','test_cirlce','powercnts','avg_write_speed','min_write_speed','max_write_speed','avg_read_speed','min_read_speed',\
            'max_read_speed','waf']
mars_commonSmartKey = []
temp_commonSmartKey = []
muil_marsKey = ['test_result','start_time','end_time','pc_name','flash_name','test_cirlce','powercnts','avg_write_speed','min_write_speed','max_write_speed','avg_read_speed','min_read_speed','max_read_speed']
for item in commonSmartKey:
    smart = 'id_'+item.lower()
    marsKey.append(smart)
    mars_commonSmartKey.append(smart)
    temp_commonSmartKey.append('p_'+item.lower())


fileLst = []
config = configparser.RawConfigParser()

def defineimg(path):
    try:
        img = Image(path)
        return True
    except:
        return False
    
def GetAllFile(curpath):
    global testno
    for dirpath,dirnames,filenames in os.walk(curpath):
        if testno == '':
            idx = dirpath.find('testno_')
            if idx != -1:
                testno = dirpath[idx+len('testno_'):len(dirpath)]
        for filename in filenames:
            fullname = os.path.join(dirpath, filename)
            fileLst.append(fullname)

def WriteErrDiskFile(strFile):
    if 0 != len(errDiskLst):
        with open(strFile,'w+') as file: 
            for errLst in errDiskLst:
                strErr = '样片:%s    PC:%s    Err:%s    Time:%s\n'%(errLst[0],errLst[1],errLst[2],errLst[3])
                file.write(strErr)


def ReadQaIniData(curpath, pattern, dataDic, keyLst, imageSuffix, recordCnt = 10, diskCnt = 2):
    unitLst = ['GB','MB/s', 'ms', '%']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        for sec in config.sections():
            if sec not in dataDic:
                #diskCnt为0表示统计所有磁盘数据
                if diskCnt == len(dataDic) and 0 != diskCnt:
                    continue
                dataDic[sec] = []
            tempLst = []
            pcNo = ''
            if 'pc_no' in config[sec]:
                pcNo = config[sec]['pc_no']
                for key in keyLst:
                    if key.lower() in config[sec]:
                        value = config[sec][key.lower()]
                        #去除单位
                        for unit in unitLst:
                            if unit in value:
                                value = value[:value.find(unit)]
                                break
                        tempLst.append(value)
                        if 'qa_err_msg' == key:
                            filemt= time.localtime(os.stat(file).st_mtime)  
                            strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                            errDiskLst.append([sec,pcNo,value,strTime])
                    else:
                        tempLst.append('')
                if len(dataDic[sec]) < recordCnt and [] != tempLst:
                    #imageSuffix为空不需要截图，只需要数据
                    if '' != imageSuffix:
                        image = imagepath+'\\%s_%s'%(sec, imageSuffix)
                        if os.path.isfile(image):
                            tempLst.append(image)
                        else:
                            tempLst.append('')
                    dataDic[sec].append(tempLst)

def ReadQaIniDataEx(curpath, pattern, dataDic, keyLst, imageSuffix, recordCnt = 10, diskCnt = 2):
    unitLst = ['GB', 'MB/s', 'ms', '%']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        for sec in config.sections():
            if sec not in dataDic:
                #diskCnt为0表示统计所有磁盘数据
                if diskCnt == len(dataDic) and 0 != diskCnt:
                    continue
                dataDic[sec] = []
                tempLst = []
                pcNo = ''
                if 'pc_no' in config[sec]:
                    pcNo = config[sec]['pc_no']
                for key in keyLst:
                    if key.lower() in config[sec]:
                        value = config[sec][key.lower()]
                        #去除单位
                        for unit in unitLst:
                            if unit in value:
                                value = value[:value.find(unit)]
                                break
                        tempLst.append(value)
                        if 'qa_err_msg' == key:
                            filemt= time.localtime(os.stat(file).st_mtime)  
                            strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                            errDiskLst.append([sec,pcNo,value,strTime])
                    else:
                        tempLst.append('')
                fileMdTime = os.path.getmtime(file)
                if len(dataDic[sec]) < recordCnt and [] != tempLst:
                    tempLst.append(fileMdTime)
                    image = imagepath+'\\%s_%s'%(sec, imageSuffix)
                    if os.path.isfile(image):
                        tempLst.append(image)
                    else:
                        tempLst.append('')
                    dataDic[sec].append(tempLst)
            else:
                fileMdTime = os.path.getmtime(file)
                if dataDic[sec][0][-2] > fileMdTime:
                    continue
                dataDic[sec] = []
                tempLst = []
                pcNo = ''
                if 'pc_no' in config[sec]:
                    pcNo = config[sec]['pc_no']
                for key in keyLst:
                    if key.lower() in config[sec]:
                        value = config[sec][key.lower()]
                        #去除单位
                        for unit in unitLst:
                            if unit in value:
                                value = value[:value.find(unit)]
                                break
                        tempLst.append(value)
                        if 'qa_err_msg' == key:
                            filemt= time.localtime(os.stat(file).st_mtime)  
                            strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                            errDiskLst.append([sec,pcNo,value,strTime])
                    else:
                        tempLst.append('')
                if len(dataDic[sec]) < recordCnt and [] != tempLst:
                    tempLst.append(fileMdTime)
                    image = imagepath+'\\%s_%s'%(sec, imageSuffix)
                    if os.path.isfile(image):
                        tempLst.append(image)
                    else:
                        tempLst.append('')
                    dataDic[sec].append(tempLst)

def getImage(pattern,tempLst,imageSuffix):
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        if imageSuffix in file:
            image = file
            if os.path.isfile(image):
                tempLst.append(image)
            else:
                tempLst.append('')

def ReadSmart(curpath, pattern, dataDic, diskCnt = 2):
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        SmartKey = temp_commonSmartKey
        for sec in config.sections():
            if sec not in dataDic:
                #diskCnt为0表示统计所有磁盘数据
                if diskCnt == len(dataDic) and 0 != diskCnt:
                    continue
                dataDic[sec] = []
            tempLst = []
            smart = ''
        #统计不为0的smart信息
            for key in SmartKey:
                if key.lower() in config[sec]:
                    value = config[sec][key.lower()]
                    #去除单位
                    if '' == value:
                        continue
                    if 0 == int(value,16):
                        continue
                    innerKey = key.lower()
                    pos = innerKey.find('p_')
                    id = innerKey[pos+len('p_'):].upper()
                    smart += '%s=%s,'%(id, value[2:].upper())
            tempLst.append(smart)    
            if 'waf' in config[sec]:
                tempLst.append(config[sec]['waf'])
            dataDic[sec].append(tempLst)

def ReadMarsInfo(curpath, pattern, dataDic, caseName, recordCnt = 2):
    unitLst = ['M/s']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        split_path = file.split("\\")
        smartpat = "\\\\".join(split_path[:-4])+'\\\\SMART\\\\\\d{14}\\\\report.ini$'
        #logging.info(file)
        if caseName not in config.sections():
            continue
        keyName = config[caseName]['flash_name']
        if keyName not in dataDic:
            #0表示样片数量不确定，需要获取全部数据
            if recordCnt == len(dataDic) and 0 != recordCnt:
                continue
            dataDic[keyName] = {}
            dataDic[keyName]['MarsTime'] = os.path.getmtime(file)
            for key in muil_marsKey:
                if key not in config[caseName]:
                    dataDic[keyName][key] = '' 
                    continue
                value = config[caseName][key]
                #去除单位
                for unit in unitLst:
                    if unit in value:
                        value = value[:value.find(unit)]
                        break
                dataDic[keyName][key] = value
            if 'HWCONFIG' in config.sections() and 'CAPACITY' in config['HWCONFIG']:
                dataDic[keyName]['cap'] = config['HWCONFIG']['CAPACITY']
            if 'PASS'== dataDic[keyName]['test_result']:
                dataDic[keyName]['test_result'] = ''
            else:
                filemt = time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                errDiskLst.append([keyName,dataDic[keyName]['flash_name'],dataDic[keyName]['test_result'],strTime])
            for smartfile in fileLst:
                if not re.match(smartpat, smartfile):
                    continue
                config.clear()
                config.read(smartfile,encoding = 'gbk')
                SmartKey = temp_commonSmartKey + ['06','07']
                for sec in config.sections():
                    if sec not in dataDic:
                        continue
                #统计不为0的smart信息
                    for key in SmartKey:
                        if key.lower() in config[sec]:
                            value = config[sec][key]
                        #去除单位
                            dataDic[sec][key] = value
                    if 'waf' in config[sec]:
                        dataDic[sec]['waf'] = config[sec]['waf']
                break
        else:
            fileMdTime = os.path.getmtime(file)
            if dataDic[keyName]['MarsTime'] > fileMdTime:
                continue
            dataDic[keyName] = {}
            dataDic[keyName]['MarsTime'] = fileMdTime
            for key in muil_marsKey:
                if key not in config[caseName]:
                    dataDic[keyName][key] = '' 
                    continue
                value = config[caseName][key]
                #去除单位
                for unit in unitLst:
                    if unit in value:
                        value = value[:value.find(unit)]
                        break
                dataDic[keyName][key] = value
            if 'HWCONFIG' in config.sections() and 'CAPACITY' in config['HWCONFIG']:
                dataDic[keyName]['cap'] = config['HWCONFIG']['CAPACITY']
            if 'PASS'== dataDic[keyName]['test_result']:
                dataDic[keyName]['test_result'] = ''
            else:
                filemt = time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                errDiskLst.append([keyName,dataDic[keyName]['flash_name'],dataDic[keyName]['test_result'],strTime])
            for smartfile in fileLst:
                if not re.match(smartpat, smartfile):
                    continue
                config.clear()
                config.read(smartfile,encoding = 'gbk')
                SmartKey = temp_commonSmartKey + ['06','07']
                for sec in config.sections():
                    if sec not in dataDic:
                        continue
                #统计不为0的smart信息
                    for key in SmartKey:
                        if key.lower() in config[sec]:
                            value = config[sec][key]
                        #去除单位
                            dataDic[sec][key] = value
                    if 'waf' in config[sec]:
                        dataDic[sec]['waf'] = config[sec]['waf']
                break

def ReadMarsIniData(curpath, pattern, dataDic, caseName, recordCnt = 2):
    unitLst = ['M/s']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        #logging.info(file)
        if caseName not in config.sections():
            continue
        keyName = config[caseName]['flash_name']
        if keyName not in dataDic:
            #0表示样片数量不确定，需要获取全部数据
            if recordCnt == len(dataDic) and 0 != recordCnt:
                continue
            dataDic[keyName] = {}
            for key in marsKey:
                if key not in config[caseName]:
                    dataDic[keyName][key] = '' 
                    continue
                value = config[caseName][key]
                #去除单位
                for unit in unitLst:
                    if unit in value:
                        value = value[:value.find(unit)]
                        break
                dataDic[keyName][key] = value
            if 'HWCONFIG' in config.sections() and 'CAPACITY' in config['HWCONFIG']:
                dataDic[keyName]['cap'] = config['HWCONFIG']['CAPACITY']
            if 'PASS'== dataDic[keyName]['test_result']:
                dataDic[keyName]['test_result'] = ''
            else:
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                errDiskLst.append([keyName,dataDic[keyName]['flash_name'],dataDic[keyName]['test_result'],strTime])

def ReadMarsIniDataPro(curpath, pattern, dataDic, keyLst, imageSuffix, recordCnt = 10, diskCnt = 2):
    unitLst = ['GB','MB/s', 'ms', '%']
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        for sec in config.sections():
            if sec not in dataDic:
                #diskCnt为0表示统计所有磁盘数据
                if diskCnt == len(dataDic) and 0 != diskCnt:
                    continue
                dataDic[sec] = {}
            pcNo = ''
            if 'pc_no' in config[sec]:
                pcNo = config[sec]['pc_no']
            for key in keyLst:
                if key.lower() in config[sec]:
                    value = config[sec][key.lower()]
                    #去除单位
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    dataDic[sec][key] = value
            if 'qa_err_msg' in config[sec]:
                filemt= time.localtime(os.stat(file).st_mtime)  
                strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
                errDiskLst.append([sec,pcNo,value,strTime])
                dataDic[sec]['test_result'] = config[sec]['qa_err_msg']
            else:
                dataDic[sec]['test_result'] = ''

                
def ReadMarsCsvData(curpath,pattern,dataDic,smartinfo):
    #fileIdx = 1
    strNo = 1
    for file in fileLst:
        if not re.match(pattern, file):
            continue
        with open(file,mode='r', buffering=-1, encoding='cp936') as csvfile:
                csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
                temp = list(csv_reader) # 读取第一行每一列的标题 
                dataDic[strNo] = temp
        ini_file_names = [filename for filename in os.listdir(os.path.dirname(file)) if filename.endswith(".ini")]
        for filesmart in ini_file_names:
            temp = []
            config.clear()
            config.read(os.path.dirname(file) + '\\' +filesmart,encoding = 'gbk')
            if 'HWCONFIG' in config.sections():
                if 'MMS_PC' in config['HWCONFIG']:
                    temp.append(config['HWCONFIG']['MMS_PC'])
                else:
                    temp.append('')
                if 'MMS_FLASH' in config['HWCONFIG']:
                    temp.append(config['HWCONFIG']['MMS_FLASH'])
                else:
                    temp.append('')
                if 'CAPACITY' in config['HWCONFIG']:
                    temp.append(config['HWCONFIG']['CAPACITY'])
                else:
                    temp.append('')
            smartinfo[strNo] = temp
        strNo += 1

def ReadH2testwData(curpath,pattern,dataDic):
    for file in fileLst:
        tempsec = ''
        if not re.match(pattern, file):
            continue
        split_path = file.split("\\")
        smartpat = "\\\\".join(split_path[:-3])+'\\\\SMART\\\\\\d{14}\\\\report.ini$'
        for filesmart in fileLst:
            if not re.match(smartpat, filesmart):
                continue
            config.clear()
            config.read(filesmart,encoding = 'gbk')
            SmartKey = temp_commonSmartKey
            smart = ''
            for sec in config.sections():
                tempsec = sec
                if tempsec not in dataDic:
                    dataDic[tempsec] = {}
                dataDic[tempsec]['mode_name'] = sec
                if 'pc_no' in config[sec]:
                    dataDic[tempsec]['pc_no'] = config[sec]['pc_no']
                if 'Cap' in config[sec]:
                    dataDic[tempsec]['Cap'] = config[sec]['Cap']
                if 'WAF' in config[sec]:
                    dataDic[tempsec]['WAF'] = config[sec]['WAF']
                dataDic[tempsec]['test_result'] = ''
                for key in SmartKey:
                    if key.lower() in config[sec]:
                        value = config[sec][key.lower()]
                        #去除单位
                        if '' == value:
                            continue
                        if 0 == int(value,16):
                            continue
                        innerKey = key.lower()
                        pos = innerKey.find('p_')
                        id = innerKey[pos+len('p_'):].upper()
                        smart += '%s=%s,'%(id, value[2:].upper())
                dataDic[tempsec]['fir smart'] = smart
        config.clear()
        config.read(file,encoding = 'gbk')
        for sec in config.sections():
            tempsec = sec
            if tempsec not in dataDic:
                dataDic[tempsec] = {}
            dataDic[tempsec]['mode_name'] = sec
            if 'qa_err_msg' in config[sec]:
                dataDic[tempsec]['qa_err_msg'] = config[sec]['qa_err_msg']
            if 'pc_no' in config[sec]:
                dataDic[sec]['pc_no'] = config[sec]['pc_no']
            if 'Cap' in config[sec]:
                dataDic[tempsec]['Cap'] = config[sec]['Cap']
            if 'write speed' in config[sec]:
                dataDic[tempsec]['fir write speed'] = config[sec]['write speed']
            if 'read speed' in config[sec]:   
                dataDic[tempsec]['fir read speed'] = config[sec]['read speed']


def ReadH2testrData(curpath,pattern,dataDic,temp):
    for file in fileLst:
        tempsec = ''
        if not re.match(pattern, file):
            continue
        split_path = file.split("\\")
        smartpat = "\\\\".join(split_path[:-3])+'\\\\SMART\\\\\\d{14}\\\\report.ini$'
        for filesmart in fileLst:
            if not re.match(smartpat, filesmart):
                continue
            config.clear()
            config.read(filesmart,encoding = 'gbk')
            SmartKey = temp_commonSmartKey
            smart = ''
            for sec in config.sections():
                tempsec = sec
                if tempsec not in dataDic:
                    dataDic[tempsec] = {}
                dataDic[tempsec]['mode_name'] = sec
                if 'pc_no' in config[sec]:
                    dataDic[tempsec]['pc_no'] = config[sec]['pc_no']
                if 'Cap' in config[sec]:
                    dataDic[tempsec]['Cap'] = config[sec]['Cap']
                if 'WAF' in config[sec]:
                    dataDic[tempsec]['WAF'] = config[sec]['WAF']
                dataDic[tempsec]['test_result'] = ''
                for key in SmartKey:
                    if key.lower() in config[sec]:
                        value = config[sec][key.lower()]
                        #去除单位
                        if '' == value:
                            continue
                        if 0 == int(value,16):
                            continue
                        innerKey = key.lower()
                        pos = innerKey.find('p_')
                        id = innerKey[pos+len('p_'):].upper()
                        smart += '%s=%s,'%(id, value[2:].upper())
                dataDic[tempsec]['sec smart'] = smart
        config.clear()
        config.read(file,encoding = 'gbk')
        for sec in config.sections():
            tempsec = sec
            if tempsec not in dataDic:
                dataDic[tempsec] = {}
            dataDic[tempsec]['mode_name'] = sec
            if 'qa_err_msg' in config[sec]:
                dataDic[tempsec]['qa_err_msg'] = config[sec]['qa_err_msg']
            if 'pc_no' in config[sec]:
                dataDic[sec]['pc_no'] = config[sec]['pc_no']
            if 'Cap' in config[sec]:
                dataDic[tempsec]['Cap'] = config[sec]['Cap']
            if 'read speed' in config[sec]:   
                dataDic[tempsec][temp + ' read speed'] = config[sec]['read speed']

def WriteData2testw(worksheet, startLine, dataDic, TempCol, smartCol, errCol):
    curLine = startLine
    for key in dataDic:
        for index,col in enumerate(TempCol):
            if col in dataDic[key]:
                worksheet['%s%d'%(smartCol[index], curLine)] = dataDic[key][col]
        if 'qa_err_msg' in dataDic[key]:
            worksheet['%s%d'%(errCol, curLine)] = dataDic[key]['qa_err_msg']
        curLine += 1

def WriteDataAndImage(worksheet, startLine, dataDic, colLst, keyLst, imgWidth, imgHeight):
    imageLine = startLine+20
    curLine = startLine
    for key in dataDic:
        imageCol = 1
        #最后一行数据是经过处理获取的最值
        limitLine = dataDic[key][-1]
        for line in dataDic[key][:-1]:
            for index,col in enumerate(colLst):
                if 0 == index:
                    worksheet['%s%d'%(col, curLine)] = key
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
                    try:
                        floatValue = float(line[index-1])
                        #和最值相差20%需要填充红色警告
                        if keyLst[index-1] not in minKey:
                            if floatValue <= limitLine[index-1]*0.8:
                                worksheet['%s%d'%(col, curLine)].fill = warnFill
                        else:
                            if floatValue >= limitLine[index-1]*1.2:
                                worksheet['%s%d'%(col, curLine)].fill = warnFill
                    except:
                        continue
            curLine += 1
            # 列表最后一项是图片路径
            if '' != line[-1] and defineimg(line[-1]):
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
            imageCol += 3
        imageLine += 1
        curLine = startLine+10
#1个样片多条记录
def WriteDataMarsCsv(worksheet, startLine, dataDic, colLst, smartinfo, smartcol):
    curLine = startLine
    for key in dataDic:
        for index,col in enumerate(smartcol):
            worksheet['%s%d'%(col, curLine)] = smartinfo[key][index]
        for item in dataDic[key]:
            limitLine = item[-1]
            for line in item[:-1]:
                for index,col in enumerate(colLst):
                    worksheet['%s%d'%(col, curLine)] = line[index].strip()
                    if float(line[index].strip()) <= limitLine[index]*0.8:
                        worksheet['%s%d'%(col, curLine)].fill = warnFill
                curLine += 1

def WriteDataMarsOverwirteCsv(worksheet, startLine, dataDic, colLst, smartinfo, smartcol):
    curLine = startLine
    for key in dataDic:
        for index,col in enumerate(smartcol):
            if key in smartinfo and index < len(smartinfo[key]):
                worksheet['%s%d'%(col, curLine)] = smartinfo[key][index]
        for index,col in enumerate(colLst):
            if key in dataDic and index < len(dataDic[key]):
                worksheet['%s%d'%(col, curLine)] = dataDic[key][index].strip()
        curLine += 1

def WriteData(worksheet, startLine, dataDic, colLst, keyLst, lineCnt = 1):
    curLine = startLine
    for key in dataDic:
        for line in dataDic[key]:
            for index,col in enumerate(colLst):
                try:
                    if 0 == index:
                        #第一列是编号，直接填key
                        worksheet['%s%d'%(col, curLine)] = key
                    else:
                        worksheet['%s%d'%(col, curLine)] = line[index-1]
                    worksheet['%s%d'%(col, curLine)].alignment = alignment
                #合并的单元格只能写一次，需要捕获异常
                except(AttributeError):
                    continue
            curLine += lineCnt
            startLine += 1
    return startLine
#1个样片只有一条记录  lineCnt兼容excel多行合并成一行的情况
def WriteDataNormal(worksheet, startLine, dataDic, colLst, keyLst, lineCnt = 1):
    curLine = startLine
    for key in dataDic:
        line = dataDic[key]
        for index,col in enumerate(colLst):
            try:
                if 0 == index:
                    #第一列是编号，直接填key
                    worksheet['%s%d'%(col, curLine)] = key
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
                worksheet['%s%d'%(col, curLine)].alignment = alignment
            #合并的单元格只能写一次，需要捕获异常
            except(AttributeError):
                continue
        curLine += lineCnt

#获取每个编号每列数据的最值
def GetMaxOrMinValueLst(keyLst,dataDic):
    for key in dataDic:
        resultLst = []
        for index,col in enumerate(keyLst):
            tempLst = [line[index] for line in dataDic[key]]
            limitData = 0
            bFirstData = True
            for data in tempLst:
                try:
                    tempData = float(data)
                    #部分列需要取最小值，例如时间等
                    if bFirstData:
                        limitData = tempData
                        bFirstData = False
                        continue
                    if col in minKey:
                        if tempData < limitData:
                            limitData = tempData
                    else:
                        if tempData > limitData:
                            limitData = tempData
                except:
                    continue
            resultLst.append(limitData)
        dataDic[key].append(resultLst)

def FmtStrHex(strHex):
    #去掉十六进制前面的多个0
    strNew = strHex.lstrip('0')
    if '' == strNew:
        strNew = '0'
    return strNew

def GetImtResultPath(strImtBmpPath, key):
    pos = strImtBmpPath.rfind('\\')
    strPath = strImtBmpPath[:pos+1] + 'inst%s_IometerResults.csv'%key
    return strPath

def GetNewIoMeterDic(oldDic, startPos, smartKey, bA23 = False):
    #startPos之后的数据为smart信息，需转换为模板所需数据[F1(G),F2(G),smart(非0),A5-A6]
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        for dataLst in oldDic[key]:
            newLst = dataLst[:startPos]
            smartLst = dataLst[startPos:]
            #F1、F2 1个单位为32M，需转化为G
            if smartLst[0] == '':
                newLst.append('')
            else:
                newLst.append((int(smartLst[0],16)*32)//1024)
            if smartLst[1] == '':
                newLst.append('')
            else:
                newLst.append((int(smartLst[1],16)*32)//1024)
            strSmart = ''
            for idx,value in enumerate(smartLst[4:]):
                if bA23 and idx == len(smartLst[4:])-2:
                    break
                if value != '' and 0 != int(value,16):
                    strSmart += '%s=%s,'%(smartKey[4+idx], FmtStrHex(value))
            if '' != strSmart:
                strSmart = strSmart[:-1]
            newLst.append(strSmart)
            if smartLst[2] == '' or smartLst[3] == '':
                newLst.append('')
            else:
                newLst.append(int(smartLst[2],16)-int(smartLst[3],16))
            if bA23:
                strImtResultPath = GetImtResultPath(smartLst[-1], key)
                newLst.append(strImtResultPath)
            newDic[key].append(newLst)
    return newDic

def GetNewIoMeterDicEx(oldDic, startPos, smartKey, bA23 = False):
    #startPos之后的数据为smart信息，需转换为模板所需数据[F1(G),F2(G),smart(非0),A5-A6]
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        for dataLst in oldDic[key]:
            newLst = dataLst[:startPos]
            smartLst = dataLst[startPos:]
           
            strSmart = ''
            for idx,value in enumerate(smartLst[4:]):
                if bA23 and idx == len(smartLst[4:])-1:
                    break
                if value != '' and 0 != int(value,16):
                    strSmart += '%s=%s,'%(smartKey[4+idx], FmtStrHex(value))
            if '' != strSmart:
                strSmart = strSmart[:-1]
            newLst.append(strSmart)
            if smartLst[2] == '' or smartLst[3] == '': 
                newLst.append('')
            else:
                newLst.append(int(smartLst[2],16)-int(smartLst[3],16))
            if bA23:
                strImtResultPath = GetImtResultPath(smartLst[-1], key)
                newLst.append(strImtResultPath)
            newDic[key].append(newLst)
    return newDic

def GetNewMarsDic(oldDic, keyLst):
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        dic = oldDic[key]
        newDic[key].append(dic['MMS_PC'])
        #容量取整数G
        if '' == dic['capacity']:
            newDic[key].append('')
        else:
            newDic[key].append(int(float(dic['capacity'])))
        newDic[key].append(dic['test_result'])
        if '' == dic['end_circle'] or '' == dic['start_circle']:
            newDic[key].append('')
        else:
            newDic[key].append(int(dic['end_circle'])-int(dic['start_circle']))
        #F1、F2 1个单位为32M，需转化为G
        if '' == dic['end_id_f1'] or '' == dic['start_id_f1']:
            write = 0
        else:
            write = (int(dic['end_id_f1'],16)-int(dic['start_id_f1'],16))*32//1024
        if '' == dic['end_id_f2'] or '' == dic['start_id_f2']:
            read = 0
        else:
            read = (int(dic['end_id_f2'],16)-int(dic['start_id_f2'],16))*32//1024
        newDic[key].append('%d/%d'%(write,read))
        if '' == dic['end_time'] or '' == dic['start_time']:
            newDic[key].append('')
        else:
            endtime = datetime.strptime(dic['end_time'], '%Y-%m-%d %H:%M:%S')
            starttime = datetime.strptime(dic['start_time'], '%Y-%m-%d %H:%M:%S')
            hours = timedelta.total_seconds(endtime-starttime)//(60*60)
            newDic[key].append('%dH'%hours)
        if 'porcnt' in keyLst:
            if '' == dic['end_por'] or '' == dic['start_por']:
                newDic[key].append('')
            else:
                newDic[key].append(int(dic['end_por'])-int(dic['start_por']))
        smart = ''
        #统计不为0的smart信息
        for innerKey in dic.keys():
            if innerKey.startswith('id_'):
                if '' == dic[innerKey]:
                    continue
                if 0 != int(dic[innerKey],16):
                    pos = innerKey.find('id_')
                    id = innerKey[pos+len('id_'):].upper()
                    if id in commonSmartKey:
                        smart += '%s=%s,'%(id, dic[innerKey][2:].upper())
        if '' != smart:
            smart = smart[:-1]
        newDic[key].append(smart)
        newDic[key].append(dic['average_write_vel'])
        newDic[key].append(dic['max_write_vel'])
        newDic[key].append(dic['min_write_vel'])
        newDic[key].append(dic['average_read_vel'])
        newDic[key].append(dic['max_read_vel'])
        newDic[key].append(dic['min_read_vel'])
        newDic[key].append(dic['write_overtime'])
        if '' == dic['id_a5'] or '' == dic['id_a6']:
            newDic[key].append('')
        else:
            newDic[key].append(int(dic['id_a5'],16)-int(dic['id_a6'],16))
    return newDic

def GetNewBitDic(oldDic, startPos, smartKey):
    #startPos之后的数据为smart信息，需转换为模板所需数据[F1(G)/F2(G),smart(非0),A5-A6]
    newDic = {}
    for key in oldDic:
        newDic[key] = []
        for dataLst in oldDic[key]:
            newLst = dataLst[:startPos]
            smartLst = dataLst[startPos:]
            #F1、F2 1个单位为32M，需转化为G
            write = 0
            if smartLst[0] != '':
                write = (int(smartLst[0],16)*32)//1024
            read = 0
            if smartLst[1] != '':
                read = (int(smartLst[1],16)*32)//1024
            newLst.append('%d/%d'%(write,read))
            strSmart = ''
            for idx,value in enumerate(smartLst[4:]):
                if value != '' and 0 != int(value,16):
                    strSmart += '%s=%s,'%(smartKey[4+idx], FmtStrHex(value))
            if '' != strSmart:
                strSmart = strSmart[:-1]
            newLst.append(strSmart)
            if smartLst[2] == '' or smartLst[3] == '':
                newLst.append('')
            else:
                newLst.append(int(smartLst[2],16)-int(smartLst[3],16))
            newDic[key].append(newLst)
    return newDic


#写时间信息
def WriteReportTime(worksheet,columnName,rowNo):
    #capIdx = 0
    filemt= time.localtime()  
    strTime = time.strftime('%Y-%m-%d %H:%M:%S', filemt)
    worksheet['%s%d'%(columnName, rowNo)] = strTime

#写操作者信息
def WriteReportOperator(worksheet,columnName,rowNo,operatorName = 'Skynet'):
    #capIdx = 0
    worksheet['%s%d'%(columnName, rowNo)] = operatorName

#imgColCnt图片暂用的列数
def WriteDataAndImageCommon(worksheet, startLine,imageStartLine,dataDic, colLst, keyLst, imgWidth, imgHeight,imgColCnt,imgMaxCntPerRow,imageStartCol = 1):
    imageLine = imageStartLine
    imageCol = imageStartCol
    curLine = startLine

    sampleCnt = 0 #样本数量
    for key in dataDic:      
        #最后一行数据是经过处理获取的最值
        for line in dataDic[key]:
            for index,col in enumerate(colLst):
                if 0 == index:
                    worksheet['%s%d'%(col, curLine)] = key
                else:
                    worksheet['%s%d'%(col, curLine)] = line[index-1]
                    
            curLine += 1
            sampleCnt += 1
            # 列表最后一项是图片路径
            if '' != line[-1] and defineimg(line[-1]):
                img = Image(line[-1])
                img.width = imgWidth
                img.height = imgHeight
                worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
            imageCol += imgColCnt #不管有没有图片，都应该加，无图片的结果留下空位

            if(sampleCnt%imgMaxCntPerRow) == 0:
                imageCol = imageStartCol
                imageLine += 1

def WriteImage(worksheet, picLst, imageStartLine, imgWidth, imgHeight):
    imageLine = imageStartLine
    imageCol = 1
    for pic in picLst:
        if pic != '' and defineimg(pic):
            img = Image(pic)
            img.width = imgWidth
            img.height = imgHeight
            worksheet.add_image(img, '%s%d'%(get_column_letter(imageCol), imageLine))
        imageCol += 7