Version 1.1.0 
'TEST SETUP ====================================================================
'Test Description
	
'Run Time
'	hours      minutes    seconds
	0          144        0
'Ramp Up Time (s)
	0
'Default Disk Workers to Spawn
	NUMBER_OF_CPUS
'Default Network Workers to Spawn
	0
'Record Results
	ALL
'Worker Cycling
'	start      step       step type
	1          1          LINEAR
'Disk Cycling
'	start      step       step type
	1          1          LINEAR
'Queue Depth Cycling
'	start      end        step       step type
	1          32         2          EXPONENTIAL
'Test Type
	NORMAL
'END test setup
'RESULTS DISPLAY ===============================================================
'Record Last Update Results,Update Frequency,Update Type
	DISABLED,0,WHOLE_TEST
'Bar chart 1 statistic
	Total I/Os per Second
'Bar chart 2 statistic
	Total MBs per Second (Decimal)
'Bar chart 3 statistic
	Average I/O Response Time (ms)
'Bar chart 4 statistic
	Maximum I/O Response Time (ms)
'Bar chart 5 statistic
	% CPU Utilization (total)
'Bar chart 6 statistic
	Total Error Count
'END results display
'ACCESS SPECIFICATIONS =========================================================
'Access specification name,default assignment
	4KRandom100%Read100%,NONE
'size,% of size,% reads,% random,delay,burst,align,reply
	4096,100,100,100,0,1,4096,0
'Access specification name,default assignment
	4KRandom100%Write100%,NONE
'size,% of size,% reads,% random,delay,burst,align,reply
	4096,100,0,100,0,1,4096,0
'Access specification name,default assignment
	4KRandom100%Read80%,NONE
'size,% of size,% reads,% random,delay,burst,align,reply
	4096,100,80,100,0,1,4096,0
'Access specification name,default assignment
	4KRandom100%Read50%,NONE
'size,% of size,% reads,% random,delay,burst,align,reply
	4096,100,50,100,0,1,4096,0
'Access specification name,default assignment
	4KRandom100%Read20%,NONE
'size,% of size,% reads,% random,delay,burst,align,reply
	4096,100,20,100,0,1,4096,0
'END access specifications
'MANAGER LIST ==================================================================
'Manager ID, manager name
	1,MB0823
'Manager network address
	
'Worker
	Worker 1
'Worker type
	DISK
'Default target settings for worker
'Number of outstanding IOs,test connection rate,transactions per connection,use fixed seed,fixed seed value
	32,DISABLED,1,DISABLED,0
'Disk maximum size,starting sector,Data pattern
	0,0,2
'End default target settings for worker
'Assigned access specs
	4KRandom100%Write100%
	4KRandom100%Read100%
	4KRandom100%Read80%
	4KRandom100%Read50%
	4KRandom100%Read20%
'End assigned access specs
'Target assignments
'End target assignments
'End worker
'End manager
'END manager list
Version 1.1.0 
