#pragma once

#include <stdint.h>
#include <windows.h>

namespace NVME_NORMAL_PROTOCOL_TAB_DEFINE
{
	struct nvme_id_power_state {
		uint16_t		max_power;	/* centiwatts */
		uint8_t			rsvd2;
		uint8_t			flags;
		uint32_t		entry_lat;	/* microseconds */
		uint32_t		exit_lat;	/* microseconds */
		uint8_t			read_tput;
		uint8_t			read_lat;
		uint8_t			write_tput;
		uint8_t			write_lat;
		uint16_t		idle_power;
		uint8_t			idle_scale;
		uint8_t			rsvd19;
		uint16_t		active_power;
		uint8_t			active_work_scale;
		uint8_t			rsvd23[9];
	};

	// Identify Controller data structure (upto v1.4) based on nvme.h
	typedef struct {
		//
		// byte 0 : 255, Controller Capabilities and Features
		//
		uint16_t    VID;                // byte [   1:   0] M - PCI Vendor ID (VID)
		uint16_t    SSVID;              // byte [   3:   2] M - PCI Subsystem Vendor ID (SSVID)
		uint8_t     SN[20];             // byte [  23:   4] M - Serial Number (SN)
		uint8_t     MN[40];             // byte [  63:  24] M - Model Number (MN)
		uint8_t     FR[8];              // byte [  71:  64] M - Firmware Revision (FR)
		uint8_t     RAB;                // byte [       72] M - Recommended Arbitration Burst (RAB)
		uint8_t     IEEE[3];            // byte [  75:  73] M - IEEE OUI Identifier (IEEE). Controller Vendor code.

		struct {
			uint8_t MultiPCIePorts : 1;     // bit [  0]
			uint8_t MultiControllers : 1;   // bit [  1]
			uint8_t SRIOV : 1;              // bit [  2]
			uint8_t ANAReport : 1;          // bit [  3] <rev1.4>
			uint8_t Reserved : 4;           // bit [7:4]
		} CMIC;                         // byte [       76] O - Controller Multi-Path I/O and Namespace Sharing Capabilities (CMIC)

		uint8_t     MDTS;               // byte [       77] M - Maximum Data Transfer Size (MDTS)
		uint16_t    CNTLID;             // byte [  79:  78] M - Controller ID (CNTLID)
		uint32_t    VER;                // byte [  83:  80] M - Version (VER)
		uint32_t    RTD3R;              // byte [  87:  84] M - RTD3 Resume Latency (RTD3R)
		uint32_t    RTD3E;              // byte [  91:  88] M - RTD3 Entry Latency (RTD3E)

		struct {
			uint32_t    Reserved0 : 8;                                  // bit [ 7: 0]
			uint32_t    NamespaceAttributeChanged : 1;                  // bit [    8]
			uint32_t    FirmwareActivation : 1;                         // bit [    9]
			uint32_t    Reserved1 : 1;                                  // bit [   10]
			uint32_t    ANAChange : 1;                                  // bit [   11] <rev1.4>
			uint32_t    PredictableLatencyEventAggregateLogChange : 1;  // bit [   12] <rev1.4>
			uint32_t    LBAStatusInfo : 1;                              // bit [   13] <rev1.4>
			uint32_t    EnduranceGroupEventAggregateLogChange : 1;      // bit [   14] <rev1.4>
			uint32_t    Reserved2 : 17;                                 // bit [31:15]
		} OAES;                         // byte [  95:  92] M - Optional Asynchronous Events Supported (OAES)

		struct {
			uint32_t    HostIdEn : 1;               // bit [    0]
			uint32_t    NoopPSPermissiveModeEn : 1; // bit [    1] <rev1.3>
			uint32_t    NVMSet : 1;                 // bit [    2] <rev1.4>
			uint32_t    ReadRecoveryLevel : 1;      // bit [    3] <rev1.4>
			uint32_t    EnduranceGroups : 1;        // bit [    4] <rev1.4>
			uint32_t    PredictableLatencyMode : 1; // bit [    5] <rev1.4>
			uint32_t    TrafficBasedKeepAlive : 1;  // bit [    6] <rev1.4>
			uint32_t    NamespaceGranularity : 1;   // bit [    7] <rev1.4>
			uint32_t    SQAssociation : 1;          // bit [    8] <rev1.4>
			uint32_t    UUIDList : 1;               // bit [    9] <rev1.4>
			uint32_t    Reserved : 22;              // bit [31:10]
		} CTRATT;                       // byte [  99:  96] M - Controller Attributes (CTRATT)

		uint16_t    RRLS;               // byte [ 101: 100] O - Read Recovery Levels Supported (RRLS) <rev1.4>
		uint8_t     Reserved0[9];       // byte [ 110: 102]

		uint8_t     CNTRLTYPE;          // byte [      111] M - Controller Type (CNTRLTYPE)             <rev1.4>
		uint8_t     FGUID[16];          // byte [ 127: 112] O - FRU Globally Unique Identifier (FGUID)  <rev1.3>
		uint16_t    CRDT1;              // byte [ 129: 128] O - Command Retry Delay Time 1 (CRDT1)      <rev1.4>
		uint16_t    CRDT2;              // byte [ 131: 130] O - Command Retry Delay Time 2 (CRDT2)      <rev1.4>
		uint16_t    CRDT3;              // byte [ 133: 132] O - Command Retry Delay Time 3 (CRDT3)      <rev1.4>

		uint8_t     Reserved1[106];     // byte [ 239: 134]
		uint8_t     Reserved2[16];      // byte [ 255: 240] Refer to the NVMe Management Interface Specification for definition

		//
		// byte 256 : 511, Admin Command Set Attributes
		//
		struct {
			uint16_t    SecurityCommands : 1;   // bit [    0]
			uint16_t    FormatNVM : 1;          // bit [    1]
			uint16_t    FirmwareCommands : 1;   // bit [    2]
			uint16_t    NamespaceCommands : 1;  // bit [    3]
			uint16_t    DeviceSelfTest : 1;     // bit [    4] <rev1.3>
			uint16_t    Directives : 1;         // bit [    5] <rev1.3>
			uint16_t    NVMeMICommands : 1;     // bit [    6] <rev1.3>
			uint16_t    VirtMgmtCommands : 1;   // bit [    7] <rev1.3>
			uint16_t    DBConfigCommand : 1;    // bit [    8] <rev1.3>
			uint16_t    GetLBAStatusCommand : 1;// bit [    9] <rev1.4>
			uint16_t    Reserved : 6;           // bit [15:10]
		} OACS;                         // byte [ 257: 256] M - Optional Admin Command Support (OACS)

		uint8_t ACL;                    // byte [      258] M - Abort Command Limit (ACL)
		uint8_t AERL;                   // byte [      259] M - Asynchronous Event Request Limit (AERL)

		struct {
			uint8_t Slot1ReadOnly : 1;          // bit [    0]
			uint8_t SlotCount : 3;              // bit [ 3: 1]
			uint8_t ActivationWithoutReset : 1; // bit [    4]
			uint8_t Reserved : 3;               // bit [ 7: 5]
		} FRMW;                         // byte [      260] M - Firmware Updates (FRMW)

		struct {
			uint8_t SmartPagePerNamespace : 1;  // bit [    0]
			uint8_t CommandEffectsLog : 1;      // bit [    1]
			uint8_t LogPageExtendedData : 1;    // bit [    2]
			uint8_t TelemetrySupport : 1;       // bit [    3] <rev1.3>
			uint8_t PersistentEventLog : 1;     // bit [    4] <rev1.4>
			uint8_t Reserved : 3;               // bit [ 7: 5]
		} LPA;                          // byte [      261] M - Log Page Attributes (LPA)

		uint8_t ELPE;                   // byte [      262] M - Error Log Page Entries (ELPE)
		uint8_t NPSS;                   // byte [      263] M - Number of Power States Support (NPSS)

		struct {
			uint8_t CommandFormatInSpec : 1;    // bit [    0]
			uint8_t Reserved : 7;               // bit [ 7: 1]
		} AVSCC;                        // byte [      264] M - Admin Vendor Specific Command Configuration (AVSCC)

		struct {
			uint8_t Supported : 1;  // bit [    0]
			uint8_t Reserved : 7;   // bit [ 7: 1]
		} APSTA;                        // byte [      265] O - Autonomous Power State Transition Attributes (APSTA)

		uint16_t    WCTEMP;             // byte [ 267: 266] M - Warning Composite Temperature Threshold (WCTEMP)
		uint16_t    CCTEMP;             // byte [ 269: 268] M - Critical Composite Temperature Threshold (CCTEMP)
		uint16_t    MTFA;               // byte [ 271: 270] O - Maximum Time for Firmware Activation (MTFA)
		uint32_t    HMPRE;              // byte [ 275: 272] O - Host Memory Buffer Preferred Size (HMPRE)
		uint32_t    HMMIN;              // byte [ 279: 276] O - Host Memory Buffer Minimum Size (HMMIN)
		uint8_t     TNVMCAP[16];        // byte [ 295: 280] O - Total NVM Capacity (TNVMCAP)
		uint8_t     UNVMCAP[16];        // byte [ 311: 296] O - Unallocated NVM Capacity (UNVMCAP)

		struct {
			uint32_t    RPMBUnitCount : 3;          // bit [ 2: 0] Number of RPMB Units
			uint32_t    AuthenticationMethod : 3;   // bit [ 5: 3] Authentication Method
			uint32_t    Reserved0 : 10;             // bit [15: 6]
			uint32_t    TotalSize : 8;              // bit [23:16] Total Size: in 128KB units.
			uint32_t    AccessSize : 8;             // bit [31:24] Access Size: in 512B units.
		} RPMBS;                        // byte [ 315: 312] O - Replay Protected Memory Block Support (RPMBS)

		uint16_t    EDSTT;              // byte [ 317: 316] O - Extended Device Self-test Time (EDSTT)  <rev1.3>
		uint8_t     DSTO;               // byte [      318] O - Device Self-test Options (DSTO)         <rev1.3>
		uint8_t     FWUG;               // byte [      319] M - Firmware Update Granularity (FWUG)      <rev1.3>
		uint16_t    KAS;                // byte [ 321: 320] M - Keep Alive Support (KAS)

		struct {
			uint16_t    Supported : 1;  // bit [    0]
			uint16_t    Reserved : 15;  // bit [15: 0]
		} HCTMA;                        // byte [ 323: 322] O - Host Controlled Thermal Management Attributes (HCTMA)  <rev1.3>

		uint16_t    MNTMT;              // byte [ 325: 324] O - Minimum Thermal Management Temperature (MNTMT) <rev1.3>
		uint16_t    MXTMT;              // byte [ 327: 326] O - Maximum Thermal Management Temperature (MXTMT) <rev1.3>

		struct {
			uint32_t    CryptoErase : 1;            // bit [    0] Controller supports Crypto Erase Sanitize
			uint32_t    BlockErase : 1;             // bit [    1] Controller supports Block Erase Sanitize
			uint32_t    Overwrite : 1;              // bit [    2] Controller supports Overwrite Santize
			uint32_t    Reserved : 26;              // bit [28: 3]
			uint32_t    NoDeallocateInhibited : 1;  // bit [   29] No-Deallocate Inhibited (NDI)                            <rev1.4>
			uint32_t    NODMMAS : 2;                // bit [31:30] No-Deallocate Modifies Media After Sanitize (NODMMAS)    <rev1.4>
		} SANICAP;                      // byte [ 331: 328] O - Sanitize Capabilities (SANICAP)                             <rev1.3>

		uint32_t    HMMINDS;            // byte [ 335: 332] O - Host Memory Buffer Minimum Descriptor Entry Size (HMMINDS)  <rev1.4>
		uint16_t    HMMAXD;             // byte [ 337: 336] O - Host Memory Maximum Descriptors Entries (HMMAXD)            <rev1.4>
		uint16_t    NSETIDMAX;          // byte [ 339: 338] O - NVM Set Identifier Maximum (NSETIDMAX)                      <rev1.4>
		uint16_t    ENDGIDMAX;          // byte [ 341: 340] O - Endurance Group Identifier Maximum (ENDGIDMAX)              <rev1.4>
		uint8_t     ANATT;              // byte [      342] O - ANA Transition Time (ANATT)                                 <rev1.4>

		struct {
			uint8_t     ReportOptimizedState : 1;       // bit [    0] able to report ANA Optimized state
			uint8_t     ReportNonOptimizedState : 1;    // bit [    1] able to report ANA Non-Optimized state
			uint8_t     ReportInaccessibleState : 1;    // bit [    2] able to report ANA Inaccessible state
			uint8_t     ReportPersistentLossState : 1;  // bit [    3] able to report ANA Persistent Loss state
			uint8_t     ReportChangeState : 1;          // bit [    4] able to report ANA Change state
			uint8_t     Reserved0 : 1;                  // bit [    5]
			uint8_t     NoGRPIDChangeDuringAttached : 1;// bit [    6]
			uint8_t     NonZeroGRPIDSupport : 1;        // bit [    7]
		} ANACAP;                       // byte [      343] O - Asymmetric Namespace Access Capabilities (ANACAP) <rev1.4>

		uint32_t    ANAGRPMAX;          // byte [ 347: 344] O - ANA Group Identifier Maximum (ANAGRPMAX)    <rev1.4>
		uint32_t    NANAGRPID;          // byte [ 351: 348] O - Number of ANA Group Identifiers (NANAGRPID) <rev1.4>
		uint32_t    PELS;               // byte [ 355: 352] O - Persistent Event Log Size (PELS)            <rev1.4>

		uint8_t     Reserved3[156];     // byte [ 511: 356]

		//
		// byte 512 : 703, NVM Command Set Attributes
		//
		struct {
			uint8_t RequiredEntrySize : 4;  // bit [ 3: 0] The value is in bytes and is reported as a power of two (2^n).
			uint8_t MaxEntrySize : 4;       // bit [ 7: 4] This value is larger than or equal to the required SQ entry size.  The value is in bytes and is reported as a power of two (2^n).
		} SQES;                         // byte [      512] M - Submission Queue Entry Size (SQES)

		struct {
			uint8_t RequiredEntrySize : 4;  // bit [ 3: 0] The value is in bytes and is reported as a power of two (2^n).
			uint8_t MaxEntrySize : 4;       // bit [ 7: 4] This value is larger than or equal to the required CQ entry size. The value is in bytes and is reported as a power of two (2^n).
		} CQES;                         // byte [      513] M - Completion Queue Entry Size (CQES)

		uint16_t    MAXCMD;             // byte [ 515: 514] M - Maximum Outstanding Commands (MAXCMD)
		uint32_t    NN;                 // byte [ 519: 516] M - Number of Namespaces (NN)

		struct {
			uint16_t    Compare : 1;            // bit [    0]
			uint16_t    WriteUncorrectable : 1; // bit [    1]
			uint16_t    DatasetManagement : 1;  // bit [    2]
			uint16_t    WriteZeroes : 1;        // bit [    3]
			uint16_t    FeatureField : 1;       // bit [    4]
			uint16_t    Reservations : 1;       // bit [    5]
			uint16_t    Timestamp : 1;          // bit [    6] <rev1.3>
			uint16_t    Verify : 1;             // bit [    7] <rev1.4>
			uint16_t    Reserved : 8;           // bit [15: 8]
		} ONCS;                         // byte [ 521: 520] M - Optional NVM Command Support (ONCS)

		struct {
			uint16_t    CompareAndWrite : 1;    // bit [    0]
			uint16_t    Reserved : 15;          // bit [15: 1]
		} FUSES;                        // byte [ 523: 522] M - Fused Operation Support (FUSES)

		struct {
			uint8_t     FormatApplyToAll : 1;           // bit [    0]
			uint8_t     SecureEraseApplyToAll : 1;      // bit [    1]
			uint8_t     CryptographicEraseSupported : 1;// bit [    2]
			uint8_t     Reserved : 5;                   // bit [ 7: 3]
		} FNA;                          // byte [      524] M - Format NVM Attributes (FNA)

		struct {
			uint8_t     Present : 1;        // bit [    0]
			uint8_t     FlushBehavior : 2;  // bit [ 2: 1] <rev1.4>
			uint8_t     Reserved : 5;       // bit [ 7: 3]
		} VWC;                          // byte [      525] M - Volatile Write Cache (VWC)

		uint16_t    AWUN;               // byte [ 527: 526] M - Atomic Write Unit Normal (AWUN)
		uint16_t    AWUPF;              // byte [ 529: 528] M - Atomic Write Unit Power Fail (AWUPF)

		struct {
			uint8_t CommandFormatInSpec : 1;    // bit [    0]
			uint8_t Reserved : 7;               // bit [ 7: 0]
		} NVSCC;                        // byte [      530] M - NVM Vendor Specific Command Configuration (NVSCC)

		struct {
			uint8_t NoWriteProtectSupport : 1;              // bit [    0]
			uint8_t WriteProtectUntilPowerCycleSupport : 1; // bit [    1]
			uint8_t PermanentWriteProtectSupport : 1;       // bit [    2]
			uint8_t Reserved : 5;                           // bit [ 7: 3]
		} NWPC;                         // byte [      531] M - Namespace Write Protection Capabilities (NWPC) <rev1.4>

		uint16_t    ACWU;               // byte [ 533: 532] O - Atomic Compare & Write Unit (ACWU)

		uint8_t Reserved5[2];           // byte [ 535: 534]

		struct {
			uint32_t    SGLSupported : 2;                           // bit [ 1: 0] <rev1.3>
			uint32_t    KeyedBBDescSupported : 1;                   // bit [    2]
			uint32_t    Reserved0 : 13;                             // bit [15: 3]
			uint32_t    BitBucketDescrSupported : 1;                // bit [   16]
			uint32_t    ByteAlignedContiguousPhysicalBuffer : 1;    // bit [   17]
			uint32_t    SGLLengthLargerThanDataLength : 1;          // bit [   18]
			uint32_t    MPTRContainingSGLDescSupported : 1;         // bit [   19]
			uint32_t    OffsetByAddrFieldSupported : 1;             // bit [   20]
			uint32_t    TransactionalSGLDataBlockDescSupported : 1; // bit [   21] <rev1.4>
			uint32_t    Reserved1 : 10;                         // bit [31:22]
		} SGLS;                         // byte [ 539: 536] O - SGL Support (SGLS)

		uint32_t    MNAN;               // byte [      540] O - Maximum Number of Allowed Namespaces (MNAN) <rev1.4>

		uint8_t Reserved6[224];         // byte [ 767: 544]

		uint8_t SUBNQN[256];            // byte [1023: 768] M - NVM Subsystem NVMe Qualified Name (SUBNQN)

		uint8_t Reserved7[768];         // byte [1791:1024]
		uint8_t Reserved8[256];         // byte [2047:1792] Refer to the NVMe over Fabrics specification

		nvme_id_power_state   PDS[32];// byte [2079:2048] M - Power State 0 Descriptor (PSD0)
		// byte [3071:2080] O - Power State Descriptors from PS1 (PSD1) to PS31 (PSD31) 

		uint8_t VS[1024];              // byte [4095:3072] Vendor Specific
	} NVME_IDENTIFY_CONTROLLER_DATA14, * PNVME_IDENTIFY_CONTROLLER_DATA14;

	typedef struct  
	{
		uint64_t LBAF0:4;
		uint64_t LBAFOthen:60; //LBA1~LBA15;
	}NVME_LBA_FORMAT;

	typedef struct
	{
		uint64_t    NSZE;                   // byte [   7:   0] M - Namespace Size (NSZE)
		uint64_t    NCAP;                   // byte [  15:   8] M - Namespace Capacity (NCAP)
		uint64_t    NUSE;                   // byte [  23:  16] M - Namespace Utilization (NUSE)

		struct {
			uint8_t ThinProvisioning : 1;           // bit [    0]
			uint8_t NameSpaceAtomicWriteUnit : 1;   // bit [    1]
			uint8_t DeallocatedOrUnwrittenError : 1;// bit [    2]
			uint8_t SkipReuseUI : 1;                // bit [    3]
			uint8_t OptPerf : 1;                    // bit [    4] <rev1.4>
			uint8_t Reserved : 3;
		} NSFEAT;                           // byte [       24] M - Namespace Features (NSFEAT)

		uint8_t   NLBAF;                    // byte [       25] M - Number of LBA Formats (NLBAF)

		struct {
			uint8_t   LbaFormatIndex : 4;           // bit [ 3: 0]
			uint8_t   MetadataInExtendedDataLBA : 1;// bit [    4]
			uint8_t   Reserved : 3;                 // bit [ 7: 5]
		} FLBAS;                            // byte [       26] M - Formatted LBA Size (FLBAS)

		struct {
			uint8_t   MetadataInExtendedDataLBA : 1;// bit [    0]
			uint8_t   MetadataInSeparateBuffer : 1; // bit [    1]
			uint8_t   Reserved : 6;                 // bit [ 7: 2]
		} MC;                               // byte [       27] M - Metadata Capabilities (MC)

		struct {
			uint8_t   ProtectionInfoType1 : 1;      // bit [    0]
			uint8_t   ProtectionInfoType2 : 1;      // bit [    1]
			uint8_t   ProtectionInfoType3 : 1;      // bit [    2]
			uint8_t   InfoAtBeginningOfMetadata : 1;// bit [    3]
			uint8_t   InfoAtEndOfMetadata : 1;      // bit [    4]
			uint8_t   Reserved : 3;                 // bit [ 7: 5]
		} DPC;                              // byte [       28] M - End-to-end Data Protection Capabilities (DPC)

		struct {
			uint8_t   ProtectionInfoTypeEnabled : 3;// bit [ 2: 0] 0 - not enabled; 1 ~ 3: enabled type; 4 ~ 7: reserved
			uint8_t   InfoAtBeginningOfMetadata : 1;// bit [    3]
			uint8_t   Reserved : 4;                 // bit [ 7: 4]
		} DPS;                              // byte [       29] M - End-to-end Data Protection Type Settings (DPS)

		struct {
			uint8_t   SharedNameSpace : 1;  // bit [    0]
			uint8_t   Reserved : 7;         // bit [ 7: 1]
		} NMIC;                             // byte [       30] O - Namespace Multi-path I/O and Namespace Sharing Capabilities (NMIC)

		union {
			struct {
				uint8_t   PersistThroughPowerLoss : 1;                  // bit [    0]
				uint8_t   WriteExclusiveReservation : 1;                // bit [    1]
				uint8_t   ExclusiveAccessReservation : 1;               // bit [    2]
				uint8_t   WriteExclusiveRegistrantsOnlyReservation : 1; // bit [    3]
				uint8_t   ExclusiveAccessRegistrantsOnlyReservation : 1;// bit [    4]
				uint8_t   WriteExclusiveAllRegistrantsReservation : 1;  // bit [    5]
				uint8_t   ExclusiveAccessAllRegistrantsReservation : 1; // bit [    6]
				uint8_t   IgnoreExistingKey : 1;                        // bit [    7]
			} DUMMYSTRUCTNAME;

			UCHAR AsUchar;
		} RESCAP;                           // byte [       31] O - Reservation Capabilities (RESCAP)

		struct {
			uint8_t   PercentageRemained : 7;   // bit [ 6: 0]
			uint8_t   Supported : 1;            // bit [    7]
		} FPI;                              // byte [       32] O - Format Progress Indicator (FPI)

		struct {
			uint8_t   ReadBehavior : 3;         // bit [ 2: 0]
			uint8_t   DeallocateInWriteZero : 1;// bit [    3]
			uint8_t   IsGuardFieldCRC : 1;      // bit [    4]
			uint8_t   Reserved : 3;             // bit [ 7: 5]
		} DLFEAT;                           // byte [       33] O - Deallocate Logical Block Features (DLFEAT)

		uint16_t    NAWUN;                  // byte [  35:  34] O - Namespace Atomic Write Unit Normal (NAWUN)
		uint16_t    NAWUPF;                 // byte [  37:  36] O - Namespace Atomic Write Unit Power Fail (NAWUPF)
		uint16_t    NACWU;                  // byte [  39:  38] O - Namespace Atomic Compare & Write Unit (NACWU)
		uint16_t    NABSN;                  // byte [  41:  40] O - Namespace Atomic Boundary Size Normal (NABSN)
		uint16_t    NABO;                   // byte [  43:  42] O - Namespace Atomic Boundary Offset (NABO)
		uint16_t    NABSPF;                 // byte [  45:  44] O - Namespace Atomic Boundary Size Power Fail (NABSPF)
		uint16_t    NOIOB;                  // byte [  47:  46] O - Namespace Optimal IO Boundary (NOIOB)
		uint8_t     NVMCAP[16];             // byte [  63:  48] O - NVM Capacity (NVMCAP)
		uint16_t    NPWG;                   // byte [  65:  64] O - Namespace Preferred Write Granularity (NPWG) <rev1.4>
		uint16_t    NPWA;                   // byte [  67:  66] O - Namespace Preferred Write Alignment (NPWA) <rev1.4>
		uint16_t    NPDG;                   // byte [  69:  68] O - Namespace Preferred Deallocate Granularity (NPDG) <rev1.4>
		uint16_t    NPDA;                   // byte [  71:  70] O - Namespace Preferred Deallocate Alignment (NPDA) <rev1.4>
		uint16_t    NOWS;                   // byte [  73:  72] O - Namespace Optimal Write Size (NOWS) <rev1.4>

		uint8_t     Reserved2[18];          // byte [  91:  74]

		uint32_t    ANAGRPID;               // byte [  65:  92] O - ANA Group Identifier (ANAGRPID) <rev1.4>

		uint8_t     Reserved3[3];           // byte [  98:  96]

		struct {
			uint8_t   WriteProtect : 1;         // bit [    0]
			uint8_t   Reserved : 7;             // bit [ 7: 1]
		} NSATTR;                           // byte [       99] O - Namespace Attributes (NSATTR) <rev1.4>

		uint16_t    NVMSETID;               // byte [ 101: 100] O - NVM Set Identifier (NVMSETID) <rev1.4>
		uint16_t    ENDGID;                 // byte [ 103: 102] O - Endurance Group Identifier (ENDGID) <rev1.4>

		uint8_t     NGUID[16];              // byte [ 119: 104] O - NAmespace Globally Unique Identifier (NGUID)
		uint8_t     EUI64[8];               // byte [ 127: 120] M - IEEE Extended Unique Identifier (EUI64)

		NVME_LBA_FORMAT LBAF[16];           // byte [ 131: 128] M - LBA Format 0 Support (LBAF0)
		// byte [ 191: 132] O - LBA Format Support; 1 (LBAF1) to 15 (LBAF15)

		uint8_t     Reserved4[192];         // byte [ 383: 192]

		uint8_t     VS[3712];               // byte [4095: 384] O - Vendor Specific (VS)
	} NVME_IDENTIFY_NAMESPACE_DATA14, * PNVME_IDENTIFY_NAMESPACE_DATA14;

	/**
	 * @brief Get Log Page - SMART/Health Information Log
	*/
	typedef struct _log_page_smart_health {
		/**
		 * @brief [00] Critical warning
		 */
		uint32_t criticalWarn:8;
		/**
		 * @brief [02:01]
		 */
		uint32_t temperature:16;
		/**
		 * @brief [03] Available spare
		 */
		uint32_t availableSpare:8;
		/**
		 * @brief [04] Available spare threshold
		 */
		uint8_t availableSpareThrd;
		/**
		 * @brief [05] Percentage used
		 */
		uint8_t pctUsed;

		/**
		 * @brief [31:06] Reserved
		 */
		uint8_t rsvd31_06[26];

		/**
		 * @brief [47:32] Data units reads
		 */
		uint64_t dataUnitReadLow;
		uint64_t dataUnitReadHigh;
		/**
		 * @brief [63:48] Data units Written
		 */
		uint64_t dataUnitWriteLow;
		uint64_t dataUnitWriteHigh;

		/**
		 * @brief [79:64] Host Read Commands
		 */
		uint64_t hostReadCmdLow;
		uint64_t hostReadCmdHigh;
		/**
		 * @brief [95:80] Host write commands
		 */
		uint64_t hostWriteCmdLow;
		uint64_t hostWriteCmdHigh;

		/**
		 * @brief [111:96] Controller busy time
		 */
		uint64_t ctrlBusyTimeLow;
		uint64_t ctrlBusyTimeHigh;
		/**
		 * @brief [127:112] Power cycles
		 */
		uint64_t pwrCyclLow;
		uint64_t pwrCyclHigh;
		/**
		 * @brief [143:128] Power on hours
		 */
		uint64_t pwrOnHourLow;
		uint64_t pwrOnHourHigh;

		/**
		 * @brief [159:144] Unsafe shutdowns
		 */
		uint64_t unsaveShutdownLow;
		uint64_t unsaveShutdownHigh;
		/**
		 * @brief [175:160] Media Errors
		 */
		uint64_t mediaErrLow;
		uint64_t mediaErrHigh;
		/**
		 * @brief [191:176] Number of error information log entries
		 */
		uint64_t numOfErrLogLow;
		uint64_t numOfErrLogHigh;
		/**
		 * @brief [195:192] Warning Composite Temperature Time (mins)
		 */
		uint32_t warningCompisiteTempTime;
		/**
		 * @brief [199:196] Critical Composite Temperature Time
		 */
		uint32_t criticalCompisiteTempTime;
		/**
		 * @brief [201:200] Temperature Sensor 1
		 */
		uint16_t tempSensor1;
		/**
		 * @brief [203:202] Temperature Sensor 2
		 */
		uint16_t tempSensor2;
		/**
		 * @brief [205:204] Temperature Sensor 3
		 */
		uint16_t tempSensor3;
		/**
		 * @brief [207:206] Temperature Sensor 4
		 */
		uint16_t tempSensor4;
		/**
		 * @brief [209:208] Temperature Sensor 5
		 */
		uint16_t tempSensor5;
		/**
		 * @brief [211:210] Temperature Sensor 6
		 */
		uint16_t tempSensor6;
		/**
		 * @brief [213:212] Temperature Sensor 7
		 */
		uint16_t tempSensor7;
		/**
		 * @brief [215:214] Temperature Sensor 8
		 */
		uint16_t tempSensor8;

		/**
		 * @brief [511:216] Reserved
		 */
		uint8_t reserved[512 - 216];

	} log_page_smart_health_t;
}

