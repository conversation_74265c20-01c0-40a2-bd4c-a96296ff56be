#pragma once
class IISQLOperate;
#include <StatusUpload/IStatusUpload.h>
#include <afxmt.h>

class CUpLoadSleepReboot
{
public:
	CUpLoadSleepReboot(void);
	virtual ~CUpLoadSleepReboot(void);

	bool UpdateSleepReboot(SLEEP_REEBOOT_STATUS* _pStatus);

	void ReleaseResource();

protected:
	bool TryConnect();
private:
	IISQLOperate *m_pSqlOperator;

	std::string m_strErrInfo;
	bool m_bConnected;
	CCriticalSection m_csLock;
};
