﻿; <PERSON><PERSON>vich's Delphi Localizer Language File.
; Generated by <PERSON>.<PERSON><PERSON><PERSON><PERSON>, 12.10.2019 5:02:05
; Version 1.0 (Victoria 5.37. 14 Oct 2021)  

[TAAMForm]
Caption='Acoustic && Power (AAM / APM)'
Font.Name='Tahoma'
SeekGrp.Caption=' MECHANIC  &&  ACOUSTIC TEST'
SeekGrp.Font.Name='Tahoma'
Label5.Caption='Limit, ms:'
Label5.Font.Name='Tahoma'
SeekTimeLed.Hint='Average access time'
SeekCntOut.Hint='Seeks counter'
SeekCntOut.Font.Name='Tahoma'
SeekStartLbaEdit.Hint='Start LBA'
SeekEndLbaEdit.Hint='End LBA'
Button2.Hint='Reset Start LBA to zero'
Button4.Hint='Reset End LBA to maximum LBA'
Button4.Caption='MAX'
AAMLed.Hint='AAM value (HDD acoustic noise level)'
AAMTrack.Hint='AAM level fine set'
AAMMinBtn.Hint='AAM Minimum (HDD Slow mode)'
AAMOffBtn.Hint='AAM OFF (Maximum acoustic level, Maximum perfomance)'
SeekBtn.Hint='Start mechanic test and AAM control'
SeekBtn.Caption='Start'
STioEdit.Hint='The respond time (ms) of HDD, above which the process will be stopped.'
SeekTmprtLed.Hint='Drive temperature'
TermoCtrlBox.Hint='Enable Thermal control'
TermoCtrlBox.Caption='t°'
SeekModeGrp.Hint='Access method'
SeekModeGrp.Items.Strings='Seek','Verify','Read'
SeekLbaOut.Hint='Current LBA'
RzPanel1.Caption='ADVANCED POWER MANAGEMENT'
RzPanel1.Font.Name='Tahoma'
ApmTrack.Hint='APM Fine tuning'
ApmOffBtn.Hint='APM OFF. Maximum performance.'
ApmLed.Hint='APM Level'
WDIdlePanel.Caption='WD IDLE TIMER'
DisWdTimerBtn.Caption='Disable'
SetWdTimerBtn.Caption='Set timer'
ReadWdTimBtn.Caption='Read timer'
WdIdleTrack.Hint='WD Idle Timer fine setting'
RestartWDBtn.Caption='Restart WD'
RestartWDBtn.Hint='Restart WD drive firmware to apply timer changes without reboot. On the bootable HDD and some systems may cause a failure.'

[TDCOForm]
Caption='Device Configuration Overlay'

[TDCOFrame]
DcoPanel.Font.Name='Tahoma'
Label14.Caption='Version:'
DcoBit5Chk.Caption='Read/Write DMA Queued'
DcoBit4Chk.Caption='Power-up in Standby'
DcoChkSecu.Caption='Security'
DcoBit2Chk.Caption='SMART error log'
DcoBit1Chk.Caption='SMART self-test'
DcoChkSMART.Caption='SMART'
DcoBit9Chk.Caption='Streaming feature set'
DCoAppBtn.Caption='Apply'
RzBitBtn27.Caption='Restore'
RzBitBtn28.Caption='Identify'
DcoBit11Chk.Caption='Forced Unit Access'
DcoBit12Chk.Caption='SMART Selective selftest'
DcoBit13Chk.Caption='SMART Conveyance selftest'
DcoBit14Chk.Caption='Write-Read-Verify'
DcoNvCacheChk.Caption='Support for NV Cache'
DcoNvCachePmChk.Hint='Reporting Support for NV Cache Power Management feature set is allowed'
DcoNvCachePmChk.Caption='Support for NV Cache PM'
DcoWRUNChk.Caption='Write Uncorrectable'
DcoTChk.Caption='Trusted Computing'
DcoFreezeBtn.Caption='Freeze'
DcoSataB7.Caption='Bit 7'
DcoSataB6.Caption='Bit 6'
DcoSataB5.Caption='Bit 5'
DcoSataB3.Caption='Bit 3'
DcoSataB2.Caption='Bit 2'
DcoSataB4.Caption='Bit 4'
DcoSataB0.Caption='NCQ'
DcoSataB1.Caption='Bit 1'
DcoRightPanel.Caption='  INSTRUCTIONS'


[TFeaForm]
Caption='Drive Features'
CacheBox.Caption=' CACHE'
CacheBox.Font.Name='Tahoma'
RzBitBtn40.Caption='Write cache OFF'
RzBitBtn41.Caption='Write cache ON'
CacheOFFbtn.Caption='Read cache OFF'
RdCacheBtn.Caption='Read cache ON'


[TFm]
Font.Name='Tahoma'
StandBtn.Hint='Drive selection. Information about the drive (passport)'
SmartPageBtn.Hint='Drive self-monitoring, analysis and reporting technology attributes'
BreakBtn.Hint='Stop all processes'
ReportBtn.Hint='Create Report'
SMLogPageBtn.Hint='Drive Self-Test Logs'
MainPauseBtn.Hint='Pause current test'
TestPageBtn.Hint='Drive Testing and Repair'
AdvPageBtn.Hint='Work with drive contents'
DriLab.Caption='Drive Info'
SmLab.Caption='S.M.A.R.T'
SmLogLab.Caption='SMART Logs'
DiskeditLab.Caption='Disk Editor'
TestRLab.Caption='Test && Repair'
ReportLab.Caption='Report'
PausLab.Caption=' Pause '
Brklab.Caption='Break All'
SSDLed.Hint='HDD spindle rotate speed indicator'
SataDevLed.Hint='SATA drive indication'
RemDrvLed.Hint='Indication of the form-factor or removable device'
VirtDrvLed.Hint='Virtual disk indication'
PspplusRstChk.Hint='Reset + ECh'
PspplusRstChk.Caption='reset+'
PASSBtn.Hint='Get drive passport (ECh command only)'
PASSBtn.Caption='Passport'
PASSBtnExt.Hint='Get extended drive information:'#13'- Passport'#13'- Real size'#13'- Power status'#13'- Interface && buffer diagn.'#13'(Details, see the "Settings")'
PASSBtnExt.Caption='EXT'
PspPanel.Psp.Hint='Press ''Passport'' button to view drive info'
ApiList.Columns.(2).Caption='Size'
ApiList.Columns.(3).Caption=' Note / click to refresh'
ApiList.Columns.(4).Caption='Letters'
PciScanPanel.Caption='  CONTROLLER'
PciScanPanel.Font.Name='Tahoma'
PCIScanBtn.Hint='PCI bus scan, search additional ATA/SATA controllers'
PCIScanBtn.Caption='PCI Scan'
PCIAllDevChk.Hint='Search all PCI/AGP devices'
PCIAllDevChk.Caption='All'
MFDevChk.Hint='Multi-function devices search (recommended)'
MFDevChk.Caption='M/F'
PortPanel.Caption='  PORT'
BasePortEdit.Hint='Base port address'
AltPortEdit.Hint='Alternative port address'
PortBox.Hint='Select Port'
slavechk.Hint='Select SLAVE IDE device'
slavechk.Caption='slave'
aschk.Hint='Use aternate port for status-control'
aschk.Caption='alt-st'
ATA7Panel.Caption='DRIVE SUPPORTED FEATURES:'
AAMInd.Caption='Automatic Acoustic Management'
SelfTestInd.Caption='S.M.A.R.T. Self Test'
ErrLogInd.Caption='S.M.A.R.T. Error Log'
HpaInd.Caption='Host Protected Area (HPA)'
ApmInd.Caption='Advanced Power Management'
SecuInd.Caption='Security options'
EncrInd.Caption='Device Encrypts All User Data'
FreeFailInd.Caption='Free-Fall Control'
PECInd.Caption='Phy Event Counters Log'
NcqInd.Caption='Native Command Queuing'
TMaxLab.Caption='LIFE MAXIMUM'
TMinLab.Caption='LIFE MINIMUM'
TCLab.Caption='TEMPERATURE'
SctTemp.Hint='Current Drive Temperature'
SctMaxt.Hint=''
SctMinT.Hint=''
SmartList.Hint='Double-click on a line - to view the changes of the SMART attribute'
SmartList.Columns.(1).Caption='ID  '
SmartList.Columns.(2).Caption='        Name'
SmartList.Columns.(3).Caption='Value'
SmartList.Columns.(4).Caption='Worst '
SmartList.Columns.(5).Caption='Treshold'
SmartList.Columns.(6).Caption='RAW value       '
SmartList.Columns.(7).Caption='Health'
SmrtStLabel.Hint='Health state reported by drive'
SmartStatLabel.Caption='Status:'
SmartBtn.Caption='Get S.M.A.R.T.'
SmartBtn.Hint='Rudiment Button. It is completely replaced by a click on the table header or an automation action'
HexRawChk.Caption='HEX raw values'
IBMSmartChk.Hint='If your drive is an IBM or HGST family drive, this checkbox activates the extended S.M.A.R.T. attributes'
IBMSmartChk.Caption='IBM super smart:'
ErrorLog.Columns.(1).Caption='Parameter'
ErrorLog.Columns.(2).Caption='Content'
ErrorLog.Columns.(3).Caption='Note'
ErrorLog.Font.Name='Arial'
SmartLog.Columns.(1).Caption='ID '
SmartLog.Columns.(2).Caption='        Name'
SmartLog.Columns.(3).Caption='Length'
RzBitBtn5.Hint='Reread log from drive'
RzBitBtn5.Caption='Refresh'
AutoReadLogChk.Hint='Automatically receive log by clicking on its name'
AutoReadLogChk.Caption='Auto read log by click'
GetLogDirBtn.Hint='Read SMART logs directory'
GetLogDirBtn.Caption='Get directory'
SmIgnCrcChk.Caption='Ignore CS'
SmIgnCrcChk.Hint='Allow display of log contents with incorrect checksums'
OldDriveLogChk.Hint='Read log with 28-bit commands - for old HDDs without LBA48 support'
OldDriveLogChk.Caption='Old drive'
grfwrchk.Hint='Turn on / off Write graph'
grfwrchk.Caption='Write'
grfrdchk.Hint='Turn on / off Read graph'
grfrdchk.Caption='Read'
Reclam2.Hint='Click to more information...'
ScriptMeter.Hint='Script progress bar / defect list progress bar'
Label60.Caption='[End LBA]'
Label60.Font.Name='Tahoma'
Label61.Caption='[Start LBA]'
Label61.Font.Name='Tahoma'
Label71.Caption='[ End time ]'
Label71.Font.Name='Tahoma'
Label58.Caption='[ timeout,ms ]'
Label58.Font.Name='Tahoma'
Label59.Caption='[ block size ]'
Label59.Font.Name='Tahoma'
MaxLbaButton.Hint='Reset End LBA to maximum'
MaxLbaButton.Caption='MAX'
ScanClZeroBtn.Hint='Reset  start LBA to zero'
Qn.Hint='Jump during surface scan'
RstAftJmpChk.Hint='Send the Soft Reset command to drive before jump (works only in PIO)'
RndScTimeEdit.Hint='Random test time (1min...23h:59m)'
StartLbaEdit.Hint='Start LBA'
EndLbaEdit.Hint='End LBA'
LbaOut.Hint='Current LBA'
ScBlockSizeBox.Hint='The size of the read / write block. One cell = one block. It is recommended set automatic selection.'
ScanTimeEdit.Hint='Block access time, after which the block will be declared defective'
LbaOut1.Hint='Current LBA-2 for butterfly test'
ScanStartBtn.Hint='Begin/Stop surface scan'
AutoSizeSC.Hint='Auto block size'
AutoSizeSC.Caption='[ auto ]'
QscanStartBtn.Hint='Start QUICK surface scan'
ScanCurBtn.Hint='Copy current LBA to Start LBA'
ScanCurBtn.Caption='CUR'                                                                
EndScanVarBox.Items.Strings='End of test','Stop spindle','Loop test','HD power off','Shutdown PC','Save screenshot','3 SS+Shutdown','3 Screenshots'
EndScanVarBox.Hint='Select an action after scanning is completed'
ScanCurEndBtn.Hint='Copy current LBA to End LBA'
ScanCurEndBtn.Caption='CUR'
ScanClrListBtn.Hint='Clear counters to zero'
ScTimeLed.Hint='Remain time (Click -> Start time)'
DftMeter.Hint='Block progress bar'
ScanRWGrp.Items.Strings='Verify','Read','Write'
ScanRWGrp.Hint='Drive access method (Warning! Writing erases data)'
ScDftChk.Hint='Analysis sectors in the block, search BAD'
ScDftChk.Caption='defectoscope'
ScRemapGroup.Hint='Defect detection action'
ScanDefPanel.Hint='Statistics of access time (milliseconds) to the blocks of the drive'
ScRemapGroup.Items.Strings='Ignore','Remap','Erase','Refresh'
ScanDirectionPanel.Hint='Scan direction'
ImgFWD.Hint='Linearly forward'
ImgREV.Hint='Linearly reverse'
ImgRND.Hint='Random address'
ImgBTF.Hint='Butterfly scan'
ScanMbLed.Hint='Current position'
EnLwChk.Caption='Long writing'
SpeedLed.Hint='Speed of scan'
ScNumLbaChk.Hint='Write LBA numbers to disk'
ScNumLbaChk.Caption='wr LBA num'
DDDChk.Hint='Data Distortion Detect System (c)'
DDDChk.Caption='DDD (API)'
TioBad.Hint='Log sector as BAD, if timeout'
TioBad.Caption='TIO-BAD'
DisGridChk.Hint='Switching Map / Graph Modes'
DisGridChk.Caption='Grid'
ScanDefList.Columns.(1).Caption='Start LBA'
ScanDefList.Columns.(2).Caption='Block'
ScanDefList.Columns.(3).Caption='Comment'
PartBox.Hint='Attempt to interpret MBR format partition table'
PartBox.Caption=' PARTITION VIEWER'
PartBtn.Caption='Get a passport first!'
PartAnalPassChk.Hint='Use the passport volume of drive to identify free space outside partitions'
PartAnalPassChk.Caption='Analyze passport'
Use2CopyChk.Hint='Use a second copy of the GPT-table instead of the main one'
Use2CopyChk.Caption='Use copy of GPT'
MbrLed.Hint='55AAh signature indicator on MBR'
MbrOnBtn.Hint='Write 55AAh signature to MBR'
MbrOnBtn.Caption='MBR ON'
MbrOffBtn.Hint='Change signature 55AAh to CC88h on MBR'
MbrOffBtn.Caption='MBR OFF'
HexNavigation.Caption='Press ''Open'''
HexEOppenBtn.Caption='Open'
HexEOppenBtn.Hint='Set editor to beginning of physical disk'
HexESaveBtn.Caption='Save'
HexESaveBtn.Hint='Write modified data to physical disk'
AdvSyncHexPV.Caption='Synchronize with partition viewer'
AdvSyncHexPV.Hint='If select section in the table - set editor to its beginning'
HexTransLab.Caption='Translation:'
HexTKBox.Hint='Switching character encoding in HEX editor'
PartList.Columns.(1).Caption='N'
PartList.Columns.(2).Caption='Boot'
PartList.Columns.(3).Caption='System'
PartList.Columns.(4).Caption='Start LBA  '
PartList.Columns.(5).Caption='End LBA   '
PartList.Columns.(6).Caption='Size     '
PartList.Columns.(7).Caption='Name'
HexE.Font.Name='Courier New'
BreakLed.Hint='The LED indicates the cancellation of all processes'
PasspBtn.Hint='Alternative PASSPORT button'
PasspBtn.Caption='Passp'
RecBtn.Hint='Recallibration command'
RecBtn.Caption='Re&call'
APIRg.Items.Strings='API','PIO'
StopBtn.Hint='Stop HDD spindle / SSD sleep'
StopBtn.Caption='&Sleep'
ResetBtn.Hint='Soft RESET'
ResetBtn.Caption='&Reset'
WrIndLab.Hint='Indicator of write operations'
RdIndLab.Hint='Indicator of read operations'
AltSndChk.Hint='Sound ON/OFF'
AltSndChk.Caption='Sound'
HintChk.Caption='Hints'
HintChk.Hint='Show hints'
IgnDRDYChk.Hint='Ignore Drive Ready bit'
IgnDRDYChk.Caption='DRDY'
biglbaChk.Hint='Enable 48-bit operations (ATA only)'
biglbaChk.Caption='48 bit'
Button1.Caption='wipe'
Button1.Font.Name='Tahoma'
PasStr.Items.Strings='1234','Get drive passport','Drive error','Port error','When changing the disc - writing was switched to reading!','WARNING! THIS DRIVE DOES NOT SUPPORT LBA !!!','Drive ATA passport','Passport USB','SCSI  Drive passport','Removable','Virtual','No media','Fixed','Floppy disk','Unknown non ATA drive','Spin UP complete.','unknown','LBA not supported','Features','[Attention!] Administrator rights required!','Support','Model:','Capacity','Firmware','Disk','Serial','S.M.A.R.T','Cache','AAM Val','APM Val','Erase Time','Security','Sector','Enabled','Disabled','Maximum','Minimum','Standby','Active','Frozen','INTERFACE FAILED!!!','Sended:','Receive:','WARNING! INTERFACE ERROR!!!','Volume info. Partition','Total','Free','View part data','Security manager','USB/SAS mode','Passport error!','Drive Passport','Comment:','Errlog','Selftest','Unknown or unsupported','hours','minutes','Status','Passed','Failed','Power:','Locked','This drive locked by ATA password!','Unlocked','Expired','Not supported','SSD ext:','Emulate:','Interface:','Name:','FSystem:','Revice it!','Unk.size','Attention! For your convenience, the program contains many automatic operations. If you use a program for data recovery, and automation interferes - it can be turned off in the settings.','NVMe drive passport','Drive technical info','Reserved'
StrCommon.Items.Strings='1234','No help file on the selected language.','Translation file not found:','Command interrupted by user!','USB device connected.','USB device removed.','This is a new device. Сlick on the list, when it will be installed.','PAUSED','RESUMED','Warning! Main INI-file','not found, and writen default sets!','Program disabled. Please, use administrator rights.','Stop current operation before closing Victoria','Press F1 to About/HELP','Surface scan','Seek test','Selected drive does not support S.M.A.R.T.','Break button has been pressed four times! As a result, the "Enable breaking internal loops" checkbox on the Setup tab was automatically checked.','Hide serial number','Show serial number','Warning! Failed to activate W-R-V on this drive. The operation is canceled.','Reserved'
StrSmart.Items.Strings='1234','OTF Errors','Raw read error rate','Throughput perfomance','Power ready time','Spin-up time','Number of spin-up times','Retired block count','Reallocated sector count','Read channel margin','Seek error rate','Seek time perfomance','Power-on time','Spin-up retries','HP Meta Rebuild Count','Recalibration retries','Power cycle count','Start/stop count','Soft read error rate','Test track recycle failure','Data path protection failure','Gas gauge','Current HELIUM level','Average MRR (Maxtor)','Minimum MRR (Maxtor)','Maximum MRR (Maxtor)','Average FHC','Max Negative FHC','Erase/Program Cycles Count','Erase/program cycles Count','Max positive FHC','DevSleep exit count','Flying height control','Entering PS1 count','Translation table rebuild','Uncorrectable sector count read or write','Number of valid spare block','Spare block count','Number of initial invalid block','Total erase count','Maximum erase count','Minimum erase count','Average erase count','Max NAND erase count from specification','Total bad block count / SMI remain life (percentage)','Reserved block count','Program fail block count','Erase fail block count','Wear leveling erase count worst','Unexpected power loss count','SMI program fail count in worst die','Erase fail count in worst die','Wear range delta','Wear leveling range percent','Used reserved block count (Chip)','Used Reserved block count (Total)','Unused Reserved Block Count (Total)','Program Fail Count (Total)','Erase Fail Count (Total)','Runtime Bad Block (Total)','I/O CRC Error Detection Count Total','Head Stability','Induced Op-Vibration Detection','Uncorrectable ECC Errors','Command timeout count','Temperature (Alt)','High Fly writes','First Temperature','Airflow temperature','G-SENSOR shock counter','Emergency retract cycles','Unsafe shutdown count','Power-off retract count','Load/unload cycle count','Temperature controller','HDA Temperature','ECC on-the-fly error count','RAISE Recovered','Hardware ECC recovered','Reallocated event count','Current pending sectors','Offline uncorrectable sectors count','Ultra DMA CRC errors','Multi zone error rate','Used reserved block count','Write error rate','Uncorrectable Soft Read Errors','Detected TA count','Off-track errors count','Percentage of drive life used','Thermal Asperity Increased','Data Address Mark Errors','Erase Fail Count','Wear leveling count','Run out cancel','ECC Errors count','Shock count WR operations','Soft ECC corrections Count','Max PE count spec','Shock rate WR operations','Thermal Asperity Rate','Min Erase Count','Flying height','Calibration Time','Max erase count','Spin high current','Erase count average','Spin buzzes count','Remaining drive life in % by erase count','Offline seek perfomance','Vibration during write','Spin running current, mA','Vibration during read','SSM errors count','Shock during write','Gr seek err/RRO-C ERP cnt','Ground load errors count','Ground SpinUp errors','Unexpectant errors count','Unlock/Mis read count','FlashROM ECC corr. count','Disk shift','G-SENSOR shock counter','Loaded hours','Load retry count','Load friction','Host total LBAs written','Load cycle count','Timed workload media wear indicator','Load-in time','Timed workload host reads percentage','Torque amplification','Workload timer','Power-off retract count','Halt system ID, flash ID','Fly height measurement','Drive life protection status','Life curve status','GMR head amplitude','SSD Life Remaining','SSD Life left','HDA Temperature','Lifetime Writes From Host','Available Reserved Space','Writes to flash','Lifetime NAND writes','Remaining Life','Power-On Hours','Lifetime HOST writes','Average erase count and maximum erase Count','Power-Off write recovery count','Second Temperature','Head flying hours','Lifetime writes from host','Total writes Gb','Total sectors write','Lifetime reads from host','Total reads Gb','Total LBA read','SATA interface downshifts','Thermal Throttle Status','SSD wear indicator','Total LBAs Read Expanded','Timed Workload Media Wear','Timed Workload Host Read/Write Ratio','Timed Workload Timer','Remaining Life Percentage','Remaining Spare Block Percentage','Read error retry rate','Writes to flash','Non-GuaTempHours (?)','Reported errors count','Free-fall counter','unknown attribut','Minimum temperature','Maximum temperature','Power Off Cycles count + Retract count','SATA Interface Downshift','Enclosure temperature','RAIN Successful Recovery page count','Cumulative host sectors written','Host program page count','FTL program page count','Less is better','The bigger - the better','Must be zero','Optimal value','unknown','Non optimal value - possible problems','Event counter','Self preserving','Online test','Error rate','Performance','Life critical','Date','Time','Point','Current value','Attribute','Base for SMART attribute graphs not found, or not yet created','SMART base updated.','SMART database contains','records','Time counter','Open point','as SMART','SMART attributes extracted from database on','Save point to file','Create database backup','Load base from file','Unscheduled refresh base','SMART base NOT updated - file or path error!','Restart the program and check the file path','SSD Protect Mode','Reserved'
StrNVMe.Items.Strings='1234','Critical Warning','Composite Temperature','Available Spare','Available Spare Threshold','Percentage Used','Data Units Read','Data Units Written','Host Read Commands','Host Write Commands','Controller Busy Time','Power Cycles','Power On Hours','Unsafe Shutdowns','Media Errors','Number of Error Information Log Entries','Warning Composite Temperature Time','Critical Composite Temperature Time','Temperature Sensor','Thermal Management Temperature 1 Transition Count','Thermal Management Temperature 2 Transition Count','Total Time For Thermal Management Temperature','Total Trotling Time','Unknown_nvme attribute','Warning','No warning','Available spare space has fallen below the threshold','Temperature has exceeded a critical threshold','Device reliability has been degraded due to significant media related errors or any internal error that degrades device reliability','Media has been placed in read only mode','Volatile memory backup device has failed','Persistent Memory Region has become read-only or unreliable','Number of Namespaces','Number of Power states','Best','Better','Good','Degraded','performance','Reserved'
IndPanel.Hint='ATA registers panel'
HDPn1.Hint='Drive name panel'
HdPn2.Hint='Drive serial number panel'
HdPn3.Hint='Drive firmware revision panel'
HdPn4.Hint='Drive size panel'
MmMenu.Caption='Menu  '
MmMenu.HLP.Caption='Help'
MmMenu.N1.Caption='Eventlog last session'
MmMenu.Quit.Caption='Emergency exit'
MmService.Caption='     Service      '
MmAcoustic.Caption='Acoustic && Power (AAM / APM)'
MmHPA.Caption='Host Protected Area (HPA)'
MmRotateSpeed.Caption='Rotate speed and Other tests'
MmSecurity.Caption='Security Operations'
MmDCO.Caption='Device Configuration Overlay (DCO)'
MmCache.Caption='Cache && volatile Features'
MmSmart.Caption='S.M.A.R.T Operations'
MmOtherFe.Caption='Non-volatile Features'
MmControl.Caption='     Actions     '
MmSurfTest.Caption='Full surface scan with grid'
Fullsurfacescanwithgraph1.Caption='Full surface scan with graph'
Quicksurfacescan1.Caption='Quick surface scan with graph'
MmSeek.Caption='HDD Mechanics test'
g1.Caption='Get S.M.A.R.T attributes'
MmShortSelfDiag.Caption='Short Self-Diagnostic (few minutes)'
MmLongSelfDiag.Caption='Long Self-Diagnostic (very long)'
MMEraseAllData.Caption='Erase All data'
MmEraseVerify.Caption='Erase and Verify data (W-R-V)'
Lng.Caption='     Language     '
Lng.DefLng.Caption='Internal'
MmSettings.Caption='     Settings    '
MmHelp.Caption='   Help   '
HLP1.Caption='Instruction manual '
ABT.Caption='About'
DNT.Caption='Donate '
Whatnew1.Caption='What''s new?'
MmBuffer.Caption='View Buffer Live'
SetasStartLBA.Caption='Set as Start LBA'
SetasEndLBA.Caption='Set as End LBA'
Exit1.Caption='Cancel'
Loadgraf.Caption='Load graph from file'
Savegraf.Caption='Save graph to file'
SaveScreenshot1.Caption='Save Screenshot to file'
ClearGraf.Caption='Clear All graphs'
SetasStartLBA1.Caption='Set as Start LBA'
SetasEndLBA1.Caption='Set as End LBA'
Close2.Caption='Close'
MenuItem1.Caption='Copy All'
Copy1.Caption='Copy String'
Copyvalue.Caption='Copy Value'
MenuItem3.Caption='Get Passport'
MenuItem2.Caption='PCI Scan'
OpenBinBassport.Caption='Open .BIN'
BigSmall1.Caption='Line spacing'
OpenDefLog.Caption='Open defect log as script'
ScanDropBtn.Hint='Surface scan options menu (script run)'
MenuItem4.Caption='Open .VDS script'
MenuItem16.Caption='Select ALL strings'
MenuItem17.Caption='Copy selected text'
LogCopyvalue.Caption='Copy value'
Savetofile1.Caption='Save log to file'
C1.Caption='Clear Log'
Openloginfolder1.Caption='Open log in folder'
Setasstart1.Caption='Set as Start'
SetasEnd1.Caption='Set as End'
GetSMART1.Caption='Get S.M.A.R.T.   [ F9 ]'
CopySm.Caption='Copy All'
SmmCopySelStr.Caption='Copy select strings'
Clearscreen1.Caption='Clear table'
OpenBINfile1.Caption='Open .BIN file'
Chk1.Caption='Line spacing'
Smm0.Caption='Copy value'
Smm1.Caption='Copy string'
Smm2.Caption='Copy all log to clipboard'
Smm3.Caption='Set value as Start LBA'
Smm4.Caption='Set value as End LBA'
Smm5.Caption='Create script for tests'
Smd2.Caption='Read && open in HEX-Viewer'
Smd0.Caption='Save log to FILE'
Smd1.Caption='Save All logs to files'
Smd3.Caption='Write log from FILE to DRIVE'
Smd6.Caption='Write log from FILE with CS'
O2.Caption='Open .BIN file as SMART-log'
Smd4.Caption='Refresh list'
Smd5.Caption='Copy list to clipboard'
Fontsize71.Caption='Font size = 7'
Fontsize81.Caption='Font size = 8'
Fontsize9default1.Caption='Font size = 9 (default)'
Fontsize121.Caption='Font size = 12'
MenuItem5.Caption='Copy ALL text to clipboard'
C2.Caption='Copy value:'
C3.Caption='Copy value:'
MPartSetRange.Caption='Set this range to scan'
Clear1.Caption='Clear all strings'
MmRescan.Caption='Rescan Drive list'
MMDft.Caption='HGST Vendor Specific'

[THeFm]
Caption='View buffers'
HexV.Font.Name='Courier New'
HVPauseBtn.Hint='Pause online update for manual research'
HexBufSw.Items.Strings='HDD','API, Copier','PIO'
HVBl.Hint='Buffer size'
HexeditIn.Hint='Data offset'
Fontsize71.Caption='Font size = 7'
Fontsize81.Caption='Font size = 8'
Fontsize9default1.Caption='Font size = 9 (default)'
LoadHexBtn.Caption='Load'
LoadHexBtn.Hint='Loading a pattern file (512B) for writing to drive on Write/Erase mode.'

[THelpForm]
HelpTab.Caption='Help'
HelpEdit.Lines.Strings='Help file not found!'
AboutTab.Caption='About'
DonateTab.Caption='Donate'
NewsTab.Caption='News'
WhatNew.Lines.Strings='Sorry. Help file not found! Please, put file "whatsnew.rtf" into Victoria work folder\LNG\xxx, and press F1.'

[THexForm]
LoadHex.Caption='FROM'#13#10'FILE'
SaveHex.Caption='SAVE'#13#10'TO FILE'
CreateHex.Caption='CREATE'#13#10'NEW'
HexReadBufBtn.Caption='READ'#13#10'BUFFER'
HexWrBufBtn.Caption='WRITE'#13#10'BUFFER'
SwapBtn.Caption='SWAP '#13#10'BYTES'

[THPAForm]
Caption='Host Protected Area'
HpaTrack.Hint='Visual adjustment HPA'
NHPABtn.Hint='Get native LBA size'
NHPABtn.Caption='RHPA'
HpaBtn.Hint='Set new max. LBA size'
HpaBtn.Caption='HPA'
L1.Hint='Example: '#13'*********'#13'100 Mb'#13'204G'#13'17%'#13'etc.'
HPATpChk.Hint='Set Temporary HPA'
HPATpChk.Caption='Temporary HPA'
HpaMbBox.Hint='Output mode: Mb or LBA'
HpaMbBox.Caption='LBA/MB'
HpaExtpassChk.Hint='Refresh EXTERNAL passport after change HPA'
HpaExtpassChk.Caption='Passport after HPA'

[TLogFM]
Caption='Eventlog'


[TminitestsForm]
Caption='Mini Tests'
RPMBox.Caption=' Determine RPM '
RPMBox.Hint='HDD speed measurement'
RzBitBtn43.Caption='Start'


[TSecuForm]
Caption='Security manager'
SecuNoteLab.Caption='Attention! Do not use this functions on the WD Passport drive !'
PwdLab.Caption='Input password:'
LockBtn.Hint='Set ATA password to drive'
LockBtn.Caption='Lock drive'
UnlockBtn.Hint='Remove password from HDD. Need valid User password or Master password (if level=High)'
UnlockBtn.Caption='Unlock drive'
PwdTypeGrp.Hint='Master or User password type'
PwdTypeGrp.Caption=' Password type '
PwdTypeGrp.Items.Strings='Master','User'
PwdLevGrp.Hint='Security level.'
PwdLevGrp.Caption=' Security level '
PwdLevGrp.Items.Strings='High','Max'
ErasePanel.Caption='Erasing data on the drive'
PwdEraseBtn.Hint='Start security erase process. '
PwdEraseBtn.Caption='Security Erase'
EraseBeepChk.Hint='Beep after drive readiness'
EraseBeepChk.Caption='Beep if completed'
PwdEdit.Hint='Field for ATA password. 32 characters maximum'
FilePwdBtn.Caption='Password from file'


[TSettingsForm]
Caption='Settings'
TabSheet3.Caption='Common'
CommonSetBox.Font.Name='Tahoma'
WColorLab.Caption='Window color:'
ELogLab.Caption='Eventlog color:'
RPanelLab.Caption='Right panel color:'
FootPanelLab.Caption='Footer color:'
SmFontLab.Caption='SMART font:'
SmBoldChk.Caption='Bold'
PassFontLab.Caption='Passport font:'
PassBoldChk.Caption='Bold'
NonDestrChk.Hint='If this tag is checked - program forbids operations of writing on tested HDD.'
NonDestrChk.Caption='Disable dangerous features (Not recommended)'
GrLinesChk.Caption='Enable lines on lists'
RussianChk.Caption='''I speak Russian'''
IfApiTab1.Caption='If API then open passport'
AutoSetFocusChk.Caption='Auto-setting focus to lists'
SoundBox.Caption=' Sound '
BellChk.Caption='Bell'
SndChk.Caption='Enable all sounds'
NightChk.Hint='Automatically turn off the sound in the period from 1 to 7 hours'
NightChk.Caption='Night mode'
UseSpk.Caption='Use PC - speaker'#13'(Win x32 Only)'
PrevHibChk.Caption='Prevent hibernation during tests'
PrevHibChk.Hint='Prevent the computer from entering sleep mode while testing the drive'
PrevMonChk.Caption='Prevent the monitor sleep'
PrevMonChk.Hint='Prevent monitor from sleeping during tests'
LsGrpBox.Caption=' Log settings: '
LogDiskBox.Hint='Drive letter for log path.'#13'NUL - default, current folder'
LogSaveTofChk.Caption='Save common log to file'
RzBitBtn2.Caption='Clear File'
NewDateDirChk.Caption='Update catalog creation date'
SepLogsChk.Caption='Separate logs for each drive'
dvintchk.Caption='Unit 1000 (on) / 1024 (off)'
ParamBarChk.Caption='Drive parameters bar'
ThSeparatorChk.Hint='Thousand separator on input LBA fields'
ThSeparatorChk.Caption='Thousand Separator'
TabSheet1.Caption='API'
oldusbchk.Hint='Try if your USB-drive has a MA6160 bridge (A-DATA USB 2.0 drives), and S.M.A.R.T. doesn''t work on it.'
oldusbchk.Caption='Old USB/SATL mode (12-bytes)'
UsbDetectChk.Caption='USB Auto Detect'
NoResetOnRfrChk.Caption='Don''t reset settings when updating the device list'
NoResetOnRfrChk.Hint='When updating the list of API devices and when autodetecting USB drives, all previously changed modes will remain unchanged. In particular, the record will not be reset when scanning the surface (be careful!), The SMART table and the list of logs will not be cleared.'
ViewLettersChk.Caption='Viewing logical drive letters in a list of physical drives'
ROHandlerChk.Caption='R/O handler for compatibility on Windows 10. Don''t check it unnecessarily!'
SctExtChk.Caption='Use SCT Ext commands (API && PIO)'
SctExtChk.Hint='The Smart Command Transport protocol supports two types of commands. Switching may help if there are problems with getting HDD temperature.'
ApiAtaIndChk.Caption='ATA Register Indicators on API mode (not recommended)'
RemapSctChk.Caption='Use SCT-commands for remap (Remap will work without disabling MBR)'
SettingPages.TabSheet2.Caption='PIO'
SetupAtaBox.Caption='  ATA protocol settings '
ATATimerRG.Caption='      ATA-timer:'
PspDblChk.Caption='Get passport on PCI-list double click'
DntRmPcChk.Caption='Don''t remember Primary channel 1F0 / 3F6'
TabSheet4.Caption='Passport'
ExtPasspBox.Caption='EXT PASSPORT BUTTON ACTIONS:'
DSFChk.Caption='Execute self-diagnostics ATA command (03 0C 00 4F C2 A0 EF)'
PspExpaChk.Caption='Disable additional commands on the "Passport" button'
PspExpaChk.Hint='Enabling this checkbox disables the function of obtaining temperature and additional technical information about the HDD on the "Passport" button. They remain on the "EXT" button.'
RSChk.Caption='Real Size'
PWSCHK.Caption='Power Status'
BDChk.Caption='Buffer diagnostic'
PspSmrtChk.Caption='Total SSD write'
OnlyScsiPassChk.Hint='Check this box, if several drives returned equal passport'
OnlyScsiPassChk.Caption='Use only SCSI passport command'
dontgetdpchk.Caption='Don''t get SCSI 25h (not recommended)'
Dont9EChk.Caption='Don''t get SCSI 9Eh (not recommended)'
Dont9EChk.Hint='Set this checkbox if the program slows down on the detect of a device or incorrectly determines its capacity'
dontgetdpchk.Hint='Set this checkbox if the program slows down on the detect of a device or incorrectly determines its capacity'
LogicChk.Caption='Show logical drives'
IgnoreFddChk.Caption='Ignore: FDD'
IgnoreHDDChk.Hint='Ignore Hard drive volumes'
CHSPassp.Caption='Show CHS parameters'
PassChk.Caption='Save .BIN-passport to file'
TabSheet5.Caption='Tests options'
Label9.Caption='Long-read actions count:'
SwapUpDnkeysChk.Hint='Swap Up and Down keys (little step of quick navigation).'
SwapUpDnkeysChk.Caption='Swap navigation Up/Dn keys'
EnaBrkInCyclChk.Hint='If this tag is checked - the program resolves interruption of internal cycles on a key "Break All" for faster exit, if tested HDD is hang.'
EnaBrkInCyclChk.Caption='Enable breaking internal loops'
lbasyncChk.Hint='If this tag is checked - the program updates maximal LBA after each reception of the passport. Otherwise - updating occurs only at change HDD.'
lbasyncChk.Caption='Refresh LBA before operation'
ResBetwLoopScanChk.Caption='Reseting between LoopScan'
TestParamPanel.Caption=' Drive Test Options affect surface testing parameters'
DontBlinkChk.Caption='Don''t blink buttons'
CorrTimHDDChk.Caption='Timings depending on HDD'
AutoRepQnChk.Caption='Auto repeat jump keys, ms:'
AdjustCPUchk.Hint='Determine CPU frequency before any tests'
AdjustCPUchk.Caption='Adjusting RDTSC before tests'
TimerBox.Caption='Timers set  (* - recommended)'
RTChk.Caption=' Scan: PIO only'
RTChk.Items.Strings='G.T.C. (Univ.)','MMTimer (NT)*'
ApiRGR.Caption='      Common RTC'
ApiRGR.Items.Strings='RDTSC (one core)','G.T.C.  (multicore)'
RecallBeforeChk.Hint='Recall HDD before Scan'
RecallBeforeChk.Caption='Recall HDD before Scan'
DelaySettings.Caption='Report delays in the log:'
ErLogBx50.Caption='Dark-gray'
ErLogBx200.Caption='Green'
ErLogBx600.Caption='Orange'
ErLogBxbig.Caption='Red'
ErLogBxErr.Caption='Defects'
StopFatalChk.Caption='Stop scan if fatal error (API only)'
RecCellWidthChk.Caption='Recalculate grid cell width'
TabSheet6.Caption='SMART'
DontSmStatChk.Caption='Don''t request SMART-status'
SmartStatusGrp.Caption='      SMART-Status source (ATA drives only):'
SmartStatusGrp.Items.Strings='Reported by the drive itself','Drive analyzes by Victoria'
AlwaysIBMChk.Caption='Always try IBM Super SMART'
SmartDiv3Chk.Caption='Split RAW on separate numbers'
SmrtChkFileSave.Caption='Save .BIN to file'
GetSmartNsChk.Caption='Get NVMe Namespace SMART (not recommended)'
GetSmartNsChk.Hint='If this checkbox is checked, then SMART is getting from the namespace. Otherwise, from a physical device'
SmBaseChk.Caption='Keep SMART database'
SmBaseIntervalChk.Caption='Database update interval (hh:mm)'
SmTrayChk.Caption='Minimize to tray instead of closing and automatic poll SMART by timer (API)'
SmHideFormChk.Caption='Hide program to tray on startup (API)'
AutoGetSmartChk.Caption='Automatically get SMART on opening (API only)'
AutoGetLogChk.Caption='Automatically get SMART-Log directory on opening (API only)'
TabSheet7.Caption='Interface'
LowColorChk.Caption='Use VGA palette only (256 colors)'
FormResizeGrp.Caption=' Form resize '
ResizeFormChk.Caption='Expand form by draging the bottom of the window, and adjust the height of the eventlog with a splitter.'
ResizeLogChk.Caption='Extend eventlog by draging the bottom edge of the window, and the rest - by a splitter.'
UsbAtaRegBox.Caption='Don''t request ATA-registers via USB bridge'
UsbAtaRegBox.Hint='Some USB bridges freeze when receiving registers. Checking a box solves this problem.'
HintTimeLab.Caption='Hint display time, s:'
ModeltoFilenameChk.Caption='Include model name in screenshot filename'
ModeltoFilenameChk.Hint='Attaches the drive model name and serial number to the screenshot filename.'


[TSmForm]
Caption='S.M.A.R.T. Operations'
SmOperPanel.Caption='SMART  TESTS  TABLE:'
SmartTimerGrp.Hint='Automatic refresh SMART-test data'
SmartTimerGrp.Caption='POLLING TIMER'
Label74.Caption='[ period, с ]'
SmTimerChk.Caption='ON'
SmartCtrlGrp.Caption=' MANAGEMENT'
SmartAtsOFFBtn.Hint='Disable attributes autosave'
SmartAtsOFFBtn.Caption='Disable attributes autosave'
SmartAtsOnBtn.Hint='Enable attributes autosave'
SmartAtsOnBtn.Caption='Auto Save attributes ON'
SmartOnBtn.Hint='Enable SMART operations'
SmartOnBtn.Caption='SMART ON'
SmartOnBtn.Font.Name='Tahoma'
SmartOFFBtn.Hint='Disable SMART operations'
SmartOFFBtn.Caption='SMART OFF'
SmartTestGrp.Caption=' SMART - TESTS'
SmtMeter.Hint='Current SMART-test progress'
SmScanBox.Items.Strings='Off-line data collect','Off-line short test','Ext. off-line routine','Selective routine','Short test (captive)','Ext. test (captive)','Selective test (captive)','Vendor specific'
SmTestBtn.Hint='Start SMART test'
SmTestBtn.Caption='Begin Test'
RzBitBtn29.Hint='Stop current SMART test'
RzBitBtn29.Caption='Abort Test'
SmartBtn.Caption='Get SMART'
SmVSTestEdit.Hint='The number of the SMART-test of the the ATA standard, that we want to run.' 
SmTestLba.Hint='Some drives (for example, WD) display the current LBA in this field during SMART tests.'
SmartTestList.Columns.(1).Caption='Parameters of SMART data collections tests / Status after tests:'
SmartTestList.Columns.(2).Caption='Value'

[TDFTForm]
Caption='IBM/HGST Vendor specific tool'
DFTMemo.Caption='These tools are for IBM / Hitachi / HGST drives only.'
DftSmartInitBtn.Caption='SMART Init'
DftSmartInitBtn.Hint='The button clears SMART attributes to the state of a new drive.'
DftLLFBtn.Caption='Low-Level Format'
DftLLFBtn.Hint='This function transfers the growing defect list to primary list, and then format the drive.'
DftSataSpeedGrp.Caption=' SATA Mode '
DftSataSpeedBtn.Caption='Set'
DftCacheOnBtn.Caption='Cache ON'
DftCacheOFFBtn.Caption='Cache OFF'

[TWriteWarn]
Caption='Attention!'
Label1.Caption='Region of disc you have entered '#13'will be erased. Please check your '#13'entries again, and correct it.'
Label2.Caption='HDD WILL BE CHANGED'
StartLbaEdit.Hint='Start LBA'
EndLbaEdit.Hint='End LBA'

[TSmartgfForm]
SmGfChangePanel.Caption='Scale by:'
FromNulChk.Caption='Y from zero'
FromNulChk.Hint='If this flag is not set, the graph is built from the minimum value of the function. Otherwise - from zero.'

[ResourceStrings]

