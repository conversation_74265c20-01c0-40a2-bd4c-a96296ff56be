﻿#pragma once

#include <TypeDef.h>

namespace ThirdPartyDiskSATACMDCfgDef
{
	typedef enum _E_SET_BOOT_SCAN_ENVIR_OP_TYPE 
	{
		SET_RDT_REG_CFG = 0,
		SET_RDT_FLASH_OP = 1,
		SET_RDT_FLASH_PARAM = 2,
		SET_WORDLINE_INFO = 3,
		SET_MODE_SWITCH = 4,
		SET_PROG_CQ_CMD = 5,
		SET_ERASE_CQ_CMD = 6,
		SET_READ_CQ_CMD = 7,
		SET_READ_STATUS_CQ_CMD = 8,
		SET_TWO_PLANE_DATAOUT = 9,
		SET_FEATURE_SET = 10,
	} E_SET_BOOT_SCAN_ENVIR_OP_TYPE;

	typedef enum _E_ENVIR_TYPE
	{
		BOOT_SCAN_ENVIR_TYPE = 0,
		ALG_RDT_ENVIR_TYPE = 1
	}E_ENVIR_TYPE;

	enum E_CODE_TYPE
	{
		E_BOOT_CODE = 0,
		E_SCAN_CODE = 1,
		E_ALG_TABLE = 2,
		E_ALG_LOGIC = 3,
		E_ALG_SUPER_INDEX = 4
	};

	enum E_FLASH_MODE
	{
		E_SDR_MODE = 0,
		E_DDR_MODE = 1
	};

	enum E_ERASE_TYPE
	{
		SLC_MODE = 0,
		MLC_MODE = 1,
		TLC_MODE = 2
	};
	static const U32 RD_STS_70_CMD = (0);
	static const U32 RD_STS_71_CMD = (1);
	static const U32 RD_STS_78_CMD = (2);
	static const U32 RD_STS_FN_CMD = (3);
	static const U32 PER_BLK_RDWR_MODE = (0);
	static const U32 ALL_BLK_RDWR_MODE = (1);

	static const U8 MAX_TLC_PRO_CQ_ADDR_PLANE_NUM = 5;
	static const U8 MAX_TLC_PRO_CQ_ADDR_NUM = 3;

	static const U32 MODE_DIS = (0);
	static const U32 MODE_EN  = (1);

	static const U32 RAND_PATTERN_MODE  = (0);
	static const U32 FIX_PATTERN_MODE  =  (1);

	static const U32 SAVE_BLK_ECC   = (0);
	static const U32 SAVE_PAGE_ECC  = (1);

	static const U32 MAX_CH_NUM = (4);
	static const U32 MAX_CE_NUM = (4);
	static const U32 MAX_LUN_NUM = (16);
	static const U32 MAX_PLN_NUM = (4);

	const static U32 COL_ADDR_ON_PLN = (0);
	const static U32 COL_ADDR_ON_LUN = (1);
	const static U32 MAX_BLK_NUM = (4);

	const static U32 R_CQ_ATTR_BIT = (24);
	const static U32 R_CQ_ATTR_CMD = (((U32)0x08)<<R_CQ_ATTR_BIT);
	const static U32 FL_CMD_MUL_PLANE_CMD1 = 0x81;
	const static U32 FL_CMD_MUL_PLANE_CMD = 0x11;
	const static U32 FL_CMD_READ4 = 0x32;
	
	static const U8 CONST_MAX_G0_FW_VER_SIZE = 40;
	static const U8 CONST_MAX_G3_FW_VER_SIZE = 40;
	static const U8 CONST_MAX_CH_NUM = 4;
	static const U8 CONST_MAX_CE_NUM_PER_CH = 4;
	static const U8 CONST_MAX_FLASH_ID_SIZE = 8;
	static const U16 CONST_MAX_USERDATA_SIZE = 256;

#pragma pack(1)

	/////////////////////////////////////////////////////////////////////////////////////////////


	typedef struct  
	{
		U8 bOpType;//具体的操作类型
		U8 bOpSct; //具体的操作的扇区个数
		U8 bReserve[2];

	}T_PARAM;
	/////////////////////////////////////////////////////////////////////////////////////////////


	/////////////////////////////////////////////////////////////////////////////////////////////
	// 私有命令参数
	//// CMD_OPCODE_READ_BASIC_INFO
	typedef struct STR_PRI_FW_VER
	{
		//G0固件版本号，无效时为0XFF
		U8 m_g0FwVersion[CONST_MAX_G0_FW_VER_SIZE];
		//G3固件版本号，无效时为0XFF
		U8 m_g3FwVersion[CONST_MAX_G3_FW_VER_SIZE];

	} PRI_FW_VER, *PPRI_FW_VER;

	//// CMD_OPCODE_READ_BASIC_INFO
	typedef struct STR_PRI_FLASH_ID
	{
		//4通道,4CE,每CE8 BYTE ID
		U8 m_flashID[CONST_MAX_CH_NUM][CONST_MAX_CE_NUM_PER_CH][CONST_MAX_FLASH_ID_SIZE];

	} PRI_FLASH_ID, *PPRI_FLASH_ID;

	typedef struct STR_PRI_BASIC_INFO
	{
		PRI_FW_VER m_strFwVer;
		PRI_FLASH_ID m_strFlashID;
		U32 dResReg0Val;
	} PRI_BASIC_INFO, *PPRI_BASIC_INFO;

	//// CMD_OPCODE_OP_RAM
	typedef struct STR_PRI_RAM_OP_INFO
	{
		//RAM地址
		U32 m_ramAddr;
		//操作扇区的个数
		U16 m_sctCnt;
		U8  m_bReserve[2];

	} PRI_RAM_OP_INFO, *PPRI_RAM_OP_INFO;

	//// CMD_OPCODE_SWITCH_INTERFACE
	typedef struct STR_PRI_CORE_SWITCH
	{
		//控制G0是否使能，0:dis，1:en
		U8  m_g0RunningEn;
		U8  m_bReserve1[3];
		//设置G0PC指针地址
		U32 m_g0PCAddr; 
		//设置G0的向量地址
		U32 m_g0VectorAddr;
		//控制G3是否使能，0:dis，1:en
		U8  m_g3RunningEn;
		U8  m_bReserve2[3];
		//设置G3PC指针地址
		U32 m_g3PCAddr;   
		U8  m_bReserve3[4];
		U32 m_resReg0Val;
		U8 m_bReserve4[480];
		U32 m_checkSum;

	} PRI_CORE_SWITCH, *PPRI_CORE_SWITCH;

	//// CMD_OPCODE_OP_BOOT_IDX
	typedef struct STR_PRI_BOOT_IDX_PARAM
	{
		U8 m_reserve[32];

	} PRI_BOOT_IDX_PARAM, *PPRI_BOOT_IDX_PARAM;

	//// CMD_OPCODE_OP_PAGE
	//// CMD_OPCODE_OP_BLK
	typedef struct STR_PRI_EWR_OP
	{
		//操作具体lun内的Page地址
		U32 m_opPageAddr;
		//操作具体lun内的块号
		U16 m_opBlk; 
		//操作BLK的Page偏移
		U16 m_opPageOffs;
		//操作的通道
		U8  m_opCh;  
		//操作的CE
		U8  m_opCe;  
		//操作的Lun
		U8  m_opLun;  
		//操作的Pln
		U8  m_opPln;  
		//操作的byte数据量
		U32 m_opByteCnt; 
		//操作的Sector数据量
		U16 m_opSctCnt; 
		//操作的种子
		U16 m_randSeed;
		// 当前擦写读的对象类型，比如擦除boot，scan，赋值为E_CODE_TYPE
		// 该字段只有PC使用
		U8 m_codeType;
		U8  m_reserve[3];
		// 指定userdata的长度，写读boot的时候，该长度为0，写读算法表时非0
		U16 wUserDataLen;
		U8  bUserData[CONST_MAX_USERDATA_SIZE];

		//***************************算法和RDT相关表格的读、写、擦时用************************
		// 表格簇大小
		U8 tabClstSec;
		// 数据簇大小
		U8 dataClstSec;
		// 擦除的类型
		E_ERASE_TYPE eraseType;

	} PRI_EWR_OP, *PPRI_EWR_OP;

	typedef struct STR_PRI_CFG_PARAM
	{
		//具体的操作类型
		U8 m_opType;
		//具体的操作的扇区个数
		U8 m_opSct; 
		// 环境类型，表示当前配置的是哪一套环境
		U8 m_envirType;
		U8 bReserve[1];
	} PRI_CFG_PARAM, *PPRI_CFG_PARAM;

	typedef  struct _FDL_PARAM_INFO
	{
		U8  bSctCnt;//除head外的扇区个数
		U8  bReverse[3];
	}FDL_PARAM_INFO;

	typedef  struct
	{
		U8  m_bDataType;   // 数据类型
		U8  m_bChan;       // 通道编号
		U8  m_bLunOfChan;  // 通道中的Lun编号
		U8  m_bClstOffs;   // 页中簇偏移
		U32  m_dRowAddr;    // Lun中的行地址
	}T_PHY_ADDR;

	typedef  struct
	{
		U8  bClstCnt;
		U8  bSctCnt;
		U16  UserDataOffs;//userdata相对起始data的所在ram的偏移
		T_PHY_ADDR m_tClstAddr[32];
	}T_RD_ClST_OP_INFO;

	typedef  struct
	{
		U8  bPage;
		U8  bSctCnt;
		U8  bReverse[2];
		T_PHY_ADDR m_tPageAddr;
		U8  bUserData[256];
	}T_WR_ClST_OP_INFO;

	typedef  struct
	{
		U8 bScntCnt;
		U8 bReserve[3];
		T_PHY_ADDR m_SuperIndexAddr;
	}T_SUPER_INDEX_OP_INFO;

	typedef  struct
	{
		U8  bEraseType;
		U8  bBlkCnt;//擦除的块个数
		U8  bSctCnt;
		U8  bReverse;
		T_PHY_ADDR m_tBlkAddr[32];
	}T_ERS_BLK_OP_INFO;

	// ********擦除返回状态************//
	typedef enum
	{
		ERS_ERR_NONE = 0,
		ERS_VDT ,   //VDT first
		ERS_FAIL,
	}ERASE_ERR_TYPE;

	typedef struct _T_ERASE_EXCEPTION
	{
		U8 bDescript;   //0 erase success,1 erase fail
		U8 ArrRev[1];
		U16 wLen;       //length of exception information
	}ERASE_EXCEPTION;

	typedef struct _T_ERR_INFO
	{
		U16 wIndex;         //index of parameter which has occur error
		ERASE_ERR_TYPE ErrType;        //error type,
	}ERASE_ERR_INFO;
	// ******************************//

	//// 逻辑操作信息
	//typedef struct STR_PRI_LOGIC_CMD_PARAM
	//{
	//	U8 m_opCode;
	//	U16 m_secNum;
	//	U32 m_logicSecOffset;
	//} PRI_LOGIC_CMD_PARAM, *PPRI_LOGIC_CMD_PARAM;

	typedef struct _SPI_RDWR_DATA
	{
		U32 opPageAddr;
		U16 opByteCnt;
		U16 opSctCnt;
	}SPI_RDWR_DATA;

	typedef struct _PRI_LG_RW
	{
		U32 lba;
		U32 secSize;
		U8  direction;
		U8  rev[3];
	}PRI_LG_RW;

	// 读写取RDT_CFG参数
	typedef struct _READ_WRITE_RDT_CFG
	{
		U8 rdtCfgOpCh;
		U8 rdtCfgOpCe;
		U16 rdtCfgOpSecCnt;
		U32 rdtCfgOpPageAddr;
	}READ_WRITE_RDT_CFG, *PREAD_WRITE_RDT_CFG;

	// 读写RDT_INFO参数
	typedef struct _READ_WRITE_RDT_INFO
	{
		U8 rdtInfoOpCh;
		U8 rdtInfoOpCe;
		U16 rdtInfoOpSecCnt;
	}READ_WRITE_RDT_INFO, *PREAD_WRITE_RDT_INFO;

	// 设置是否进行RDT理想扫描参数
	typedef struct _SET_IS_RDT_RUN_PARAM
	{
		U8 bRdtRun;
	}SET_IS_RDT_RUN_PARAM, *PSET_IS_RDT_RUN_PARAM;

	typedef struct _PRIVATE_CMD_EX
	{
		U8 opCmd;
		U8 subOpCmd;
		U8 reserved[2];

		union 
		{
			struct 
			{
				SPI_RDWR_DATA spiRdWrParam;
				U8 exParam[500];
			}spiRandWR;

			struct 
			{
				PRI_LG_RW priLgRw;
				U8 rev[496];
			}priLogWR;

			// 读写取RDT_CFG参数
			READ_WRITE_RDT_CFG readWriteRdtCfg;

			// 读写RDT_INFO参数
			READ_WRITE_RDT_INFO readWriteRdtInfo;

			// 设置是否进行RDT理想扫描参数
			SET_IS_RDT_RUN_PARAM setIsRdtRunParam;
		};
	}PRIVATE_CMD_EX;

	typedef  struct
	{   
		U8  m_bDataType;   // 数据类型
		U8  m_bChan;       // 通道编号
		U8  m_bLunOfChan;  // 通道中的Lun编号
		U8  m_bClstOffs;   // 页中簇偏移
		U32  m_dRowAddr;    // Lun中的行地址
	} T_FLASH_PHY_ADDR; 

	typedef	struct _BOOT_ENVIR_CFG
	{
		U8 bEccType;  //记录该操作采用的ecc模式，1表示BCH模式，0表示LDPC模式（LDPC下只采用模式7）
		U8 bEccMode;  //记录该操作时，采用BCH时的ECC模式
		U16 wDmaLen;   //记录该操作，所选用的dma长度
		U16 wEcc_Threshold;
		U16 wReserve;
	}T_BOOT_ENVIR_CFG;

	typedef struct
	{
		U32  m_dRowAddr;    // Lun中的行地址
		U16 wColAddr;       //HAL参数指针，由上层初始化
		U8  m_bChan;       // 通道编号
		U8  m_bLunOfChan;  // 通道中的Lun编号
		U32 secCnt;
		
		U8 bEccType;  //记录该操作采用的ecc模式，1表示BCH模式，0表示LDPC模式（LDPC下只采用模式7）
		U8 bEccMode;  //记录该操作时，采用BCH时的ECC模式
		U16 wDmaLen;   //记录该操作，所选用的dma长度
		U16 wEccThreshold;
		U16 wInvalidDataLen;  //用于boot写表中记录无效数据的长度，表示最后一段无效数据的长度
		U32 dDataLen;         //需要操作的数据量byte数
	}T_BOOT_PARA;

	typedef struct _RW_PAGE_PARAM
	{
		U8 codeType;

		union
		{
			T_BOOT_PARA bootPara;
			PRI_EWR_OP priEwrOp;
		}DetailParam;
	}RW_PAGE_PARAM, *PRW_PAGE_PARAM;
	/////////////////////////////////////////////////////////////////////////////////////////////

	//////////////////////////////扫描scan的环境参数/////////////////////////////////////////////
	typedef enum 
	{
		RDT_CFG_TABLE = 101
	}TABLE_TYPE;

	static const S8 RDT_VERSION[] = "20170110";
	static const S8 VERIFY_KEY[] = "jkjk872s";

	#define UART_DEBUG_ON_TEST (1)

	#define ONLINE_MODE (0)
	#define RDT_MODE    (1)

	#define ATTRIBUTE_PACKED  __attribute__ ((packed))

	#define TOTAL_CH_CNT 4
	#define CH_INVALID 	 					(0xFF)
	#define CE_INVALID                      (0xFF)
	#define CH_VALID						(0x1)
	#define CE_VALID						(0x1)
	#define INVALID_BLK						(0xFFFF)
	#define INVALID_WL						(0xFFFFFFFF)
	#define INVALID_PAGE					(0xFFFFFFFF)
	#define INVALID_SEQ						(0xFF)
	#define INVALID_CMD						(0xFF)

	#define RDT_CFG_KEY0	(0x5555aaaa)
	#define RDT_CFG_KEY1	(0xaaaa5555)
	#define RDT_INFO_KEY0	(0x5a5a5a5a)
	#define RDT_INFO_KEY1	(0xa5a5a5a5)

	//为了支持TLC Flash的Copyback扫描，因此提供源块记录字段：

	//#define ENABLE 	(0x1)
	#define DISABLE (0x0)

	#define DIR_READ (1)
	#define DIR_WRITE (0)

	enum FLASH_TYPE
	{
	SLC_TYPE = 0,
	MLC_TYPE = 1,
	TLC_TYPE = 3
	};

#if 0
	//读状态的定义：
	typedef struct _READ_STS
	{
		U8   bOPMode;       //0:70,1:71,2:78 addr  3:fn
		U8   bReserve[3];
	}T_READ_STS;

	//改变随机读的列地址定义：
	typedef struct _READ_COL
	{
		U8   bOPMode;       //0:05 Col E0, 1:00 Row Col 05 Col E0
		U8   bReserve[3];
	}T_READ_COL;

	//改变随机写的列地址定义：
	typedef struct _WRITE_COL
	{
		U8   bOPMode;       //0:85 Col,1:85 Row Col
		U8   bReserve[3];
	}T_WRITE_COL;

	//Multiplane和需加前缀命令的控制CQ定义：
	typedef struct _CQ_CFG
	{
		U32 dPreErsCQCmd[4];
		U32 dPreProgCQCmd[4];
		U32 dPreReadCQCmd[4];
		U32 dMultiPlnCQWrCmd1; //多Plane编程时,非最后Plane的编程命令，如0x11。
		U32 dMultiPlnCQWrCmd2; //多Plane编程时，启动命令（如0x80,0x81、或0x85（开启cache编程））
		U32 dMultiPlnCQRdCmd;  //多Plane读操作时，非最后一个Page的读命令码，如0X32替换0X30  
	}T_CQ_CFG;

	/*flash类型*/
	enum E_FLASH_TYPE
	{
		E_FLASH_TYPE_SLC = 0,
		E_FLASH_TYPE_MLC = 0,
		E_FLASH_TYPE_ED3 = 3,
		E_FLASH_TYPE_OBP = 4,
		E_FLASH_TYPE_MLC_3D = 5,
		E_FLASH_TYPE_TLC_3D = 6
	};

	/*OBP类型flash的子类型*/
	enum OBP_FLASH_SUBTYPE
	{
		OBP_19NM_ACG_FLASH  =90,  //19NM ACG
		OBP_19NM_ADG_FLASH  =95,   //19NM ADG
		OBP_SP_FLASH        =105
	};

	/*ED3类型flash的子类型*/
	enum ED3_FLASH_SUBTYPE
	{
		ED3_8M2M_TLC	= 200
	};

	/*ED3的program序号*/
	enum ED3_PROGRAM_SEQ
	{
		FIRST_PROG		= 0,
		SECOND_PROG		= 1,
		THIRD_PROG		= 2,
		ED3_PROG_MAX	= 3
	};

	typedef struct _ED3_PARAM
	{
		U16	wTLC_PageNub[ED3_PROG_MAX];	// program order对应的page number(LSB+CSB+MSB)
		U8  wProgSeq;			// 编程序号(eg: 1st PGM/2nd PGM/3rd PGM)
		U8	wPageCnt;			// pargram order实际对应的page number
	}T_TLC_PARAM;

	typedef struct  _COPY_BACK_CTRL
	{
		//	U16 wSourceBlkNum[MAX_CH_NUM][MAX_LUN_NUM][MAX_PLN_NUM][MAX_BLK_NUM];
		U16 wSourceBlkNum[MAX_CH_NUM][MAX_CE_NUM][4][MAX_PLN_NUM][MAX_BLK_NUM];
		U8  bUsefulChNum;
		U8  bUsefulChipNum;
		U8  bUsefulLunNum;
		U8  bUsefulPlnNum;
		U8  bUsefulBlkNum;
		U8  bReserve[3];
	}T_COPY_BACK;

	//具体的定义则如下：
	typedef struct
	{
		T_READ_STS       tRdStsCfg;      //读状态控制参数
		T_READ_COL       tRdColCfg;      //随机读控制参数
		T_WRITE_COL      tWrColCfg;      //随机写控制参数
		T_CQ_CFG         tCQCfg;         //CQ配置控制
		T_COPY_BACK      tCBCtrl;        //copyback操作的开关
		U8               bMultiPlnOpen;  //多plane操作的开关，0:disable
		U8               bInterleaveOpen;//interleave模式的开关
		U8               bMultiChOpen;   //多通道操作模式的开关；
		U8               bReadColOpen;   //带随机读列地址操作的开关;
		U8               bWriteColOpen;  //带随机写列地址操作的开关；
		U8               bReserve[2];
	}T_FLASH_OP;
#endif

	typedef struct
	{
		U8 m_bWordlineType;       //0:  单Wordline结构。   1: 奇偶Wordline结构
		U8 m_bReserved0[3];
		U16 m_wSlcPageOfBlk;
		U16 m_wWordlineMax;       //每个块中包含的最大Wordline个数
		U16 m_wSmallPageBaseNum;  //PageToWLMapTab的结尾段，小的页号基本值
		U16 m_wBigPageBaseNum;    //PageToWLMapTab的结尾段，大的页号基本值
		//Flash的Group A/B/C 表。
		U16  m_wPageGroupMapTabLowWL[8][4]; //由Wordline号，查找映射的Page Number
		U16  m_wPageGroupMapTabHighWL[8][4];
		U8  m_bSmallPageMappedWlNum[8*4]; //由Page Number查找对应的Wordline号，以及Wordline内的page 偏移号
		U8  m_bSmallPageMappedOffs[8*4];    //物理页偏移对应的WL内PageOffs
		U16  m_wBigPageMappedWlNum[8*4];
		U8  m_bBigPageMappedOffs[8*4];
		U16  m_wSmallOrderBase;    //OrderToWLMapTab的结尾段，小的Order号基本值
		U16  m_wBigOrderBase;      //OrderToWLMapTab的结尾段，大的Order号基本值
		U16  m_wBeginSegOrderWlThres;  //起始特殊Order对应最末Wordline值  (ED3 Flash为第一个Wordline，即0， 而Samsung TLC为最后一个包含3个Page的Wordline)
		U16  m_wEndSegOrderWlThres;    //末尾特殊Order对应最小Wordline值  (ED3 Flash为最后一个Wordline， Samsung TLC为最后一个包含3个Page的Wordline)
		//TLC Flash的 Program Order表。
		U16  m_wPrgOrderTabLowWL[8][4];        //由Wordline号，查找映射的Program Order信息
		U16  m_wPrgOrderTabHighWL[8][4];
		U8  m_bSmallOrderMappedWlNum[8*4];   //由Program Order查找对应的Wordline号，以及Wordline内的编程次序
		U8  m_bSmallOrderMappedCycle[8*4];    //Program Order对应的WL内第几次编程(1st/2nd/3rd Program)
		U16  m_wBigOrderMappedWlNum[8*4];
		U8  m_bBigOrderMappedCycle[8*4];
	}T_UDP_WL_PARAM;

	typedef struct
	{
		/**************通用寄存器************************/
		U32 dNfCfg;
		U32 dFioSpopCtrl;
		U32 dDdrTimingCfg;
		U32 dAleCleTimCfg;
		U32 dMcuCfgCle;
		U32 dMcuCfgAle;
		U32 dChkStRbCnt;
		U32 dChkCqCtrl;
		U32 dBadColumnCtrl;
		U32 dBadcolInfoBaddr;
		U32 dECCFuncSel;
		U32 dReserve[9];
		/******************写表相关寄存器*********************/
		U32 dEnCtrl;				//err enter int
		U32 dEccCtrl;				//dma2x en
		U32 dBchMode;				// BCH Mode,dmax_ecc_cfg
		U32 dDMA2LenCfg;			// Len Cfg,user data len,user data attach len
		U32 dDMA2RndCtrl;			// Randomizer Configuration
		U32 dDMA2RndSeed;
		U32 bUserDataEn;			//是否带USER-DATA要根据写表的情形
		U32 dFilterColumnEn;		//是否带跳列(1表示需要跳列，0表示不需要)
		U32 dReserve2[8];
	}T_RDT_REG_CFG;

	/* 			PRNG时会用到此数据结构
	 * 假设数据簇有10个DMA，即bDmaOfClst = 10.
	 * 前面9次DMA的大小 = wDmaSize + wParitySize;
	 * 最后一次DMA的大小 = wDmaSize + wParitySize + wUserDataSize*/
	typedef struct
	{
		U16 wDmaSize;			// DMA size大小
		U8  wParitySize;		// parity size大小
		U8  wUserDataSize;		// 用户数据size
		U8  bSectOfClst;		// 一个簇中的扇区数
		U8  bDmaOfClst;			// 一个簇DMA的次数
		U8  bClstOfPage;		// 一个page中的簇数
	}T_ALGO_CFG;

	typedef struct
	{
		U8               bMultiPlnOpen;  //多plane操作的开关，0:disable
		U8               bInterleaveOpen;//interleave模式的开关
		U8               bMultiChOpen;   //多通道操作模式的开关；
		U8               bReadColOpen;   //带随机读列地址操作的开关;
		U8               bWriteColOpen;  //带随机写列地址操作的开关；
		U8               bReserve[2];
	}T_RDT_FLASH_OP;

	typedef struct
	{
		U8 m_bChanNum;                    //设备包含通道的总个数
		U8 m_bChipOfChan;                 //一个通道包含的物理片选数
		U8 m_bLunOfChip;                  //每Chip包含的Lun数
		U8 m_bLunOfChan;                  //一个通道包含的lun数
		U8 m_bLunOfDev;                   //设备包含总的lun数
		U8 m_bPlaneOfLun;                 //每个lun包含plane个数
		U16 m_wPageOfBlk;                  //每个block有多少个可用物理page
		U16 m_wRowOfBlk;                   //Block对应的行地址大小，主要用于擦除
		U16 m_wRowGapOfBlkInPlane;         //一个plane内，相邻两个blk的行地址间隔
		U32 m_dRowGapOfPlane;              //每个palne间，的行地址间隔（与m_wPageOfBlk相同?）
		U32 m_dRowGapOf2LUN;               //两个lun之间行地址的间隔
		U16 m_wBlkOfLUN;                   //每个lun包含的block个数
		U8 m_bSectOfPage;                 //1个page有多少个扇区
		U8 m_bSectOfDMA;                  //1个DMA有多少个扇区
		U8 m_bCellType;                 //1： SLC, 2:MLC, 3:TLC
		U8 m_bBlockGapInPlane;			// 一个plane内相邻block的间隔block数
		U16 m_BlockOfPlane;				// 一个plane包含的block个数
		U16 m_wWordlineMax;				//每个块中包含的最大Wordline个数
		U16 m_wPageSize;				//page大小，以字节为单位
		U16 m_wParitySize;				//page对应的parity大小，以字节为单位
		U16 m_wAlgUserdata;				//上层算法使用的userdata长度
		U16 m_wSlcWordlineMax;			//SLC模式下block中包含的WL数(也即page数)
		U8  bReserve[2];
	}T_RDT_FLASH_PARAM;

	enum TLC_PAGE
	{
		LSB_PAGE=0,
		CSB_PAGE=1,
		MSB_PAGE=2,
		MAX_PAGE=3
	};

	enum FLASH_PLANE
	{
		PLANE0=0,
		PLANE1=1,
		PLANE2=2,
		PLANE3=3,
		MAX_PLANE=4
	};

	enum PROG_SEQ
	{
		PROG_1ST=0,
		PROG_2ND=1,
		PROG_3RD=2
	};

	const U8 PRG_CYCLE_1ST = 0;
	const U8 PRG_CYCLE_2ND  = 1;
	const U8 PRG_CYCLE_3RD = 2;
	const U8 TOTAL_PRG_CYCLE = 3;
	const U8 MAX_WL_OF_PAGE = 3;
	const U8 MAX_SEGMENT = 8; //目前看到B16A需要8段的数据模

	typedef struct
	{
		U16   wWLCnt;   //这个段包含的wordline个数
		U16   wMinWLNum;//这个段最小wordline号
		U16   wMaxWLNum;//这个段最大wordline号
		U16   wPageBaseNum[MAX_WL_OF_PAGE];//page基值
		U8   bPageUnit[MAX_WL_OF_PAGE];   //page系数

		U8   bPrgCycleNum;//这个段中，每个wordline所需编程的次数
		U16   wPrgOrderBaseNum[TOTAL_PRG_CYCLE];//order基值
		U8   bPrgOrderUnit[TOTAL_PRG_CYCLE];   //order系数
		U8   bPrgPageCnt[TOTAL_PRG_CYCLE];//这个段中，wordline每次编程所包含的page个数
		U8   bPrgPageIdx[TOTAL_PRG_CYCLE][MAX_WL_OF_PAGE];//这个段中，wordline每次编程的page号索引
		U8   bReserve[3];
	}T_WL_MATH;//44Byte


	typedef struct _WORDLINE_INFO
	{
		U8   bSegCnt;//段的个数，暂时最大支持8个段 
		U8   bPrgCycleMaxCnt;
		U16   wPrgOrderTotalCnt;
		U16   wWordlineTotalCnt;
		U8	bExtraWLNum;//描述编程完最后一个wordline，需要额外编程几个wordline
		U8   bReserve[1];
		T_WL_MATH tWlMath[MAX_SEGMENT]; 
	}WORDLINE_INFO, *PWORDLINE_INFO;//356Byte

const U8 MAX_PREFIX_CQ_CMD = 4;
const U8 MAX_SUFFIX_CQ_CMD = 4;
	typedef struct
	{
		U8 PreAddrCQCmd [MAX_PREFIX_CQ_CMD]; 	// 驱动的前缀命令
		U8 AftDataCQCmd [MAX_SUFFIX_CQ_CMD];	// 驱动的后缀命令
		U8 bCmdAddrArray[MAX_PREFIX_CQ_CMD+MAX_SUFFIX_CQ_CMD];//表示上述两表每项为命令还是地址，0表示命令，1表示地址。0-3项表示前缀项，4-7项后缀项
	}T_CQ_CMD_UNIT;



	/*注意：类ED3的Flash prog时使用的是WL地址而不是page地址*/
	/*描述WL中编程的驱动模型*/
	typedef struct
	{
		U8  	bWLorOffsAddrMode;			// 1表示WL地址, 0表示page地址
		U8	tNoFinalProgCmd;		//非final plane的program结束命令,一般为0x11
		U8	tFinalPlaneProgCmd;		// prog命令的结束命令,一般为0x10
		U8	tFirstProgCmd[MAX_PLANE];			// program的首个命令,一般为0x80

		U8   bExtraCmdorAddrMode;     //是否发送额外的命令，像samsung Flash TLC编程最后需要加8Bh···10h命令
		U8   bExtraWLorOffsAddrMode;  //同上述
		U8   bExtra1stProCQCmd[MAX_PLANE];		 //特别Flash（如Samsung）会发送额外的命令，像8Bh
		U8   bExtra2ndProCQCmd[MAX_PLANE];       //一般是10h
		U8	bProCacheCmd;	//Cache Pro

		U8	bReverse[38];

		/*************************下面字段用于TLC program************************************/
		// WL中第一次编程的驱动命令
		T_CQ_CMD_UNIT tProg1stUnit[MAX_PLANE][MAX_PAGE];
		// WL中第二次编程的驱动命令
		T_CQ_CMD_UNIT tProg2ndUnit[MAX_PLANE][MAX_PAGE];
		// WL中第三次编程的驱动命令
		T_CQ_CMD_UNIT tProg3rdUnit[MAX_PLANE][MAX_PAGE];
	}T_PROG_CQ_CMD;

	/*描述擦除块的驱动模型*/
	typedef struct
	{
		U8		tFirstEraseCmd;			// 擦除的首个命令,一般为0x60
		U8		tNoFinalEraseCmd;		// 非final plane的擦除结束命令,一般为0xD1
		U8		tFinalPlaneEraseCmd;	// 擦除命令的结束命令,一般为0xD0
		U8		bReverse[5];
	}T_ERASE_CQ_CMD;

	typedef struct
	{
		U8		tWLFlag;				// 1表示WL地址,0表示page地址
		U8		tFirstReadCmd;			// read的首个命令,一般为0x00
		U8		tNoFinalReadCmd;		// 多plane时的中间read命令,一般为0x32
		U8		tFinalReadCmd;			// read命令的结束命令,一般为0x30
		U8		tPagePreCmd[MAX_PAGE];	// 操作TLC Flash的page的前缀命令,一般为 0x01/0x02/0x03
		U8		bReverse[1];
	}T_READ_CQ_CMD;

	typedef struct
	{
		U8		tReadCmd;				// 一般为0x00
		U8		tReadCmdComfirm;		// 一般为0x05
		U8		tRandomCmd;				// 一般为0xe0
	}T_TWO_PLANE_DATAOUT;

	typedef struct
	{
		U8		tStatus;			// 读取整个target的状态命令，一般为0x70
		U8		tEnhancedStatus;	// 读选定LUN的状态命令，一般为0x78
	}T_READ_STATUS_CQ_CMD;

	typedef struct
	{
		U8 bFlashMode; //SDR<->DDR
		U8 bSetFeatureCmd; // set feature命令, 一般为0xEF
		U8 bFeatureAddr; // set feature地址, 01h表示Timing mode,02h表示Toggle 2.0 specific,10h表示Driver strength,30h表示External VPP
		U8 bPxData[4];				// 4字节的脉冲数据
		/***********下述俩字段只在开启flash retry时才有效************/
		U8 bRetryFeatureAddr;       //retry需要设置feature时的地址，b0kb为0x89
		U8 bRetryModeCnt;           //最多支持多少种retry模式
	}T_FEATURE_SET;

	typedef struct
	{
		U8		tSLCModeFlag;			// 是否有SLC模式切换命令（1表示有，0表示没有）
		U8		tSLCDisableFlag;		// 是否有SLC disable命令（1表示有，0表示没有）
		U8		tToSLCModeCmd;			// 切换到SLC模式的命令,一般为0xDA
		U8		tSLCDisableCmd;			// 切换到TLC模式的命令，一般为0xDF
		U8      bSlcMode;				// 非0表示SLC
		U8		bL06B_TO_B0KBEn;		// L06B转B0KB
		U8   	bReserve[2];
	}T_MODE_SWITCH;	// 8 Bytes
	///////////////////////////////////////////////////////////////////////////////////////////

#pragma pack()
}