

import PublicFuc,ErrDiskInfo
import configparser
import csv
import os,re
import time
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors,Font
from datetime import datetime,timedelta

titleFill = PatternFill('solid')
contentFill = PatternFill('solid')

SPEED_LOW_ERR = 0 #速度过低
SPEED_HIGH_ERR = 1 #速度过高

FW_VERSION = '' #固件版本
MP_VERSION = '' #量产工具版本
FLASH_ID = ''  #flash的ID
FLASH_NAME = ''  #flash的型号
FLASH_MCU = '' #主控版本号
TEST_CASE = '' #测试Case，又名量产模式，低格1，低格2，高格等
TEST_TIME = '' #测试时间
MP_FILE_PATH = '' #量产数据路径

def Run(curpath, workBook, alignment):
    ws = workBook['MP_Function']
    ws.alignment = alignment

    global TEST_TIME
    TEST_TIME = PublicFuc.GetDate()

    global MP_FILE_PATH
    MP_FILE_PATH = curpath + '\\MANUAL_MP'

    ProDetail(curpath, ws)
    ProSummary(curpath, ws)
    PublicFuc.WriteReportTime(ws,'AA',27)
    PublicFuc.WriteReportOperator(ws,'AD',27)
    FillTableHeaderInfo(ws)
    #ProEntry(workBook) #必须放在最后位置，不能提前到ProDetail前
    
dataDicIndexByFile = {} #按照文件组织起来的字典
listAllSampleResultData = [] #所有样本量产原始数据
mp_column_name_map = {} #量产结果列映射
h2_column_name_map = {} #H2结果列映射
h5_column_name_map = {} #H5结果列映射
dataDicIndexBySample = {} #按照样本组织起来的字典
dicMarsData={}
conclusionDicByCap = {}#汇总表格数据字典

#g_usb_version = 'USB3' 
data_dir = 'MANUAL_MP'
data_h5_dir = 'MANUAL_H5'
START_ROW = 30
CONCLUSION_DATA_START_ROW_NO = 2
CONCLUSION_DATA_START_COLUMN_NO = 10
MPTotalTime = 0
H2_CASE_EXISTS = False
H2_2_CASE_EXISTS = False
H5_CASE_EXISTS = False
COPYFILE_CASE_EXISTS = False
PRECHECK_CASE_EXISTS = False

titleFill = PatternFill('solid')

#填充表头信息
def FillTableHeaderInfo(worksheet):
    worksheet['A4'] = FLASH_MCU
    worksheet['B4'] = MP_VERSION
    worksheet['C4'] = FLASH_NAME
    worksheet['D4'] = FLASH_ID 
    worksheet['E4'] = TEST_CASE
    mpFilePath = MP_FILE_PATH
    mpFilePathModfied = PublicFuc.modfiyPathName(mpFilePath)
    worksheet['F4'] = mpFilePathModfied
    worksheet['F4'].hyperlink = mpFilePathModfied
    worksheet['F4'].style = "Hyperlink"
    worksheet['F4'].alignment = Alignment(wrapText=True)
    worksheet['G4'] = TEST_TIME

    worksheet['C27'] = '量产版本：' + MP_VERSION
    worksheet['C28'] = '开卡模式（测试Case）：' + TEST_CASE

#填写封面
def ProEntry(workBook):
    ws = workBook['Full Test Reports']
    totalMPSampleCnt = len(dataDicIndexBySample)
    ws['J16'] = totalMPSampleCnt
    ws['K16'] = MPTotalTime
    totalH2SampleCnt = GetTestedH2Cnt()
    anyH2Time = GetAnyH2TestTime()
    ws['J18'] = totalH2SampleCnt
    ws['K18'] = anyH2Time
    totalCopyFileSampleCnt = GetTestedCopyFileCnt()
    anyCopyFileTime = GetAnyCopyFileTestTime()
    ws['J19'] = totalCopyFileSampleCnt
    ws['K19'] = anyCopyFileTime

#获取所有的H2测试数量
def GetTestedH2Cnt():
    cnt = 0
    for key in dataDicIndexBySample:
        if 'H2' in dataDicIndexBySample[key]:
            cnt += 1
    return cnt

#获取H2任意片测试的时间
def GetAnyH2TestTime():
    h2Time = ''
    for key in dataDicIndexBySample:
        if 'H2' in dataDicIndexBySample[key]:
            h2Time = dataDicIndexBySample[key]['H2'][2] #注意调用时机，注意位置处是否是想要的数据
            break
    return h2Time

#获取所有的CopyFile测试数量
def GetTestedCopyFileCnt():
    cnt = 0
    for key in dataDicIndexBySample:
        if 'MARS_COPYFILE' in dataDicIndexBySample[key]:
            cnt += 1
    return cnt

#获取CopyFile任意片测试的时间
def GetAnyCopyFileTestTime():
    copyFileTime = ''
    for key in dataDicIndexBySample:
        if 'MARS_COPYFILE' in dataDicIndexBySample[key]:
            copyFileTime = dataDicIndexBySample[key]['MARS_COPYFILE'][-1] #注意调用时机，注意位置处是否是想要的数据
            break
    return copyFileTime

#获取量产的时间
def GetMPTotalTime(_dataDicIndexByFile):
    totalSecond = 0
    for fileIdx in _dataDicIndexByFile:
        fileDic = _dataDicIndexByFile[fileIdx]
        maxTimeSecond = 0
        for dataRow in fileDic:
            timeStr = dataRow[mp_column_name_map['量产耗时']]
            timeValue = timeStr.split(':')
            timeSecond = int(timeValue[0])*60 + int(timeValue[1])
            if timeSecond > maxTimeSecond:
                maxTimeSecond = timeSecond
        totalSecond += maxTimeSecond

    hour = int(totalSecond/3600)
    lefSeconds = totalSecond%3600
    minutes = int(lefSeconds/60)
    seconds = lefSeconds%60
    timeStr = '%d:%d:%d'%(hour,minutes,seconds)
    return timeStr                
        

def ProDetail(curpath, worksheet):
    #MP数据收集
    pattern = '.+\\\\'+data_dir+'\\\\.*(?i)MP.*\.csv$'
    #从原始日志中读取测试结果到字典中，字典的key是简单的递增顺序数，也可代表文件的序号，内容为量产工具每次启动测试的一次结果。
    mpCsvHeader = ReadCsvData(curpath,pattern,dataDicIndexByFile)
    #初始化原始日志csv文件中各种信息的列号对应关系
    InitMPCsvColumnNameMap(mp_column_name_map,mpCsvHeader)

    #得到MP的叠加时间
    #global MPTotalTime
    #MPTotalTime = GetMPTotalTime(dataDicIndexByFile)

    #合并所有的测试数据到一个list中,得到最新测试结果文件的结果
    MergeAllSampleData(dataDicIndexByFile,listAllSampleResultData)
    InitFillMpDataToDic(listAllSampleResultData)
    NormalizeMPData()
    cnt = len(dataDicIndexBySample)
    #H2数据收集
    #DUT测试的H2结果数据收集
    pattern = '.+\\\\Plan22\\\\T_GE_SD_C7\\\\H2_1\\\\.+.csv$'
    #pattern = '.+\\\\'+data_dir+'\\\\.+\\\\Plan22\\\\T_GE_SD_C7\\\\H2_1\\\\\D+_\d{14}.csv$'
    #初始化原始日志csv文件中各种信息的列号对应关系
    InitH2CsvColumnNameMap(h2_column_name_map)
    #从原始日志中读取测试结果到字典中，字典的key是简单的递增顺序数，也可代表文件的序号，内容为量产工具每次启动测试的一次结果。
    dataDicIndexByFile.clear()
    ReadCsvData(curpath,pattern,dataDicIndexByFile)
    #合并所有的测试数据到一个list中
    listAllSampleResultData.clear()
    MergeAllSampleData(dataDicIndexByFile,listAllSampleResultData)
    InitFillH2DataToDic(listAllSampleResultData,'H2')
    
    pattern = '.+\\\\Plan39\\\\T_GE_SD_C7\\\\H2_1\\\\.+.csv$'
    dataDicIndexByFile.clear()
    ReadCsvData(curpath,pattern,dataDicIndexByFile)
    #合并所有的测试数据到一个list中
    listAllSampleResultData.clear()
    MergeAllSampleData(dataDicIndexByFile,listAllSampleResultData)
    InitFillH2DataToDic(listAllSampleResultData,'H2')
    NormalizeDutH2Data('H2')
    cnt = len(dataDicIndexBySample)

    #收集第二次的H2测试，部分测试Plan增加了2次H2测试
    pattern = '.+\\\\Plan22\\\\T_GE_SD_C7\\\\H2_2\\\\.+.csv$'
    dataDicIndexByFile.clear()
    ReadCsvData(curpath,pattern,dataDicIndexByFile)
    #合并所有的测试数据到一个list中
    listAllSampleResultData.clear()
    MergeAllSampleData(dataDicIndexByFile,listAllSampleResultData)
    InitFillH2DataToDic(listAllSampleResultData,'H2_2')
    NormalizeDutH2Data('H2_2')

    #合并整理H2数据
    CombineAndNormalizeAllTypeH2Data('H2')

    #合并整理H2数据
    CombineAndNormalizeAllTypeH2Data('H2_2')

    #CopyFile数据收集
    dicMarsData.clear()
    #得到plan22和plan23的数据
    pattern = '.+\\\\Plan22\\\\T_GE_SD_C8\\\\Mars_文件拷贝\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    marsCopyKey = ['TEST_RESULT','START_TIME','END_TIME']
    ReadMarsIniDataLocal(curpath, pattern, dicMarsData, 'AT_CopyFile', 'MARS_COPYFILE', marsCopyKey, '',0)
    cnt = len(dataDicIndexBySample)

    #合并数据
    CombineAndNormalizeCopyFileData()

    #预检测数据
    dicMarsData.clear()
    pattern = '.+\\\\Plan22\\\\T_GE_SD_C63\\\\Mars_DiskPreCheck\\\\\d{14}\\\\.+\\\\report\\\\mms.+.ini$'
    marsCopyKey = ['TEST_RESULT']
    ReadMarsIniDataLocal(curpath, pattern, dicMarsData, 'DiskPreCheck', 'MARS_PRECHECK', marsCopyKey, '',0)

    CombineAndNormalizePreCheckData()


    AnalyzeH2Result('H2')
    AnalyzeH2Result('H2_2')
    AnalyzeCopyFileResult()
    cnt = len(dataDicIndexBySample)

    # H5Log数据收集
    pattern = '.+\\\\' + data_h5_dir + '\\\\.+.csv$'
    InitH5CsvColumnNameMap(h5_column_name_map)
    dataDicIndexByFile.clear()
    ReadCsvDataEx(curpath, pattern, dataDicIndexByFile)
    listAllSampleResultData.clear()
    MergeAllSampleData(dataDicIndexByFile, listAllSampleResultData)
    InitFillH5DataToDic(listAllSampleResultData, 'H5')

    #直接生成详细数据，填充详细数据
    startLine = START_ROW #详细数据起始行

    InitReportTemplateInWorkSheet(worksheet)
    
    WriteDetailResult2WorkSheet(worksheet,startLine,dataDicIndexBySample)

def ProSummary(curpath, worksheet):
    #获取详细数据
    GetEmptyConclusionDicIndexByCap()
    InitSummaryData()
    #CalcRatioOfConclusionDic() 率由报告模板自动计算。
    WriteConclusionData2WorkSheet(worksheet)

#按照实际模式数量生成汇总表格
def InitConlusionReportTemplateInWorkSheet(worksheet):
    CapColumnCnt = len(conclusionDicByCap)

    #填充标题
    rawkeyList = conclusionDicByCap.keys()
    keyList = sorted(rawkeyList,key=PreProcessCapacityKey,reverse=True)
    i = 0
    for cap in keyList:
        tmpResult = conclusionDicByCap[cap]
        str = '%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+i*4), CONCLUSION_DATA_START_ROW_NO)
        worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+i*4), CONCLUSION_DATA_START_ROW_NO)] = cap
        i += 1
        if i >= 3:
            break #只填3种具体容量，其它容量按低容处理

    #填充内容
    spRowList = [9, 13, 17, 21, 25]
    curRow = CONCLUSION_DATA_START_ROW_NO + 2
    while curRow < 26:
        curCol = CONCLUSION_DATA_START_COLUMN_NO
        while curCol < 30:
            if curCol < 26:
                if curRow in spRowList:
                    worksheet['%s%d' % (get_column_letter(curCol), curRow)] = '/'
                    curCol += 4
                else:
                    worksheet['%s%d' % (get_column_letter(curCol), curRow)] = '/'
                    curCol += 1
            else:
                if curRow < 6:
                    worksheet['%s%d' % (get_column_letter(curCol), curRow)] = '/'
                    curCol += 1
                elif curCol == 26 and curRow%2 == 1 or curRow in spRowList:
                    curCol += 1
                    continue
                else:
                    worksheet['%s%d' % (get_column_letter(curCol), curRow)] = '/'
                    curCol += 1
        curRow += 1


def PreProcessCapacityKey(_rawKey):
        newKey = 0
        if 'M' in _rawKey:
            newKey = _rawKey[:_rawKey.find('M')]
        return int(newKey)

def WriteConclusionData2WorkSheet(worksheet):
    #绘制excel模板
    InitConlusionReportTemplateInWorkSheet(worksheet)
    CapColumnCnt = len(conclusionDicByCap)
    #capIdx = 0
    rawkeyList = conclusionDicByCap.keys()
    keyList = sorted(rawkeyList,key=PreProcessCapacityKey,reverse=True)
    if len(keyList) == 0:
        return
    
    lowCapDic = {} #低容数据
    lowCapDic['MP'] = {'6P':[0]*2,'4P':[0]*2,'2P':[0]*2,'1P':[0]*2}
    lowCapDic['PRE_CHECK'] = {'6P':[0]*2,'4P':[0]*2,'2P':[0]*2,'1P':[0]*2}
    lowCapDic['H2'] = {'6P':[0]*2,'4P':[0]*2,'2P':[0]*2,'1P':[0]*2}
    lowCapDic['H2_2'] = {'6P':[0]*2,'4P':[0]*2,'2P':[0]*2,'1P':[0]*2}
    lowCapDic['COPY_FILE'] ={'6P':[0]*2,'4P':[0]*2,'2P':[0]*2,'1P':[0]*2}
    lowCapDic['H5'] = {'6P':[0]*2,'4P':[0]*2,'2P':[0]*2,'1P':[0]*2}
    for capIdx,cap in enumerate(keyList):
        if capIdx < 3:
            #填MP数据
            childCapDicMP = conclusionDicByCap[cap]['MP']
            #写'B6PT''B4PT''B2PT''B1PT'
            for i,val in enumerate(childCapDicMP['6P']):
                worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + capIdx*4), CONCLUSION_DATA_START_ROW_NO+2 + i)] = val
                break #不填比例
            for i,val in enumerate(childCapDicMP['4P']):
                worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + capIdx*4 + 1), CONCLUSION_DATA_START_ROW_NO+2 + i)] = val
                break #不填比例
            for i,val in enumerate(childCapDicMP['2P']):
                worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + capIdx*4 + 2), CONCLUSION_DATA_START_ROW_NO+2 + i)] = val
                break #不填比例
            for i,val in enumerate(childCapDicMP['1P']):
                worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + capIdx*4 + 3), CONCLUSION_DATA_START_ROW_NO+2 + i)] = val
                break #不填比例

            #填PRECHECK数据
            if PRECHECK_CASE_EXISTS:
                childCapDicPreCheck = conclusionDicByCap[cap]['PRE_CHECK']
                WriteCommonConclusionData2WorkSheet(worksheet, capIdx, childCapDicPreCheck, 8)

            #填H2数据
            if H2_CASE_EXISTS:
                childCapDicH2 = conclusionDicByCap[cap]['H2']
                WriteCommonConclusionData2WorkSheet(worksheet, capIdx, childCapDicH2, 12)

            #填H2_2数据
            if H2_2_CASE_EXISTS:
                childCapDicH2_2 = conclusionDicByCap[cap]['H2_2']
                WriteCommonConclusionData2WorkSheet(worksheet, capIdx, childCapDicH2_2, 16)

            #填CopyFile数据
            if COPYFILE_CASE_EXISTS:
                childCapDicCopyfile = conclusionDicByCap[cap]['COPY_FILE']
                WriteCommonConclusionData2WorkSheet(worksheet, capIdx, childCapDicCopyfile, 20)

            #填H5数据
            if H5_CASE_EXISTS:
                childCapDicH5 = conclusionDicByCap[cap]['H5']
                WriteCommonConclusionData2WorkSheet(worksheet, capIdx, childCapDicH5, 4)

        else:
            #第四种容量及其更低的容量汇总到低容中。
            #汇总MP数据
            childCapDicMP = conclusionDicByCap[cap]['MP']
            GetLowCapDic(lowCapDic, childCapDicMP, 'MP')

            #汇总PreCheck数据
            childCapDicPreCheck = conclusionDicByCap[cap]['PRE_CHECK']
            GetLowCapDic(lowCapDic, childCapDicPreCheck, 'PRE_CHECK')

            #汇总H2数据
            childCapDicH2 = conclusionDicByCap[cap]['H2']
            GetLowCapDic(lowCapDic, childCapDicH2, 'H2')

            #汇总H2_2数据
            childCapDicH2_2 = conclusionDicByCap[cap]['H2_2']
            GetLowCapDic(lowCapDic, childCapDicH2_2, 'H2_2')

            #汇总CopyFile数据
            childCapDicCopyfile = conclusionDicByCap[cap]['COPY_FILE']
            GetLowCapDic(lowCapDic, childCapDicCopyfile, 'COPY_FILE')

            #汇总H5数据
            childCapDicH5 = conclusionDicByCap[cap]['H5']
            GetLowCapDic(lowCapDic, childCapDicH5, 'H5')

     
    #写低容数据
    if len(keyList) >= 4:
        #一定有低容数据
        #填MP数据
        childCapDicMP = lowCapDic['MP']
        WriteCommonConclusionData2WorkSheetEx(worksheet, childCapDicMP, 2)

        #填PRECHECK数据
        if PRECHECK_CASE_EXISTS:
            childCapDicPreCheck = lowCapDic['PRE_CHECK']
            WriteCommonConclusionData2WorkSheetEx(worksheet, childCapDicPreCheck, 8)

        #填H2数据
        if H2_CASE_EXISTS:
            childCapDicH2 = lowCapDic['H2']
            WriteCommonConclusionData2WorkSheetEx(worksheet, childCapDicH2, 12)

        #填H2_2数据
        if H2_2_CASE_EXISTS:
            childCapDicH2_2 = lowCapDic['H2_2']
            WriteCommonConclusionData2WorkSheetEx(worksheet, childCapDicH2_2, 16)

        #填CopyFile数据
        if COPYFILE_CASE_EXISTS:
            childCapDicCopyfile = lowCapDic['COPY_FILE']
            WriteCommonConclusionData2WorkSheetEx(worksheet, childCapDicCopyfile, 20)

        #填H5数据
        if H5_CASE_EXISTS:
            childCapDicH5 = lowCapDic['H5']
            WriteCommonConclusionData2WorkSheetEx(worksheet, childCapDicH5, 4)

    #写不良率列
    totalMpCnt = GetAllMPCnt()
    totalPreCheckCnt = GetAllCnt('PRE_CHECK')
    totalH2Cnt = GetAllCnt('H2')
    totalH2_2Cnt = GetAllCnt('H2_2')
    totalCopyfileCnt = GetAllCnt('COPY_FILE')
    totalH5Cnt = GetAllCnt('H5')

    totalPassMpCnt = GetAllPassMPCnt()
    totalPassPreCheckCnt = GetAllPassCnt('PRE_CHECK')
    totalPassH2Cnt = GetAllPassCnt('H2')
    totalPassH2_2Cnt = GetAllPassCnt('H2_2')
    totalPassCopyfileCnt = GetAllPassCnt('COPY_FILE')
    totalPassH5Cnt = GetAllPassCnt('H5')

    totalFailMpCnt = totalMpCnt - totalPassMpCnt
    totalFailPreCheckCnt = totalPreCheckCnt - totalPassPreCheckCnt
    totalFailH2Cnt = totalH2Cnt - totalPassH2Cnt
    totalFailH2_2Cnt = totalH2_2Cnt - totalPassH2_2Cnt
    totalFailCopyfileCnt = totalCopyfileCnt - totalPassCopyfileCnt
    totalFailH5Cnt = totalH5Cnt - totalPassH5Cnt

    worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 3), CONCLUSION_DATA_START_ROW_NO + 2)] = totalMpCnt
    worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO+ 4*4), CONCLUSION_DATA_START_ROW_NO+2)] = totalFailMpCnt
    worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 1), CONCLUSION_DATA_START_ROW_NO + 2)] = GetAllBlockPassMPCnt()
    worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 2), CONCLUSION_DATA_START_ROW_NO + 2)] = GetAllPagePassMPCnt()

    if PRECHECK_CASE_EXISTS:
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 1), CONCLUSION_DATA_START_ROW_NO + 2 + 6)] = GetAllCnt('PRE_CHECK_B')
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 2), CONCLUSION_DATA_START_ROW_NO + 2 + 6)] = GetAllCnt('PRE_CHECK_P')
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 3), CONCLUSION_DATA_START_ROW_NO + 2 + 6)] = totalPreCheckCnt
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4), CONCLUSION_DATA_START_ROW_NO + 2 + 6)] = totalFailPreCheckCnt
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 1), CONCLUSION_DATA_START_ROW_NO + 2 + 6 + 1)] = GetAllPassCnt('PRE_CHECK_B')
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 2), CONCLUSION_DATA_START_ROW_NO + 2 + 6 + 1)] = GetAllPassCnt('PRE_CHECK_P')
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 3), CONCLUSION_DATA_START_ROW_NO + 2 + 6 + 1)] = totalPassPreCheckCnt

    if H2_CASE_EXISTS:
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 1), CONCLUSION_DATA_START_ROW_NO + 2 + 6 + 4)] = GetAllCnt('H2_B')
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 2), CONCLUSION_DATA_START_ROW_NO + 2 + 6 + 4)] = GetAllCnt('H2_P')
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 3), CONCLUSION_DATA_START_ROW_NO + 2 + 6 + 4)] = totalH2Cnt
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4), CONCLUSION_DATA_START_ROW_NO + 2 + 6 + 4)] = totalFailH2Cnt
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 1), CONCLUSION_DATA_START_ROW_NO + 2 + 6 + 4 + 1)] = GetAllPassCnt('H2_B')
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 2), CONCLUSION_DATA_START_ROW_NO + 2 + 6 + 4 + 1)] = GetAllPassCnt('H2_P')
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 3), CONCLUSION_DATA_START_ROW_NO + 2 + 6 + 4 + 1)] = totalPassH2Cnt

    if H2_2_CASE_EXISTS:
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 1), CONCLUSION_DATA_START_ROW_NO + 2 + 6 + 8)] = GetAllCnt('H2_2_B')
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 2), CONCLUSION_DATA_START_ROW_NO + 2 + 6 + 8)] = GetAllCnt('H2_2_P')
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 3), CONCLUSION_DATA_START_ROW_NO + 2 + 6 + 8)] = totalH2_2Cnt
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4), CONCLUSION_DATA_START_ROW_NO + 2 + 6 + 8)] = totalFailH2_2Cnt
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 1), CONCLUSION_DATA_START_ROW_NO + 2 + 6 + 8 + 1)] = GetAllPassCnt('H2_2_B')
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 2), CONCLUSION_DATA_START_ROW_NO + 2 + 6 + 8 + 1)] = GetAllPassCnt('H2_2_P')
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 3), CONCLUSION_DATA_START_ROW_NO + 2 + 6 + 8 + 1)] = totalPassH2_2Cnt

    if COPYFILE_CASE_EXISTS:
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 1), CONCLUSION_DATA_START_ROW_NO + 2 + 6 + 12)] = GetAllCnt('COPY_FILE_B')
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 2), CONCLUSION_DATA_START_ROW_NO + 2 + 6 + 12)] = GetAllCnt('COPY_FILE_P')
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 3), CONCLUSION_DATA_START_ROW_NO + 2 + 6 + 12)] = totalCopyfileCnt
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4), CONCLUSION_DATA_START_ROW_NO + 2 + 6 + 12)] = totalFailCopyfileCnt
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 1), CONCLUSION_DATA_START_ROW_NO + 2 + 6 + 12 + 1)] = GetAllPassCnt('COPY_FILE_B')
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 2), CONCLUSION_DATA_START_ROW_NO + 2 + 6 + 12 + 1)] = GetAllPassCnt('COPY_FILE_P')
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 3), CONCLUSION_DATA_START_ROW_NO + 2 + 6 + 12 + 1)] = totalPassCopyfileCnt

    if H5_CASE_EXISTS:
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 1), CONCLUSION_DATA_START_ROW_NO + 2 + 2)] = GetAllCnt('H5_B')
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 2), CONCLUSION_DATA_START_ROW_NO + 2 + 2)] = GetAllCnt('H5_P')
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 3), CONCLUSION_DATA_START_ROW_NO + 2 + 2)] = totalH5Cnt
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4), CONCLUSION_DATA_START_ROW_NO + 2 + 2)] = totalFailH5Cnt
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 1), CONCLUSION_DATA_START_ROW_NO + 2 + 2 + 1)] = GetAllPassCnt('H5_B')
        worksheet['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 4 * 4 + 2), CONCLUSION_DATA_START_ROW_NO + 2 + 2 + 1)] = GetAllPassCnt('H5_P')

    #量产比例
    if totalMpCnt > 0:
        for col in range(10, 29):
            if worksheet['%s%d'%(get_column_letter(col), CONCLUSION_DATA_START_ROW_NO+2)].value != '/':
                worksheet['%s%d'%(get_column_letter(col), CONCLUSION_DATA_START_ROW_NO+2+1)] = "%.2f" % (float(worksheet['%s%d'%(get_column_letter(col), CONCLUSION_DATA_START_ROW_NO+2)].value)/totalMpCnt*100) + '%'
    #BIN级良率
    for row in range(8, 28, 4):
        for col in range(10, 26):
            if worksheet['%s%d'%(get_column_letter(col), row-2)].value != '/':
                if worksheet['%s%d'%(get_column_letter(col), row-2)].value == 0:
                    worksheet['%s%d'%(get_column_letter(col), row)] = '0.00%'
                else:
                    worksheet['%s%d'%(get_column_letter(col), row)] = "%.2f" % (float(worksheet['%s%d'%(get_column_letter(col), row-1)].value)/float(worksheet['%s%d'%(get_column_letter(col), row-2)].value)*100) + '%'

    #容量段良率
    for row in range(9, 29, 4):
        for col in range(10, 26, 4):
            if worksheet['%s%d' % (get_column_letter(col), row-3)].value != '/':
                totalCnt = worksheet['%s%d' % (get_column_letter(col), row-3)].value + worksheet['%s%d' % (get_column_letter(col+1), row-3)].value + worksheet['%s%d' % (get_column_letter(col+2), row-3)].value + worksheet['%s%d' % (get_column_letter(col+3), row-3)].value
                totalPassCnt = worksheet['%s%d' % (get_column_letter(col), row-2)].value + worksheet['%s%d' % (get_column_letter(col+1), row-2)].value + worksheet['%s%d' % (get_column_letter(col+2), row-2)].value + worksheet['%s%d' % (get_column_letter(col+3), row-2)].value
                if totalCnt == 0:
                    worksheet['%s%d' % (get_column_letter(col), row)] = '0.00%'
                else:
                    worksheet['%s%d' % (get_column_letter(col), row)] = "%.2f" % float(totalPassCnt/totalCnt*100) + '%'

    # #汇总补充
    worksheet['%s%d' % (get_column_letter(29), 5)] = "%.2f" % ((1 - float(worksheet['%s%d' % (get_column_letter(26), 5)].value[:-1]) / 100) * 100) + '%'
    for row in range(8, 28, 4):
        if worksheet['%s%d' % (get_column_letter(26), row-2)].value != '/':
            if worksheet['%s%d' % (get_column_letter(29), row-2)].value != 0:
                worksheet['%s%d' % (get_column_letter(26), row)] = "%.2f" % (float(worksheet['%s%d' % (get_column_letter(26), row-2)].value)/float(worksheet['%s%d' % (get_column_letter(29), row-2)].value)*100) + '%'
            else:
                worksheet['%s%d' % (get_column_letter(26), row)] = '0.00%'
        if worksheet['%s%d' % (get_column_letter(27), row-2)].value != '/':
            if worksheet['%s%d' % (get_column_letter(27), row-2)].value == 0:
                worksheet['%s%d' % (get_column_letter(27), row)] = '0.00%'
            else:
                worksheet['%s%d' % (get_column_letter(27), row)] = "%.2f" % (float(worksheet['%s%d' % (get_column_letter(27), row-1)].value)/float(worksheet['%s%d' % (get_column_letter(27), row-2)].value)*100) + '%'
        if worksheet['%s%d' % (get_column_letter(28), row - 2)].value != '/':
            if worksheet['%s%d' % (get_column_letter(28), row - 2)].value == 0:
                worksheet['%s%d' % (get_column_letter(28), row)] = '0.00%'
            else:
                worksheet['%s%d' % (get_column_letter(28), row)] = "%.2f" %(float(worksheet['%s%d' % (get_column_letter(28), row - 1)].value)/float(worksheet['%s%d' % (get_column_letter(28), row - 2)].value) * 100) + '%'
        if worksheet['%s%d' % (get_column_letter(26), row)].value != '/':
            worksheet['%s%d' % (get_column_letter(29), row)] = "%.2f" %((1-float(worksheet['%s%d' % (get_column_letter(26), row)].value[:-1])/100)*100) + '%'

    #写错误的样品编号
    #strAllMPErrSampleNo,strAllH2ErrSampleNo,strAllCopyfileErrSampleNo = GetAllErrSampleNo()
    #worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + CapColumnCnt*3 + 2), CONCLUSION_DATA_START_ROW_NO+2)] = strAllMPErrSampleNo
    #worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + CapColumnCnt*3 + 2), CONCLUSION_DATA_START_ROW_NO+2+2)] = strAllH2ErrSampleNo
    #worksheet['%s%d'%(get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + CapColumnCnt*3 + 2), CONCLUSION_DATA_START_ROW_NO+2+2+3)] = strAllCopyfileErrSampleNo

def  WriteCommonConclusionData2WorkSheet(ws, capIdx, childCapDic, rep):
    # 写'B6PT''B4PT''B2PT''B1PT'
    for i, val in enumerate(childCapDic['6P']):
        ws['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + capIdx * 4), CONCLUSION_DATA_START_ROW_NO + rep + i)] = val
        if i > 0:
            break  # 不填比例
    for i, val in enumerate(childCapDic['4P']):
        ws['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + capIdx * 4 + 1),  CONCLUSION_DATA_START_ROW_NO + rep + i)] = val
        if i > 0:
            break  # 不填比例
    for i, val in enumerate(childCapDic['2P']):
        ws['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + capIdx * 4 + 2), CONCLUSION_DATA_START_ROW_NO + rep + i)] = val
        if i > 0:
            break  # 不填比例
    for i, val in enumerate(childCapDic['1P']):
        ws['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + capIdx * 4 + 3), CONCLUSION_DATA_START_ROW_NO + rep + i)] = val
        if i > 0:
            break  # 不填比例

def  WriteCommonConclusionData2WorkSheetEx(ws, childCapDic, rep):
    # 写'B6PT''B4PT''B2PT''B1PT'
    for i, val in enumerate(childCapDic['6P']):
        ws['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 3 * 4), CONCLUSION_DATA_START_ROW_NO + rep + i)] = val
        if i > 0:
            break  # 不填比例
    for i, val in enumerate(childCapDic['4P']):
        ws['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 3 * 4 + 1), CONCLUSION_DATA_START_ROW_NO + rep + i)] = val
        if i > 0:
            break  # 不填比例
    for i, val in enumerate(childCapDic['2P']):
        ws['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 3 * 4 + 2), CONCLUSION_DATA_START_ROW_NO + rep + i)] = val
        if i > 0:
            break  # 不填比例
    for i, val in enumerate(childCapDic['1P']):
        ws['%s%d' % (get_column_letter(CONCLUSION_DATA_START_COLUMN_NO + 3 * 4 + 3), CONCLUSION_DATA_START_ROW_NO + rep + i)] = val
        if i > 0:
            break  # 不填比例

def GetLowCapDic(lowCapDic, childCap, idx):
    for i, val in enumerate(childCap['6P']):
        lowCapDic[idx]['6P'][i] += val
    for i, val in enumerate(childCap['4P']):
        lowCapDic[idx]['4P'][i] += val
    for i, val in enumerate(childCap['2P']):
        lowCapDic[idx]['2P'][i] += val
    for i, val in enumerate(childCap['1P']):
        lowCapDic[idx]['1P'][i] += val

#获取所有的错误样本编号
def GetAllErrSampleNoOldVersion():
    strSampleNoSet = ''
    for sampleNo in dataDicIndexBySample:
        if 'H2' in dataDicIndexBySample[sampleNo]:
            if dataDicIndexBySample[sampleNo]['H2'][-3] != 'PASS':
                strSampleNoSet += ','
                strSampleNoSet += sampleNo
                continue
        if 'MARS_COPYFILE' in dataDicIndexBySample[sampleNo]:
            if dataDicIndexBySample[sampleNo]['MARS_COPYFILE'][0] != 'PASS':
                strSampleNoSet += ','
                strSampleNoSet += sampleNo
    return strSampleNoSet

#获取所有的错误样本编号
def GetAllErrSampleNo():
    strMpErrSampleNoSet = ''
    strH2ErrSampleNoSet = ''
    strCopyfileErrSampleNoSet = ''
    
    for sampleNo in dataDicIndexBySample:
        if 'MP' in dataDicIndexBySample[sampleNo]:
            if dataDicIndexBySample[sampleNo]['MP'][0].upper() != '0XFA0':
                strMpErrSampleNoSet += sampleNo
                strMpErrSampleNoSet += ','
        if 'H2' in dataDicIndexBySample[sampleNo]:
            if dataDicIndexBySample[sampleNo]['H2'][-3] != 'PASS':
                strH2ErrSampleNoSet += sampleNo
                strH2ErrSampleNoSet += ','
        if 'MARS_COPYFILE' in dataDicIndexBySample[sampleNo]:
            if dataDicIndexBySample[sampleNo]['MARS_COPYFILE'][0] != 'PASS':
                strCopyfileErrSampleNoSet += sampleNo
                strCopyfileErrSampleNoSet += ','

    if strMpErrSampleNoSet != '':
        strMpErrSampleNoSet = strMpErrSampleNoSet[:-1]
    if strH2ErrSampleNoSet != '':
        strH2ErrSampleNoSet = strH2ErrSampleNoSet[:-1]
    if strCopyfileErrSampleNoSet != '':
        strCopyfileErrSampleNoSet = strCopyfileErrSampleNoSet[:-1]
    return strMpErrSampleNoSet,strH2ErrSampleNoSet,strCopyfileErrSampleNoSet

#获取总的量产数量
def GetAllMPCnt():
    return len(dataDicIndexBySample)

#获取总的量产通过的数量
def GetAllPassMPCnt():
    totalMpCnt = 0
    for cap in conclusionDicByCap:
        for mp_mode in conclusionDicByCap[cap]['MP']:
            totalMpCnt += conclusionDicByCap[cap]['MP'][mp_mode][0]

    return totalMpCnt

#获取总block模式的总数量
def GetAllBlockMPCnt():
    totalMpCnt = 0
    for sampleNo in dataDicIndexBySample:
        if 'MP' not in dataDicIndexBySample[sampleNo]:
            continue #量产数据都不在，也没有容量信息，无法进行匹配
        mpMode = dataDicIndexBySample[sampleNo]['MP'][3].upper()
        if IsModeBlockValid(mpMode):
            totalMpCnt += 1
    return totalMpCnt

#获取量产通过总block模式的总数量
def GetAllBlockPassMPCnt():
    totalMpCnt = 0
    for cap in conclusionDicByCap:
        for mp_mode in conclusionDicByCap[cap]['MP_B']:
            totalMpCnt += conclusionDicByCap[cap]['MP_B'][mp_mode][0]

    return totalMpCnt

#获取总PAGE模式的总数量
def GetAllPageMPCnt():
    totalMpCnt = 0
    for sampleNo in dataDicIndexBySample:
        if 'MP' not in dataDicIndexBySample[sampleNo]:
            continue #量产数据都不在，也没有容量信息，无法进行匹配
        mpMode = dataDicIndexBySample[sampleNo]['MP'][3].upper()
        if IsModeBlockValid(mpMode):
            totalMpCnt += 1
    return totalMpCnt

#获取量产通过总Page模式的总数量
def GetAllPagePassMPCnt():
    totalMpCnt = 0
    for cap in conclusionDicByCap:
        for mp_mode in conclusionDicByCap[cap]['MP_P']:
            totalMpCnt += conclusionDicByCap[cap]['MP_P'][mp_mode][0]

    return totalMpCnt

#获取总的数量
def GetAllCnt(caseName):
    totalCnt = 0
    for cap in conclusionDicByCap:
        curCnt = conclusionDicByCap[cap][caseName]
        for idx in curCnt:
            totalCnt += curCnt[idx][0]
    return totalCnt

#获取总的成功的数量
def GetAllPassCnt(caseName):
    totalCnt = 0
    for cap in conclusionDicByCap:
        curCnt = conclusionDicByCap[cap][caseName]
        for idx in curCnt:
            totalCnt +=curCnt[idx][1]
    return totalCnt

#获取总的BLOCK模式H2数量
def GetAllBlockH2Cnt():
    totalH2 = 0
    for cap in conclusionDicByCap:
        curH2Cnt = conclusionDicByCap[cap]['H2_B'][0]
        totalH2 += curH2Cnt
    return totalH2

#获取总的PASS的BLOCK模式H2成功的数量
def GetAllBlockPassH2Cnt():
    totalH2 = 0
    for cap in conclusionDicByCap:
        curH2Cnt = conclusionDicByCap[cap]['H2_B'][1]
        totalH2 += curH2Cnt
    return totalH2

#获取总的Page模式H2数量
def GetAllPageH2Cnt():
    totalH2 = 0
    for cap in conclusionDicByCap:
        curH2Cnt = conclusionDicByCap[cap]['H2_P'][0]
        totalH2 += curH2Cnt
    return totalH2

#获取总的PASS的Page模式H2成功的数量
def GetAllPagePassH2Cnt():
    totalH2 = 0
    for cap in conclusionDicByCap:
        curH2Cnt = conclusionDicByCap[cap]['H2_P'][1]
        totalH2 += curH2Cnt
    return totalH2

#获取总的CopyFile数量
def GetAllCopyFileCnt():
    totalCopyFile = 0
    for cap in conclusionDicByCap:
        curCopyFile = conclusionDicByCap[cap]['COPY_FILE'][0]
        totalCopyFile += curCopyFile
    return totalCopyFile

#获取总的CopyFile成功的数量
def GetAllPassCopyFileCnt():
    totalCopyFile = 0
    for cap in conclusionDicByCap:
        curCopyFile = conclusionDicByCap[cap]['COPY_FILE'][1]
        totalCopyFile += curCopyFile
    return totalCopyFile

#获取总Block的CopyFile数量
def GetAllBlockCopyFileCnt():
    totalCopyFile = 0
    for cap in conclusionDicByCap:
        curCopyFile = conclusionDicByCap[cap]['COPY_FILE_B'][0]
        totalCopyFile += curCopyFile
    return totalCopyFile

#获取总Block的CopyFile成功的数量
def GetAllBlockPassCopyFileCnt():
    totalCopyFile = 0
    for cap in conclusionDicByCap:
        curCopyFile = conclusionDicByCap[cap]['COPY_FILE_B'][1]
        totalCopyFile += curCopyFile
    return totalCopyFile

#获取总Page的CopyFile数量
def GetAllPageCopyFileCnt():
    totalCopyFile = 0
    for cap in conclusionDicByCap:
        curCopyFile = conclusionDicByCap[cap]['COPY_FILE_P'][0]
        totalCopyFile += curCopyFile
    return totalCopyFile

#获取总Block的CopyFile成功的数量
def GetAllPagePassCopyFileCnt():
    totalCopyFile = 0
    for cap in conclusionDicByCap:
        curCopyFile = conclusionDicByCap[cap]['COPY_FILE_P'][1]
        totalCopyFile += curCopyFile
    return totalCopyFile

#判定模式是否是我们需要统计的合法模式
def CalcRatioOfConclusionDic():
    totalMpCnt = GetAllMPCnt()
    totalH2Cnt = GetAllH2Cnt()
    totalCopyfileCnt = GetAllCopyFileCnt()
    for cap in conclusionDicByCap:
        #计算量产
        for mp_mode in conclusionDicByCap[cap]['MP']:
            curCnt = conclusionDicByCap[cap]['MP'][mp_mode][0]
            tmpRate = ''
            if totalMpCnt != 0:
                tmpRate = "%.2f%%" % (float(curCnt)*100/totalMpCnt)
            conclusionDicByCap[cap]['MP'][mp_mode][1] = tmpRate
        #计算H2
        tmpRate = ''
        if conclusionDicByCap[cap]['H2'][0] != 0:
            tmpRate = "%.2f%%" % (float(conclusionDicByCap[cap]['H2'][1])*100/float(conclusionDicByCap[cap]['H2'][0]))
        conclusionDicByCap[cap]['H2'][2] = tmpRate
        #计算CopyFile
        tmpRate = ''
        if conclusionDicByCap[cap]['COPY_FILE'][0] != 0:
            tmpRate = "%.2f%%" % (float(conclusionDicByCap[cap]['COPY_FILE'][1])*100/float(conclusionDicByCap[cap]['COPY_FILE'][0]))
        conclusionDicByCap[cap]['COPY_FILE'][2] = tmpRate

        

def InitSummaryData():
    for sampleNo in dataDicIndexBySample:
        if 'MP' not in dataDicIndexBySample[sampleNo]:
            continue #量产数据都不在，也没有容量信息，无法进行匹配
        curCap = dataDicIndexBySample[sampleNo]['MP'][1] #获取当前数据的容量
        if curCap not in conclusionDicByCap:
            continue
        mpModePrefix = ''
        mpMode = dataDicIndexBySample[sampleNo]['MP'][3].upper()
        if IsModeBlockValid(mpMode):
            errCode = dataDicIndexBySample[sampleNo]['MP'][0].upper()
            if errCode == '0XFA0':
                mpModePrefix = mpMode[0:3].upper()
                conclusionDicByCap[curCap]['MP_B'][mpModePrefix][0] += 1 #有效模式且不出错的才添加到量产中
        elif IsModePageValid(mpMode):
            errCode = dataDicIndexBySample[sampleNo]['MP'][0].upper()
            if errCode == '0XFA0':
                mpModePrefix = mpMode[0:3].upper()
                conclusionDicByCap[curCap]['MP_P'][mpModePrefix][0] += 1 #有效模式且不出错的才添加到量产中

        if 'MARS_PRECHECK' in dataDicIndexBySample[sampleNo]:
            InitSummaryDictData(sampleNo, 'MARS_PRECHECK', curCap, 'PRE_CHECK', mpMode, mpModePrefix, 0)

        if 'H2' in dataDicIndexBySample[sampleNo]:
            InitSummaryDictData(sampleNo, 'H2', curCap, 'H2', mpMode, mpModePrefix, -3)

        if 'H2_2' in dataDicIndexBySample[sampleNo]:
           InitSummaryDictData(sampleNo, 'H2_2', curCap, 'H2_2', mpMode, mpModePrefix, -3)

        if 'MARS_COPYFILE' in dataDicIndexBySample[sampleNo]:
            InitSummaryDictData(sampleNo, 'MARS_COPYFILE', curCap, 'COPY_FILE', mpMode, mpModePrefix, 0)

        if 'H5' in dataDicIndexBySample[sampleNo]:
            InitSummaryDictData(sampleNo, 'H5', curCap, 'H5', mpMode, mpModePrefix, 10)

    #得到两种合并的数据
    for curCap in conclusionDicByCap:
        DataFusion(conclusionDicByCap[curCap], 'MP')
        DataFusion(conclusionDicByCap[curCap], 'PRE_CHECK')
        DataFusion(conclusionDicByCap[curCap], 'H2')
        DataFusion(conclusionDicByCap[curCap], 'H2_2')
        DataFusion(conclusionDicByCap[curCap], 'COPY_FILE')
        DataFusion(conclusionDicByCap[curCap], 'H5')

def InitSummaryDictData(sampleNo, idx_d, curCap, idx_c, mpMode, mpModePrefix, id_l):
    if mpModePrefix == '':
        return
    linePreCheck = dataDicIndexBySample[sampleNo][idx_d]
    if IsModeBlockValid(mpMode):
        conclusionDicByCap[curCap][idx_c +'_B'][mpModePrefix][0] += 1  # 总数量
    elif IsModePageValid(mpMode):
        conclusionDicByCap[curCap][idx_c +'_P'][mpModePrefix][0] += 1  # 总数量
    if linePreCheck[id_l] == 'PASS':
        conclusionDicByCap[curCap][idx_c][mpModePrefix[1:]][1] += 1  # PASS数量
        if IsModeBlockValid(mpMode):
            conclusionDicByCap[curCap][idx_c +'_B'][mpModePrefix][1] += 1  # PASS数量
        elif IsModePageValid(mpMode):
            conclusionDicByCap[curCap][idx_c +'_P'][mpModePrefix][1] += 1  # PASS数量

# 合并数据
def DataFusion(dict, idx):
    dict[idx]['6P'][0] = dict[idx+'_B']['B6P'][0] + dict[idx+'_P']['P6P'][0]
    dict[idx]['4P'][0] = dict[idx+'_B']['B4P'][0] + dict[idx+'_P']['P4P'][0]
    dict[idx]['2P'][0] = dict[idx+'_B']['B2P'][0] + dict[idx+'_P']['P2P'][0]
    dict[idx]['1P'][0] = dict[idx+'_B']['B1P'][0] + dict[idx+'_P']['P1P'][0]

#判定模式是否是我们需要统计的合法模式
def IsModeValid(mode):
    if IsModeBlockValid(mode) or IsModePageValid(mode):
        return True
    else:
        return False

#判定是否为合法的块模式
def IsModeBlockValid(mode):
    if mode != 'B6PT' and mode != 'B4PT' and mode != 'B2PT' and mode != 'B1PT'and mode != 'B6PS' and mode != 'B4PS' and mode != 'B2PS' and mode != 'B1PS':
        return False
    else:
        return True

#判定是否为合法的页模式
def IsModePageValid(mode):
    if mode != 'P6PT' and mode != 'P4PT' and mode != 'P2PT' and mode != 'P1PT'and mode != 'P6PS' and mode != 'P4PS' and mode != 'P2PS' and mode != 'P1PS':
        return False
    else:
        return True

#得到空的以容量为索引的记录
def GetEmptyConclusionDicIndexByCap():
    for sampleNo in dataDicIndexBySample:
        if 'MP' not in dataDicIndexBySample[sampleNo]:
            continue

        tmprow = dataDicIndexBySample[sampleNo]['MP']
        if tmprow == []:
            continue

        global PRECHECK_CASE_EXISTS
        global H2_CASE_EXISTS
        global H2_2_CASE_EXISTS
        global COPYFILE_CASE_EXISTS
        global H5_CASE_EXISTS
        if PRECHECK_CASE_EXISTS == False and 'MARS_PRECHECK' in dataDicIndexBySample[sampleNo]:
            PRECHECK_CASE_EXISTS = True
        if H2_CASE_EXISTS == False and 'H2' in dataDicIndexBySample[sampleNo]:
            H2_CASE_EXISTS = True
        if H2_2_CASE_EXISTS == False and 'H2_2' in dataDicIndexBySample[sampleNo]:
            H2_2_CASE_EXISTS = True
        if COPYFILE_CASE_EXISTS == False and 'MARS_COPYFILE' in dataDicIndexBySample[sampleNo]:
            COPYFILE_CASE_EXISTS = True
        if H5_CASE_EXISTS == False and 'H5' in dataDicIndexBySample[sampleNo]:
            H5_CASE_EXISTS = True

        tmpCap = tmprow[1] #此索引处为容量
        if tmpCap != '' and tmpCap != '0M' and tmpCap != 0 and tmpCap != '0':
            if tmpCap in conclusionDicByCap:
                continue
            conclusionDicByCap[tmpCap] = {}
            conclusionDicByCap[tmpCap]['MP'] = {'6P':[0]*2,'4P':[0]*2,'2P':[0]*2,'1P':[0]*2} #此值汇总 MP_B和MP_P
            conclusionDicByCap[tmpCap]['MP_B'] = {'B6P':[0]*2,'B4P':[0]*2,'B2P':[0]*2,'B1P':[0]*2}
            conclusionDicByCap[tmpCap]['MP_P'] = {'P6P':[0]*2,'P4P':[0]*2,'P2P':[0]*2,'P1P':[0]*2}
            conclusionDicByCap[tmpCap]['PRE_CHECK'] = {'6P':[0]*2, '4P':[0]*2, '2P':[0]*2, '1P':[0]*2}
            conclusionDicByCap[tmpCap]['PRE_CHECK_B'] = {'B6P':[0]*2, 'B4P':[0]*2, 'B2P':[0]*2, 'B1P':[0]*2}
            conclusionDicByCap[tmpCap]['PRE_CHECK_P'] = {'P6P':[0]*2, 'P4P':[0]*2, 'P2P':[0]*2, 'P1P':[0]*2}
            conclusionDicByCap[tmpCap]['H2'] = {'6P':[0]*2, '4P':[0]*2, '2P':[0]*2, '1P':[0]*2}
            conclusionDicByCap[tmpCap]['H2_B'] = {'B6P':[0]*2, 'B4P':[0]*2, 'B2P':[0]*2, 'B1P':[0]*2}
            conclusionDicByCap[tmpCap]['H2_P'] = {'P6P':[0]*2, 'P4P':[0]*2, 'P2P':[0]*2, 'P1P':[0]*2}
            conclusionDicByCap[tmpCap]['H2_2'] = {'6P':[0]*2, '4P':[0]*2, '2P':[0]*2, '1P':[0]*2}
            conclusionDicByCap[tmpCap]['H2_2_B'] = {'B6P':[0]*2, 'B4P':[0]*2, 'B2P':[0]*2, 'B1P':[0]*2}
            conclusionDicByCap[tmpCap]['H2_2_P'] = {'P6P':[0]*2, 'P4P':[0]*2, 'P2P':[0]*2, 'P1P':[0]*2}
            conclusionDicByCap[tmpCap]['COPY_FILE'] = {'6P':[0]*2, '4P':[0]*2, '2P':[0]*2, '1P':[0]*2}
            conclusionDicByCap[tmpCap]['COPY_FILE_B'] = {'B6P':[0]*2, 'B4P':[0]*2, 'B2P':[0]*2, 'B1P':[0]*2}
            conclusionDicByCap[tmpCap]['COPY_FILE_P'] = {'P6P':[0]*2, 'P4P':[0]*2, 'P2P':[0]*2, 'P1P':[0]*2}
            conclusionDicByCap[tmpCap]['H5'] = {'6P':[0]*2, '4P':[0]*2, '2P':[0]*2, '1P':[0]*2}
            conclusionDicByCap[tmpCap]['H5_B'] = {'B6P':[0]*2, 'B4P':[0]*2, 'B2P':[0]*2, 'B1P':[0]*2}
            conclusionDicByCap[tmpCap]['H5_P'] = {'P6P':[0]*2, 'P4P':[0]*2, 'P2P':[0]*2, 'P1P':[0]*2}


#分析并设置H2的结果测试结果
def AnalyzeCopyFileResult():
    for sampleID in dataDicIndexBySample:
        if 'MARS_COPYFILE' in dataDicIndexBySample[sampleID]:
            strResult = dataDicIndexBySample[sampleID]['MARS_COPYFILE'][0]
            if strResult.upper() != 'TRUE' and strResult.upper() != 'PASS':
                #记录错误信息
                PublicFuc.AppendErrDiskInfo('Copy File_Err',sampleID,strResult,dataDicIndexBySample[sampleID]['MARS_COPYFILE'][-2],dataDicIndexBySample[sampleID]['MARS_COPYFILE'][-1])
            else:
                #进一步通过速度来判定
                dataDicIndexBySample[sampleID]['MARS_COPYFILE'][0] = 'PASS' #统一称为PASS结果
              

#分析并设置H2的结果测试结果
def AnalyzeH2Result(caseName):
    for sampleID in dataDicIndexBySample:
        if caseName in dataDicIndexBySample[sampleID]:
            strResult = dataDicIndexBySample[sampleID][caseName][-3]
            if strResult.upper() != 'TRUE' and strResult.upper() != 'PASS':
                #记录错误信息
                PublicFuc.AppendErrDiskInfo('H2_Err',sampleID,strResult,dataDicIndexBySample[sampleID][caseName][-2],dataDicIndexBySample[sampleID][caseName][-1])
            else:
                #进一步通过速度来判定
                if 'MP' not in dataDicIndexBySample[sampleID]:
                    continue
                strSpeedClass = dataDicIndexBySample[sampleID]['MP'][4].upper()
                bSpeedConclusion = SpeedErrDetect(strSpeedClass,dataDicIndexBySample[sampleID][caseName][0])
                if bSpeedConclusion == True:
                    dataDicIndexBySample[sampleID][caseName][-3] = 'PASS' #统一称为PASS结果
                else:

                    errMsg = ''
                    rawWspeed = dataDicIndexBySample[sampleID][caseName][0]
                    if  rawWspeed == '':
                        errMsg = '无速度信息'
                    elif bSpeedConclusion == SPEED_HIGH_ERR:
                        errMsg = '高于速度等级范围'
                    else:
                        errMsg = '低于速度等级范围'
                    dataDicIndexBySample[sampleID][caseName][-3] = errMsg
                    PublicFuc.AppendErrDiskInfo('H2_Err',sampleID,errMsg,dataDicIndexBySample[sampleID][caseName][-2],dataDicIndexBySample[sampleID][caseName][-1])

#合并并且更新各种来源的H2数据，合并的时候，对于相同样本编号的需要比较时间
def CombineAndNormalizeCopyFileData():
    for sampleID in dicMarsData:
        if 'MARS_COPYFILE' in dicMarsData[sampleID]:
            if sampleID in dataDicIndexBySample:
                if 'MP' not in dataDicIndexBySample[sampleID]:
                    continue #没有量产数据的copyfile不要
                #如果样本已经在总字典中
                if 'MARS_COPYFILE' not in dataDicIndexBySample[sampleID]:
                    formalizedLine = []
                    formalizedLine.append(dicMarsData[sampleID]['MARS_COPYFILE'][0])#测试结果
                    testTime = GetMarTime(dicMarsData[sampleID]['MARS_COPYFILE'][1],dicMarsData[sampleID]['MARS_COPYFILE'][2])
                    formalizedLine.append(testTime)
                    formalizedLine.append(dicMarsData[sampleID]['MMS_PC']) #PC编号
                    formalizedLine.append(dicMarsData[sampleID]['MARS_COPYFILE_file_path']) #文件路径
                    dataDicIndexBySample[sampleID]['MARS_COPYFILE'] = formalizedLine
            else:
                TODO_NOTHING = 0#总表中不存在的数据，是不需要统计的，因为总表是量产，量产时所有测试的第一步。此处添加一个无意义的表达式，留下扩展


#合并并且更新各种来源的precheck
def CombineAndNormalizePreCheckData():
    for sampleID in dicMarsData:
        if 'MARS_PRECHECK' in dicMarsData[sampleID]:
            if sampleID in dataDicIndexBySample:
                if 'MP' not in dataDicIndexBySample[sampleID]:
                    continue #没有量产数据的copyfile不要
                #如果样本已经在总字典中
                if 'MARS_PRECHECK' not in dataDicIndexBySample[sampleID]:
                    formalizedLine = []
                    if dicMarsData[sampleID]['MARS_PRECHECK'][0].upper() == 'TRUE':
                        formalizedLine.append('PASS')#测试结果統一用PASS表示
                    else:
                        formalizedLine.append(dicMarsData[sampleID]['MARS_PRECHECK'][0])#测试结果
                    formalizedLine.append(dicMarsData[sampleID]['MMS_PC']) #PC编号
                    formalizedLine.append(dicMarsData[sampleID]['MARS_PRECHECK_file_path']) #文件路径
                    dataDicIndexBySample[sampleID]['MARS_PRECHECK'] = formalizedLine
            else:
                TODO_NOTHING = 0#总表中不存在的数据，是不需要统计的，因为总表是量产，量产时所有测试的第一步。此处添加一个无意义的表达式，留下扩展


#合并并且更新各种来源的H2数据，合并的时候，对于相同样本编号的需要比较时间
def CombineAndNormalizeAllTypeH2Data(caseName):
    for sampleID in dicMarsData:
        if caseName in dicMarsData[sampleID]:
            if sampleID in dataDicIndexBySample:
                #如果样本已经在总字典中
                if 'MP' not in dataDicIndexBySample[sampleID]:
                    continue #没有量产数据的H2不要
                if caseName in dataDicIndexBySample[sampleID]:
                    #且已经存在H2则判断是否需要更新
                    marsH2Time = dicMarsData[sampleID]['file_time']
                    orgTime = dataDicIndexBySample[sampleID][caseName][-1]
                    if marsH2Time > orgTime:
                        formalizedLine = []
                        formalizedLine.append(dicMarsData[sampleID][caseName][0])#写速度
                        formalizedLine.append(dicMarsData[sampleID][caseName][1])#读速度
                        #testTime = GetMarTime(dicMarsData[sampleID]['H2'][2],dicMarsData[sampleID]['H2'][3])
                        #formalizedLine.append(testTime)
                        formalizedLine.append(dicMarsData[sampleID][caseName][-1])
                        formalizedLine.append(dicMarsData[sampleID]['MMS_PC']) #添加PC编号信息
                        formalizedLine.append(dicMarsData[sampleID][caseName+'_file_path'])
                        dataDicIndexBySample[sampleID][caseName] = formalizedLine
                    else:
                        del dataDicIndexBySample[sampleID][caseName][-1]
                else:
                    formalizedLine = []
                    formalizedLine.append(dicMarsData[sampleID][caseName][0])#写速度
                    formalizedLine.append(dicMarsData[sampleID][caseName][1])#读速度
                    #testTime = GetMarTime(dicMarsData[sampleID]['H2'][2],dicMarsData[sampleID]['H2'][3])
                    #formalizedLine.append(testTime)
                    formalizedLine.append(dicMarsData[sampleID][caseName][-1])
                    formalizedLine.append(dicMarsData[sampleID]['MMS_PC']) #添加PC编号信息
                    formalizedLine.append(dicMarsData[sampleID][caseName+'_file_path'])
                    dataDicIndexBySample[sampleID][caseName] = formalizedLine
            else:
                TODO_NOTHING = 0#总表中不存在的数据，是不需要统计的，因为总表是量产，量产时所有测试的第一步。此处添加一个无意义的表达式，留下扩展

    #以上逻辑只是把Mars中有的Sample的H2信息更新到总字典中，还需要将原字典整个梳理去掉最后的时间
    for sampleID in dataDicIndexBySample:
        if sampleID in dicMarsData and caseName in dicMarsData[sampleID]:
            continue #MarsData中已经规范化了的则不再做处理
        if caseName in dataDicIndexBySample[sampleID]:
            del dataDicIndexBySample[sampleID][caseName][-1] #去除尾巴上的时间

                
def GetMarTime(startTimeStr,endtimeStr):
    if '' == startTimeStr or '' == endtimeStr:
        return ''
    else:
        try:
            endtime = datetime.strptime(endtimeStr, '%Y-%m-%d %H:%M:%S')
            starttime = datetime.strptime(startTimeStr, '%Y-%m-%d %H:%M:%S')
            totalSecond = timedelta.total_seconds(endtime-starttime)
            totalSecond = int(totalSecond)
            hour = int(totalSecond/3600)
            lefSeconds = totalSecond%3600
            minutes = int(lefSeconds/60)
            seconds = lefSeconds%60
            timeStr = '%d:%d:%d'%(hour,minutes,seconds)
            return timeStr
        except:
            return ''

#按照只需要统计到表格的数据，规范化只感兴趣的量产结果数据列
def NormalizeMPData():
    for sampleNo in dataDicIndexBySample:
        if 'MP' in dataDicIndexBySample[sampleNo]:
            line = dataDicIndexBySample[sampleNo]['MP']
            if line != None and len(line) > 0:
                mpLine = []
                #mpLine.append(line[mp_column_name_map['测试PC编号']])
                mpLine.append(line[mp_column_name_map['ErrCode']])
                mpLine.append(line[mp_column_name_map['容量']])
                mpLine.append(line[mp_column_name_map['坏块数']])
                #mpLine.append(line[mp_column_name_map['Bin级']])
                mpLine.append(line[mp_column_name_map['量产模式']])
                #mpLine.append(line[mp_column_name_map['Speed']])
                mpLine.append(line[mp_column_name_map['SpeedClass']])
                if mp_column_name_map['BadColNum'] == -1:
                    mpLine.append('') #没有此列填空内容
                else:
                    mpLine.append(line[mp_column_name_map['BadColNum']])
                mpLine.append(line[mp_column_name_map['量产耗时']])
                mpLine.append(line[mp_column_name_map['测试PC编号']])
                mpLine.append(line[-2]) #编号信息所在路径文件
                dataDicIndexBySample[sampleNo]['MP'] = mpLine

                #获取需要填充到表格头的固定数据信息
                #begin此段为独立的记录公共信息的代码块
                global FW_VERSION
                global MP_VERSION
                global TEST_CASE
                global FLASH_NAME
                global FLASH_MCU
                global FLASH_ID
                if FW_VERSION == '':
                    FW_VERSION = line[mp_column_name_map['fw_version']]
                if MP_VERSION == '':
                    MP_VERSION = line[mp_column_name_map['mp_version']]
                    if MP_VERSION != '':
                        MP_VERSION = 'V'+MP_VERSION #需求在量产工具版本前加V
                if TEST_CASE == '':
                    TEST_CASE = line[mp_column_name_map['test_case']]
                if FLASH_NAME == '':
                    FLASH_NAME = line[mp_column_name_map['FlashName']]
                if FLASH_MCU == '':
                    FLASH_MCU = line[mp_column_name_map['FlashMCU']]
                if FLASH_ID == '' and mp_column_name_map['flash_id'] != -1:
                    FLASH_ID = line[mp_column_name_map['flash_id']]
                #end
            
#规范化DUT的h2结果数据
def NormalizeDutH2Data(keyName):
    for sampleNo in dataDicIndexBySample:
        if keyName in dataDicIndexBySample[sampleNo]:
            line = dataDicIndexBySample[sampleNo][keyName]
            if line != None and len(line) > 0:
                NormalLine = []
                NormalLine.append(line[h2_column_name_map['写速度']])
                NormalLine.append(line[h2_column_name_map['读速度']])
                #NormalLine.append(line[h2_column_name_map['测试时间']])
                NormalLine.append(line[h2_column_name_map['结果']])
                NormalLine.append(line[h2_column_name_map['测试PC编号']]) #加入PC编号信息
                NormalLine.append(line[-2]) #加入样片所属文件信息
                NormalLine.append(line[-1])#仍然保留时间，要和其它H2进行最后比较。
                dataDicIndexBySample[sampleNo][keyName] = NormalLine

def ReadMarsIniDataLocal(curpath, pattern, dataDic, caseName, caseKey, keyLst, imageSuffix, diskCnt = 0):
    unitLst = ['M/s']
    config = configparser.RawConfigParser()
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        #必须清除
        config.clear()
        config.read(file,encoding = 'gbk')
        if 'HWCONFIG' not in config.sections():
            continue
        keyName = config['HWCONFIG']['mms_flash']
        if keyName not in dataDic:
            #0表示样片数量不确定，需要获取全部数据
            if diskCnt == len(dataDic) and 0 != diskCnt:
                continue
            dataDic[keyName] = {}
        if caseName not in config.sections():
            continue
        dataDic[keyName]['MMS_PC'] = config['HWCONFIG']['MMS_PC']
        if caseKey in dataDic[keyName]:
            oldTime = dataDic[keyName]['file_time']
            fileMdTime = os.path.getmtime(file)
            if fileMdTime < oldTime:
                continue#数据不是新的，不做读取覆盖

            dataDic[keyName]['file_time'] = fileMdTime
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            tempLst = []
            for key in keyLst:
                if 'Cap' == key:
                    cap = config['HWCONFIG']['capacity']
                    if '' == cap:
                        tempLst.append('')
                    else:
                        tempLst.append(str(int(float(cap))))
                    continue
                if 'MMS_PC' == key:
                    tempLst.append(config['HWCONFIG']['MMS_PC'])
                    continue
                if key.lower() in config[caseName]:
                    value = config[caseName][key.lower()]
                    #去除单位
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    tempLst.append(value)
                else:
                    tempLst.append('')

            #imageSuffix为空不需要截图，只需要数据
            if '' != imageSuffix:
                pos = file.rfind('\\report')
                imagepath = file[:pos]
                image = ''
                for i in os.listdir(imagepath):
                    if i.endswith('.bmp'):
                        image = os.path.join(imagepath,i)
                        break
                if os.path.isfile(image):
                    tempLst.append(image)
                else:
                    tempLst.append('')
            dataDic[keyName][caseKey] = tempLst    
            #shiqingkuangkangengxin
        else:
            fileMdTime = os.path.getmtime(file)
            dataDic[keyName]['file_time'] = fileMdTime
            dataDic[keyName]['capacity'] = config['HWCONFIG']['capacity']
            tempLst = []
            for key in keyLst:
                if 'Cap' == key:
                    cap = config['HWCONFIG']['capacity']
                    if '' == cap:
                        tempLst.append('')
                    else:
                        tempLst.append(str(int(float(cap))))
                    continue
                if 'MMS_PC' == key:
                    tempLst.append(config['HWCONFIG']['MMS_PC'])
                    continue
                if key.lower() in config[caseName]:
                    value = config[caseName][key.lower()]
                    #去除单位
                    for unit in unitLst:
                        if unit in value:
                            value = value[:value.find(unit)]
                            break
                    tempLst.append(value)
                else:
                    tempLst.append('')

            #imageSuffix为空不需要截图，只需要数据
            if '' != imageSuffix:
                pos = file.rfind('\\report')
                imagepath = file[:pos]
                image = ''
                for i in os.listdir(imagepath):
                    if i.endswith('.bmp'):
                        image = os.path.join(imagepath,i)
                        break
                if os.path.isfile(image):
                    tempLst.append(image)
                else:
                    tempLst.append('')
            dataDic[keyName][caseKey] = tempLst
        dataDic[keyName][caseKey+'_file_path'] = file
               
#按照实际数据条数生成表格样式
def InitReportTemplateInWorkSheet(worksheet):
    nRowCnt = len(dataDicIndexBySample)
    remindNo = 0
    nMaxSampleNo = -1
    nowSample = ''
    if nRowCnt == 0:
        return
    keySortLst = sorted(dataDicIndexBySample.keys(), reverse=False)
    for item in keySortLst:
        ordeNo,SampleNo = PublicFuc.GetOrderNoFromSampleNo(item)
        if ordeNo == -1:
            continue
        if nowSample != '' and nowSample != SampleNo:
            nMaxSampleNo += remindNo
        nowSample = SampleNo
        remindNo = ordeNo
    nMaxSampleNo += remindNo
    if nMaxSampleNo != -1:
        nRowCnt = nMaxSampleNo + 1
 
    #cellfont=Font('Times New Roman',size=10,color=colors.BLACK,bold=False,italic=False)
    for rowIdx in range(nRowCnt):
       for col in range(1,24+1):
           worksheet['%s%d'%(get_column_letter(col), START_ROW+rowIdx)].alignment = PublicFuc.alignment
           worksheet['%s%d'%(get_column_letter(col), START_ROW+rowIdx)].border = PublicFuc.my_border('thin', 'thin', 'thin', 'thin')
           #worksheet['%s%d'%(get_column_letter(col), rowIdx)].font = cellfont
       worksheet['%s%d'%(get_column_letter(1), START_ROW+rowIdx)] = rowIdx+1
       worksheet['%s%d' % (get_column_letter(24), START_ROW + rowIdx)].alignment = Alignment(wrapText=True)

       

#初始化MP量产数据
def InitFillMpDataToDic(listSampleResultData):
    for line in listSampleResultData:
        sampleNo = line[mp_column_name_map['物料编号']]
        if sampleNo == '':
            continue #没有样本编号的数据不加入统计，可能是作废的测试数据。
        if sampleNo not in dataDicIndexBySample:
            dataDicIndexBySample[sampleNo] = {}
        if 'MP' in dataDicIndexBySample[sampleNo]:
            if dataDicIndexBySample[sampleNo]['MP'] != None and len(dataDicIndexBySample[sampleNo]['MP']) > 0:
                if dataDicIndexBySample[sampleNo]['MP'][-1] < line[-1]:
                    dataDicIndexBySample[sampleNo]['MP'] = line #更新时间的信息来了，才需要覆盖
        else:
            dataDicIndexBySample[sampleNo]['MP'] = line

 #初始化H2量产数据
def InitFillH2DataToDic(listSampleResultData,keyName):
    for line in listSampleResultData:
        sampleNo = line[h2_column_name_map['物料编号']]
        if sampleNo not in dataDicIndexBySample:
            continue #没有量产数据的H2不要
        if 'MP' not in dataDicIndexBySample[sampleNo]:
            continue #没有量产数据的H2不要
        if keyName in dataDicIndexBySample[sampleNo]:
            if dataDicIndexBySample[sampleNo][keyName] != None and len(dataDicIndexBySample[sampleNo][keyName]) > 0:
                if dataDicIndexBySample[sampleNo][keyName][10].strip() == "":
                    #如果原来取的无效数据，则需要覆盖，不比较任何时间
                    dataDicIndexBySample[sampleNo][keyName] = line
                else:
                    if dataDicIndexBySample[sampleNo][keyName][-1] < line[-1]:#时间比较
                        if line[10].strip() != "":
                            dataDicIndexBySample[sampleNo][keyName] = line #更新时间的信息来了，才需要覆盖
        else:
            dataDicIndexBySample[sampleNo][keyName] = line

def InitFillH5DataToDic(listSampleResultData,keyName):
    sampleNodic = {}
    for sampleNo in dataDicIndexBySample:
        ordeNo,SampleNo = PublicFuc.GetOrderNoFromSampleNo(sampleNo)
        sampleNodic[str(ordeNo)] = sampleNo
    for line in listSampleResultData:
        sampleNo = line[h5_column_name_map['物料编号']]
        if sampleNo == '' or sampleNo is None:
            portNo = line[h5_column_name_map['端口号']]
            sampleNo = sampleNodic[portNo] if portNo in sampleNodic else ''
        if sampleNo == '' or sampleNo not in dataDicIndexBySample:
            continue
        if keyName in dataDicIndexBySample[sampleNo]:
            if dataDicIndexBySample[sampleNo][keyName] != None and len(dataDicIndexBySample[sampleNo][keyName]) > 0:
                if dataDicIndexBySample[sampleNo][keyName][-1] < line[-1]:#时间比较
                    dataDicIndexBySample[sampleNo][keyName] = line #更新时间的信息来了，才需要覆盖
        else:
            dataDicIndexBySample[sampleNo][keyName] = line


def InitMPCsvColumnNameMap(columnNameMap,listHeader):
    #columnNameMap['端口'] = 0
    if listHeader == []:
        return
    columnNameMap.clear()
    columnNameMap['物料编号'] = PublicFuc.GetIndex('物料编号',listHeader)
    columnNameMap['测试PC编号'] = PublicFuc.GetIndex('测试PC编号',listHeader)
    columnNameMap['ErrCode'] = PublicFuc.GetIndex('ErrCode',listHeader)
    columnNameMap['容量'] = PublicFuc.GetIndex('容量',listHeader)
    columnNameMap['坏块数'] = PublicFuc.GetIndex('坏块数',listHeader)
    columnNameMap['Bin级'] = PublicFuc.GetIndex('Bin级',listHeader)
    columnNameMap['量产模式'] = PublicFuc.GetIndex('量产模式',listHeader)
    columnNameMap['量产耗时'] = PublicFuc.GetIndex('量产耗时',listHeader)
    columnNameMap['Speed'] = PublicFuc.GetIndex('Speed(M/s)',listHeader)
    columnNameMap['SpeedClass'] = PublicFuc.GetIndex('SpeedClass',listHeader)
    columnNameMap['BadColNum'] = PublicFuc.GetIndex('BadColNum',listHeader)

    columnNameMap['mp_version'] = PublicFuc.GetIndex('量产工具版本',listHeader) #量产工具版本
    columnNameMap['fw_version'] = PublicFuc.GetIndex('固件版本',listHeader) #固件版本
    columnNameMap['FlashName'] = PublicFuc.GetIndex('FlashType',listHeader) #Flash型号
    columnNameMap['FlashMCU'] = PublicFuc.GetIndex('主控版本',listHeader) #主控
    columnNameMap['test_case'] = PublicFuc.GetIndex('测试Case',listHeader) #量产模式：低格1之类的
    columnNameMap['flash_id'] = PublicFuc.GetIndex('Flash ID',listHeader) #flash ID

    #columnNameMap['容量'] = 11
    #columnNameMap['坏块数'] = 12
    #columnNameMap['Bin级'] = 13
    #columnNameMap['量产模式'] = 14
    #columnNameMap['量产耗时'] = 17
    #columnNameMap['Speed'] = 15
    #columnNameMap['SpeedClass'] = 16
    


def InitH2CsvColumnNameMap(columnNameMap):
    columnNameMap.clear()
    #columnNameMap['端口'] = 0
    columnNameMap['物料编号'] = 3
    columnNameMap['写速度'] = 14
    columnNameMap['读速度'] = 15
    columnNameMap['测试时间'] = 29
    columnNameMap['结果'] = 17
    columnNameMap['测试PC编号'] = 5

def InitH5CsvColumnNameMap(columnNameMap):
    columnNameMap.clear()
    columnNameMap['物料编号'] = 23
    columnNameMap['写速度'] = 20
    columnNameMap['读速度'] = 21
    columnNameMap['测试时间'] = 8
    columnNameMap['测试结果'] = 10
    columnNameMap['端口号'] = 1

#将所有的文件数据合并到一个列表中
def MergeAllSampleData(dataDicIndexByFile,mergedList):
    mergedList.clear()
    for idx in dataDicIndexByFile:
        for item in dataDicIndexByFile[idx]:
            mergedList.append(item)

detailDataFont=Font('宋体',size=11,color=colors.BLACK,bold=False,italic=False)

def GetUsbVersion():
    strUsbVersion = 'USB3' 
    pattern = '.+\\\\MP_H2_SD2.0\\\\.+.csv$'
    for file in PublicFuc.fileLst:
        if re.match(pattern, file):
            strUsbVersion = 'USB2'
            break
    return strUsbVersion

def SpeedErrDetect(_speedClass,_wspeed):
    if _wspeed == '':
        return False
    max_delta = 1.5 #可以允许1.5M的偏差
    wspeed = 0
    try:
        wspeed = float(_wspeed)
    except:
        return False

    upperSpeedClass = _speedClass.upper()

    #进一步判定是否有速度超标,标准来源于钉钉的任务项详情描述
    if upperSpeedClass == 'C6':
        if wspeed > (10 + max_delta):
            return SPEED_HIGH_ERR
        else:
            return True
    elif upperSpeedClass == 'U1N' or upperSpeedClass == 'U1':  #U1N改为了U1，需要兼容旧MP版本统计。
        if wspeed > (14 + max_delta):
            return SPEED_HIGH_ERR
        elif wspeed < (10 - max_delta):
            return SPEED_LOW_ERR
        else:
            return True
    elif upperSpeedClass == 'U1H':
        if wspeed > (19 + max_delta):
            return SPEED_HIGH_ERR
        elif wspeed < (15 - max_delta):
            return SPEED_LOW_ERR
        else:
            return True
    elif upperSpeedClass == 'U1S':
        if wspeed > (25 + max_delta):
            return SPEED_HIGH_ERR
        elif wspeed < (20 - max_delta):
            return SPEED_LOW_ERR
        else:
            return True
    elif upperSpeedClass == 'U3':
        if wspeed > (36 + max_delta):
            return SPEED_HIGH_ERR
        elif wspeed < (26 - max_delta):
            return SPEED_LOW_ERR
        else:
            return True
    elif upperSpeedClass == 'U3H':
        if wspeed > (46 + max_delta):
            return SPEED_HIGH_ERR
        elif wspeed < (37 - max_delta):
            return SPEED_LOW_ERR
        else:
            return True
    elif upperSpeedClass == 'U3S':
        if wspeed > (56 + max_delta):
            return SPEED_HIGH_ERR
        elif wspeed < (47 - max_delta):
            return SPEED_LOW_ERR
        else:
            return True
    elif upperSpeedClass == 'V6':
        if wspeed < (57 - max_delta):
            return SPEED_LOW_ERR
        else:
            return True
    else:
        return True #说明没有模式规则的速度限制

#写详细数据
def WriteDetailResult2WorkSheet(worksheet, startLine, dataDic):
    curLine = startLine
    realline = startLine
    curSample = ''
    nOrderNo = 0
    #rowIdx = 1
    #排序输出
    keySortLst = sorted(dataDic.keys(), reverse=False)
    for sampleKey in keySortLst:
        sampleDic = dataDic[sampleKey]
        oldNo = nOrderNo
        nOrderNo, strSample = PublicFuc.GetOrderNoFromSampleNo(sampleKey)
        if nOrderNo == -1:
            continue
        if curSample != '' and curSample != strSample:
            realline = realline + oldNo
        curSample = strSample
        curLine = realline + nOrderNo - 1
        worksheet['%s%d'%(get_column_letter(2), curLine)] = sampleKey
        try:
            #第一列是编号，直接填行号
            if 'MP' in sampleDic:
                lineMp = sampleDic['MP']
                if len(lineMp) < 2:
                    continue
                errCode = lineMp[0]
                capMB = lineMp[1]
                if errCode.upper() == '0XFA0':
                    worksheet['%s%d'%(get_column_letter(3), curLine)] = 'PASS'                   
                else:
                    worksheet['%s%d'%(get_column_letter(3), curLine)] = errCode
                    worksheet['%s%d'%(get_column_letter(3), curLine)].fill = PublicFuc.warnFill
                    PublicFuc.AppendErrDiskInfo('MP_Err',sampleKey,errCode,lineMp[-2],lineMp[-1])

                worksheet['%s%d'%(get_column_letter(4), curLine)] = capMB

                for i in range(5):                   
                    if i == 0 or i==3:
                        try:
                            worksheet['%s%d'%(get_column_letter(5+i), curLine)] = int(lineMp[2+i])
                        except:
                            worksheet['%s%d'%(get_column_letter(5+i), curLine)] = lineMp[2+i]
                    else:
                        worksheet['%s%d'%(get_column_letter(5+i), curLine)] = lineMp[2+i]

            #填充precheck数据
            if 'MARS_PRECHECK' in sampleDic:
                linePrecheckFile = sampleDic['MARS_PRECHECK']
                for i in range(1):
                    worksheet['%s%d'%(get_column_letter(15+i), curLine)] = linePrecheckFile[i]
                    if i == 0:
                        strResult = linePrecheckFile[0]
                        if strResult.upper() != 'PASS' and strResult.upper() != 'TRUE':
                            worksheet['%s%d'%(get_column_letter(15+i), curLine)].fill = PublicFuc.warnFill
            
            #填充H2数据
            if 'H2' in sampleDic:
                lineH2 = sampleDic['H2']
                for i in range(3):
                    if i < 2:
                        try:
                            worksheet['%s%d'%(get_column_letter(16+i), curLine)] = float(lineH2[i])
                        except:
                            worksheet['%s%d'%(get_column_letter(16+i), curLine)] = lineH2[i]
                    else:
                        worksheet['%s%d'%(get_column_letter(16+i), curLine)] = lineH2[i]
                result = lineH2[2].upper()
                if result != 'PASS':
                    worksheet['%s%d'%(get_column_letter(18), curLine)].fill = PublicFuc.warnFill

            #填充H2_2数据
            if 'H2_2' in sampleDic:
                lineH2 = sampleDic['H2_2']
                for i in range(3):
                    if i < 2:
                        try:
                            worksheet['%s%d'%(get_column_letter(19+i), curLine)] = float(lineH2[i])
                        except:
                            worksheet['%s%d'%(get_column_letter(19+i), curLine)] = lineH2[i]
                    else:
                        worksheet['%s%d'%(get_column_letter(19+i), curLine)] = lineH2[i]
                result = lineH2[2].upper()
                if result != 'PASS':
                    worksheet['%s%d'%(get_column_letter(21), curLine)].fill = PublicFuc.warnFill

            if 'MARS_COPYFILE' in sampleDic:
                lineCopyFile = sampleDic['MARS_COPYFILE']
                for i in range(2):
                    worksheet['%s%d'%(get_column_letter(22+i), curLine)] = lineCopyFile[i]
                    if i == 0:
                        strResult = lineCopyFile[0]
                        if strResult.upper() != 'PASS' and strResult.upper() != 'TRUE':
                            worksheet['%s%d'%(get_column_letter(22+i), curLine)].fill = PublicFuc.warnFill

            if 'H5' in sampleDic:
                lineH5 = sampleDic['H5']
                worksheet['%s%d'%(get_column_letter(10), curLine)] = lineH5[h5_column_name_map['端口号']]
                worksheet['%s%d'%(get_column_letter(11), curLine)] = lineH5[h5_column_name_map['写速度']]
                worksheet['%s%d'%(get_column_letter(12), curLine)] = lineH5[h5_column_name_map['读速度']]
                worksheet['%s%d'%(get_column_letter(13), curLine)] = GetSpendTime(lineH5[h5_column_name_map['测试时间']])
                worksheet['%s%d'%(get_column_letter(14), curLine)] = lineH5[h5_column_name_map['测试结果']]
                if lineH5[h5_column_name_map['测试结果']].upper() != 'PASS':
                    worksheet['%s%d'%(get_column_letter(14), curLine)].fill = PublicFuc.warnFill

        except(AttributeError):
            continue
        #rowIdx += 1 
        #curLine += 1


def GetSpendTime(seconds):
    total_seconds = int(seconds)
    hours = total_seconds // 3600
    minutes = (total_seconds % 3600) // 60
    seconds = total_seconds % 60

    # 格式化时间字符串
    formatted_time = "{:02d}:{:02d}:{:02d}".format(hours, minutes, seconds)
    return formatted_time

def GetBinShowTxt(binValue):
    if binValue == 0:
        return ''
    else:
        return binValue
    


def ReadCsvData(curpath,pattern,dataDic):
    csvHeaderColumn = []
    fileIdx = 1
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除

        file_data = []
        mdTime = os.path.getmtime(file) #file[pos+1:];#
        with open(file, encoding='gb18030') as csvfile:
            csv_reader = csv.reader(_.replace('\x00', '') for _ in csvfile)  # 使用csv.reader读取csvfile中的文件csv.reader(csvfile)
            try:
                birth_header = next(csv_reader)  # 读取第一行每一列的标题
            except:
                continue
            if csvHeaderColumn == []:
                csvHeaderColumn = birth_header
            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中 
                if row == [] or len(row) != len(csvHeaderColumn):
                    continue #量产工具可能有乱码，乱行
                row.append(file) #把文件路径信息添加，用于输出错误信息路径
                row.append(mdTime)
                file_data.append(row)
        dataDic[fileIdx]=file_data
        fileIdx += 1
    return csvHeaderColumn

def ReadCsvDataEx(curpath,pattern,dataDic):
    csvHeaderColumn = []
    fileIdx = 1
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除

        file_data = []
        mdTime = os.path.getmtime(file) #file[pos+1:];#
        with open(file, encoding='gb18030') as csvfile:
            csv_reader = csv.reader(_.replace('\x00', '') for _ in csvfile)  # 使用csv.reader读取csvfile中的文件csv.reader(csvfile)
            try:
                birth_header = next(csv_reader)  # 读取第一行每一列的标题
            except:
                continue
            if csvHeaderColumn == []:
                csvHeaderColumn = birth_header
            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中
                if row == []:
                    continue
                if len(row) < len(csvHeaderColumn):
                    diff = len(csvHeaderColumn) - len(row)
                    for i in range(diff):
                        row.append('')  #补充数据
                row.append(file) #把文件路径信息添加，用于输出错误信息路径
                row.append(mdTime)
                file_data.append(row)
        dataDic[fileIdx]=file_data
        fileIdx += 1
    return csvHeaderColumn

#初始化制定区域边框为所有框线
def format_border(s_column, s_index, e_column , e_index):
    for row in tuple(sheet[s_column + str(s_index):e_column + str(e_index)]):
        for cell in row:
            cell.border = my_border('thin', 'thin', 'thin', 'thin')