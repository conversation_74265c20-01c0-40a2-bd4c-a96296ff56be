import PublicFuc
import configparser
import csv
import os,re
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors,Font
from datetime import datetime,timedelta


diH2VerifyData ={} #存放文件拷贝相关数据
DETAIL_DATA_START_ROW_NO = 5

def Run(curpath, workBook, alignment):
    ws = workBook['Data Retention']
    ws.alignment = alignment
    ProDutH2Verify(curpath, ws)

def ProDutH2Verify(curpath, worksheet):

    pattern = '.+\\\\Plan4\\\\T_GE_U2_C2\\\\Data Retention校验\\\\.+.csv$'
    ReadDUTH2VerifyCsvData(curpath,pattern,diH2VerifyData,'H2_VERIFY')
    WriteData2WorkSheet(worksheet)
    PublicFuc.WriteReportTime(worksheet,'I',1)
    PublicFuc.WriteReportOperator(worksheet,'K',1)

def ReadDUTH2VerifyCsvData(curpath,pattern,dataDic,caseKey):
    for file in PublicFuc.fileLst:
        if not re.match(pattern, file):
            continue
        pos = file.rfind('\\')
        imagepath = file[:pos]
        #必须清除

        #file_data = []
        with open(file,mode='r', buffering=-1, encoding='cp936') as csvfile:
            csv_reader = csv.reader(csvfile)  # 使用csv.reader读取csvfile中的文件
            birth_header = next(csv_reader)  # 读取第一行每一列的标题
            for row in csv_reader:  # 将csv 文件中的数据保存到file_data中
                key = row[3]
                wSpeed = row[14]
                rSpeed = row[15]
                result = row[17]
                firstErr = row[18]
                tempRow = [wSpeed,rSpeed,result,firstErr]
                if key not in dataDic:
                    dataDic[key] = {}
                if caseKey not in dataDic[key]:
                    dataDic[key][caseKey] = tempRow


def WriteData2WorkSheet(worksheet):
    #绘制excel模板
    InitReportTemplateInWorkSheet(worksheet)

    rowIdx = 0
    keyList = sorted(diH2VerifyData.keys())
    for sampleNo in keyList:
        sampleDic = diH2VerifyData[sampleNo]
        worksheet['%s%d'%(get_column_letter(1), DETAIL_DATA_START_ROW_NO+rowIdx)] = rowIdx+1 #编号
        worksheet['%s%d'%(get_column_letter(3), DETAIL_DATA_START_ROW_NO+rowIdx)] = sampleNo
        if 'H2_VERIFY' in sampleDic:
            tmpRow = sampleDic['H2_VERIFY']      
            for col in range(len(tmpRow)):
                worksheet['%s%d'%(get_column_letter(4+col), DETAIL_DATA_START_ROW_NO+rowIdx)] = tmpRow[col]
                if (col == 2) and tmpRow[col].upper() != 'PASS' and tmpRow[col] != '':
                    worksheet['%s%d'%(get_column_letter(4+col), DETAIL_DATA_START_ROW_NO+rowIdx)].fill = PublicFuc.warnFill
            rowIdx += 1

def InitReportTemplateInWorkSheet(worksheet):
    #titleFont=Font('宋体',size=11,color=colors.BLACK,bold=True,italic=False)
    cellfont=Font('微软雅黑',size=10,color=colors.BLACK,bold=False,italic=False)
    for rowIdx in range(len(diH2VerifyData)):
        for col in range(7):
            worksheet['%s%d'%(get_column_letter(col+1), DETAIL_DATA_START_ROW_NO+rowIdx)].alignment = PublicFuc.alignment
            worksheet['%s%d'%(get_column_letter(col+1), DETAIL_DATA_START_ROW_NO+rowIdx)].border = PublicFuc.my_border('thin', 'thin', 'thin', 'thin')
            worksheet['%s%d'%(get_column_letter(col+1), DETAIL_DATA_START_ROW_NO+rowIdx)].font = cellfont