from openpyxl import  utils,Workbook
import os,sys,logging,traceback,time
import re
import test_result,MTT_PV_Report_UFS
curpath = os.path.split(sys.argv[0])[0]
reportPath = curpath
curtime = time.strftime("%Y%m%d%H%M%S", time.localtime())
logname = os.path.join(reportPath, 'BLX_HR_template_' + curtime + '.log')
#日志配置
logging.basicConfig(filename = logname,
                    filemode='w',
                    format = '%(asctime)s-%(name)s-%(levelname)s-%(module)s: %(message)s',
                    datefmt = '%Y-%m-%d %H:%M:%S %p',
                    level = logging.INFO)

try:
    import openpyxl
    from openpyxl.styles import Alignment
    resultFileName = 'UFS数据汇总.xlsx'
    resultFile = os.path.join(reportPath, resultFileName)
    logging.info('程序开始运行！')
    print('开始汇总报告，请等待。。。')
    if len(sys.argv) <= 1:
        print('请输入测试单号!!!')
    testNo = sys.argv[1]
    UFSrtemplateFile = os.path.join(curpath, 'UFS_template.xlsx')
    wb = openpyxl.load_workbook(filename = UFSrtemplateFile)
    alignment = Alignment(horizontal='center',vertical='center') 
    test_result.Run(wb,alignment,testNo)
    MTT_PV_Report_UFS.Run(curpath,reportPath,wb,'',False)
    wb.save(resultFile)
    logging.info('结束！')
except:
    print(traceback.format_exc())
    logging.error(traceback.format_exc())
    
