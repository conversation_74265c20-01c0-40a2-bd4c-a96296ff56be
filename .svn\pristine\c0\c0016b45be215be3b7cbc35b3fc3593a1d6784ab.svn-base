import configparser
import csv
import os,re,time,logging
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors,Font
from datetime import datetime,timedelta
import openpyxl
import chardet,re,sys
import pyodbc


nX1Point=58
nX2Point=116
nX3Point=234
g_PointCnt={'x1':116,'x2':232,'x4':464}
g_AllData={'x1':{},'x2':{},'x4':{}}
g_StandardVal={'x1':[],'x2':[],'x4':[]}
g_nSustainedIndex=-2  #-2:初始值；-1：null；>0 sustained index
g_nStartPoint=-1
g_nEndPoint=-1

warnFill = PatternFill('solid', fgColor='FF0000')
red_font = Font(color='FF0000')#红色字体
bold_blue_font = Font(bold=True,color='0000FF')#红色字体
bold_red_font = Font(bold=True,color='FF0000')#红色字体
titlefont=Font('微软雅黑',size=11,color=colors.BLACK,bold=True,italic=False)
alignL = Alignment(horizontal='left',vertical='center',wrap_text=True)

autoAlignt = Alignment(wrapText=True)

strExcelName='eMMC_'

g_bRmsRun=False
lPath=[]


def Run(curpath,rptPath, wb, alignment,bRmsRun):
    # XU4ResultDir=os.path.join(curpath,'emmc_pv_report_template.xlsx')
    # resultFile=os.path.join(curpath,'emmc_pv_report.xlsx')
    # wb = openpyxl.load_workbook(XU4ResultDir)
    
    # InitialData(wb)
    global g_bRmsRun
    g_bRmsRun=bRmsRun
    lDir=GetPVDir(rptPath)
    if len(lDir) == 0:
        return
    GetData(lDir)
    bDataExist=False
    strSheetName=''
    global strExcelName
    for key,val in g_AllData.items():
        if val != {}:
            bDataExist=True
            strSheetName=val['fw'][0]+' '+val['fw'][1]
            strExcelName+=val['fw'][0]+'_'+val['fw'][1]+'_'+val['fw'][2]
            break
    if not bDataExist:
        return
    

    if strSheetName!='' and strSheetName!='null null':
        excel=os.path.join(lDir[0],'PV','Test_Standard.xlsx')
        if os.path.exists(excel):
            wbTmp = openpyxl.load_workbook(excel)
            if strSheetName in wbTmp:
                ws=wbTmp[strSheetName]
                InitialData(ws)
                wbTmp.close()
            else:
                wbTmp.close()
                excel=os.path.join(curpath,'emmc_pv_report_template.xlsx')
                wbTmp = openpyxl.load_workbook(excel)
                ws=wbTmp['Test report']
                InitialData(ws)
                wbTmp.close()
        else:
            excel=os.path.join(curpath,'emmc_pv_report_template.xlsx')
            wbTmp = openpyxl.load_workbook(excel)
            ws=wbTmp['Test report']
            InitialData(ws)
            wbTmp.close()
    else:
        excel=os.path.join(curpath,'emmc_pv_report_template.xlsx')
        wbTmp = openpyxl.load_workbook(excel)
        ws=wbTmp['Test report']
        InitialData(ws)
        wbTmp.close()

    WriteXlsData(wb)

    # wb.save(resultFile)
    print('emmc_pv报告统计完成')

# 获取log中样片的类型和测试的时间，提前筛选最新的测试数据
def GetTypeAndTime(listDir:list):
    dicRet={}
    dicTime={}
    for path in listDir:
        strTime='0'
        strType='x1'
        strFile='Passed verify performance proxima SW_SR_RR_RW hs400.log'
        strWRFile=os.path.join(path,strFile)
        if not os.path.exists(strWRFile):
            strFile=GetFailedFile(strFile)
            strWRFile=os.path.join(path,strFile)
        if os.path.exists(strWRFile):
            nInxex=path.rfind('_')
            if nInxex != -1:
                strTime=path[nInxex+1:]
            strCoding='UTF-8'
            nCnt=3
            while nCnt>0:
                try:
                    with open(strWRFile,encoding=strCoding) as file:
                        bStart=False
                        for line in file.readlines():
                            if not bStart:
                                if '>>>' in line:
                                    nIndex=line.find('>>>')
                                    strType=line[nIndex+3:nIndex+5].lower()
                                    bStart=True
                                    break
                    break
                except Exception as e:
                    strCoding='gb2312'
                    nCnt-=1
                    if nCnt == 1:
                        with open(strWRFile, 'rb') as file:
                            raw_data = file.read()
                            result = chardet.detect(raw_data)
                            strCoding = result['encoding']
        if strType not in dicTime:
            dicTime[strType]=strTime
            dicRet[strType]=path
        else:
            if int(strTime) > int(dicTime[strType]):
                dicTime[strType]=strTime
                dicRet[strType]=path
    return list(dicRet.values())

def InitialData(ws):
    nColumn=4
    for key,value in g_StandardVal.items():
        for i in range(4,21,1):
            g_StandardVal[key].append(float(ws.cell(row=i,column=nColumn).value))
        for i in range(22,38,1):
            g_StandardVal[key].append(float(ws.cell(row=i,column=nColumn).value))
        nLastNum=g_StandardVal[key][-1]
        g_StandardVal[key]+=[nLastNum]*7
        for i in range(45,53,1):
            g_StandardVal[key].append(float(ws.cell(row=i,column=nColumn).value))
        nColumn+=2
    # # 30 31 32 33 47
    # for key,value in g_StandardVal.items():
    #     if len(g_StandardVal[key])>=48:
    #         g_StandardVal[key][30]=0.999
    #         g_StandardVal[key][31]=0.99
    #         g_StandardVal[key][32]=0.99
    #         g_StandardVal[key][33]=0.99
    #         g_StandardVal[key][47]=0.999


def GetPVDir(curpath):
    global g_bRmsRun
    lPath.append(curpath)
    if g_bRmsRun==True:
        strDirName=os.path.dirname(curpath)
        strBaseName=os.path.basename(curpath)
        nIdx=strBaseName.rfind('#')
        if nIdx!=-1:
            strTmp=strBaseName[nIdx:]
            nIdxTmp=strTmp.find('_')
            if nIdxTmp!=-1:
                strQueryName=strBaseName[0:nIdx]+strTmp[0:nIdxTmp]+'%'
                Conn=pyodbc.connect('DRIVER={SQL Server};SERVER=172.18.2.22;DATABASE=ys_rms;UID=yswms;PWD=**************')
                cursor=Conn.cursor()
                strCmd=f'select [key] from task_task where 产品线=\'EMMC\' and [key] like \'{strQueryName}\''
                cursor.execute(strCmd)
                rows=cursor.fetchall()
                for obj in rows:
                    strTestDir=os.path.join(strDirName,str(obj[0]))
                    if strTestDir!=curpath:
                        lPath.append(strTestDir)


    listDir=[]
    for obj in lPath:
        strXu4Dir=os.path.join(obj,'XU4')
        for dirpath,dirnames,filenames in os.walk(strXu4Dir):
            for dir in dirnames:
                if dir.startswith('PV-'):
                    listDir.append(os.path.join(dirpath,dir))
    return listDir

# {'x1':{'time':'123','data':{'t1':'','t2':''}}}
def GetData(listDir:list):
    lLastData=GetTypeAndTime(listDir)
    for onedir in lLastData:
        global g_nSustainedIndex
        global g_nStartPoint
        global g_nEndPoint
        g_nSustainedIndex=-2
        g_nStartPoint=-1
        g_nEndPoint=-1
        strTime,strType,listWRDate=GetWRData(onedir)
        if strTime=='' or strType=='' or len(listWRDate) == 0:
            continue
        else:
            if 'time' in g_AllData[strType]:
                if int(strTime) < int(g_AllData[strType]['time']):
                    continue
            lSWData=GetNormalData(onedir,strType,
                                  'Passed verify performance proxima mmc_empty_card_seq_write_2cycle.log',
                                  GetSWData)
            lFMData=GetNormalData(onedir,strType,
                                  'Passed mmc_tools FIRMWARE get_smart_info.log',
                                  GetFMData,3)
            lRPMBData=GetNormalData(onedir,strType,
                                  'Passed verify performance proxima RPMB_WYS_flow_performance.log',
                                  GetRPMBData,4)
            lFileName=['Passed verify PV latency mmc_init_with_PON_Latency 1.log',
            'Passed verify PV latency mmc_init_with_PON_Latency 0.log',
            'Passed verify PV latency mmc_init_after_spor_Latency.log',
            'Passed verify PV latency mmc_boot_mode_Latency.log',
            'Passed verify PV latency mmc_init_after_partition_Latency.log',
            'Passed verify PV latency mmc_sleep_Latency 0.log',
            'Passed verify PV latency mmc_sleep_Latency 1.log',
            # 'Passed verify PV latency mmc_auto_save_mode_Latency.log',
            'Passed verify PV latency mmc_CE_Write_Latency ignore.log',
            'Passed verify PV latency mmc_CE_Read_Latency ignore hs400 1000 8 1000.log',
            'Passed verify PV latency mmc_CE_Read_Latency ignore hs400 1000 2048 50000.log',
            'Passed verify PV latency mmc_QOS_write_Latency 0 1024.log',
            'Passed verify PV latency mmc_QOS_write_Latency 0 32.log',
            'Passed verify PV latency mmc_normal_erase_Latency ignore.log']
            lPointCnt=[1,1,1,2,1,1,1,1,1,1,1,1,2]
            lLatencyData=[]
            for obj,cnt in zip(lFileName,lPointCnt):
                lData=GetNormalData(onedir,strType,obj,GetLatencyData,cnt)
                lLatencyData+=lData
            
            lParLanData=GetNormalData(onedir,strType,
                                  'Passed verify PV latency mmc_Switch_Partition_Latency.log',
                                  GetSwitch_Partition_LatencyData,8)
            lPonLanData=GetNormalData(onedir,strType,
                                  'Passed verify PV latency mmc_set_PON_Latency.log',
                                  Getset_PON_LatencyData,3)
            lHPIData=GetNormalData(onedir,strType,
                                  'Passed verify PV latency mmc_HPI_Latency.log',
                                  GetHPI_LatencyData,2)
            lConfDevLanData=GetNormalData(onedir,strType,
                                  'Passed verify PV latency mmc_Switch_to_configure_device_Latency.log',
                                  Getconfigure_device_LatencyData,2)
            lFluLanData=GetNormalData(onedir,strType,
                                  'Passed verify PV latency mmc_Flush_Latency.log',
                                  GetFlush_LatencyData,1)
            lDiffSizeWrSmoothness=GetNormalData(onedir,strType,
                                  'Passed verify performance proxima diff_size_WR_smoothness.log',
                                  Getdiff_size_WR_smoothnessData,14)
            
            lIOLatency32k=GetNormalData(onedir,strType,
                                  'Passed verify PV latency mmc_QOS_write_Latency 0 1024.log',
                                  GetIOLatencyData,1021)
            
            lIOLatency16k=GetNormalData(onedir,strType,
                                  'Passed verify PV latency mmc_QOS_write_Latency 0 32.log',
                                  GetIOLatencyData,1021)
            
      
            lUAData=CalUserArea(listWRDate,lSWData,strType)
            lTestRpt=lUAData+lRPMBData+lLatencyData+lParLanData+lPonLanData+lHPIData+lConfDevLanData+lFluLanData
            g_AllData[strType]['time']=strTime
            g_AllData[strType]['fw']=lFMData
            g_AllData[strType]['data']={}
            g_AllData[strType]['data']['testrpt']=lTestRpt
            g_AllData[strType]['data']['wr']=listWRDate
            g_AllData[strType]['data']['sw']=lSWData
            g_AllData[strType]['data']['diffsizewr']=lDiffSizeWrSmoothness
            g_AllData[strType]['data']['IOL32k']=lIOLatency32k
            g_AllData[strType]['data']['IOL16k']=lIOLatency16k




# get WR data,return tuple (timestamp,capacity type,data)
def GetWRData(path:str)->tuple:
    listWRData=[]
    strTime='0'
    strType='x1'
    nPointCnt=g_PointCnt[strType]
    strFile='Passed verify performance proxima SW_SR_RR_RW hs400.log'
    strWRFile=os.path.join(path,strFile)
    if not os.path.exists(strWRFile):
        strFile=GetFailedFile(strFile)
        strWRFile=os.path.join(path,strFile)
    if os.path.exists(strWRFile):
        nInxex=path.rfind('_')
        if nInxex != -1:
            strTime=path[nInxex+1:]
        strCoding='UTF-8'
        nCnt=3
        while nCnt>0:
            try:
                with open(strWRFile,encoding=strCoding) as file:
                    bStart=False
                    strPattern=r'.* (.*)\t(.*)\t(.*)\t(.*)\t(.*)\t(.*)\t(.*).*'
                    for line in file.readlines():
                        if not bStart:
                            if '>>>' in line:
                                nIndex=line.find('>>>')
                                strType=line[nIndex+3:nIndex+5].lower()
                                nPointCnt=g_PointCnt[strType]
                                bStart=True
                                continue
                        if bStart and nPointCnt!=0:
                            listRet=re.findall(strPattern,line)
                            if len(listRet) > 0:
                                # if 'GB' not in listRet[0][0][0].isdigit():
                                if listRet[0][0][0].isdigit():
                                    nPointCnt-=1
                                    listWRData.append([x for x in listRet[0][1:]])
                        elif nPointCnt==0:
                            break
                break
            except Exception as e:
                strCoding='gb2312'
                nCnt-=1
                if nCnt == 1:
                    with open(strWRFile, 'rb') as file:
                        raw_data = file.read()
                        result = chardet.detect(raw_data)
                        strCoding = result['encoding']
        if nCnt==0:
            raise -1
    return (strTime,strType,listWRData)

def GetNormalData(path:str,CapType:str,strFileName,funcOp,nPointCnt:int=0)->list:
    listWRData=[]
    strType=CapType
    if nPointCnt == 0:
        nPointCnt=g_PointCnt[strType]
    strFile=os.path.join(path,strFileName)
    if not os.path.exists(strFile):
        strTmp=GetFailedFile(strFileName)
        strFile=os.path.join(path,strTmp)
    listWRData=funcOp(strFile,nPointCnt)
    if nPointCnt != -1:
        listWRData=listWRData[0:nPointCnt]
    return listWRData

# Passed verify performance proxima mmc_empty_card_seq_write_2cycle.log
def GetSWData(strPath:str,nPointCnt:int):
    listWRData=[]
    if os.path.exists(strPath):
        strCoding='UTF-8'
        nCnt=3
        while nCnt>0:
            try:
                with open(strPath,encoding=strCoding) as file:
                    bStart=False
                    strPattern=r'.* (.*)\t(.*).*'
                    for line in file.readlines():
                        if not bStart:
                            if 'area\tSW' in line:
                                bStart=True
                                continue
                        if bStart and nPointCnt!=0:
                            listRet=re.findall(strPattern,line)
                            if len(listRet) > 0:
                                nPointCnt-=1
                                listWRData.append(listRet[0][1])
                        elif nPointCnt==0:
                            break
                break
            except Exception as e:
                strCoding='gb2312'
                nCnt-=1
                if nCnt == 1:
                    with open(strPath, 'rb') as file:
                        raw_data = file.read()
                        result = chardet.detect(raw_data)
                        strCoding = result['encoding']
        if nCnt==0:
            raise -1

    if 0!=nPointCnt:
        listWRData+=['null']*nPointCnt
    return listWRData

# Passed mmc_tools FIRMWARE get_smart_info.log
def GetFMData(strPath:str,nPointCnt:int):
    listWRData=[]
    if os.path.exists(strPath):
        strCoding='UTF-8'
        nCnt=3
        while nCnt>0:
            try:
                with open(strPath,encoding=strCoding) as file:
                    strpCtrl=r'.* Controller: (.*).*'
                    strpFlash=r'.* Flash: (.*).*'
                    strpFWVer=r'.* FW Ver: (.*).*'

                    for line in file.readlines():
                        if len(listWRData) == 3:
                            break
                        listRet=re.findall(strpCtrl,line)
                        if len(listRet) > 0:
                            listWRData.append(listRet[0])
                            continue

                        listRet=re.findall(strpFlash,line)
                        if len(listRet) > 0:
                            listWRData.append(listRet[0])
                            continue

                        listRet=re.findall(strpFWVer,line)
                        if len(listRet) > 0:
                            listWRData.append(listRet[0])
                            continue  
                break
            except Exception as e:
                strCoding='gb2312'
                nCnt-=1
                if nCnt == 1:
                    with open(strPath, 'rb') as file:
                        raw_data = file.read()
                        result = chardet.detect(raw_data)
                        strCoding = result['encoding']
        if nCnt==0:
            raise -1
    if len(listWRData) <   nPointCnt:
          listWRData+=['null']*(nPointCnt-len(listWRData))       
    return listWRData

# Passed verify performance proxima RPMB_WYS_flow_performance.log
def GetRPMBData(strPath:str,nPointCnt:int):
    listWRData=[]
    if os.path.exists(strPath):
        strCoding='UTF-8'
        nCnt=3
        while nCnt>0:
            try:
                with open(strPath,encoding=strCoding) as file:
                    nType=0
                    strPattern=r'.*rate: \[\[ (.*) \]\].*'
                    for line in file.readlines():
                        if len(listWRData) == 4:
                            break

                        if 'RPMB SW Start' in line:
                            nType=1      
                            continue
                        elif 'RPMB SR Start' in line:
                            nType=2
                            continue
                        elif 'RPMB RR Start' in line:
                            nType=3
                            strPattern=r'.*Write IOPS is (.*) .*'
                            continue
                        elif 'RPMB RW Start' in line:
                            nType=4
                            strPattern=r'.*Write IOPS is (.*) .*'
                            continue
                        if nType == 0:
                            continue
                        else:
                            listRet=re.findall(strPattern,line)
                            if len(listRet) > 0:
                                listWRData.append(listRet[0])
                                continue  
                break
            except Exception as e:
                strCoding='gb2312'
                nCnt-=1
                if nCnt == 1:
                    with open(strPath, 'rb') as file:
                        raw_data = file.read()
                        result = chardet.detect(raw_data)
                        strCoding = result['encoding']
        if nCnt==0:
            raise -1


    if len(listWRData) <   nPointCnt:
          listWRData+=['null']*(nPointCnt-len(listWRData))  
    listWRData[2],listWRData[3]=listWRData[3],listWRData[2]
    return listWRData

# latency file
def GetLatencyData(strPath:str,nPointCnt:int):
    listWRData=[]
    if os.path.exists(strPath):
        strCoding='UTF-8'
        nCnt=3
        while nCnt>0:
            try:
                with open(strPath,encoding=strCoding) as file:
                    strPattern=r'.*Latency->.*actual (.*)\n'
                    for line in file.readlines():
                        listRet=re.findall(strPattern,line)
                        if len(listRet) > 0:
                            strVal=DealUnit(listRet[0])
                            listWRData.append(strVal) 
                break
            except Exception as e:
                strCoding='gb2312'
                nCnt-=1
                if nCnt == 1:
                    with open(strPath, 'rb') as file:
                        raw_data = file.read()
                        result = chardet.detect(raw_data)
                        strCoding = result['encoding']
        if nCnt==0:
            raise -1
    if len(listWRData) <   nPointCnt:
          listWRData+=['null']*(nPointCnt-len(listWRData))       
    return listWRData


# Passed verify PV latency mmc_Switch_Partition_Latency.log
def GetSwitch_Partition_LatencyData(strPath:str,nPointCnt:int):
    listWRData=[]
    if os.path.exists(strPath):
        strCoding='UTF-8'
        nCnt=3
        while nCnt>0:
            try:
                with open(strPath,encoding=strCoding) as file:
                    strPattern=r'.*Latency->.*actual (.*)\n'
                    for line in file.readlines():
                        listRet=re.findall(strPattern,line)
                        if len(listRet) > 0:
                            strVal=DealUnit(listRet[0])
                            listWRData.append(strVal) 
                break
            except Exception as e:
                strCoding='gb2312'
                nCnt-=1
                if nCnt == 1:
                    with open(strPath, 'rb') as file:
                        raw_data = file.read()
                        result = chardet.detect(raw_data)
                        strCoding = result['encoding']
        if nCnt==0:
            raise -1
    if len(listWRData) <   nPointCnt:
          listWRData+=['null']*(nPointCnt-len(listWRData))        
    return listWRData

# Passed verify PV latency mmc_set_PON_Latency.log
def Getset_PON_LatencyData(strPath:str,nPointCnt:int):
    listWRData=[]
    if os.path.exists(strPath):
        strCoding='UTF-8'
        nCnt=3
        while nCnt>0:
            try:
                with open(strPath,encoding=strCoding) as file:
                    strPattern=r'.*Latency->.*actual (.*)\n'
                    for line in file.readlines():
                        listRet=re.findall(strPattern,line)
                        if len(listRet) > 0:
                            strVal=DealUnit(listRet[0])
                            listWRData.append(strVal)  
                break
            except Exception as e:
                strCoding='gb2312'
                nCnt-=1
                if nCnt == 1:
                    with open(strPath, 'rb') as file:
                        raw_data = file.read()
                        result = chardet.detect(raw_data)
                        strCoding = result['encoding']
        if nCnt==0:
            raise -1
  
    if len(listWRData) <   nPointCnt:
          listWRData+=['null']*(nPointCnt-len(listWRData))     
    return listWRData

# Passed verify PV latency mmc_HPI_Latency.log
def GetHPI_LatencyData(strPath:str,nPointCnt:int):
    listWRData=[]
    if os.path.exists(strPath):
        strCoding='UTF-8'
        nCnt=3
        while nCnt>0:
            try:
                with open(strPath,encoding=strCoding) as file:
                    strPattern=r'.*Latency->.*actual (.*)\n'
                    for line in file.readlines():
                        listRet=re.findall(strPattern,line)
                        if len(listRet) > 0:
                            strVal=DealUnit(listRet[0])
                            listWRData.append(strVal)
                break
            except Exception as e:
                strCoding='gb2312'
                nCnt-=1
                if nCnt == 1:
                    with open(strPath, 'rb') as file:
                        raw_data = file.read()
                        result = chardet.detect(raw_data)
                        strCoding = result['encoding']
        if nCnt==0:
            raise -1

    if len(listWRData) <   nPointCnt:
          listWRData+=['null']*(nPointCnt-len(listWRData))         
    return listWRData

# Passed verify PV latency mmc_Switch_to_configure_device_Latency.log
def Getconfigure_device_LatencyData(strPath:str,nPointCnt:int):
    listWRData=[]
    if os.path.exists(strPath):
        strCoding='UTF-8'
        nCnt=3
        while nCnt>0:
            try:
                with open(strPath,encoding=strCoding) as file:
                    strPattern=r'.*Latency.*actual (.*)\n'
                    for line in file.readlines():
                        listRet=re.findall(strPattern,line)
                        if len(listRet) > 0:
                            strVal=DealUnit(listRet[0])
                            listWRData.append(strVal) 
                break
            except Exception as e:
                strCoding='gb2312'
                nCnt-=1
                if nCnt == 1:
                    with open(strPath, 'rb') as file:
                        raw_data = file.read()
                        result = chardet.detect(raw_data)
                        strCoding = result['encoding']
        if nCnt==0:
            raise -1
 
    if len(listWRData) <   nPointCnt:
          listWRData+=['null']*(nPointCnt-len(listWRData))       
    return listWRData

# Passed verify PV latency mmc_Flush_Latency.log
def GetFlush_LatencyData(strPath:str,nPointCnt:int):
    listWRData=[]
    if os.path.exists(strPath):
        strCoding='UTF-8'
        nCnt=3
        while nCnt>0:
            try:
                with open(strPath,encoding=strCoding) as file:
                    strPattern=r'.*Latency->.*actual (.*)\n'
                    for line in file.readlines():
                        listRet=re.findall(strPattern,line)
                        if len(listRet) > 0:
                            strVal=DealUnit(listRet[0])
                            listWRData.append(strVal)  
                break
            except Exception as e:
                strCoding='gb2312'
                nCnt-=1
                if nCnt == 1:
                    with open(strPath, 'rb') as file:
                        raw_data = file.read()
                        result = chardet.detect(raw_data)
                        strCoding = result['encoding']
        if nCnt==0:
            raise -1
    if len(listWRData) <   nPointCnt:
          listWRData+=['null']*(nPointCnt-len(listWRData))       
    return listWRData
        
# Passed verify performance proxima diff_size_WR_smoothness.log
def Getdiff_size_WR_smoothnessData(strPath:str,nPointCnt:int):
    listWRData=[]
    if os.path.exists(strPath):
        strCoding='UTF-8'
        nCnt=3
        while nCnt>0:
            try:
                with open(strPath,encoding=strCoding) as file:
                    # strPattern=r'.*Latency->.*actual (.*)\n'
                    strPattern=r'.* ([0-9\.]+)\s+([0-9\.]+)\s+([0-9\.]+)\s+([0-9\.]+)\s+([0-9\.]+).*'
                    for line in file.readlines():
                        listRet=re.findall(strPattern,line)
                        if len(listRet) > 0:
                            # strVal=DealUnit(listRet[0])
                            listWRData.append(listRet[0])  
                break
            except Exception as e:
                strCoding='gb2312'
                nCnt-=1
                if nCnt == 1:
                    with open(strPath, 'rb') as file:
                        raw_data = file.read()
                        result = chardet.detect(raw_data)
                        strCoding = result['encoding']
        if nCnt==0:
            raise -1

    if len(listWRData) <   nPointCnt:
          nAddCnt=nPointCnt-len(listWRData)
          for i in range(0,nAddCnt):
              listWRData.append(['null']*5)   
    return listWRData

# Passed verify PV latency mmc_QOS_write_Latency 0 1024.log
# Passed verify PV latency mmc_QOS_write_Latency 0 32.log
def GetIOLatencyData(strPath:str,nPointCnt:int):
    # nPointCnt,前17个是柱状图的值，后四个元素是特定的比例对应的值，注意
    lBarChartVal=[0]*17
    lLineChartVal=[0]*1000
    lfPer=[0.8,0.9,0.99,0.999]
    listIOLData=[0]*(nPointCnt)
    if os.path.exists(strPath):
        strCoding='UTF-8'
        nCnt=3
        while nCnt>0:
            try:
                with open(strPath,encoding=strCoding) as file:
                    # strPattern=r'.*Latency->.*actual (.*)\n'
                    strPattern=r'.* ([0-9]+)\s+([0-9]+)\s+([0-9]+)\s+([0-9]+)\s+([0-9]+)\s+([0-9]+)\s+([0-9]+)\s+([0-9]+)\s+([0-9]+)\s+([0-9]+).*'
                    for line in file.readlines():
                        listRet=re.findall(strPattern,line)
                        if len(listRet) > 0:
                            for obj in listRet[0]:
                                ind1,ind2=GetChartValIndex(obj)
                                lBarChartVal[ind1]+=1
                                lLineChartVal[ind2]+=1                         
   
                break
            except Exception as e:
                if 'unknown encoding' not in e.args[0]:
                    print('emmc_pv报告统计 error')
                    raise -1
                strCoding='gb2312'
                nCnt-=1
                if nCnt == 1:
                    with open(strPath, 'rb') as file:
                        raw_data = file.read()
                        result = chardet.detect(raw_data)
                        strCoding = result['encoding']
        if nCnt==0:
            raise -1
    nTotal=sum(lLineChartVal)
    nCnt=len(lLineChartVal)
    nInd=1
    lTmp=[]
    nSum=lLineChartVal[0]
    for fPerc in lfPer:
        fPInd=nTotal*fPerc
        while nInd<nCnt:
            if fPInd<=nSum:
                lTmp.append(nInd*0.2)
                break
            nSum+=lLineChartVal[nInd]
            nInd+=1
    listIOLData=lBarChartVal+lLineChartVal+lTmp
    listIOLData=[str(x) for x in listIOLData]

    if len(listIOLData) <   nPointCnt:
          listIOLData+=['null']*(nPointCnt-len(listIOLData)) 
    return listIOLData


def GetChartValIndex(strVal:str)->tuple:
    nFirst=0
    nSecond=0
    nIOLatency=(int(strVal)/1000)
    if nIOLatency>=2000:
        nInd=-1
    elif nIOLatency>=1000:
        nInd=-2
    elif nIOLatency>=700:
        nInd=-3
    elif nIOLatency>=500:
        nInd=-4
    elif nIOLatency>=350:
        nInd=-5
    elif nIOLatency>=250:
        nInd=-6
    elif nIOLatency>=167:
        nInd=-7
    elif nIOLatency>=100:
        nInd=-8
    elif nIOLatency>=64:
        nInd=-9
    elif nIOLatency>=32:
        nInd=-10
    elif nIOLatency>=16:
        nInd=-11
    elif nIOLatency>=8:
        nInd=-12
    elif nIOLatency>=4:
        nInd=-13
    elif nIOLatency>=3:
        nInd=-14
    elif nIOLatency>=2:
        nInd=-15
    elif nIOLatency>=1:
        nInd=-16
    else:
        nInd=-17
    nFirst=nInd
    if nIOLatency>=200:
        nIOLatency=200
    nSecond=int(nIOLatency/0.2)
    if nSecond>=1000:
        nSecond=999
    return (nFirst,nSecond)


def DealUnit(strVal:str)->str:
    if ' ' in strVal:
        strVal=strVal.split(' ')[0]
    if '%' in strVal:
        strVal=strVal[0:-1]
        strVal=str(round(float(strVal)/100,6))
    return strVal


def CalUserArea(lWRData:list,lSWData:list,strType:str):
    lSW=[]
    lSR=[]
    lRRpre=[]
    lRWqd32=[]
    lRWqd1=[]
    lRRpost=[]
    for row in lWRData:
        lSW.append(row[0])
        lSR.append(row[1])
        lRRpre.append(row[2])
        lRWqd32.append(row[3])
        lRWqd1.append(row[4])
        lRRpost.append(row[5])
    nPointCnt=g_PointCnt[strType]

    # CalCloseData函数，clean点的取值算法，记录SW的clean点的平均值以及序号，后面的WR sheet中的列根据SW的clean点的序号去除最后一个点处理
    # SW
    strSWAver=CalCloseData(lSW[0:int(nPointCnt/2)],5.0,nPointCnt/2)
    strSWSus=CalSustainedPoint(lSW[0:int(nPointCnt/2)],float(strSWAver))
    # strSWSus=CalSustainedPoint(lSWData[0:int(nPointCnt/2)],float(strSWAver))
    fSWDirtyAver=0.0
    if 'null' not in lSW:
        fSWDirtyAver=sum(map(float,lSW[int(nPointCnt/2):]))/(int(nPointCnt/2))
    strSWDirtyAver=str(round(fSWDirtyAver,3))
    # SR
    strSRAver=CalCloseData(lSR[0:int(nPointCnt/2)],5.0,nPointCnt/2)
    strSRSus=CalSustainedPoint(lSR[0:int(nPointCnt/2)],float(strSRAver))

    # RW QD32
    strRWQ32Aver=CalCloseData(lRWqd32[0:int(nPointCnt/2)],500.0,nPointCnt/2)
    strRWQ32Sus=CalSustainedPoint(lRWqd32[0:int(nPointCnt/2)],float(strRWQ32Aver))
    fRWQ32DirtyAver=0.0
    if 'null' not in lRWqd32:
        fRWQ32DirtyAver=sum(map(float,lRWqd32[int(nPointCnt/2):]))/(int(nPointCnt/2))
    strRWQ32DirtyAver=str(round(fRWQ32DirtyAver,3))

    # RW QD1
    strRWQ1Aver=CalCloseData(lRWqd1[0:int(nPointCnt/2)],500.0,nPointCnt/2)

    # RR pre QD32
    strRRpreAver=CalCloseData(lRRpre[0:int(nPointCnt/2)],500.0,nPointCnt/2)
    strRRpreSus=CalSustainedPoint(lRRpre[0:int(nPointCnt/2)],float(strRRpreAver))

    # RR post QD32
    strRRpostAver=CalCloseData(lRRpost[0:int(nPointCnt/2)],500.0,nPointCnt/2)
    strRRpostSus=CalSustainedPoint(lRRpost[0:int(nPointCnt/2)],float(strRRpostAver))
    return [strSWAver,strSWSus,strSWDirtyAver,strSRAver,strSRSus,
            strRWQ32Aver,strRWQ32Sus,strRWQ32DirtyAver,strRWQ1Aver,
            strRRpreAver,strRRpostAver,strRRpreSus,strRRpostSus]


# calculate average
def CalCloseData(lData:list,fDistance:float,nPointCnt:int)->str:
    fAver=0.0
    lOrgData=[]
    fSum=0.0
    global g_nStartPoint
    global g_nEndPoint
    if len(lData) == 0 or 'null' in lData:
        return 'null'
    if isinstance(lData[0],str):
        lOrgData=list(map(float,lData))
    if g_nStartPoint!=-1 and g_nEndPoint!=-1 and g_nStartPoint < g_nEndPoint:
        # 非SW，去掉最后一个点
        nEndPoint=g_nEndPoint-1
        for i in range(g_nStartPoint,nEndPoint):
            fSum+=lOrgData[i]
        fAver=fSum/(nEndPoint-g_nStartPoint)
        return str(round(fAver,3))
    
    lCalData=[lOrgData[1]]
    g_nStartPoint=1
    g_nEndPoint=2
    while(g_nEndPoint<nPointCnt):
        if len(lCalData)>=10:
            break
        elif len(lCalData)>=5 and abs(lOrgData[g_nEndPoint]-lOrgData[g_nEndPoint-1])>fDistance:
            break
        elif abs(lOrgData[g_nEndPoint]-lOrgData[g_nEndPoint-1])<fDistance:
            lCalData.append(lOrgData[g_nEndPoint])
            g_nEndPoint+=1
        elif len(lCalData)<5 and abs(lOrgData[g_nEndPoint]-lOrgData[g_nEndPoint-1])>fDistance:
            lCalData=[lOrgData[g_nEndPoint]]
            g_nStartPoint=g_nEndPoint
            g_nEndPoint+=1
    if len(lCalData) < 5:
        g_nStartPoint=-1
        g_nEndPoint=-1
        return str(round(fAver,3))
    else:
        fAver=sum(lCalData)/len(lCalData)
        return str(round(fAver,3))
    
    # if len(lData) >= 10:
    #     nCnt=10
    #     fSum=lOrgData[0]
    #     for i in range(1,10):
    #         if abs(lOrgData[i]-lOrgData[i-1]) > fDistance:
    #             fSum=sum(lOrgData[0:5])
    #             nCnt=5
    #             break
    #         else:
    #             fSum+=lOrgData[i]      
    #     fAver=fSum/nCnt
    #     fAver=round(fAver,3)
    # elif len(lData) >= 5:
    #     fAver=sum(lOrgData[0:5])/5
    #     fAver=round(fAver,3)
    # else:
    #     fAver=sum(lOrgData)/len(fAver)
    #     fAver=round(fAver,3)
    # return str(fAver)

# Get Sustained
def CalSustainedPoint(lData:list,fBase:float)->str:
    global g_nSustainedIndex
    strRet='null'
    lOrgData=[]
    if -1 == g_nSustainedIndex:
        return strRet
    elif g_nSustainedIndex >= 0:  
        if len(lData) == 0 or 'null' in lData:
            return 'null'     
        lOrgData=list(map(float,lData))
        lOrgData=lOrgData[1:]
        fAver=sum(lOrgData[g_nSustainedIndex:g_nSustainedIndex+10])/10
        strRet=str(round(fAver,3))
        return strRet
    else:
        if len(lData) == 0 or 'null' in lData:
            return 'null'
        lOrgData=list(map(float,lData))
        lOrgData=lOrgData[1:]
        
        nInd=0
        for obj in lOrgData:
            if abs(obj-fBase) > 50:
                break
            nInd+=1
        if (nInd+10) <= len(lOrgData): 
            g_nSustainedIndex=nInd  
            fAver=sum(lOrgData[nInd:nInd+10])/10
            strRet=str(round(fAver,3))
        else:
            g_nSustainedIndex=-1
            strRet='null'    
        return strRet

def WriteXlsData(wb):
    for key,val in g_AllData.items():
        if val=={}:
            continue
        WritTestRpt(wb,key,val['data']['testrpt'],val['fw'])
        WriteWR(wb,key,val['data']['wr'])
        WriteSW(wb,key,val['data']['sw'])
        WriteDiffSizeWrSmooth(wb,key,val['data']['diffsizewr'])
        WriteIOL32K(wb,key,val['data']['IOL32k'])
        WriteIOL16K(wb,key,val['data']['IOL16k'])



def WritTestRpt(wb,strType:str,lData:list,lFW:list):
    nCol=5
    nRow=4
    if strType == 'x1':
        nCol=5
    elif strType == 'x2':
        nCol=7
    elif strType == 'x4':
        nCol=9
    lTarget=g_StandardVal[strType]
    if len(lTarget) > len(lData):
        lData+=['null']*(len(lTarget)-len(lData))
    lData=['0' if x=='null' else x for x in lData]
    ws=wb['PV Test report']
    lDataTmp=list(map(float,lData))
    for i in range(5,13):
        lDataTmp[i]=round(lDataTmp[i]/1000,3)
    
    #写入测试值
    NormalWrite(ws,list(map(float,lDataTmp[0:17])),nRow,nCol,False)
    NormalWrite(ws,list(map(float,lDataTmp[17:])),nRow+18,nCol,False)
    # 写入标准值
    NormalWrite(ws,list(map(float,lTarget[0:17])),nRow,nCol-1,False)
    NormalWrite(ws,list(map(float,lTarget[17:33])),nRow+18,nCol-1,False)
    NormalWrite(ws,list(map(float,lTarget[40:])),nRow+41,nCol-1,False)
    strFW=' '.join(lFW)
    NormalWrite(ws,[strFW],1,1,False,Font(bold=True,size=16),Alignment(horizontal='left'))


    nInd=0
    nRowTmp=nRow
    for strTar,strVal in zip(lTarget,lDataTmp):
        if strVal == 'null':
            nInd+=1
            continue
        # if nInd==30 or nInd==31 or nInd==32 or nInd==33 or nInd==47:
        #     nRowTmp=nRow+nInd+1
        #     if float(strTar) > float(strVal):
        #         NormalWrite(ws,[float(strVal)],nRowTmp,nCol,False,red_font)
        if nInd>16:
            nRowTmp=nRow+nInd+1
            if float(strTar) < float(strVal):
                NormalWrite(ws,[float(strVal)],nRowTmp,nCol,False,red_font)
        else:
            nRowTmp=nRow+nInd
            if float(strTar) > float(strVal):
                NormalWrite(ws,[float(strVal)],nRowTmp,nCol,False,red_font)
        nInd+=1
    
def WriteWR(wb,strType:str,lData:list):
    strWsName=strType+'_'+'WR'
    ws=wb[strWsName]
    nRow=2
    if 'null' in lData:
        lData=['0' if x=='null' else x for x in lData]
    for obj in lData:
        NormalWrite(ws,list(map(float,obj)),nRow,2,True)
        nRow+=1
    WriteChartTitle(ws,strType)
    

def WriteSW(wb,strType:str,lData:list):
    strWsName=strType+'_'+'SW full card twice'
    ws=wb[strWsName]
    if 'null' in lData:
        lData=['0' if x=='null' else x for x in lData]
    NormalWrite(ws,list(map(float,lData)),2,2,False)
    WriteChartTitle(ws,strType)

def WriteDiffSizeWrSmooth(wb,strType:str,lData:list):
    strWsName='diff_size_WR_smoothness'
    ws=wb[strWsName]
    lConvData=[]
    for obj in lData:
        if 'null' in obj:
            lConvData.append(['0' if x=='null' else x for x in obj])
        else:
            lConvData.append(obj)
    nCol=1
    nRow=2
    if strType == 'x1':
        nRow=2
    elif strType == 'x2':
        nRow=19
    elif strType == 'x4':
        nRow=36
    for lOneline in lConvData:
        NormalWrite(ws,list(map(float,lOneline)),nRow,nCol,True)
        nRow+=1

def WriteIOL32K(wb,strType:str,lData:list):
    if 'null' in lData:
        lData=(['0' if x=='null' else x for x in lData])

    strWsName=strType+'_confidence_map'
    ws=wb[strWsName]
    lnTmp=[int(x) for x in lData[0:17]]
    NormalWrite(ws,lnTmp,2,2,False)
    lnTmp=[int(x) for x in lData[17:-4]]
    NormalWrite(ws,lnTmp,21,2,False)
    lfTmp=[float(x) for x in lData[-4:]]
    NormalWrite(ws,lfTmp,2,6,False)


def WriteIOL16K(wb,strType:str,lData:list):
    if 'null' in lData:
        lData=(['0' if x=='null' else x for x in lData])
    strWsName=strType+'_confidence_map'
    ws=wb[strWsName]
    lnTmp=[int(x) for x in lData[0:17]]
    NormalWrite(ws,lnTmp,2,3,False)
    lnTmp=[int(x) for x in lData[17:-4]]
    NormalWrite(ws,lnTmp,21,4,False)
    lfTmp=[float(x) for x in lData[-4:]]
    NormalWrite(ws,lfTmp,2,7,False)

def ChangeChartData(ws,strType):
    charts=ws._charts
    for chart in charts:
        orgTitle=chart.title.tx.rich.p[0].r[0].t
        lTitle=orgTitle.split(' ')
        for i in range(len(lTitle)):
            if 'FW' in lTitle[i]:
                lTitle[i]='FW'+g_AllData[strType]['fw'][2]
                break
        chart.title.tx.rich.p[0].r[0].t=' '.join(lTitle)


def WriteChartTitle(ws,strType):
    charts=ws._charts
    for chart in charts:
        orgTitle=chart.title.tx.rich.p[0].r[0].t
        lTitle=orgTitle.split(' ')
        for i in range(len(lTitle)):
            if 'FW' in lTitle[i]:
                lTitle[i]='FW'+g_AllData[strType]['fw'][2]
                break
        chart.title.tx.rich.p[0].r[0].t=' '.join(lTitle)

def NormalWrite(ws,listObj:list,nStartRow:int,nStartCol:int,bRow:bool,rgb="00000000",alignment=Alignment(horizontal='center',vertical='center')):
    ws.alignment=alignment
    if len(listObj) > 0:
        if bRow == True:
            for OneCell in listObj:
                ws['%s%d'%(get_column_letter(nStartCol),nStartRow)] = OneCell
                # ws['%s%d'%(get_column_letter(nStartCol), nStartRow)].border = my_border()
                ws['%s%d'%(get_column_letter(nStartCol), nStartRow)].alignment=alignment
                # ws['%s%d'%(get_column_letter(nStartCol), nStartRow)].backgroundColor=rgb
                ws['%s%d'%(get_column_letter(nStartCol), nStartRow)].font=rgb
                nStartCol+=1
        else:
            for OneCell in listObj:
                ws['%s%d'%(get_column_letter(nStartCol),nStartRow)] = OneCell
                # ws['%s%d'%(get_column_letter(nStartCol), nStartRow)].border = my_border()
                ws['%s%d'%(get_column_letter(nStartCol), nStartRow)].alignment=alignment
                ws['%s%d'%(get_column_letter(nStartCol), nStartRow)].font=rgb
                nStartRow+=1

def my_border(t_border='thin', b_border='thin', l_border='thin', r_border='thin'):
    border = Border(top=Side(border_style=t_border, color=colors.BLACK),
                    bottom=Side(border_style=b_border, color=colors.BLACK),
                    left=Side(border_style=l_border, color=colors.BLACK),
                    right=Side(border_style=r_border, color=colors.BLACK))
    return border

def GetFailedFile(strFile:str)->str:
    strRet=strFile
    nIndex=strFile.find(' ')
    if nIndex!=-1:
        strRet='Failed '+strFile[nIndex+1:]
    return strRet



if __name__ == '__main__':
    curPath=sys.argv[0]
    curPath=os.path.dirname(curPath)
    excelTmp=os.path.join(curPath,'XU4_template.xlsx')
    wb = openpyxl.load_workbook(excelTmp)
    rptPath=curPath
    if len(sys.argv)>1:
        rptPath=sys.argv[1]
    else:
        rptPath=curPath
    Run(curPath,rptPath,wb,'',False)
    strExcelName+='_XU4_PV_Report.xlsx'
    resultFile=os.path.join(curPath,strExcelName)
    wb.save(resultFile)

