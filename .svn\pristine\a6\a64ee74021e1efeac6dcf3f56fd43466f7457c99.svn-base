import PublicFuc,re,copy
from openpyxl.utils import get_column_letter
from openpyxl.drawing.image import Image
from openpyxl.styles import  PatternFill,Alignment,Border,Side,colors,Font
import sys

warnFill = PatternFill('solid', fgColor='FF0000')

def Run(curpath, workBook, alignment):
    ws = workBook['主页']
    ws.alignment = alignment
    ProHTBit(ws, workBook)
    ProRdDisturb(ws, workBook)
    ProHTH2Test(ws, workBook)
    ProHWLDH2Test(ws, workBook)
    ProLWHDH2Test(ws, workBook)
    ProCDMPicture(ws, workBook)
    ProASSDPicture(ws, workBook)

def ProHTBit(ws, workBook):
    wsTmp = workBook['02-高温BIT老化']
    targetLineNo = 5
    MIN_LINE = 5
    MAX_LINE = 104
    colList = ['C','H','D','G','J']
    dataList = GetSpecialMultiDataList(MIN_LINE, MAX_LINE, colList, wsTmp)
    if dataList == []:
        return
    testResult = 'PASS'
    realstr = ''
    passCnt = 0
    for item in dataList:
        if item[1] != '' and item[1] != 'PASS':
            testResult = 'FAIL'
        else:
            passCnt += 1
        realstr += item[0] + ': '
        if item[3] != '':
            realstr += item[3] + '圈、'
        if item[4] != '':
            realstr += '写/读数据量=' + item[4] + ' G' + '\n'
    if realstr != '':
        realstr = realstr[:-1]

    # ws['E%d' % targetLineNo] = str(dataList[0][2]) + 'G'
    # ws['F%d' % targetLineNo] = len(dataList)
    # ws['I%d' % targetLineNo] = passCnt
    # ws['J%d' % targetLineNo] = len(dataList) - passCnt
    # ws['K%d' % targetLineNo] = testResult
    ws['L%d' % targetLineNo] = realstr
    # if testResult == 'FAIL':
    #     ws['K%d' % targetLineNo].fill = warnFill

def ProRdDisturb(ws, workBook):
    wsTmp = workBook['03-常温ReadDisturb']
    targetLineNo = 6
    MIN_LINE = 4
    MAX_LINE = 103
    colList = ['C','R','D','T']
    dataList = GetSpecialMultiDataList(MIN_LINE, MAX_LINE, colList, wsTmp)
    if dataList == []:
        return
    testResult = 'PASS'
    realstr = ''
    passCnt = 0
    for item in dataList:
        if item[1] != '' and item[1] != 'PASS':
            testResult = 'FAIL'
        else:
            passCnt += 1
        realstr += item[0] + ': '
        if item[3] != '':
            realstr += '写/读数据量=' + item[3] + ' G' + '\n'
    if realstr != '':
        realstr = realstr[:-1]

    # ws['E%d' % targetLineNo] = str(dataList[0][2]) + 'G'
    # ws['F%d' % targetLineNo] = len(dataList)
    # ws['I%d' % targetLineNo] = passCnt
    # ws['J%d' % targetLineNo] = len(dataList) - passCnt
    # ws['K%d' % targetLineNo] = testResult
    ws['L%d' % targetLineNo] = realstr
    # if testResult == 'FAIL':
    #     ws['K%d' % targetLineNo].fill = warnFill

def ProHTH2Test(ws, workBook):
    wsTmp = workBook['04-高温数据保持']
    targetLineNo = 7
    MIN_LINE = 4
    MAX_LINE = 103
    colList = ['B','H','L','C','F','G','K']
    dataList = GetSpecialMultiDataList(MIN_LINE, MAX_LINE, colList, wsTmp)
    if dataList == []:
        return
    testResult = 'PASS'
    realstr = ''
    passCnt = 0
    for item in dataList:
        if item[1] != '' and item[1] != 'PASS':
            testResult = 'FAIL'
        elif item[2] != '' and item[2] != 'PASS':
            testResult = 'FAIL'
        else:
            passCnt += 1

    wrRateRT = getMaxandMinData(dataList, 4)
    rdRateRT = getMaxandMinData(dataList, 5)
    rdRateHT = getMaxandMinData(dataList, 6)

    realstr += '常温写速度:' + wrRateRT[0] + '~' + wrRateRT[1] + '\n'
    realstr += '常温读速度:' + rdRateRT[0] + '~' + rdRateRT[1] + '\n'
    realstr += '烘烤后读速度:' + rdRateHT[0] + '~' + rdRateHT[1]

    # ws['E%d' % targetLineNo] = str(dataList[0][3]) + 'G'
    # ws['F%d' % targetLineNo] = len(dataList)
    # ws['I%d' % targetLineNo] = passCnt
    # ws['J%d' % targetLineNo] = len(dataList) - passCnt
    # ws['K%d' % targetLineNo] = testResult
    ws['L%d' % targetLineNo] = realstr
    # if testResult == 'FAIL':
    #     ws['K%d' % targetLineNo].fill = warnFill

def ProHWLDH2Test(ws, workBook):
    wsTmp = workBook['05-高写低读、低写高读']
    targetLineNo = 8
    MIN_LINE = 4
    MAX_LINE = 11
    colList = ['B','H','L','C','F','G','K']
    dataList = GetSpecialMultiDataList(MIN_LINE, MAX_LINE, colList, wsTmp)
    if dataList == []:
        return
    testResult = 'PASS'
    realstrHW = '高温写速度:'
    realstrHR = '高温读速度:'
    realstrLW = '低温读速度:'
    passCnt = 0
    for item in dataList:
        if item[1] != '' and item[1] != 'PASS':
            testResult = 'FAIL'
        elif item[2] != '' and item[2] != 'PASS':
            testResult = 'FAIL'
        else:
            passCnt += 1
        if item[4] == None:
            continue
        realstrHW += item[4] + '、'
        if item[5] == None:
            continue
        realstrHR += item[5] + '、'
        if item[6] == None:
            continue
        realstrLW += item[6] + '、'

    if len(realstrHW) > 7:
        realstrHW = realstrHW[:-1] + '\n'
    if len(realstrHR) > 7:
        realstrHR = realstrHR[:-1] + '\n'
    if len(realstrLW) > 7:
        realstrLW = realstrLW[:-1] + '\n'

    # ws['E%d' % targetLineNo] = str(dataList[0][3]) + 'G'
    # ws['F%d' % targetLineNo] = len(dataList)
    # ws['I%d' % targetLineNo] = passCnt
    # ws['J%d' % targetLineNo] = len(dataList) - passCnt
    # ws['K%d' % targetLineNo] = testResult
    ws['L%d' % targetLineNo] = realstrHW + realstrHR + realstrLW
    # if testResult == 'FAIL':
    #     ws['K%d' % targetLineNo].fill = warnFill

def ProLWHDH2Test(ws, workBook):
    wsTmp = workBook['05-高写低读、低写高读']
    targetLineNo = 9
    MIN_LINE = 15
    MAX_LINE = 22
    colList = ['B','H','L','C','F','G','K']
    dataList = GetSpecialMultiDataList(MIN_LINE, MAX_LINE, colList, wsTmp)
    if dataList == []:
        return
    testResult = 'PASS'
    realstrLW = '低温写速度:'
    realstrLR = '低温读速度:'
    realstrHW = '高温读速度:'
    passCnt = 0
    for item in dataList:
        if item[1] != '' and item[1] != 'PASS':
            testResult = 'FAIL'
        elif item[2] != '' and item[2] != 'PASS':
            testResult = 'FAIL'
        else:
            passCnt += 1
        if item[4] == None:
            continue
        realstrLW += item[4] + '、'
        if item[5] == None:
            continue
        realstrLR += item[5] + '、'
        if item[6] == None:
            continue
        realstrHW += item[6] + '、'

    if len(realstrLW) > 7:
        realstrLW = realstrLW[:-1] + '\n'
    if len(realstrLR) > 7:
        realstrLR = realstrLR[:-1] + '\n'
    if len(realstrHW) > 7:
        realstrHW = realstrHW[:-1] + '\n'

    # ws['E%d' % targetLineNo] = str(dataList[0][3]) + 'G'
    # ws['F%d' % targetLineNo] = len(dataList)
    # ws['I%d' % targetLineNo] = passCnt
    # ws['J%d' % targetLineNo] = len(dataList) - passCnt
    # ws['K%d' % targetLineNo] = testResult
    ws['L%d' % targetLineNo] = realstrLW + realstrLR + realstrHW
    # if testResult == 'FAIL':
    #     ws['K%d' % targetLineNo].fill = warnFill

def ProCDMPicture(ws, workBook):
    wsTmp = workBook['06-初始_性能测试']
    MIN_LINE = 5
    MAX_LINE = 24
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'C', wsTmp)

    if totalSampleCnt < 1:
        return

    for image in wsTmp._images:
        newImg = copy.deepcopy(image)
        if image.anchor == 'A25':
            newImg.width = 529
            newImg.height = 273
            ws.add_image(newImg, 'B12')

def ProASSDPicture(ws, workBook):
    wsTmp = workBook['06-初始_性能测试']
    MIN_LINE = 30
    MAX_LINE = 49
    totalSampleCnt = GetSpecialDataCnt(MIN_LINE, MAX_LINE, 'C', wsTmp)

    if totalSampleCnt < 1:
        return

    for image in wsTmp._images:
        newImg = copy.deepcopy(image)
        if image.anchor == 'A50':
            newImg.width = 400
            newImg.height = 400
            ws.add_image(newImg, 'I12')


def GetSpecialMultiDataList(_startRow,_endRow,_colNameList,ws,step = 1):
    dic = []
    if _startRow > _endRow:
        return dic
    for rowNo in range(_startRow,_endRow+1,step):
        oneRow = []
        for _colName in _colNameList:
            if _colName == '':
                oneRow.append('')
            else:
                celPosSample = '%s%d'%(_colName, rowNo)
                celValue = ws[celPosSample].value
                oneRow.append(celValue)

        if IsValidData(oneRow):
            dic.append(oneRow)
        else:
            break

    return dic

def GetSpecialDataCnt(_startRow, _endRow, _colName, ws, step=1):
    cnt = 0
    if _startRow > _endRow:
        return 0
    for rowNo in range(_startRow, _endRow + 1, step):
        celPosSample = '%s%d' % (_colName, rowNo)
        celValue = ws[celPosSample].value
        if celValue != '' and celValue != None:
            cnt += 1
    return cnt

def IsValidData(dataLine):
    if len(dataLine) < 3:
        return False

    isValid = False
    for idx in range(1, len(dataLine)):
        if dataLine[idx] != None:
            isValid = True

    return isValid

def getMaxandMinData(list, key):
    maxDat = sys.float_info.min
    minDat = sys.float_info.max
    for item in list:
        if item[key] != None and item[key] != '':
            rtDat = item[key]
            if 'MByte/s' in rtDat:
                rtDat = rtDat.replace('MByte/s', '')
                rtDat = float(rtDat)/1024
            else:
                rtDat = rtDat.replace('GByte/s', '')
                rtDat = float(rtDat)
            if rtDat > maxDat:
                maxDat = rtDat
            if rtDat < minDat:
                minDat = rtDat

    strMinDat = ''
    strMaxDat = ''
    if maxDat == sys.float_info.min or minDat == sys.float_info.max:
        return ['', '']
    if maxDat < 1.0:
        maxDat *= 1024
        strMaxDat += str(maxDat) + 'MByte/s'
    else:
        strMaxDat += str(maxDat) + 'GByte/s'
    if minDat < 1:
        minDat *= 1024
        strMinDat += str(minDat) + 'MByte/s'
    else:
        strMinDat += str(minDat) + 'GByte/s'

    return [strMinDat, strMaxDat]


