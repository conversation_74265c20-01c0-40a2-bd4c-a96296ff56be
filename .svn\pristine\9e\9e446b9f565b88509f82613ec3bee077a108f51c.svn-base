﻿<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN" "http://www.w3.org/TR/html4/loose.dtd">
<html onmousewheel="return !event.shiftKey" oncontextmenu="if(event.srcElement.id!='Comment'){return false}" onselectstart="if(event.srcElement.id!='Comment'){return false}" ondragover="return false">
<head>
  <title>Queues &amp; Threads</title>
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <link id="StyleSheet" href="../theme/default/Main.css" rel="stylesheet" type="text/css" />
</head>
<body onmousewheel="return !event.shiftKey" oncontextmenu="if(event.srcElement.id!='Comment'){return false}" onselectstart="if(event.srcElement.id!='Comment'){return false}" ondragover="return false">
<table border="0" cellspacing="0" cellpadding="4" align="center">
  <tr>
    <td>&nbsp;</td><td align="center">Queues</td><td align="center">Threads</td>
  </tr>
  <tr>
    <td>Sequential</td>
    <td align="center">
      <select name="SequentialQueues" id="SequentialQueues" title="Sequential Queues">
      </select>
    </td>
    <td align="center">
      <select name="SequentialThreads" id="SequentialThreads" title="Sequential Threads">
      </select>
    </td>
  </tr>
  <tr>
    <td>Random</td>
    <td align="center">
      <select name="RandomQueues" id="RandomQueues" title="Random Queues">
      </select>
    </td>
    <td align="center">
      <select name="RandomThreads" id="RandomThreads" title="Random Threads">
      </select>
    </td>
  </tr>  
</table>
</body>
</html>
