# -*- mode: python -*-

block_cipher = None


a = Analysis(['ufsreport.py'],
             pathex=['E:\\svn\\xu4_Ufs'],
             binaries=[],
             datas=[],
             hiddenimports=[],
             hookspath=[],
             runtime_hooks=[],
             excludes=['altgraph', 'amqp', 'anyjson', 'apk-parse', 'APScheduler', 'asgiref', 'asn1crypto', 'astroid', 'autopep8', 'billiard', 'bootstraps', 'celery', 'celery-with-redis', 'certifi', 'cffi', 'charset-normalizer', 'chinesecalendar', 'click', 'colorama', 'coloredlogs', 'comtypes', 'cryptography', 'cycler', 'decorator', 'Deprecated', 'Django', 'django-apscheduler', 'django-bootstrap4', 'django-cors-headers', 'django-mssql-backend', 'django-mysql', 'django-pytds', 'django-ratelimit', 'emqx-extension-sdk', 'et-xmlfile', 'Flask', 'Flask-WTF', 'future', 'goto-statement', 'grpcio', 'grpcio-tools', 'humanfriendly', 'idna', 'imageio', 'isort', 'itsdangerous', 'Jinja2', 'joblib', 'kiwisolver', 'kombu', 'lazy-object-proxy', 'macholib', 'MarkupSafe', 'matplotlib', 'mccabe', 'networkx', 'np', 'numpy', 'opencc-python-reimplemented', 'opencv-contrib-python', 'opencv-python', 'packaging', 'pandas', 'pefile', 'Pillow', 'pip', 'portalocker', 'prettytable', 'protobuf', 'py', 'pycharts', 'pycodestyle', 'pycparser', 'pyecharts', 'pygame', 'pyinstall', 'PyInstaller', 'pylint', 'pymssql', 'PyMySQL', 'pyparsing', 'PyPDF2', 'pyperclip', 'pypiwin32', 'pyreadline', 'pytesseract', 'python-dateutil', 'python-logstash', 'python-tds', 'pytz', 'PyWavelets', 'pywifi', 'pywin32', 'pywin32-ctypes', 'PyYAML', 'qrcode', 'redis', 'reportlab', 'retry', 'scikit-image', 'scikit-learn', 'scipy', 'selenium', 'setuptools', 'simplejson', 'sip', 'six', 'sqlparse', 'threadpoolctl', 'tifffile', 'toml', 'tomli', 'tornado', 'typed-ast', 'typing-extensions', 'tzlocal', 'urllib3', 'vine', 'Werkzeug', 'wrapt', 'WTForms', 'xlrd', 'XlsxWriter', 'xlwings', 'zipstream'],
             win_no_prefer_redirects=False,
             win_private_assemblies=False,
             cipher=block_cipher,
             noarchive=False)
pyz = PYZ(a.pure, a.zipped_data,
             cipher=block_cipher)
exe = EXE(pyz,
          a.scripts,
          a.binaries,
          a.zipfiles,
          a.datas,
          [],
          name='ufsreport',
          debug=False,
          bootloader_ignore_signals=False,
          strip=False,
          upx=True,
          runtime_tmpdir=None,
          console=True )
